"""
手动输入对话框
"""
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import date, datetime

class ManualInputDialog:
    def __init__(self, parent, data_processor):
        self.parent = parent
        self.data_processor = data_processor
        self.result = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("手动输入开奖数据")
        self.dialog.geometry("400x300")
        self.dialog.configure(bg='#f0f0f0')
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
    
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (300 // 2)
        self.dialog.geometry(f"400x300+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_label = ttk.Label(self.dialog, text="✏️ 手动输入开奖数据", 
                               font=('Arial', 14, 'bold'), background='#f0f0f0')
        title_label.pack(pady=10)
        
        # 主框架
        main_frame = tk.Frame(self.dialog, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # 期号输入
        period_frame = tk.Frame(main_frame, bg='#f0f0f0')
        period_frame.pack(fill='x', pady=5)
        ttk.Label(period_frame, text="期号:", background='#f0f0f0').pack(side='left')
        self.period_var = tk.StringVar()
        ttk.Entry(period_frame, textvariable=self.period_var, width=15).pack(side='right')
        
        # 日期输入
        date_frame = tk.Frame(main_frame, bg='#f0f0f0')
        date_frame.pack(fill='x', pady=5)
        ttk.Label(date_frame, text="开奖日期:", background='#f0f0f0').pack(side='left')
        self.date_var = tk.StringVar(value=date.today().strftime('%Y-%m-%d'))
        ttk.Entry(date_frame, textvariable=self.date_var, width=15).pack(side='right')
        
        # 正码输入
        regular_frame = tk.Frame(main_frame, bg='#f0f0f0')
        regular_frame.pack(fill='x', pady=5)
        ttk.Label(regular_frame, text="正码 (6个):", background='#f0f0f0').pack(anchor='w')
        
        # 6个正码输入框
        numbers_frame = tk.Frame(main_frame, bg='#f0f0f0')
        numbers_frame.pack(fill='x', pady=5)
        
        self.regular_vars = []
        for i in range(6):
            var = tk.StringVar()
            self.regular_vars.append(var)
            ttk.Entry(numbers_frame, textvariable=var, width=5).pack(side='left', padx=2)
        
        # 特码输入
        special_frame = tk.Frame(main_frame, bg='#f0f0f0')
        special_frame.pack(fill='x', pady=5)
        ttk.Label(special_frame, text="特码:", background='#f0f0f0').pack(side='left')
        self.special_var = tk.StringVar()
        ttk.Entry(special_frame, textvariable=self.special_var, width=5).pack(side='right')
        
        # 按钮框架
        button_frame = tk.Frame(main_frame, bg='#f0f0f0')
        button_frame.pack(fill='x', pady=20)
        
        ttk.Button(button_frame, text="✅ 确定", command=self.save_data).pack(side='left', padx=5)
        ttk.Button(button_frame, text="❌ 取消", command=self.cancel).pack(side='right', padx=5)
    
    def save_data(self):
        """保存数据"""
        try:
            # 验证输入
            period_number = self.period_var.get().strip()
            if not period_number:
                messagebox.showerror("输入错误", "请输入期号")
                return
            
            date_str = self.date_var.get().strip()
            try:
                draw_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                messagebox.showerror("输入错误", "日期格式错误，请使用 YYYY-MM-DD 格式")
                return
            
            # 验证正码
            regular_numbers = []
            for i, var in enumerate(self.regular_vars):
                num_str = var.get().strip()
                if not num_str:
                    messagebox.showerror("输入错误", f"请输入第{i+1}个正码")
                    return
                try:
                    num = int(num_str)
                    if not (1 <= num <= 49):
                        messagebox.showerror("输入错误", f"正码必须在1-49之间")
                        return
                    regular_numbers.append(num)
                except ValueError:
                    messagebox.showerror("输入错误", f"第{i+1}个正码必须是数字")
                    return
            
            # 检查正码重复
            if len(set(regular_numbers)) != 6:
                messagebox.showerror("输入错误", "正码不能重复")
                return
            
            # 验证特码
            special_str = self.special_var.get().strip()
            if not special_str:
                messagebox.showerror("输入错误", "请输入特码")
                return
            try:
                special_number = int(special_str)
                if not (1 <= special_number <= 49):
                    messagebox.showerror("输入错误", "特码必须在1-49之间")
                    return
            except ValueError:
                messagebox.showerror("输入错误", "特码必须是数字")
                return
            
            # 保存数据
            success = self.data_processor.manual_input_record(
                period_number=period_number,
                draw_date=draw_date,
                regular_numbers=regular_numbers,
                special_number=special_number
            )
            
            if success:
                messagebox.showinfo("保存成功", "开奖数据已成功保存")
                self.result = True
                self.dialog.destroy()
            else:
                messagebox.showerror("保存失败", "数据保存失败，请检查数据是否已存在")
                
        except Exception as e:
            messagebox.showerror("保存错误", f"保存数据时发生错误：{e}")
    
    def cancel(self):
        """取消"""
        self.result = False
        self.dialog.destroy()
