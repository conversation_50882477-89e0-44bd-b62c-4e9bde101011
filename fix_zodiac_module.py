"""
修复生肖扩展模组的预测方法参数问题
"""

# 读取现有文件
with open("src/perfect_prediction_system.py", "r", encoding="utf-8") as f:
    content = f.read()

print("🔧 修复生肖扩展模组预测方法")
print("=" * 40)

# 查找MockZodiacExtendedModule类并修复predict方法
class_start = content.find("class MockZodiacExtendedModule:")
if class_start != -1:
    print("✅ 找到MockZodiacExtendedModule类")
    
    # 查找predict方法
    predict_start = content.find("def predict(self, target_date):", class_start)
    if predict_start != -1:
        print("✅ 找到predict方法，修复参数")
        
        # 修复predict方法的参数
        old_signature = "def predict(self, target_date):"
        new_signature = "def predict(self, target_date=None, historical_data=None):"
        
        content = content.replace(old_signature, new_signature)
        
        # 写回文件
        with open("src/perfect_prediction_system.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        print("✅ 已修复predict方法参数")
        print("📁 文件已更新: src/perfect_prediction_system.py")
    else:
        print("❌ 未找到predict方法")
else:
    print("❌ 未找到MockZodiacExtendedModule类")

# 验证修复
print("\n🔍 验证修复结果...")

try:
    import sys
    sys.path.append('src')
    
    # 重新导入模块
    import importlib
    if 'perfect_prediction_system' in sys.modules:
        importlib.reload(sys.modules['perfect_prediction_system'])
    
    from perfect_prediction_system import PerfectPredictionSystem
    
    # 创建系统并初始化
    system = PerfectPredictionSystem()
    system.initialize_modules()
    
    print("✅ 系统初始化成功")
    
    # 测试生肖扩展模组
    if hasattr(system, 'zodiac_extended') and system.zodiac_extended:
        print("✅ 生肖扩展模组存在")
        
        try:
            # 测试无参数调用
            result1 = system.zodiac_extended.predict()
            print(f"✅ 无参数预测: {len(result1.get('predicted_numbers', []))} 个号码")
            
            # 测试带参数调用
            result2 = system.zodiac_extended.predict("2025-06-23")
            print(f"✅ 带参数预测: {len(result2.get('predicted_numbers', []))} 个号码")
            
            # 测试生肖预测
            if hasattr(system.zodiac_extended, 'predict_zodiacs'):
                zodiacs = system.zodiac_extended.predict_zodiacs([1, 2, 3, 4])
                print(f"✅ 生肖预测: {zodiacs}")
            
        except Exception as e:
            print(f"❌ 生肖扩展模组测试失败: {e}")
    else:
        print("❌ 生肖扩展模组不存在")
    
    # 测试完整预测
    print(f"\n🎯 测试完整预测功能:")
    try:
        result = system.run_complete_prediction("2025-06-23")
        print(f"✅ 完整预测成功")
        
        final_results = result.get('final_results', {})
        if final_results:
            numbers = final_results.get('recommended_16_numbers', [])
            zodiacs = final_results.get('recommended_4_zodiacs', [])
            confidence = final_results.get('overall_confidence', 0)
            
            print(f"   📊 推荐号码: {len(numbers)} 个 - {numbers}")
            print(f"   🐲 推荐生肖: {len(zodiacs)} 个 - {zodiacs}")
            print(f"   📈 整体置信度: {confidence}")
        
        # 检查模组预测结果
        module_predictions = result.get('module_predictions', {})
        if module_predictions:
            print(f"\n📋 各模组预测结果:")
            for module_name, prediction in module_predictions.items():
                if isinstance(prediction, dict) and 'predicted_numbers' in prediction:
                    numbers = prediction['predicted_numbers']
                    confidence = prediction.get('confidence', 0)
                    print(f"   {module_name}: {len(numbers)} 个号码, 置信度: {confidence}")
        
    except Exception as e:
        print(f"❌ 完整预测失败: {e}")
        import traceback
        traceback.print_exc()
    
except Exception as e:
    print(f"❌ 验证失败: {e}")
    import traceback
    traceback.print_exc()
