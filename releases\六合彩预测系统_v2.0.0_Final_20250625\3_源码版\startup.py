"""
六合彩预测系统启动脚本
"""

import sys
import os
from pathlib import Path

def setup_environment():
    """设置运行环境"""
    # 获取可执行文件目录
    if getattr(sys, 'frozen', False):
        # 打包后的环境
        base_path = Path(sys._MEIPASS)
        app_path = Path(sys.executable).parent
    else:
        # 开发环境
        base_path = Path(__file__).parent
        app_path = base_path
    
    # 添加路径
    sys.path.insert(0, str(base_path))
    sys.path.insert(0, str(base_path / "src"))
    
    # 设置工作目录
    os.chdir(app_path)
    
    return base_path, app_path

if __name__ == "__main__":
    try:
        # 设置环境
        base_path, app_path = setup_environment()
        
        # 启动主程序
        from lottery_prediction_gui import LotteryPredictionGUI
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        window = LotteryPredictionGUI()
        window.show()
        
        sys.exit(app.exec_())
        
    except Exception as e:
        import traceback
        error_msg = f"启动失败: {e}\n\n详细错误:\n{traceback.format_exc()}"
        
        try:
            from PyQt5.QtWidgets import QApplication, QMessageBox
            app = QApplication(sys.argv)
            QMessageBox.critical(None, "启动错误", error_msg)
        except:
            print(error_msg)
            input("按回车键退出...")
