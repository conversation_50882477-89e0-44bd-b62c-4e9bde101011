"""
简化的历史模拟预测演示
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Any
from datetime import datetime, timedelta
import hashlib
from collections import Counter

class SimpleBacktestDemo:
    """简化的回测演示系统"""
    
    def __init__(self):
        """初始化"""
        # 生肖映射
        self.zodiac_mapping = {
            1: '鼠', 13: '鼠', 25: '鼠', 37: '鼠', 49: '鼠',
            2: '牛', 14: '牛', 26: '牛', 38: '牛',
            3: '虎', 15: '虎', 27: '虎', 39: '虎',
            4: '兔', 16: '兔', 28: '兔', 40: '兔',
            5: '龙', 17: '龙', 29: '龙', 41: '龙',
            6: '蛇', 18: '蛇', 30: '蛇', 42: '蛇',
            7: '马', 19: '马', 31: '马', 43: '马',
            8: '羊', 20: '羊', 32: '羊', 44: '羊',
            9: '猴', 21: '猴', 33: '猴', 45: '猴',
            10: '鸡', 22: '鸡', 34: '鸡', 46: '鸡',
            11: '狗', 23: '狗', 35: '狗', 47: '狗',
            12: '猪', 24: '猪', 36: '猪', 48: '猪'
        }
        
        print("🎯 简化历史模拟预测系统初始化完成")
    
    def generate_historical_data(self, start_date: str, end_date: str) -> List[Dict]:
        """生成历史开奖数据"""
        print(f"📊 生成历史开奖数据: {start_date} 到 {end_date}")
        
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        historical_data = []
        current_date = start_dt
        period_num = 1
        
        while current_date <= end_dt:
            date_str = current_date.strftime('%Y-%m-%d')
            
            # 使用日期生成确定性的历史开奖结果
            date_seed = int(hashlib.md5(f"historical_{date_str}".encode()).hexdigest()[:8], 16)
            np.random.seed(date_seed % 100000)
            
            # 生成6个正码和1个特码
            regular_numbers = sorted(np.random.choice(range(1, 50), size=6, replace=False).tolist())
            special_number = np.random.randint(1, 50)
            
            record = {
                'draw_date': date_str,
                'period_number': f"2024{period_num:03d}",
                'regular_numbers': regular_numbers,
                'special_number': special_number,
                'special_zodiac': self.zodiac_mapping.get(special_number, '未知'),
                'sum_value': sum(regular_numbers) + special_number
            }
            
            historical_data.append(record)
            current_date += timedelta(days=1)
            period_num += 1
        
        print(f"✅ 生成了 {len(historical_data)} 期历史开奖数据")
        return historical_data
    
    def simple_predict(self, training_data: List[Dict], target_date: str) -> Dict[str, Any]:
        """简化的预测算法"""
        
        # 统计特码频率
        special_numbers = [record['special_number'] for record in training_data]
        special_freq = Counter(special_numbers)
        
        # 统计生肖频率
        zodiacs = [record['special_zodiac'] for record in training_data]
        zodiac_freq = Counter(zodiacs)
        
        # 基于频率生成预测
        # 选择出现频率最高的号码作为预测
        hot_numbers = [num for num, freq in special_freq.most_common(8)]
        hot_zodiacs = [zodiac for zodiac, freq in zodiac_freq.most_common(4)]
        
        # 生成确定性预测（基于目标日期）
        date_seed = int(hashlib.md5(f"predict_{target_date}".encode()).hexdigest()[:8], 16)
        np.random.seed(date_seed % 100000)
        
        # 从热门号码中随机选择
        if len(hot_numbers) >= 6:
            predicted_special_numbers = hot_numbers[:6]
        else:
            predicted_special_numbers = hot_numbers + list(np.random.choice(range(1, 50), size=6-len(hot_numbers), replace=False))
        
        # 融合预测：包含更多号码
        fusion_numbers = hot_numbers + list(np.random.choice(range(1, 50), size=4, replace=False))
        fusion_numbers = sorted(list(set(fusion_numbers)))[:12]
        
        prediction = {
            'target_date': target_date,
            'special_numbers': predicted_special_numbers,
            'top_zodiacs': hot_zodiacs,
            'fusion_numbers': fusion_numbers,
            'training_size': len(training_data)
        }
        
        return prediction
    
    def calculate_accuracy(self, prediction: Dict, actual: Dict) -> Dict[str, Any]:
        """计算预测准确性"""
        
        pred_special = prediction['special_numbers']
        pred_zodiacs = prediction['top_zodiacs']
        pred_fusion = prediction['fusion_numbers']
        
        actual_special = actual['special_number']
        actual_zodiac = actual['special_zodiac']
        actual_all = actual['regular_numbers'] + [actual['special_number']]
        
        # 计算命中情况
        special_hit = actual_special in pred_special
        zodiac_hit = actual_zodiac in pred_zodiacs
        
        # 计算覆盖率
        fusion_coverage = sum(1 for num in actual_all if num in pred_fusion)
        coverage_rate = fusion_coverage / len(actual_all)
        
        accuracy = {
            'special_hit': special_hit,
            'zodiac_hit': zodiac_hit,
            'coverage_count': fusion_coverage,
            'coverage_rate': coverage_rate,
            'predicted_special': pred_special,
            'actual_special': actual_special,
            'predicted_zodiacs': pred_zodiacs,
            'actual_zodiac': actual_zodiac
        }
        
        return accuracy
    
    def run_backtest(self, start_date: str, end_date: str, window_size: int = 10) -> List[Dict]:
        """运行回测"""
        print(f"🚀 开始简化回测...")
        print(f"📅 回测期间: {start_date} 到 {end_date}")
        print(f"📊 训练窗口: {window_size} 期")
        print("=" * 50)
        
        # 生成历史数据
        historical_data = self.generate_historical_data(start_date, end_date)
        
        # 确定预测开始日期
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        prediction_start_dt = start_dt + timedelta(days=window_size)
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        results = []
        current_date = prediction_start_dt
        
        print(f"📊 开始预测回测 (从 {prediction_start_dt.strftime('%Y-%m-%d')} 开始)...")
        
        while current_date <= end_dt:
            target_date = current_date.strftime('%Y-%m-%d')
            
            # 获取训练数据
            training_data = []
            for record in historical_data:
                record_dt = datetime.strptime(record['draw_date'], '%Y-%m-%d')
                if record_dt < current_date:
                    training_data.append(record)
            
            # 只使用最近的window_size期数据
            training_data = training_data[-window_size:]
            
            if len(training_data) >= window_size:
                # 进行预测
                prediction = self.simple_predict(training_data, target_date)
                
                # 获取实际结果
                actual_result = None
                for record in historical_data:
                    if record['draw_date'] == target_date:
                        actual_result = record
                        break
                
                if actual_result:
                    # 计算准确性
                    accuracy = self.calculate_accuracy(prediction, actual_result)
                    
                    result = {
                        'target_date': target_date,
                        'prediction': prediction,
                        'actual': actual_result,
                        'accuracy': accuracy
                    }
                    
                    results.append(result)
                    
                    # 显示结果
                    special_status = "✅" if accuracy['special_hit'] else "❌"
                    zodiac_status = "✅" if accuracy['zodiac_hit'] else "❌"
                    
                    print(f"🎯 {target_date}: 特码{special_status} 生肖{zodiac_status} 覆盖{accuracy['coverage_rate']:.0%}")
            
            current_date += timedelta(days=1)
        
        print(f"\n✅ 回测完成！共回测 {len(results)} 期")
        return results
    
    def analyze_performance(self, results: List[Dict]):
        """分析回测性能"""
        if not results:
            print("❌ 没有回测结果可分析")
            return
        
        print(f"\n📊 回测性能分析:")
        print("=" * 50)
        
        total_tests = len(results)
        special_hits = sum(1 for r in results if r['accuracy']['special_hit'])
        zodiac_hits = sum(1 for r in results if r['accuracy']['zodiac_hit'])
        
        coverage_rates = [r['accuracy']['coverage_rate'] for r in results]
        avg_coverage = np.mean(coverage_rates)
        
        special_accuracy = special_hits / total_tests
        zodiac_accuracy = zodiac_hits / total_tests
        
        print(f"📊 总回测期数: {total_tests}")
        print(f"🎯 特码预测:")
        print(f"   命中次数: {special_hits}/{total_tests}")
        print(f"   命中率: {special_accuracy:.1%}")
        
        print(f"🐲 生肖预测:")
        print(f"   命中次数: {zodiac_hits}/{total_tests}")
        print(f"   命中率: {zodiac_accuracy:.1%}")
        
        print(f"🔀 号码覆盖:")
        print(f"   平均覆盖率: {avg_coverage:.1%}")
        print(f"   覆盖率范围: {min(coverage_rates):.1%} - {max(coverage_rates):.1%}")
        
        # 显示详细结果
        print(f"\n📋 详细回测结果:")
        for i, result in enumerate(results[:5]):  # 显示前5期
            acc = result['accuracy']
            print(f"   期号{i+1} ({result['target_date']}):")
            print(f"      特码: 预测{acc['predicted_special'][:3]}... 实际{acc['actual_special']} {'✅' if acc['special_hit'] else '❌'}")
            print(f"      生肖: 预测{acc['predicted_zodiacs']} 实际{acc['actual_zodiac']} {'✅' if acc['zodiac_hit'] else '❌'}")
            print(f"      覆盖: {acc['coverage_rate']:.1%} ({acc['coverage_count']}/7)")
        
        if len(results) > 5:
            print(f"   ... (还有 {len(results)-5} 期结果)")
        
        return {
            'total_tests': total_tests,
            'special_accuracy': special_accuracy,
            'zodiac_accuracy': zodiac_accuracy,
            'avg_coverage': avg_coverage
        }

def main():
    """主演示函数"""
    print("🎯 简化历史模拟预测演示")
    print("功能: 验证预测算法在历史数据上的表现")
    print()
    
    # 创建回测系统
    backtest_demo = SimpleBacktestDemo()
    
    # 设置回测参数
    start_date = "2024-06-01"
    end_date = "2024-06-15"  # 15天数据
    window_size = 5  # 使用5期历史数据进行预测
    
    print(f"📊 回测设置:")
    print(f"   回测期间: {start_date} 到 {end_date}")
    print(f"   训练窗口: {window_size} 期")
    print()
    
    # 运行回测
    results = backtest_demo.run_backtest(start_date, end_date, window_size)
    
    # 分析性能
    if results:
        performance = backtest_demo.analyze_performance(results)
        
        print(f"\n💡 回测结论:")
        if performance['special_accuracy'] > 0.1:
            print(f"   ✅ 特码预测表现良好 ({performance['special_accuracy']:.1%})")
        else:
            print(f"   ⚠️ 特码预测需要改进 ({performance['special_accuracy']:.1%})")
        
        if performance['zodiac_accuracy'] > 0.2:
            print(f"   ✅ 生肖预测表现优秀 ({performance['zodiac_accuracy']:.1%})")
        else:
            print(f"   ⚠️ 生肖预测需要优化 ({performance['zodiac_accuracy']:.1%})")
        
        if performance['avg_coverage'] > 0.3:
            print(f"   ✅ 号码覆盖表现出色 ({performance['avg_coverage']:.1%})")
        else:
            print(f"   ⚠️ 号码覆盖需要提升 ({performance['avg_coverage']:.1%})")
    
    print(f"\n🎊 历史模拟预测演示完成！")
    print(f"📊 此功能可用于验证和优化预测算法")
    
    return results

if __name__ == "__main__":
    main()
