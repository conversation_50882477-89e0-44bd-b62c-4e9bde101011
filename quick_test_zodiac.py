"""
快速测试特码生肖分析器
"""
import sys
import os

def main():
    print("🔧 快速测试特码生肖分析器")
    print("=" * 50)
    
    try:
        # 导入分析器
        from special_zodiac_analyzer import SpecialZodiacAnalyzer
        analyzer = SpecialZodiacAnalyzer()
        print("✅ 分析器初始化成功")
        
        # 测试生肖映射
        print("\n📋 测试生肖映射:")
        test_numbers = [1, 13, 25, 37, 49, 6, 12, 22]
        for num in test_numbers:
            zodiac = analyzer.get_zodiac_by_number(num, 2025)
            print(f"   号码{num} → 生肖{zodiac}")
        
        # 测试反向映射
        print("\n📋 测试反向映射:")
        test_zodiacs = ["蛇", "鼠", "马"]
        for zodiac in test_zodiacs:
            numbers = analyzer.get_numbers_by_zodiac(zodiac, 2025)
            print(f"   生肖{zodiac} → 号码{numbers}")
        
        # 测试综合预测
        print("\n📋 测试综合预测:")
        result = analyzer.comprehensive_special_zodiac_prediction(days=50)
        print(f"   推荐生肖: {result['recommended_zodiacs']}")
        print(f"   推荐号码: {result['recommended_numbers']}")
        print(f"   置信度: {result['confidence']:.1%}")
        print(f"   数据源: {result['method']}")
        
        print("\n✅ 所有测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
