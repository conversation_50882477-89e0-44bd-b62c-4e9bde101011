# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['lottery_prediction_gui.py'],
    pathex=[],
    binaries=[],
    datas=[('src', 'src'), ('data', 'data'), ('config', 'config'), ('*.json', '.')],
    hiddenimports=['PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'sqlite3', 'pandas', 'numpy', 'scikit-learn'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='六合彩预测系统_v2.0.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='六合彩预测系统_v2.0.0',
)
