"""
快速修复Windows版本的完美预测系统问题
"""

import os
import shutil
from pathlib import Path

def quick_fix():
    """快速修复Windows版本"""
    print("🔧 快速修复Windows版本...")
    
    # 目标目录
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    
    if not windows_dir.exists():
        print("❌ Windows版本目录不存在")
        return False
    
    print(f"📁 目标目录: {windows_dir}")
    
    # 1. 创建logs目录
    logs_dir = windows_dir / "logs"
    logs_dir.mkdir(exist_ok=True)
    print(f"✅ 创建logs目录: {logs_dir}")
    
    # 2. 复制缺失的文件
    missing_files = [
        "historical_backtest.py",
        "consistency_test.py"
    ]
    
    for file_name in missing_files:
        source_file = Path(file_name)
        target_file = windows_dir / file_name
        
        if source_file.exists():
            shutil.copy2(source_file, target_file)
            print(f"✅ 复制文件: {file_name}")
        else:
            print(f"⚠️ 源文件不存在: {file_name}")
    
    # 3. 创建data目录（如果不存在）
    data_dir = windows_dir / "data"
    data_dir.mkdir(exist_ok=True)
    print(f"✅ 确保data目录存在: {data_dir}")
    
    # 4. 复制数据库文件（如果存在）
    db_files = ["data/lottery.db", "data/lottery_test.db"]
    for db_file in db_files:
        source_db = Path(db_file)
        target_db = windows_dir / db_file
        
        if source_db.exists():
            target_db.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(source_db, target_db)
            print(f"✅ 复制数据库: {db_file}")
        else:
            print(f"⚠️ 数据库文件不存在: {db_file}")
    
    # 5. 创建models目录
    models_dir = data_dir / "models"
    models_dir.mkdir(exist_ok=True)
    print(f"✅ 创建models目录: {models_dir}")
    
    # 6. 复制模型文件（如果存在）
    models_source = Path("data/models")
    if models_source.exists():
        for model_file in models_source.glob("*"):
            if model_file.is_file():
                target_model = models_dir / model_file.name
                shutil.copy2(model_file, target_model)
                print(f"✅ 复制模型文件: {model_file.name}")
    
    print("\n🎉 快速修复完成！")
    return True

def test_fix():
    """测试修复结果"""
    print("\n🧪 测试修复结果...")
    
    # 切换到Windows版本目录
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    original_dir = Path.cwd()
    
    try:
        os.chdir(windows_dir)
        
        # 添加路径
        import sys
        sys.path.insert(0, str(Path.cwd()))
        sys.path.insert(0, str(Path.cwd() / "src"))
        
        # 测试完美预测系统
        from src.perfect_prediction_system import PerfectPredictionSystem
        
        print("✅ 成功导入 PerfectPredictionSystem")
        
        # 创建实例
        system = PerfectPredictionSystem()
        print("✅ 成功创建 PerfectPredictionSystem 实例")
        
        # 初始化模块
        system.initialize_modules()
        print("✅ 成功初始化所有模块")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
        
    finally:
        os.chdir(original_dir)

def main():
    """主函数"""
    print("🚀 Windows版本快速修复工具")
    print("=" * 40)
    
    # 执行修复
    fix_success = quick_fix()
    
    if fix_success:
        # 测试修复结果
        test_success = test_fix()
        
        if test_success:
            print("\n🎉 修复成功！完美预测系统现在应该可以正常工作了。")
            print("\n📝 下一步:")
            print("  1. 重新启动GUI程序")
            print("  2. 检查完美预测系统是否可用")
            return True
        else:
            print("\n⚠️ 修复完成，但测试仍有问题")
            return False
    else:
        print("\n❌ 修复失败")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*40}")
    if success:
        print("✅ 快速修复完成！")
    else:
        print("❌ 修复失败，请检查错误信息")
    
    input("\n按回车键退出...")
