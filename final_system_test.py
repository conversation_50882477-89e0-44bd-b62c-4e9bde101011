"""
六合彩预测系统最终综合测试
测试核心模组交互和准确性
"""
import sys
import os
import sqlite3
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_integrity():
    """测试数据库完整性"""
    print("🔧 测试数据库完整性")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        # 检查数据量
        cursor.execute('SELECT COUNT(*) FROM lottery_results')
        total_records = cursor.fetchone()[0]
        print(f"✅ 数据库记录: {total_records} 条")
        
        # 验证关键数据
        cursor.execute('SELECT special_number FROM lottery_results WHERE draw_date = "2024-10-21"')
        result = cursor.fetchone()
        
        # 检查数据范围
        cursor.execute('SELECT MIN(draw_date), MAX(draw_date) FROM lottery_results')
        date_range = cursor.fetchone()
        print(f"✅ 数据范围: {date_range[0]} 到 {date_range[1]}")
        
        conn.close()
        
        if result and result[0] == 48:
            print("✅ 关键数据验证通过 (2024-10-21: 特码48)")
            return True
        else:
            print(f"❌ 关键数据验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_prediction_consistency():
    """测试预测一致性"""
    print("\n🔧 测试预测一致性")
    print("=" * 50)
    
    try:
        from consistent_predictor import ConsistentSpecialNumberPredictor
        predictor = ConsistentSpecialNumberPredictor()
        
        test_date = "2025-06-22"
        
        # 运行多次预测检查一致性
        results = []
        for i in range(3):
            result = predictor.run_consistent_prediction(test_date)
            if result and 'special_number_prediction' in result:
                special_pred = result['special_number_prediction']
                final_recs = special_pred.get('final_recommendations', [])
                results.append(set(final_recs))
        
        if len(results) >= 2:
            # 检查一致性
            consistent = all(results[0] == r for r in results[1:])
            print(f"✅ 预测一致性: {'通过' if consistent else '失败'}")
            
            # 检查号码有效性
            if results:
                sample_numbers = list(results[0])
                valid_range = all(1 <= num <= 49 for num in sample_numbers)
                print(f"✅ 号码范围: {'有效' if valid_range else '无效'}")
                print(f"✅ 预测数量: {len(sample_numbers)} 个号码")
                
                return consistent and valid_range
        
        return False
        
    except Exception as e:
        print(f"❌ 预测一致性测试失败: {e}")
        return False

def test_zodiac_analysis():
    """测试生肖分析"""
    print("\n🔧 测试生肖分析")
    print("=" * 50)
    
    try:
        from special_zodiac_analyzer import SpecialZodiacAnalyzer
        analyzer = SpecialZodiacAnalyzer()
        
        # 运行分析
        result = analyzer.comprehensive_special_zodiac_prediction(days=30)
        
        if result:
            # 检查结果结构
            has_zodiacs = 'top_4_zodiacs' in result or 'recommended_zodiacs' in result
            has_numbers = 'recommended_numbers' in result
            has_confidence = 'confidence' in result
            
            print(f"✅ 生肖分析结果: {'完整' if has_zodiacs and has_numbers else '部分'}")
            print(f"✅ 置信度信息: {'有' if has_confidence else '无'}")
            
            # 检查推荐号码
            rec_numbers = result.get('recommended_numbers', [])
            if rec_numbers:
                valid_numbers = all(1 <= num <= 49 for num in rec_numbers)
                print(f"✅ 推荐号码: {len(rec_numbers)} 个，{'有效' if valid_numbers else '无效'}")
                return valid_numbers
            else:
                print("⚠️ 无推荐号码")
                return True  # 分析完成但无推荐也算正常
        else:
            print("❌ 生肖分析无结果")
            return False
            
    except Exception as e:
        print(f"❌ 生肖分析测试失败: {e}")
        return False

def test_data_retrieval():
    """测试数据获取功能"""
    print("\n🔧 测试数据获取功能")
    print("=" * 50)
    
    try:
        # 测试不依赖GUI的数据获取
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        # 测试特定日期数据获取
        test_date = "2024-10-21"
        cursor.execute('''
            SELECT period_number, regular_1, regular_2, regular_3, regular_4, regular_5, regular_6, special_number
            FROM lottery_results
            WHERE draw_date = ?
        ''', (test_date,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            period_number, r1, r2, r3, r4, r5, r6, special = result
            print(f"✅ 数据获取成功: {test_date}")
            print(f"   期号: {period_number}")
            print(f"   正码: [{r1}, {r2}, {r3}, {r4}, {r5}, {r6}]")
            print(f"   特码: {special}")
            
            # 验证数据正确性
            if special == 48:
                print("✅ 数据正确性验证通过")
                return True
            else:
                print(f"❌ 数据正确性验证失败: 期望48, 实际{special}")
                return False
        else:
            print(f"❌ 未找到{test_date}的数据")
            return False
            
    except Exception as e:
        print(f"❌ 数据获取测试失败: {e}")
        return False

def test_module_integration():
    """测试模块集成"""
    print("\n🔧 测试模块集成")
    print("=" * 50)
    
    try:
        # 测试预测器和生肖分析器的协同工作
        from consistent_predictor import ConsistentSpecialNumberPredictor
        from special_zodiac_analyzer import SpecialZodiacAnalyzer
        
        # 1. 获取预测结果
        predictor = ConsistentSpecialNumberPredictor()
        pred_result = predictor.run_consistent_prediction("2025-06-22")
        
        # 2. 获取生肖分析结果
        analyzer = SpecialZodiacAnalyzer()
        zodiac_result = analyzer.comprehensive_special_zodiac_prediction(days=30)
        
        # 3. 检查结果
        pred_success = pred_result and 'special_number_prediction' in pred_result
        zodiac_success = zodiac_result and ('recommended_numbers' in zodiac_result or 'top_4_zodiacs' in zodiac_result)
        
        print(f"✅ 预测器模块: {'正常' if pred_success else '异常'}")
        print(f"✅ 生肖分析器: {'正常' if zodiac_success else '异常'}")
        
        if pred_success and zodiac_success:
            print("✅ 模块集成测试通过")
            return True
        else:
            print("❌ 模块集成测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 模块集成测试失败: {e}")
        return False

def run_final_test():
    """运行最终测试"""
    print("🎯 六合彩预测系统最终综合测试")
    print("=" * 70)
    
    tests = [
        ("数据库完整性", test_database_integrity),
        ("预测一致性", test_prediction_consistency),
        ("生肖分析", test_zodiac_analysis),
        ("数据获取", test_data_retrieval),
        ("模块集成", test_module_integration)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 70)
    print("🎯 最终测试结果汇总")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    print(f"📈 通过率: {passed/total*100:.1f}%")
    
    # 评估系统状态
    if passed == total:
        print("\n🎊 恭喜！系统完全正常！")
        print("💡 所有核心模组交互正常，预测功能准确可靠")
        print("🚀 系统已准备好投入使用")
    elif passed >= total * 0.8:
        print("\n✅ 系统基本正常！")
        print("💡 核心功能运行良好，少数模块可能需要微调")
        print("🔧 建议检查失败的模块并优化")
    elif passed >= total * 0.6:
        print("\n⚠️ 系统部分正常")
        print("💡 基础功能可用，但需要修复部分问题")
        print("🔧 建议优先修复失败的核心模块")
    else:
        print("\n❌ 系统存在较多问题")
        print("💡 需要全面检查和修复")
        print("🔧 建议逐个模块排查问题")
    
    return results

if __name__ == "__main__":
    run_final_test()
