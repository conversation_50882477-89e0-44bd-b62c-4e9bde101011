"""
六合彩预测工具 - 主配置文件
"""
import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent
DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"
MODELS_DIR = DATA_DIR / "models"

# 数据库配置
DATABASE_CONFIG = {
    "type": "sqlite",
    "path": DATA_DIR / "lottery.db",
    "echo": False  # 是否打印SQL语句
}

# 生肖配置
ZODIAC_CONFIG = {
    "names": ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"],
    "base_year": 1984,  # 鼠年基准
    "extended_dimensions": {
        "琴棋书画": {
            "琴": ["兔", "蛇", "鸡"],
            "棋": ["鼠", "牛", "狗"], 
            "书": ["马", "龙", "虎"],
            "画": ["羊", "猴", "猪"]
        },
        "四季分类": {
            "春": ["虎", "兔", "龙"],
            "夏": ["蛇", "马", "羊"],
            "秋": ["猴", "鸡", "狗"], 
            "冬": ["猪", "鼠", "牛"]
        },
        "波色分类": {
            "红肖": ["鼠", "兔", "马", "鸡"],
            "绿肖": ["牛", "龙", "羊", "狗"],
            "蓝肖": ["虎", "蛇", "猴", "猪"]
        }
    }
}

# 预测配置
PREDICTION_CONFIG = {
    "initial_range": (16, 24),  # 初选号码范围
    "final_range": (12, 16),    # 最终推荐范围
    "zodiac_count": 4,          # 生肖预测数量
    "confidence_threshold": 0.6  # 置信度阈值
}

# 模型配置
MODEL_CONFIG = {
    "random_forest": {
        "n_estimators": 100,
        "max_depth": 10,
        "random_state": 42
    },
    "xgboost": {
        "n_estimators": 100,
        "max_depth": 6,
        "learning_rate": 0.1,
        "random_state": 42
    }
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
    "rotation": "10 MB",
    "retention": "30 days"
}
