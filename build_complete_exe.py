"""
快速启动完整EXE打包流程
"""

import sys
import os
from pathlib import Path

def main():
    """主函数"""
    print("Starting the complete EXE packaging process...")
    
    # 检查是否在正确的目录
    current_dir = Path.cwd()
    gui_file = current_dir / "lottery_prediction_gui.py"
    
    if not gui_file.exists():
        print("❌ 未找到 lottery_prediction_gui.py，请确保在项目根目录运行此脚本")
        return False
    
    print(f"Project directory: {current_dir}")
    
    # 导入并运行完整构建器
    try:
        from complete_exe_package_builder import CompleteEXEPackageBuilder
        
        builder = CompleteEXEPackageBuilder()
        success = builder.run_complete_build_process()
        
        if success:
            print("\nEXE packaging successful!")
            print("Please check the releases/ directory for the distribution package")
            print("Use debug_start.bat to test the EXE file")
        else:
            print("\nEXE packaging failed")
            print("Please check exe_build.log for details")
            
        return success
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 complete_exe_package_builder.py 文件存在")
        return False
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        return False

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
