import re

# 读取完美预测系统文件
with open("src/perfect_prediction_system.py", "r", encoding="utf-8") as f:
    content = f.read()

print("🔍 完美预测系统融合分析返回检查")
print("=" * 50)

# 查找融合分析相关的方法
fusion_methods = [
    "fusion_analysis",
    "get_fusion_analysis", 
    "generate_fusion_analysis",
    "weights_used"
]

for method in fusion_methods:
    if method in content:
        print(f"✅ 找到: {method}")
        
        # 查找返回语句
        method_start = content.find(method)
        if method_start != -1:
            # 查找该方法附近的return语句
            method_section = content[method_start:method_start+2000]
            return_statements = re.findall(r"return\s+{[^}]*}", method_section)
            
            if return_statements:
                print(f"   返回语句:")
                for ret in return_statements[:2]:
                    print(f"   {ret[:80]}...")
            else:
                print(f"   未找到return语句")
    else:
        print(f"❌ 未找到: {method}")

# 查找融合分析字典构建
print("\n🔍 查找融合分析字典构建:")
fusion_dict_patterns = [
    r"fusion_analysis\s*=\s*{[^}]*}",
    r"\"fusion_method\":\s*[^,]*",
    r"\"weights_used\":\s*[^,]*"
]

for pattern in fusion_dict_patterns:
    matches = re.findall(pattern, content, re.MULTILINE)
    if matches:
        print(f"✅ 找到模式: {pattern}")
        for match in matches[:3]:
            print(f"   {match}")
    else:
        print(f"❌ 未找到模式: {pattern}")

# 查找可能的问题代码
print("\n🔍 查找可能的问题代码:")
problem_patterns = [
    r"fusion_method.*N/A",
    r"weights_used.*{}",
    r"weights_used.*None"
]

for pattern in problem_patterns:
    matches = re.findall(pattern, content, re.MULTILINE)
    if matches:
        print(f"⚠️ 发现问题: {pattern}")
        for match in matches:
            print(f"   {match}")
