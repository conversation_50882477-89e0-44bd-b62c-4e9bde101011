"""
六合彩预测系统全面功能测试
"""

import sys
import os
import sqlite3
import json
from pathlib import Path
from datetime import datetime

def comprehensive_system_test():
    """全面系统测试"""
    print("🧪 六合彩预测系统全面功能测试")
    print("=" * 70)
    
    # 切换到Windows版本目录
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    original_dir = Path.cwd()
    
    if not windows_dir.exists():
        print("❌ Windows版本目录不存在")
        return False
    
    try:
        os.chdir(windows_dir)
        
        # 添加路径
        sys.path.insert(0, str(Path.cwd()))
        sys.path.insert(0, str(Path.cwd() / "src"))
        
        print(f"📁 测试目录: {Path.cwd()}")
        
        # 执行各项测试
        test_results = {}
        
        # 1. 核心模块导入测试
        test_results['core_imports'] = test_core_imports()
        
        # 2. 数据库连接测试
        test_results['database'] = test_database_connection()
        
        # 3. 配置文件测试
        test_results['config'] = test_config_files()
        
        # 4. 预测模块测试
        test_results['prediction_modules'] = test_prediction_modules()
        
        # 5. 融合管理器测试
        test_results['fusion_manager'] = test_fusion_manager()
        
        # 6. 性能监控测试
        test_results['performance_monitor'] = test_performance_monitor()
        
        # 7. 增强回测测试
        test_results['enhanced_backtest'] = test_enhanced_backtest()
        
        # 8. 模块间互动测试
        test_results['module_interactions'] = test_module_interactions()
        
        # 9. GUI组件测试
        test_results['gui_components'] = test_gui_components()
        
        # 10. 文件系统测试
        test_results['file_system'] = test_file_system()
        
        # 生成测试报告
        generate_test_report(test_results)
        
        return all(test_results.values())
        
    finally:
        os.chdir(original_dir)

def test_core_imports():
    """测试核心模块导入"""
    print("\n1️⃣ 核心模块导入测试")
    print("-" * 40)
    
    core_modules = [
        ('PyQt5.QtWidgets', 'QApplication'),
        ('src.lottery_prediction_system', 'LotteryPredictionSystem'),
        ('src.perfect_prediction_system', 'PerfectPredictionSystem'),
        ('src.dynamic_fusion_manager_v3', 'DynamicFusionManager'),
        ('src.fusion_manager', 'FusionManager'),
        ('special_number_predictor', 'SpecialNumberPredictor'),
        ('consistent_predictor', 'ConsistentPredictor'),
        ('historical_backtest', 'HistoricalBacktest'),
        ('enhanced_hit_rate_optimizer', 'EnhancedHitRateOptimizer'),
        ('optimal_pattern_selector', 'OptimalPatternSelector'),
        ('adaptive_optimizer', 'AdaptiveOptimizer'),
        ('performance_monitor', 'PerformanceMonitor'),
        ('lottery_prediction_gui', 'LotteryPredictionGUI'),
    ]
    
    success_count = 0
    for module_name, class_name in core_modules:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"  ✅ {module_name}.{class_name}")
            success_count += 1
        except Exception as e:
            print(f"  ❌ {module_name}.{class_name}: {e}")
    
    success_rate = success_count / len(core_modules)
    print(f"📊 导入成功率: {success_rate:.1%} ({success_count}/{len(core_modules)})")
    
    return success_rate >= 0.8

def test_database_connection():
    """测试数据库连接"""
    print("\n2️⃣ 数据库连接测试")
    print("-" * 40)
    
    try:
        # 检查数据库文件
        db_path = Path("data/lottery.db")
        if not db_path.exists():
            print(f"  ⚠️ 数据库文件不存在: {db_path}")
            # 创建测试数据库
            db_path.parent.mkdir(exist_ok=True)
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS lottery_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    draw_date TEXT NOT NULL,
                    period_number TEXT NOT NULL,
                    regular_1 INTEGER,
                    regular_2 INTEGER,
                    regular_3 INTEGER,
                    regular_4 INTEGER,
                    regular_5 INTEGER,
                    regular_6 INTEGER,
                    special_number INTEGER
                )
            ''')
            conn.commit()
            conn.close()
            print(f"  ✅ 创建测试数据库: {db_path}")
        
        # 测试连接
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"  📋 数据库表: {[table[0] for table in tables]}")
        
        # 检查数据量
        cursor.execute("SELECT COUNT(*) FROM lottery_results")
        count = cursor.fetchone()[0]
        print(f"  📊 历史数据量: {count} 条")
        
        conn.close()
        print(f"  ✅ 数据库连接正常")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据库连接失败: {e}")
        return False

def test_config_files():
    """测试配置文件"""
    print("\n3️⃣ 配置文件测试")
    print("-" * 40)
    
    config_files = [
        "config/master_config.json",
        "config/perfect_prediction_config.json",
        "config/enhanced_backtest_config.json",
        "config/historical_backtest_config.json",
        "config/special_predictor_config.json",
    ]
    
    success_count = 0
    for config_file in config_files:
        try:
            config_path = Path(config_file)
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"  ✅ {config_file} (keys: {len(config)})")
                success_count += 1
            else:
                print(f"  ⚠️ {config_file} (不存在)")
        except Exception as e:
            print(f"  ❌ {config_file}: {e}")
    
    success_rate = success_count / len(config_files)
    print(f"📊 配置文件成功率: {success_rate:.1%} ({success_count}/{len(config_files)})")
    
    return success_rate >= 0.6

def test_prediction_modules():
    """测试预测模块"""
    print("\n4️⃣ 预测模块测试")
    print("-" * 40)
    
    try:
        # 测试特码预测器
        from special_number_predictor import SpecialNumberPredictor
        predictor = SpecialNumberPredictor()
        print(f"  ✅ 特码预测器创建成功")
        
        # 测试一致性预测器
        from consistent_predictor import ConsistentPredictor
        consistent = ConsistentPredictor()
        print(f"  ✅ 一致性预测器创建成功")
        
        # 测试完美预测系统
        from src.perfect_prediction_system import PerfectPredictionSystem
        perfect_system = PerfectPredictionSystem()
        print(f"  ✅ 完美预测系统创建成功")
        
        # 测试历史回测
        from historical_backtest import HistoricalBacktest
        backtest = HistoricalBacktest()
        print(f"  ✅ 历史回测创建成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 预测模块测试失败: {e}")
        return False

def test_fusion_manager():
    """测试融合管理器"""
    print("\n5️⃣ 融合管理器测试")
    print("-" * 40)
    
    try:
        from src.dynamic_fusion_manager_v3 import DynamicFusionManager
        fusion_manager = DynamicFusionManager()
        print(f"  ✅ 动态融合管理器创建成功")
        
        # 测试关键方法
        methods_to_test = [
            'get_module_statistics',
            'show_config',
            'configure_static_weights',
            'fuse_predictions'
        ]
        
        for method_name in methods_to_test:
            if hasattr(fusion_manager, method_name):
                print(f"  ✅ 方法 {method_name} 存在")
            else:
                print(f"  ❌ 方法 {method_name} 不存在")
                return False
        
        # 测试统计信息获取
        stats = fusion_manager.get_module_statistics()
        if isinstance(stats, dict):
            print(f"  ✅ 统计信息格式正确 (模块数: {len(stats)})")
        else:
            print(f"  ❌ 统计信息格式错误: {type(stats)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 融合管理器测试失败: {e}")
        return False

def test_performance_monitor():
    """测试性能监控"""
    print("\n6️⃣ 性能监控测试")
    print("-" * 40)
    
    try:
        from performance_monitor import PerformanceMonitor
        monitor = PerformanceMonitor()
        print(f"  ✅ 性能监控器创建成功")
        
        # 测试与融合管理器的集成
        from src.dynamic_fusion_manager_v3 import DynamicFusionManager
        fusion_manager = DynamicFusionManager()
        
        # 测试统计信息获取
        stats = fusion_manager.get_module_statistics()
        print(f"  ✅ 性能统计获取成功")
        
        # 测试配置显示
        config_info = fusion_manager.show_config()
        if isinstance(config_info, str) and len(config_info) > 0:
            print(f"  ✅ 配置信息显示正常")
        else:
            print(f"  ❌ 配置信息显示异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 性能监控测试失败: {e}")
        return False

def test_enhanced_backtest():
    """测试增强回测"""
    print("\n7️⃣ 增强回测测试")
    print("-" * 40)
    
    try:
        # 测试最优模式选择器
        from optimal_pattern_selector import OptimalPatternSelector
        selector = OptimalPatternSelector()
        print(f"  ✅ 最优模式选择器创建成功")
        
        # 测试关键方法
        if hasattr(selector, 'apply_optimal_pattern_to_perfect_prediction'):
            print(f"  ✅ 应用最优模式方法存在")
        else:
            print(f"  ❌ 应用最优模式方法不存在")
            return False
        
        # 测试自适应优化器
        from adaptive_optimizer import AdaptiveOptimizer
        optimizer = AdaptiveOptimizer()
        print(f"  ✅ 自适应优化器创建成功")
        
        # 测试增强命中率优化器
        from enhanced_hit_rate_optimizer import EnhancedHitRateOptimizer
        enhancer = EnhancedHitRateOptimizer()
        print(f"  ✅ 增强命中率优化器创建成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 增强回测测试失败: {e}")
        return False

def test_module_interactions():
    """测试模块间互动"""
    print("\n8️⃣ 模块间互动测试")
    print("-" * 40)
    
    try:
        # 测试融合管理器与预测模块的互动
        from src.dynamic_fusion_manager_v3 import DynamicFusionManager
        from src.perfect_prediction_system import PerfectPredictionSystem
        
        fusion_manager = DynamicFusionManager()
        perfect_system = PerfectPredictionSystem()
        
        print(f"  ✅ 核心模块创建成功")
        
        # 测试数据流
        test_predictions = {
            "traditional_analysis": {
                "numbers": [1, 5, 8, 12, 15, 18, 22, 25, 28, 31],
                "confidence": 0.75
            },
            "machine_learning": {
                "numbers": [3, 8, 12, 15, 22, 28, 35, 38, 42, 45],
                "confidence": 0.84
            }
        }
        
        # 测试融合预测 (使用简化方法)
        fusion_result = fusion_manager.fuse_predictions_simple(test_predictions)
        if 'final_16_numbers' in fusion_result:
            print(f"  ✅ 融合预测正常 (推荐: {len(fusion_result['final_16_numbers'])} 个号码)")
        else:
            print(f"  ❌ 融合预测异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模块间互动测试失败: {e}")
        return False

def test_gui_components():
    """测试GUI组件"""
    print("\n9️⃣ GUI组件测试")
    print("-" * 40)
    
    try:
        # 测试GUI主类导入
        from lottery_prediction_gui import LotteryPredictionGUI
        print(f"  ✅ GUI主类导入成功")
        
        # 测试PyQt5组件
        from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout
        from PyQt5.QtCore import Qt
        print(f"  ✅ PyQt5组件导入成功")
        
        # 注意：不实际创建GUI实例，避免显示窗口
        print(f"  ✅ GUI组件测试完成")
        
        return True
        
    except Exception as e:
        print(f"  ❌ GUI组件测试失败: {e}")
        return False

def test_file_system():
    """测试文件系统"""
    print("\n🔟 文件系统测试")
    print("-" * 40)
    
    try:
        # 检查关键目录
        directories = ['src', 'data', 'config', 'logs']
        for directory in directories:
            dir_path = Path(directory)
            if dir_path.exists():
                print(f"  ✅ 目录 {directory}/ 存在")
            else:
                print(f"  ⚠️ 目录 {directory}/ 不存在")
                dir_path.mkdir(exist_ok=True)
                print(f"  ✅ 创建目录 {directory}/")
        
        # 检查关键文件
        key_files = [
            'lottery_prediction_gui.py',
            'special_number_predictor.py',
            'consistent_predictor.py',
            'historical_backtest.py'
        ]
        
        for file_name in key_files:
            file_path = Path(file_name)
            if file_path.exists():
                size_kb = file_path.stat().st_size / 1024
                print(f"  ✅ 文件 {file_name} ({size_kb:.1f} KB)")
            else:
                print(f"  ❌ 文件 {file_name} 不存在")
                return False
        
        return True

    except Exception as e:
        print(f"  ❌ 文件系统测试失败: {e}")
        return False

def generate_test_report(test_results):
    """生成测试报告"""
    print("\n" + "="*70)
    print("📋 全面系统测试报告")
    print("="*70)

    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    success_rate = passed_tests / total_tests

    print(f"\n📊 总体结果:")
    print(f"  总测试项: {total_tests}")
    print(f"  通过测试: {passed_tests}")
    print(f"  失败测试: {total_tests - passed_tests}")
    print(f"  成功率: {success_rate:.1%}")

    print(f"\n📋 详细结果:")
    test_names = {
        'core_imports': '核心模块导入',
        'database': '数据库连接',
        'config': '配置文件',
        'prediction_modules': '预测模块',
        'fusion_manager': '融合管理器',
        'performance_monitor': '性能监控',
        'enhanced_backtest': '增强回测',
        'module_interactions': '模块间互动',
        'gui_components': 'GUI组件',
        'file_system': '文件系统'
    }

    for test_key, result in test_results.items():
        test_name = test_names.get(test_key, test_key)
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")

    # 系统健康度评估
    print(f"\n🏥 系统健康度评估:")
    if success_rate >= 0.9:
        health_status = "🟢 优秀"
        health_desc = "系统功能完整，可以正常使用"
    elif success_rate >= 0.8:
        health_status = "🟡 良好"
        health_desc = "系统主要功能正常，有少量问题"
    elif success_rate >= 0.6:
        health_status = "🟠 一般"
        health_desc = "系统部分功能有问题，需要修复"
    else:
        health_status = "🔴 差"
        health_desc = "系统存在严重问题，需要全面检查"

    print(f"  状态: {health_status}")
    print(f"  描述: {health_desc}")

    # 问题诊断
    failed_tests = [test_names.get(k, k) for k, v in test_results.items() if not v]
    if failed_tests:
        print(f"\n🔧 需要修复的问题:")
        for i, failed_test in enumerate(failed_tests, 1):
            print(f"  {i}. {failed_test}")

    # 建议
    print(f"\n💡 建议:")
    if success_rate >= 0.9:
        print("  - 系统状态良好，可以正常使用")
        print("  - 建议定期运行测试确保系统稳定")
    elif success_rate >= 0.8:
        print("  - 主要功能正常，建议修复失败的测试项")
        print("  - 可以继续使用，但需要注意相关功能")
    else:
        print("  - 建议优先修复失败的测试项")
        print("  - 修复后重新运行测试验证")

    # 保存测试报告
    save_test_report(test_results, success_rate)

def save_test_report(test_results, success_rate):
    """保存测试报告到文件"""
    try:
        report_content = f"""六合彩预测系统全面测试报告

测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
测试版本: v2.0.0 (修复版)

=== 测试结果概览 ===
总测试项: {len(test_results)}
通过测试: {sum(test_results.values())}
失败测试: {len(test_results) - sum(test_results.values())}
成功率: {success_rate:.1%}

=== 详细测试结果 ===
"""

        test_names = {
            'core_imports': '核心模块导入',
            'database': '数据库连接',
            'config': '配置文件',
            'prediction_modules': '预测模块',
            'fusion_manager': '融合管理器',
            'performance_monitor': '性能监控',
            'enhanced_backtest': '增强回测',
            'module_interactions': '模块间互动',
            'gui_components': 'GUI组件',
            'file_system': '文件系统'
        }

        for test_key, result in test_results.items():
            test_name = test_names.get(test_key, test_key)
            status = "通过" if result else "失败"
            report_content += f"{test_name}: {status}\n"

        report_content += f"""
=== 系统健康度 ===
"""
        if success_rate >= 0.9:
            report_content += "状态: 优秀 - 系统功能完整，可以正常使用\n"
        elif success_rate >= 0.8:
            report_content += "状态: 良好 - 系统主要功能正常，有少量问题\n"
        elif success_rate >= 0.6:
            report_content += "状态: 一般 - 系统部分功能有问题，需要修复\n"
        else:
            report_content += "状态: 差 - 系统存在严重问题，需要全面检查\n"

        # 保存到文件
        report_path = Path("logs/system_test_report.txt")
        report_path.parent.mkdir(exist_ok=True)

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"\n📄 测试报告已保存: {report_path}")

    except Exception as e:
        print(f"\n⚠️ 保存测试报告失败: {e}")

def main():
    """主函数"""
    print("🎯 六合彩预测系统全面功能测试")
    print("=" * 70)
    print("📋 测试范围:")
    print("  1. 核心模块导入")
    print("  2. 数据库连接")
    print("  3. 配置文件")
    print("  4. 预测模块")
    print("  5. 融合管理器")
    print("  6. 性能监控")
    print("  7. 增强回测")
    print("  8. 模块间互动")
    print("  9. GUI组件")
    print("  10. 文件系统")

    print("\n⏳ 开始测试...")

    # 执行全面测试
    success = comprehensive_system_test()

    print("\n" + "="*70)
    if success:
        print("🎉 全面测试完成：系统整体状态良好！")
        print("✅ 所有主要功能正常工作")
        print("✅ 模块间互动正常")
        print("✅ 可以安全使用系统")
    else:
        print("⚠️ 全面测试完成：发现一些问题")
        print("📋 请查看详细测试报告")
        print("🔧 建议修复失败的测试项")

    print("📄 详细报告已保存到 logs/system_test_report.txt")
    print("="*70)

    return success

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
