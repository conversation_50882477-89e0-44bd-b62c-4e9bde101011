"""
调试完美预测系统问题
"""

import sys
import os
from pathlib import Path

def debug_perfect_prediction():
    """调试完美预测系统问题"""
    print("🔍 调试完美预测系统问题")
    print("=" * 50)
    
    # 切换到Windows版本目录
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    original_dir = Path.cwd()
    
    if not windows_dir.exists():
        print("❌ Windows版本目录不存在")
        return False
    
    try:
        os.chdir(windows_dir)
        sys.path.insert(0, str(Path.cwd()))
        sys.path.insert(0, str(Path.cwd() / "src"))
        
        print(f"📁 当前目录: {Path.cwd()}")
        
        # 1. 测试完美预测系统导入和创建
        print("\n1️⃣ 测试完美预测系统...")
        try:
            from src.perfect_prediction_system import PerfectPredictionSystem
            perfect_system = PerfectPredictionSystem()
            perfect_system.initialize_modules()
            print("✅ 完美预测系统创建和初始化成功")
            
            # 测试运行预测
            print("\n2️⃣ 测试运行预测...")
            result = perfect_system.run_complete_prediction("2025-06-27")
            
            if result and 'final_results' in result:
                print("✅ 完美预测运行成功")
                print(f"📊 推荐号码数量: {len(result['final_results'].get('recommended_16_numbers', []))}")
                print(f"📊 推荐生肖数量: {len(result['final_results'].get('recommended_4_zodiacs', []))}")
            else:
                print("❌ 完美预测运行失败或结果格式错误")
                print(f"结果类型: {type(result)}")
                if result:
                    print(f"结果键: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
                
        except Exception as e:
            print(f"❌ 完美预测系统测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 3. 测试GUI初始化逻辑
        print("\n3️⃣ 测试GUI初始化逻辑...")
        try:
            # 模拟GUI初始化过程
            PERFECT_SYSTEM_AVAILABLE = True
            
            if PERFECT_SYSTEM_AVAILABLE:
                print("正在初始化完美预测系统...")
                test_perfect_system = PerfectPredictionSystem()
                print("完美预测系统创建成功，正在初始化模块...")
                test_perfect_system.initialize_modules()
                print("模块初始化成功，获取融合管理器...")
                test_fusion_manager = test_perfect_system.fusion_manager
                
                if test_fusion_manager is not None:
                    print("✅ 融合管理器获取成功")
                    print("✅ GUI初始化逻辑正常")
                else:
                    print("❌ 融合管理器为None")
                    return False
            else:
                print("❌ PERFECT_SYSTEM_AVAILABLE 为 False")
                return False
                
        except Exception as e:
            print(f"❌ GUI初始化逻辑测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        return True
        
    finally:
        os.chdir(original_dir)

def create_perfect_prediction_fix():
    """创建完美预测系统修复"""
    print("\n🔧 创建完美预测系统修复")
    print("-" * 30)
    
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    gui_file = windows_dir / "lottery_prediction_gui.py"
    
    if not gui_file.exists():
        print("❌ GUI文件不存在")
        return False
    
    try:
        # 读取文件内容
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复run_perfect_prediction方法，添加更详细的检查
        old_method = '''    def run_perfect_prediction(self):
        """运行完美预测系统"""
        if not self.perfect_prediction_system:
            QMessageBox.warning(self, "系统不可用", "完美预测系统不可用，请检查系统配置。")
            return'''
        
        new_method = '''    def run_perfect_prediction(self):
        """运行完美预测系统"""
        # 详细检查完美预测系统状态
        if not hasattr(self, 'perfect_prediction_system'):
            QMessageBox.warning(self, "系统不可用", "完美预测系统属性不存在，请重启程序。")
            return
            
        if not self.perfect_prediction_system:
            QMessageBox.warning(self, "系统不可用", "完美预测系统不可用，请检查系统配置。")
            return
            
        # 检查融合管理器
        if not hasattr(self.perfect_prediction_system, 'fusion_manager') or not self.perfect_prediction_system.fusion_manager:
            QMessageBox.warning(self, "融合管理器不可用", "融合管理器未初始化，请重启程序。")
            return
            
        print(f"🚀 开始运行完美预测系统...")
        print(f"系统状态: {type(self.perfect_prediction_system)}")
        print(f"融合管理器状态: {type(self.perfect_prediction_system.fusion_manager)}")'''
        
        if old_method in content:
            content = content.replace(old_method, new_method)
            print("✅ run_perfect_prediction方法已修复")
        else:
            print("⚠️ 未找到目标方法，可能已经修改过")
        
        # 备份并保存
        backup_file = gui_file.with_suffix('.py.backup4')
        with open(backup_file, 'w', encoding='utf-8') as f:
            with open(gui_file, 'r', encoding='utf-8') as original:
                f.write(original.read())
        
        with open(gui_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"💾 文件已备份: {backup_file}")
        print("✅ 修复完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def create_debug_button():
    """创建调试按钮功能"""
    print("\n🔧 创建调试按钮功能")
    print("-" * 30)
    
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    gui_file = windows_dir / "lottery_prediction_gui.py"
    
    try:
        # 读取文件内容
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加调试方法
        debug_method = '''
    def debug_perfect_prediction_system(self):
        """调试完美预测系统"""
        debug_info = []
        debug_info.append("🔍 完美预测系统调试信息")
        debug_info.append("=" * 50)
        
        # 检查系统属性
        debug_info.append(f"hasattr(self, 'perfect_prediction_system'): {hasattr(self, 'perfect_prediction_system')}")
        
        if hasattr(self, 'perfect_prediction_system'):
            debug_info.append(f"self.perfect_prediction_system is not None: {self.perfect_prediction_system is not None}")
            
            if self.perfect_prediction_system:
                debug_info.append(f"系统类型: {type(self.perfect_prediction_system)}")
                debug_info.append(f"hasattr(fusion_manager): {hasattr(self.perfect_prediction_system, 'fusion_manager')}")
                
                if hasattr(self.perfect_prediction_system, 'fusion_manager'):
                    debug_info.append(f"fusion_manager is not None: {self.perfect_prediction_system.fusion_manager is not None}")
                    
                    if self.perfect_prediction_system.fusion_manager:
                        debug_info.append(f"融合管理器类型: {type(self.perfect_prediction_system.fusion_manager)}")
                        
                        # 测试关键方法
                        try:
                            stats = self.perfect_prediction_system.fusion_manager.get_module_statistics()
                            debug_info.append(f"get_module_statistics(): 成功 ({len(stats)} 个模块)")
                        except Exception as e:
                            debug_info.append(f"get_module_statistics(): 失败 - {e}")
                        
                        try:
                            result = self.perfect_prediction_system.run_complete_prediction("2025-06-27")
                            if result and 'final_results' in result:
                                debug_info.append("run_complete_prediction(): 成功")
                            else:
                                debug_info.append("run_complete_prediction(): 结果格式错误")
                        except Exception as e:
                            debug_info.append(f"run_complete_prediction(): 失败 - {e}")
        
        # 检查PERFECT_SYSTEM_AVAILABLE
        debug_info.append(f"PERFECT_SYSTEM_AVAILABLE: {PERFECT_SYSTEM_AVAILABLE}")
        
        # 显示调试信息
        debug_text = "\\n".join(debug_info)
        QMessageBox.information(self, "调试信息", debug_text)
        print(debug_text)
'''
        
        # 在类的末尾添加调试方法
        class_end_pattern = "if __name__ == '__main__':"
        if class_end_pattern in content:
            content = content.replace(class_end_pattern, debug_method + "\n" + class_end_pattern)
            print("✅ 调试方法已添加")
        else:
            print("⚠️ 未找到插入位置")
        
        # 添加调试按钮到完美预测标签页
        button_pattern = '''        self.perfect_predict_button = QPushButton("🚀 运行完美预测")
        self.perfect_predict_button.clicked.connect(self.run_perfect_prediction)
        self.perfect_predict_button.setEnabled(PERFECT_SYSTEM_AVAILABLE)'''
        
        new_button_pattern = '''        self.perfect_predict_button = QPushButton("🚀 运行完美预测")
        self.perfect_predict_button.clicked.connect(self.run_perfect_prediction)
        self.perfect_predict_button.setEnabled(PERFECT_SYSTEM_AVAILABLE)
        
        self.debug_perfect_button = QPushButton("🔍 调试系统状态")
        self.debug_perfect_button.clicked.connect(self.debug_perfect_prediction_system)'''
        
        if button_pattern in content:
            content = content.replace(button_pattern, new_button_pattern)
            
            # 添加按钮到布局
            layout_pattern = '''        button_layout.addWidget(self.perfect_predict_button)
        button_layout.addWidget(self.clear_perfect_button)'''
            
            new_layout_pattern = '''        button_layout.addWidget(self.perfect_predict_button)
        button_layout.addWidget(self.debug_perfect_button)
        button_layout.addWidget(self.clear_perfect_button)'''
            
            content = content.replace(layout_pattern, new_layout_pattern)
            print("✅ 调试按钮已添加")
        else:
            print("⚠️ 未找到按钮模式")
        
        # 保存修改
        with open(gui_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 调试功能添加完成")
        return True
        
    except Exception as e:
        print(f"❌ 添加调试功能失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 完美预测系统调试工具")
    print("=" * 70)
    
    # 1. 调试完美预测系统
    success = debug_perfect_prediction()
    
    if success:
        print("\n🎉 调试完成：完美预测系统功能正常！")
        print("问题可能在GUI的状态管理或事件处理")
    else:
        print("\n❌ 调试发现问题")
    
    # 2. 创建修复
    print("\n🔧 应用修复...")
    fix_success = create_perfect_prediction_fix()
    
    # 3. 添加调试功能
    debug_success = create_debug_button()
    
    if fix_success and debug_success:
        print("\n🎉 修复完成！")
        print("📋 下一步:")
        print("  1. 重新启动程序")
        print("  2. 在完美预测标签页点击'调试系统状态'按钮")
        print("  3. 查看详细的调试信息")
        print("  4. 尝试运行完美预测")
    else:
        print("\n❌ 修复失败")
    
    return success and fix_success and debug_success

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
