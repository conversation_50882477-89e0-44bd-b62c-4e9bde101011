"""
数据库结构修复和完整数据导入脚本
解决表结构不匹配问题，确保所有字段完整
"""
import sys
sys.path.insert(0, 'src')

import pandas as pd
from datetime import datetime, date
import os
import json
from loguru import logger

def fix_database_structure():
    print("🔧 数据库结构修复和完整数据导入")
    print("=" * 80)
    
    try:
        # 1. 初始化完整数据库结构
        print("\n📊 步骤1: 初始化完整数据库结构...")
        from src.data_layer.database.init_db import initialize_database
        
        if not initialize_database():
            print("❌ 数据库初始化失败")
            return False
        print("✅ 完整数据库结构创建成功")
        
        # 2. 创建系统组件
        print("\n📊 步骤2: 创建系统组件...")
        from src.data_layer.database.models import create_database_engine, get_session
        from src.data_layer.year_mapping.lunar_year_manager import LunarYearManager
        from src.data_layer.year_mapping.number_attribute_mapper import NumberAttributeMapper
        from src.data_layer.data_import.lottery_data_processor import LotteryDataProcessor
        
        engine = create_database_engine()
        session = get_session(engine)
        lunar_manager = LunarYearManager(session)
        attr_mapper = NumberAttributeMapper()
        data_processor = LotteryDataProcessor(lunar_manager, attr_mapper, session)
        print("✅ 系统组件创建成功")
        
        # 3. 读取历史数据
        print("\n📊 步骤3: 读取历史数据文件...")
        df = pd.read_csv("历史数据.csv", encoding='utf-8')
        print(f"✅ 成功读取 {len(df)} 条记录")
        
        # 显示数据样本
        print(f"\n📋 数据样本:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            print(f"   {row['issue']} | {row['date']} | 正码: {row['numbers']} | 特码: {row['special']}")
        
        # 4. 批量导入数据
        print(f"\n📊 步骤4: 开始批量导入数据...")
        success_count = 0
        error_count = 0
        
        for index, row in df.iterrows():
            try:
                # 解析数据
                period_number = str(row['issue'])
                draw_date_str = str(row['date'])
                numbers_str = str(row['numbers']).replace('"', '').strip()
                special_number = int(row['special'])
                
                # 转换日期格式
                draw_date = datetime.strptime(draw_date_str, '%Y-%m-%d').date()
                
                # 解析正码号码
                regular_numbers = [int(x.strip()) for x in numbers_str.split(',')]
                
                # 数据验证
                if len(regular_numbers) != 6:
                    error_count += 1
                    logger.warning(f"期号 {period_number}: 正码数量错误 {len(regular_numbers)}")
                    continue
                
                if not all(1 <= n <= 49 for n in regular_numbers):
                    error_count += 1
                    logger.warning(f"期号 {period_number}: 正码范围错误")
                    continue
                
                if not (1 <= special_number <= 49):
                    error_count += 1
                    logger.warning(f"期号 {period_number}: 特码范围错误 {special_number}")
                    continue
                
                # 使用完整的数据处理器导入（包含所有扩展字段）
                success = data_processor.manual_input_record(
                    period_number=period_number,
                    draw_date=draw_date,
                    regular_numbers=regular_numbers,
                    special_number=special_number
                )
                
                if success:
                    success_count += 1
                    if success_count % 100 == 0:
                        print(f"   已导入 {success_count} 条记录...")
                else:
                    error_count += 1
                    logger.warning(f"导入失败: {period_number}")
                    
            except Exception as e:
                error_count += 1
                logger.error(f"处理记录失败 {index}: {e}")
        
        # 5. 验证导入结果
        print(f"\n📊 步骤5: 验证导入结果...")
        from src.data_layer.database.models import LotteryData
        
        total_records = session.query(LotteryData).count()
        
        print(f"\n" + "="*80)
        print("📊 数据导入完成统计")
        print("="*80)
        print(f"✅ 成功导入: {success_count} 条")
        print(f"❌ 导入失败: {error_count} 条")
        print(f"📊 成功率: {success_count/(success_count+error_count)*100:.1f}%")
        print(f"📋 数据库中总记录数: {total_records}")
        
        # 6. 验证数据库结构完整性
        print(f"\n📊 步骤6: 验证数据库结构完整性...")
        
        # 检查最新记录的所有字段
        latest_record = session.query(LotteryData).order_by(LotteryData.draw_date.desc()).first()
        if latest_record:
            print(f"✅ 最新记录验证:")
            print(f"   期号: {latest_record.period_number}")
            print(f"   日期: {latest_record.draw_date}")
            print(f"   正码: {json.loads(latest_record.regular_numbers) if latest_record.regular_numbers else []}")
            print(f"   特码: {latest_record.special_number}")
            print(f"   自然年: {latest_record.natural_year}")
            print(f"   农历年: {latest_record.lunar_year}")
            print(f"   特码生肖: {latest_record.special_zodiac}")
            print(f"   特码五行: {latest_record.special_wuxing}")
            print(f"   生肖波色: {latest_record.zodiac_color_wave}")
        
        # 7. 显示最新几条记录
        latest_records = session.query(LotteryData).order_by(LotteryData.draw_date.desc()).limit(5).all()
        print(f"\n📋 最新5条记录:")
        for record in latest_records:
            regular_nums = json.loads(record.regular_numbers) if record.regular_numbers else []
            print(f"   {record.period_number} | {record.draw_date} | 正码: {regular_nums} | 特码: {record.special_number} | 生肖: {record.special_zodiac}")
        
        # 8. 数据统计分析
        print(f"\n📊 步骤7: 数据统计分析...")
        date_range = session.query(LotteryData.draw_date).order_by(LotteryData.draw_date).all()
        if date_range:
            start_date = date_range[0][0]
            end_date = date_range[-1][0]
            print(f"   📅 数据时间范围: {start_date} 至 {end_date}")
            print(f"   📅 数据跨度: {(end_date - start_date).days} 天")
        
        # 统计生肖分布
        zodiac_stats = session.query(LotteryData.special_zodiac, 
                                    session.query(LotteryData).filter(
                                        LotteryData.special_zodiac == LotteryData.special_zodiac
                                    ).count().label('count')).group_by(LotteryData.special_zodiac).all()
        
        print(f"   🐲 生肖分布统计:")
        for zodiac, count in zodiac_stats[:6]:  # 显示前6个
            print(f"      {zodiac}: {count}次")
        
        session.close()
        logger.info("数据库结构修复和数据导入完成")
        return True
        
    except Exception as e:
        logger.error(f"修复过程发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = fix_database_structure()
    
    if result:
        print("\n" + "="*80)
        print("🎊 数据库结构修复成功！")
        print("="*80)
        print("✅ 完整数据库结构已创建")
        print("✅ 901期历史数据已导入")
        print("✅ 所有扩展字段已填充")
        print("✅ 预测系统可以正常运行")
        print("="*80)
    else:
        print("\n❌ 数据库结构修复失败！")
        print("请检查错误日志并重试。")
