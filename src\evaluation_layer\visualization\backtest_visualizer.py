"""
回测结果可视化 - 生成各种图表和报告
"""
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Optional, Tuple
import json
from datetime import datetime
import warnings

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class BacktestVisualizer:
    """回测结果可视化器"""
    
    def __init__(self, figsize: Tuple[int, int] = (12, 8)):
        """初始化可视化器"""
        self.figsize = figsize
        self.color_palette = sns.color_palette("husl", 10)
        
    def plot_performance_comparison(self, 
                                  backtest_results: Dict[str, Any],
                                  metrics: List[str] = None,
                                  save_path: str = None) -> None:
        """绘制性能比较图"""
        
        if not backtest_results:
            print("❌ 没有回测结果可视化")
            return
        
        if metrics is None:
            metrics = ['hit_rate', 'exact_match_rate', 'precision', 'recall', 'f1_score']
        
        # 提取数据
        strategies = []
        metric_data = {metric: [] for metric in metrics}
        
        for strategy_name, result in backtest_results.items():
            if 'performance' not in result:
                continue
                
            strategies.append(strategy_name)
            performance = result['performance']
            
            for metric in metrics:
                value = performance.get(metric, 0)
                metric_data[metric].append(value)
        
        if not strategies:
            print("❌ 没有有效的性能数据")
            return
        
        # 创建子图
        n_metrics = len(metrics)
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        axes = axes.flatten()
        
        for i, metric in enumerate(metrics):
            if i >= len(axes):
                break
                
            ax = axes[i]
            values = metric_data[metric]
            
            # 柱状图
            bars = ax.bar(strategies, values, color=self.color_palette[:len(strategies)])
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{value:.3f}', ha='center', va='bottom')
            
            ax.set_title(f'{metric.replace("_", " ").title()}')
            ax.set_ylabel('Score')
            ax.tick_params(axis='x', rotation=45)
            ax.grid(True, alpha=0.3)
        
        # 隐藏多余的子图
        for i in range(len(metrics), len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ 性能比较图已保存到: {save_path}")
        
        plt.show()
    
    def plot_hit_rate_timeline(self, 
                              backtest_results: Dict[str, Any],
                              save_path: str = None) -> None:
        """绘制命中率时间线"""
        
        if not backtest_results:
            print("❌ 没有回测结果可视化")
            return
        
        plt.figure(figsize=self.figsize)
        
        for i, (strategy_name, result) in enumerate(backtest_results.items()):
            predictions = result.get('predictions', [])
            actual_results = result.get('actual_results', [])
            prediction_dates = result.get('prediction_dates', [])
            
            if not predictions or not actual_results:
                continue
            
            # 计算每个预测的命中率
            hit_rates = []
            dates = []
            
            for pred, actual, date in zip(predictions, actual_results, prediction_dates):
                if 'error' in actual:
                    continue
                
                pred_numbers = pred.get('predicted_numbers', [])
                actual_numbers = actual.get('all_numbers', [])
                
                if pred_numbers and actual_numbers:
                    matches = len(set(pred_numbers) & set(actual_numbers))
                    hit_rate = matches / len(actual_numbers)
                    hit_rates.append(hit_rate)
                    dates.append(pd.to_datetime(date))
            
            if hit_rates and dates:
                # 绘制时间线
                plt.plot(dates, hit_rates, 
                        label=strategy_name, 
                        color=self.color_palette[i % len(self.color_palette)],
                        linewidth=2, alpha=0.8)
                
                # 添加移动平均线
                if len(hit_rates) > 10:
                    window = min(20, len(hit_rates) // 5)
                    moving_avg = pd.Series(hit_rates).rolling(window=window).mean()
                    plt.plot(dates, moving_avg, 
                            linestyle='--', 
                            color=self.color_palette[i % len(self.color_palette)],
                            alpha=0.6, linewidth=1)
        
        plt.title('命中率时间线', fontsize=16, fontweight='bold')
        plt.xlabel('日期')
        plt.ylabel('命中率')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ 命中率时间线已保存到: {save_path}")
        
        plt.show()
    
    def plot_number_frequency_heatmap(self, 
                                     backtest_results: Dict[str, Any],
                                     strategy_name: str = None,
                                     save_path: str = None) -> None:
        """绘制号码频率热力图"""
        
        if not backtest_results:
            print("❌ 没有回测结果可视化")
            return
        
        # 选择策略
        if strategy_name and strategy_name in backtest_results:
            selected_results = {strategy_name: backtest_results[strategy_name]}
        else:
            # 选择第一个策略
            selected_results = {list(backtest_results.keys())[0]: list(backtest_results.values())[0]}
        
        for strategy, result in selected_results.items():
            predictions = result.get('predictions', [])
            actual_results = result.get('actual_results', [])
            
            if not predictions or not actual_results:
                continue
            
            # 统计预测号码频率
            pred_freq = np.zeros(49)
            actual_freq = np.zeros(49)
            
            for pred, actual in zip(predictions, actual_results):
                if 'error' in actual:
                    continue
                
                pred_numbers = pred.get('predicted_numbers', [])
                actual_numbers = actual.get('all_numbers', [])
                
                for num in pred_numbers:
                    if 1 <= num <= 49:
                        pred_freq[num - 1] += 1
                
                for num in actual_numbers:
                    if 1 <= num <= 49:
                        actual_freq[num - 1] += 1
            
            # 创建热力图数据
            heatmap_data = np.array([pred_freq, actual_freq])
            
            # 绘制热力图
            plt.figure(figsize=(15, 6))
            
            sns.heatmap(heatmap_data, 
                       xticklabels=range(1, 50),
                       yticklabels=['预测频率', '实际频率'],
                       annot=False, 
                       cmap='YlOrRd',
                       cbar_kws={'label': '频率'})
            
            plt.title(f'{strategy} - 号码频率热力图', fontsize=16, fontweight='bold')
            plt.xlabel('号码')
            plt.ylabel('类型')
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"✅ 号码频率热力图已保存到: {save_path}")
            
            plt.show()
            break  # 只绘制一个策略
    
    def plot_prediction_accuracy_distribution(self, 
                                             backtest_results: Dict[str, Any],
                                             save_path: str = None) -> None:
        """绘制预测准确率分布"""
        
        if not backtest_results:
            print("❌ 没有回测结果可视化")
            return
        
        plt.figure(figsize=self.figsize)
        
        for i, (strategy_name, result) in enumerate(backtest_results.items()):
            predictions = result.get('predictions', [])
            actual_results = result.get('actual_results', [])
            
            if not predictions or not actual_results:
                continue
            
            # 计算每个预测的准确率
            accuracies = []
            
            for pred, actual in zip(predictions, actual_results):
                if 'error' in actual:
                    continue
                
                pred_numbers = pred.get('predicted_numbers', [])
                actual_numbers = actual.get('all_numbers', [])
                
                if pred_numbers and actual_numbers:
                    matches = len(set(pred_numbers) & set(actual_numbers))
                    accuracy = matches / len(actual_numbers)
                    accuracies.append(accuracy)
            
            if accuracies:
                # 绘制分布直方图
                plt.hist(accuracies, bins=20, alpha=0.7, 
                        label=strategy_name,
                        color=self.color_palette[i % len(self.color_palette)])
        
        plt.title('预测准确率分布', fontsize=16, fontweight='bold')
        plt.xlabel('准确率')
        plt.ylabel('频次')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ 准确率分布图已保存到: {save_path}")
        
        plt.show()
    
    def plot_fusion_comparison(self, 
                              fusion_results: Dict[str, Any],
                              save_path: str = None) -> None:
        """绘制融合策略比较"""
        
        if not fusion_results:
            print("❌ 没有融合结果可视化")
            return
        
        # 提取融合方法和性能数据
        methods = []
        hit_rates = []
        precisions = []
        recalls = []
        
        for method, result in fusion_results.items():
            if 'fusion_performance' not in result:
                continue
            
            performance = result['fusion_performance']
            methods.append(method)
            hit_rates.append(performance.get('hit_rate', 0))
            precisions.append(performance.get('precision', 0))
            recalls.append(performance.get('recall', 0))
        
        if not methods:
            print("❌ 没有有效的融合性能数据")
            return
        
        # 创建雷达图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 柱状图比较
        x = np.arange(len(methods))
        width = 0.25
        
        ax1.bar(x - width, hit_rates, width, label='命中率', alpha=0.8)
        ax1.bar(x, precisions, width, label='精确率', alpha=0.8)
        ax1.bar(x + width, recalls, width, label='召回率', alpha=0.8)
        
        ax1.set_xlabel('融合方法')
        ax1.set_ylabel('性能分数')
        ax1.set_title('融合策略性能比较')
        ax1.set_xticks(x)
        ax1.set_xticklabels(methods, rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 散点图：命中率 vs 精确率
        ax2.scatter(hit_rates, precisions, s=100, alpha=0.7)
        
        for i, method in enumerate(methods):
            ax2.annotate(method, (hit_rates[i], precisions[i]), 
                        xytext=(5, 5), textcoords='offset points')
        
        ax2.set_xlabel('命中率')
        ax2.set_ylabel('精确率')
        ax2.set_title('命中率 vs 精确率')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ 融合比较图已保存到: {save_path}")
        
        plt.show()
    
    def generate_comprehensive_report(self, 
                                    backtest_results: Dict[str, Any],
                                    fusion_results: Dict[str, Any] = None,
                                    output_dir: str = "reports") -> str:
        """生成综合报告"""
        
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 生成各种图表
        print("📊 生成综合可视化报告...")
        
        # 1. 性能比较图
        perf_path = os.path.join(output_dir, f"performance_comparison_{timestamp}.png")
        self.plot_performance_comparison(backtest_results, save_path=perf_path)
        
        # 2. 命中率时间线
        timeline_path = os.path.join(output_dir, f"hit_rate_timeline_{timestamp}.png")
        self.plot_hit_rate_timeline(backtest_results, save_path=timeline_path)
        
        # 3. 号码频率热力图
        heatmap_path = os.path.join(output_dir, f"number_frequency_{timestamp}.png")
        self.plot_number_frequency_heatmap(backtest_results, save_path=heatmap_path)
        
        # 4. 准确率分布
        dist_path = os.path.join(output_dir, f"accuracy_distribution_{timestamp}.png")
        self.plot_prediction_accuracy_distribution(backtest_results, save_path=dist_path)
        
        # 5. 融合策略比较（如果有）
        fusion_path = None
        if fusion_results:
            fusion_path = os.path.join(output_dir, f"fusion_comparison_{timestamp}.png")
            self.plot_fusion_comparison(fusion_results, save_path=fusion_path)
        
        # 生成HTML报告
        html_report = self._generate_html_report(
            backtest_results, fusion_results, timestamp,
            perf_path, timeline_path, heatmap_path, dist_path, fusion_path
        )
        
        report_path = os.path.join(output_dir, f"backtest_report_{timestamp}.html")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_report)
        
        print(f"✅ 综合报告已生成: {report_path}")
        return report_path
    
    def _generate_html_report(self, 
                             backtest_results: Dict[str, Any],
                             fusion_results: Dict[str, Any],
                             timestamp: str,
                             *image_paths) -> str:
        """生成HTML报告"""
        
        html_template = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>澳门六合彩预测系统 - 回测报告</title>
            <style>
                body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }}
                .header {{ text-align: center; color: #2c3e50; }}
                .section {{ margin: 30px 0; }}
                .metric-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                .metric-table th, .metric-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                .metric-table th {{ background-color: #f2f2f2; }}
                .image-container {{ text-align: center; margin: 20px 0; }}
                .image-container img {{ max-width: 100%; height: auto; }}
                .summary-box {{ background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>澳门六合彩预测系统 - 回测报告</h1>
                <p>生成时间: {timestamp}</p>
            </div>
            
            <div class="section">
                <h2>📊 回测概览</h2>
                <div class="summary-box">
                    <p><strong>测试策略数量:</strong> {len(backtest_results)}</p>
                    <p><strong>融合策略数量:</strong> {len(fusion_results) if fusion_results else 0}</p>
                    <p><strong>报告生成时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
            </div>
            
            <div class="section">
                <h2>📈 性能指标对比</h2>
                <div class="image-container">
                    <img src="{image_paths[0]}" alt="性能比较图">
                </div>
            </div>
            
            <div class="section">
                <h2>📉 命中率时间线</h2>
                <div class="image-container">
                    <img src="{image_paths[1]}" alt="命中率时间线">
                </div>
            </div>
            
            <div class="section">
                <h2>🔥 号码频率分析</h2>
                <div class="image-container">
                    <img src="{image_paths[2]}" alt="号码频率热力图">
                </div>
            </div>
            
            <div class="section">
                <h2>📊 准确率分布</h2>
                <div class="image-container">
                    <img src="{image_paths[3]}" alt="准确率分布">
                </div>
            </div>
        """
        
        if fusion_results and len(image_paths) > 4:
            html_template += f"""
            <div class="section">
                <h2>🔀 融合策略比较</h2>
                <div class="image-container">
                    <img src="{image_paths[4]}" alt="融合策略比较">
                </div>
            </div>
            """
        
        html_template += """
            <div class="section">
                <h2>📋 详细数据</h2>
                <p>详细的回测数据和分析结果请参考生成的JSON文件。</p>
            </div>
            
            <div class="section">
                <h2>💡 建议</h2>
                <div class="summary-box">
                    <ul>
                        <li>关注命中率和稳定性的平衡</li>
                        <li>考虑使用融合策略提升整体性能</li>
                        <li>定期重新评估和调整策略参数</li>
                        <li>注意过拟合风险，保持模型泛化能力</li>
                    </ul>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_template
