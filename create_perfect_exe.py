"""
六合彩预测系统完美EXE打包工具
基于深度分析的完整复刻方案
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path
import json

def create_exe_package():
    """创建完美EXE打包"""
    print("🚀 六合彩预测系统完美EXE打包")
    print("=" * 60)
    
    # 1. 环境检查
    print("\n1️⃣ 环境检查...")
    check_environment()
    
    # 2. 创建打包目录结构
    print("\n2️⃣ 创建打包目录结构...")
    create_package_structure()
    
    # 3. 复制核心文件
    print("\n3️⃣ 复制核心文件...")
    copy_core_files()
    
    # 4. 创建启动脚本
    print("\n4️⃣ 创建启动脚本...")
    create_startup_script()
    
    # 5. 创建PyInstaller配置
    print("\n5️⃣ 创建PyInstaller配置...")
    create_pyinstaller_spec()
    
    # 6. 执行打包
    print("\n6️⃣ 执行EXE打包...")
    build_exe()
    
    # 7. 后处理优化
    print("\n7️⃣ 后处理优化...")
    post_process_exe()
    
    print("\n🎉 EXE打包完成！")

def check_environment():
    """检查打包环境"""
    required_packages = [
        'PyInstaller',
        'PyQt5', 
        'numpy',
        'pandas',
        'scikit-learn',
        'xgboost'
    ]
    
    print("📦 检查必需包...")
    for package in required_packages:
        try:
            __import__(package.lower().replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - 需要安装")
            install_package(package)

def install_package(package):
    """安装缺失的包"""
    print(f"🔧 安装 {package}...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        print(f"✅ {package} 安装成功")
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")

def create_package_structure():
    """创建打包目录结构"""
    base_dir = Path("EXE_Package")
    
    # 清理旧目录
    if base_dir.exists():
        shutil.rmtree(base_dir)
    
    # 创建目录结构
    directories = [
        "EXE_Package",
        "EXE_Package/src",
        "EXE_Package/src/algorithm_layer",
        "EXE_Package/src/independent_modules", 
        "EXE_Package/data",
        "EXE_Package/config",
        "EXE_Package/logs",
        "EXE_Package/resources",
        "EXE_Package/dist",
        "EXE_Package/build"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"  📁 创建: {directory}")

def copy_core_files():
    """复制核心文件"""
    source_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    target_dir = Path("EXE_Package")
    
    # 核心Python文件
    core_files = [
        "lottery_prediction_gui.py",
        "special_number_predictor.py",
        "consistent_predictor.py", 
        "historical_backtest.py",
        "consistency_test.py",
        "adaptive_optimizer.py",
        "optimal_pattern_selector.py",
        "enhanced_hit_rate_optimizer.py"
    ]
    
    print("📄 复制核心文件...")
    for file in core_files:
        source_file = source_dir / file
        if source_file.exists():
            shutil.copy2(source_file, target_dir / file)
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} - 文件不存在")
    
    # 复制src目录
    print("📁 复制src目录...")
    src_source = source_dir / "src"
    src_target = target_dir / "src"
    
    if src_source.exists():
        shutil.copytree(src_source, src_target, dirs_exist_ok=True)
        print("  ✅ src目录复制完成")
    
    # 复制数据库
    print("💾 复制数据库...")
    db_source = source_dir / "data" / "lottery.db"
    db_target = target_dir / "data" / "lottery.db"
    
    if db_source.exists():
        shutil.copy2(db_source, db_target)
        print("  ✅ 数据库复制完成")
    
    # 复制配置文件
    print("⚙️ 复制配置文件...")
    config_files = [
        "optimal_config.json",
        "enhanced_backtest_config.json",
        "optimization_config.json",
        "fusion_config.json"
    ]
    
    for config_file in config_files:
        source_file = source_dir / config_file
        if source_file.exists():
            shutil.copy2(source_file, target_dir / "config" / config_file)
            print(f"  ✅ {config_file}")

def create_startup_script():
    """创建启动脚本"""
    startup_script = '''# -*- coding: utf-8 -*-
"""
六合彩预测系统 - EXE版本启动脚本
"""

import sys
import os
from pathlib import Path

def setup_environment():
    """设置运行环境"""
    # 获取EXE运行目录
    if getattr(sys, 'frozen', False):
        # PyInstaller打包后的路径
        application_path = Path(sys._MEIPASS)
        exe_dir = Path(sys.executable).parent
    else:
        # 开发环境路径
        application_path = Path(__file__).parent
        exe_dir = application_path
    
    # 添加路径到sys.path
    sys.path.insert(0, str(application_path))
    sys.path.insert(0, str(application_path / "src"))
    
    # 设置工作目录
    os.chdir(exe_dir)
    
    # 创建必要目录
    for directory in ["data", "config", "logs"]:
        (exe_dir / directory).mkdir(exist_ok=True)
    
    # 设置编码
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    return application_path, exe_dir

def main():
    """主函数"""
    try:
        # 设置环境
        app_path, exe_dir = setup_environment()
        
        print("🚀 启动六合彩预测系统...")
        print(f"📁 应用路径: {app_path}")
        print(f"📁 执行路径: {exe_dir}")
        
        # 导入并启动GUI
        from lottery_prediction_gui import MainWindow
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        app.setApplicationName("六合彩预测系统")
        app.setApplicationVersion("v2.0.0")
        
        window = MainWindow()
        window.show()
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
'''
    
    with open("EXE_Package/main.py", 'w', encoding='utf-8') as f:
        f.write(startup_script)
    
    print("✅ 启动脚本创建完成")

def create_pyinstaller_spec():
    """创建PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 获取当前目录
current_dir = Path.cwd()

a = Analysis(
    ['main.py'],
    pathex=[str(current_dir)],
    binaries=[],
    datas=[
        ('data', 'data'),
        ('config', 'config'),
        ('src', 'src'),
        ('*.json', '.'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'numpy',
        'pandas',
        'sklearn',
        'sklearn.ensemble',
        'sklearn.svm',
        'sklearn.neural_network',
        'sklearn.preprocessing',
        'sklearn.model_selection',
        'sklearn.feature_selection',
        'sklearn.decomposition',
        'xgboost',
        'sqlite3',
        'json',
        'datetime',
        'collections',
        'typing',
        'logging',
        'warnings',
        'src.perfect_prediction_system',
        'src.dynamic_fusion_manager_v3',
        'src.stability_optimizer_v3',
        'src.enhanced_feature_engineering_v2',
        'src.independent_modules.ml_module',
        'src.independent_modules.traditional_module',
        'src.independent_modules.zodiac_extended_module',
        'src.independent_modules.special_zodiac_module',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'tkinter',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='六合彩预测系统_v2.0.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 显示控制台用于调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if Path('icon.ico').exists() else None,
)
'''
    
    with open("EXE_Package/lottery_system.spec", 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ PyInstaller配置文件创建完成")

def build_exe():
    """执行EXE打包"""
    os.chdir("EXE_Package")
    
    try:
        print("🔨 开始PyInstaller打包...")
        
        # 执行打包命令
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm', 
            'lottery_system.spec'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ PyInstaller打包成功")
        else:
            print("❌ PyInstaller打包失败")
            print("错误输出:", result.stderr)
            
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
    
    finally:
        os.chdir("..")

def post_process_exe():
    """后处理优化"""
    exe_dir = Path("EXE_Package/dist")
    
    if not exe_dir.exists():
        print("❌ EXE目录不存在")
        return
    
    print("🔧 后处理优化...")
    
    # 创建最终发布目录
    release_dir = Path("六合彩预测系统_v2.0.0_EXE版")
    if release_dir.exists():
        shutil.rmtree(release_dir)
    
    release_dir.mkdir()
    
    # 复制EXE文件
    exe_file = exe_dir / "六合彩预测系统_v2.0.0.exe"
    if exe_file.exists():
        shutil.copy2(exe_file, release_dir / "六合彩预测系统_v2.0.0.exe")
        print("✅ EXE文件复制完成")
    
    # 创建使用说明
    readme_content = """
# 六合彩预测系统 v2.0.0 - EXE版

## 🚀 快速启动
双击 "六合彩预测系统_v2.0.0.exe" 即可启动

## 📋 系统功能
- 特码预测 (传统分析 + 机器学习)
- 完美预测系统 (四模块协同)
- 性能监控 (实时性能跟踪)
- 增强回测 (参数优化)
- 数据管理 (历史数据导入)

## 🔧 系统要求
- Windows 7/8/10/11
- 内存: 4GB以上
- 硬盘: 500MB可用空间

## 📞 技术支持
如有问题请联系技术支持

## 📝 版本信息
版本: v2.0.0
打包时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open(release_dir / "使用说明.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    # 创建启动批处理
    bat_content = """@echo off
echo 启动六合彩预测系统...
start "" "六合彩预测系统_v2.0.0.exe"
"""
    
    with open(release_dir / "启动系统.bat", 'w', encoding='gbk') as f:
        f.write(bat_content)
    
    print("✅ 后处理完成")
    print(f"📁 发布目录: {release_dir.absolute()}")

def main():
    """主函数"""
    print("🎯 六合彩预测系统完美EXE打包工具")
    print("基于深度系统分析的完整复刻方案")
    print("=" * 70)
    
    try:
        create_exe_package()
        
        print("\n🎉 EXE打包完成！")
        print("📋 打包结果:")
        print("  ✅ 完整功能复刻")
        print("  ✅ 所有模块集成")
        print("  ✅ 数据库包含")
        print("  ✅ 配置文件完整")
        print("  ✅ 独立运行")
        
        print("\n📁 输出文件:")
        print("  六合彩预测系统_v2.0.0_EXE版/")
        print("  ├── 六合彩预测系统_v2.0.0.exe")
        print("  ├── 启动系统.bat")
        print("  └── 使用说明.txt")
        
    except Exception as e:
        print(f"\n❌ 打包失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    from datetime import datetime
    main()
    input("\n按回车键退出...")
