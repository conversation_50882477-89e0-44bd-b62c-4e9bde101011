#!/usr/bin/env python3
"""
六合彩预测系统 - 核心预测模块
提供特码和生肖预测功能
"""

import sqlite3
import random
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from .special_number_predictor import SpecialNumberPredictor

class LotteryPredictionSystem:
    """六合彩预测系统"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # 初始化预测器
        self.special_predictor = SpecialNumberPredictor()
        
        # 生肖映射
        self.zodiac_mapping = {
            1: '鼠', 13: '鼠', 25: '鼠', 37: '鼠', 49: '鼠',
            2: '牛', 14: '牛', 26: '牛', 38: '牛',
            3: '虎', 15: '虎', 27: '虎', 39: '虎',
            4: '兔', 16: '兔', 28: '兔', 40: '兔',
            5: '龙', 17: '龙', 29: '龙', 41: '龙',
            6: '蛇', 18: '蛇', 30: '蛇', 42: '蛇',
            7: '马', 19: '马', 31: '马', 43: '马',
            8: '羊', 20: '羊', 32: '羊', 44: '羊',
            9: '猴', 21: '猴', 33: '猴', 45: '猴',
            10: '鸡', 22: '鸡', 34: '鸡', 46: '鸡',
            11: '狗', 23: '狗', 35: '狗', 47: '狗',
            12: '猪', 24: '猪', 36: '猪', 48: '猪'
        }
        
        self.logger.info("🎯 特码预测系统初始化完成")
        print(f"📊 生肖映射: {len(self.zodiac_mapping)} 个号码")
        print(f"📊 生肖种类: {len(set(self.zodiac_mapping.values()))} 个")
    
    def predict_special_number(self, target_date: str, analysis_days: int = 100) -> Dict[str, Any]:
        """预测特码号码"""
        try:
            self.logger.info(f"开始特码预测，目标日期: {target_date}")
            
            # 获取历史数据
            historical_data = self._get_historical_data(target_date, analysis_days)
            
            if not historical_data:
                self.logger.warning("历史数据不足，使用默认预测")
                return self._get_default_special_prediction(target_date)
            
            # 使用特码预测器
            prediction_result = self.special_predictor.predict(historical_data, target_date)
            
            # 格式化结果
            result = {
                "recommended_numbers": prediction_result.get("predicted_numbers", []),
                "confidence": prediction_result.get("confidence", 0.6),
                "analysis_summary": {
                    "historical_records": len(historical_data),
                    "hot_numbers": prediction_result.get("analysis", {}).get("hot_numbers", []),
                    "cold_numbers": prediction_result.get("analysis", {}).get("cold_numbers", []),
                    "patterns": prediction_result.get("analysis", {}).get("patterns", {})
                },
                "prediction_method": "special_number_predictor",
                "target_date": target_date,
                "prediction_time": datetime.now().isoformat()
            }
            
            self.logger.info(f"特码预测完成，推荐 {len(result['recommended_numbers'])} 个号码")
            return result
            
        except Exception as e:
            self.logger.error(f"特码预测失败: {e}")
            return self._get_default_special_prediction(target_date)
    
    def predict_zodiac(self, target_date: str, analysis_days: int = 100) -> Dict[str, Any]:
        """预测生肖"""
        try:
            self.logger.info(f"开始生肖预测，目标日期: {target_date}")
            
            # 获取历史数据
            historical_data = self._get_historical_data(target_date, analysis_days)
            
            if not historical_data:
                return self._get_default_zodiac_prediction(target_date)
            
            # 统计生肖频率
            zodiac_frequency = {}
            for record in historical_data[-50:]:  # 最近50期
                special_number = record.get('special_number', 0)
                if special_number in self.zodiac_mapping:
                    zodiac = self.zodiac_mapping[special_number]
                    zodiac_frequency[zodiac] = zodiac_frequency.get(zodiac, 0) + 1
            
            # 选择频率最高的4个生肖
            sorted_zodiacs = sorted(zodiac_frequency.items(), key=lambda x: x[1], reverse=True)
            recommended_zodiacs = [zodiac for zodiac, freq in sorted_zodiacs[:4]]
            
            # 如果不足4个，随机补充
            all_zodiacs = list(set(self.zodiac_mapping.values()))
            while len(recommended_zodiacs) < 4:
                remaining_zodiacs = [z for z in all_zodiacs if z not in recommended_zodiacs]
                if remaining_zodiacs:
                    recommended_zodiacs.append(random.choice(remaining_zodiacs))
                else:
                    break
            
            # 计算置信度
            total_frequency = sum(zodiac_frequency.values())
            top_frequency = sum(freq for zodiac, freq in sorted_zodiacs[:4])
            confidence = (top_frequency / total_frequency) if total_frequency > 0 else 0.5
            confidence = max(0.5, min(0.9, confidence))
            
            result = {
                "recommended_zodiacs": recommended_zodiacs,
                "confidence": round(confidence, 3),
                "zodiac_frequency": zodiac_frequency,
                "analysis_summary": {
                    "historical_records": len(historical_data),
                    "zodiac_distribution": zodiac_frequency
                },
                "prediction_method": "zodiac_frequency_analysis",
                "target_date": target_date,
                "prediction_time": datetime.now().isoformat()
            }
            
            self.logger.info(f"生肖预测完成，推荐 {len(recommended_zodiacs)} 个生肖")
            return result
            
        except Exception as e:
            self.logger.error(f"生肖预测失败: {e}")
            return self._get_default_zodiac_prediction(target_date)
    
    def _get_historical_data(self, before_date: str, days: int) -> List[Dict[str, Any]]:
        """获取历史数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT draw_date, special_number, period_number
                FROM lottery_results 
                WHERE draw_date < ? 
                ORDER BY draw_date DESC 
                LIMIT ?
            ''', (before_date, days))
            
            data = []
            for row in cursor.fetchall():
                data.append({
                    "draw_date": row[0],
                    "special_number": row[1],
                    "period_number": row[2]
                })
            
            conn.close()
            return data
            
        except Exception as e:
            self.logger.error(f"获取历史数据失败: {e}")
            return []
    
    def _get_default_special_prediction(self, target_date: str) -> Dict[str, Any]:
        """获取默认特码预测"""
        # 生成确定性随机号码
        random.seed(hash(target_date) % 1000000)
        recommended_numbers = sorted(random.sample(range(1, 50), 16))
        
        return {
            "recommended_numbers": recommended_numbers,
            "confidence": 0.5,
            "analysis_summary": {
                "historical_records": 0,
                "hot_numbers": [],
                "cold_numbers": [],
                "patterns": {}
            },
            "prediction_method": "default_random",
            "target_date": target_date,
            "prediction_time": datetime.now().isoformat(),
            "note": "数据不足，使用默认预测"
        }
    
    def _get_default_zodiac_prediction(self, target_date: str) -> Dict[str, Any]:
        """获取默认生肖预测"""
        # 生成确定性随机生肖
        random.seed(hash(target_date + "zodiac") % 1000000)
        all_zodiacs = list(set(self.zodiac_mapping.values()))
        recommended_zodiacs = random.sample(all_zodiacs, 4)
        
        return {
            "recommended_zodiacs": recommended_zodiacs,
            "confidence": 0.5,
            "zodiac_frequency": {},
            "analysis_summary": {
                "historical_records": 0,
                "zodiac_distribution": {}
            },
            "prediction_method": "default_random",
            "target_date": target_date,
            "prediction_time": datetime.now().isoformat(),
            "note": "数据不足，使用默认预测"
        }
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            "name": "六合彩预测系统",
            "version": "1.0.0",
            "features": [
                "特码预测",
                "生肖预测",
                "历史数据分析",
                "模式识别"
            ],
            "zodiac_count": len(set(self.zodiac_mapping.values())),
            "number_range": "1-49",
            "prediction_methods": [
                "频率分析",
                "模式识别",
                "趋势分析"
            ]
        }

# 兼容性函数
def create_lottery_system(db_path: str = "data/lottery.db") -> LotteryPredictionSystem:
    """创建六合彩预测系统实例"""
    return LotteryPredictionSystem(db_path)

def predict_lottery_numbers(target_date: str, db_path: str = "data/lottery.db") -> Dict[str, Any]:
    """预测六合彩号码的便捷函数"""
    system = create_lottery_system(db_path)
    
    special_result = system.predict_special_number(target_date)
    zodiac_result = system.predict_zodiac(target_date)
    
    return {
        "special_numbers": special_result,
        "zodiacs": zodiac_result,
        "target_date": target_date,
        "prediction_time": datetime.now().isoformat()
    }
