import sys
import os
sys.path.append('src')

def check_module_implementations():
    """检查模组具体实现"""
    print("🔧 分析模组具体实现检查")
    print("=" * 50)
    
    try:
        from perfect_prediction_system import PerfectPredictionSystem
        
        # 创建并初始化系统
        system = PerfectPredictionSystem()
        system.initialize_modules()
        
        print(f"📊 检查初始化后的模组状态:")
        
        # 检查各模组的实际类型和方法
        module_attrs = [
            'traditional_analysis',
            'machine_learning', 
            'zodiac_extended',
            'special_zodiac_module',
            'fusion_manager',
            'smart_filter'
        ]
        
        for attr in module_attrs:
            if hasattr(system, attr):
                module = getattr(system, attr)
                if module is not None:
                    print(f"\n✅ {attr}:")
                    print(f"   类型: {type(module).__name__}")
                    print(f"   模块: {module.__class__.__module__}")
                    
                    # 检查关键方法
                    methods_to_check = ['predict', 'analyze', 'filter_numbers', 'run_prediction']
                    found_methods = []
                    for method in methods_to_check:
                        if hasattr(module, method):
                            found_methods.append(method)
                    
                    if found_methods:
                        print(f"   方法: {', '.join(found_methods)}")
                    else:
                        print(f"   ⚠️ 未找到标准方法")
                    
                    # 检查模组的所有方法
                    all_methods = [method for method in dir(module) if not method.startswith('_')]
                    if all_methods:
                        print(f"   所有方法: {', '.join(all_methods[:5])}{'...' if len(all_methods) > 5 else ''}")
                else:
                    print(f"\n❌ {attr}: None")
            else:
                print(f"\n❌ {attr}: 不存在")
        
        # 测试预测功能
        print(f"\n🎯 测试预测功能:")
        try:
            result = system.run_complete_prediction("2025-06-23")
            print(f"   ✅ run_complete_prediction: 成功")
            print(f"   📊 结果类型: {type(result)}")
            
            if isinstance(result, dict):
                print(f"   📋 结果键值: {list(result.keys())}")
                
                # 检查最终结果
                final_results = result.get('final_results', {})
                if final_results:
                    print(f"   🎯 最终结果:")
                    for key, value in final_results.items():
                        if isinstance(value, list):
                            print(f"      {key}: {len(value)} 个项目")
                        else:
                            print(f"      {key}: {value}")
            
        except Exception as e:
            print(f"   ❌ run_complete_prediction 失败: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_module_implementations()
