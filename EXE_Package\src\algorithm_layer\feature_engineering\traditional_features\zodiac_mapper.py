"""
生肖特征映射器
"""
from typing import Dict, List
from datetime import date
from loguru import logger

class ZodiacMapper:
    def __init__(self):
        # 12生肖基础映射
        self.zodiac_names = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
        
        # 生肖扩展属性
        self.zodiac_attributes = {
            "鼠": {"color": "蓝", "season": "冬", "time": "夜", "direction": "左", "nature": "阴"},
            "牛": {"color": "红", "season": "冬", "time": "夜", "direction": "右", "nature": "阴"},
            "虎": {"color": "蓝", "season": "春", "time": "夜", "direction": "左", "nature": "阳"},
            "兔": {"color": "绿", "season": "春", "time": "日", "direction": "右", "nature": "阴"},
            "龙": {"color": "红", "season": "春", "time": "日", "direction": "左", "nature": "阳"},
            "蛇": {"color": "绿", "season": "夏", "time": "日", "direction": "右", "nature": "阴"},
            "马": {"color": "红", "season": "夏", "time": "日", "direction": "左", "nature": "阳"},
            "羊": {"color": "绿", "season": "夏", "time": "日", "direction": "右", "nature": "阴"},
            "猴": {"color": "蓝", "season": "秋", "time": "日", "direction": "左", "nature": "阳"},
            "鸡": {"color": "红", "season": "秋", "time": "日", "direction": "右", "nature": "阴"},
            "狗": {"color": "绿", "season": "秋", "time": "夜", "direction": "左", "nature": "阳"},
            "猪": {"color": "蓝", "season": "冬", "time": "夜", "direction": "右", "nature": "阴"}
        }
    
    def get_zodiac_by_number(self, number: int, lunar_year: int) -> str:
        """根据号码和农历年获取生肖"""
        # 简化映射：根据号码模12
        zodiac_index = (number - 1) % 12
        return self.zodiac_names[zodiac_index]
    
    def get_zodiac_features(self, zodiac: str) -> Dict:
        """获取生肖特征"""
        if zodiac not in self.zodiac_attributes:
            return {}
        
        attrs = self.zodiac_attributes[zodiac]
        features = {
            f'zodiac_color_{attrs["color"]}': 1,
            f'zodiac_season_{attrs["season"]}': 1,
            f'zodiac_time_{attrs["time"]}': 1,
            f'zodiac_direction_{attrs["direction"]}': 1,
            f'zodiac_nature_{attrs["nature"]}': 1
        }
        
        # 其他颜色、季节等设为0
        for color in ["红", "绿", "蓝"]:
            if f'zodiac_color_{color}' not in features:
                features[f'zodiac_color_{color}'] = 0
        
        for season in ["春", "夏", "秋", "冬"]:
            if f'zodiac_season_{season}' not in features:
                features[f'zodiac_season_{season}'] = 0
        
        return features
    
    def extract_zodiac_features(self, numbers: List[int], special: int, lunar_year: int) -> Dict:
        """提取号码的生肖特征"""
        features = {}
        
        # 正码生肖分布
        zodiac_count = {}
        for zodiac in self.zodiac_names:
            zodiac_count[zodiac] = 0
        
        for number in numbers:
            zodiac = self.get_zodiac_by_number(number, lunar_year)
            zodiac_count[zodiac] += 1
        
        # 生肖分布特征
        for zodiac, count in zodiac_count.items():
            features[f'regular_zodiac_{zodiac}'] = count
        
        # 特码生肖
        special_zodiac = self.get_zodiac_by_number(special, lunar_year)
        features['special_zodiac'] = special_zodiac
        
        # 特码生肖特征
        special_zodiac_features = self.get_zodiac_features(special_zodiac)
        for key, value in special_zodiac_features.items():
            features[f'special_{key}'] = value
        
        # 生肖多样性
        unique_zodiacs = sum(1 for count in zodiac_count.values() if count > 0)
        features['zodiac_diversity'] = unique_zodiacs
        
        return features
