"""
检查年份映射系统和预测回测的一致性
"""

import sqlite3
from datetime import datetime, date
import json

def check_spring_festival_mapping():
    """检查春节日期映射"""
    print("📅 春节日期映射检查")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect("data/lottery.db")
        cursor = conn.cursor()
        
        # 获取春节映射数据
        cursor.execute("""
            SELECT lunar_year, zodiac_name, spring_festival_date, 
                   year_start_date, year_end_date 
            FROM lunar_year_mapping 
            WHERE lunar_year BETWEEN 2023 AND 2025 
            ORDER BY lunar_year
        """)
        
        mappings = cursor.fetchall()
        
        print("🗓️ 农历年份和春节日期:")
        for mapping in mappings:
            year, zodiac, spring_date, start_date, end_date = mapping
            print(f"  {year}年{zodiac}年:")
            print(f"    春节日期: {spring_date}")
            print(f"    有效期间: {start_date} 至 {end_date}")
        
        # 检查实际的春节日期
        actual_spring_festivals = {
            2023: "2023-01-22",  # 兔年春节
            2024: "2024-02-10",  # 龙年春节  
            2025: "2025-01-29"   # 蛇年春节
        }
        
        print("\n✅ 春节日期验证:")
        for mapping in mappings:
            year, zodiac, spring_date, _, _ = mapping
            expected = actual_spring_festivals.get(year)
            if spring_date == expected:
                print(f"  {year}年: ✅ 正确 ({spring_date})")
            else:
                print(f"  {year}年: ❌ 错误 (数据库:{spring_date}, 实际:{expected})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def check_cross_year_data():
    """检查跨年数据的处理"""
    print("\n🔄 跨年数据处理检查")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect("data/lottery.db")
        cursor = conn.cursor()
        
        # 检查2024年春节前后的数据
        print("🔍 2024年春节(2024-02-10)前后数据:")
        cursor.execute("""
            SELECT draw_date, natural_year, lunar_year, special_number, 
                   special_zodiac, special_wuxing 
            FROM lottery_data 
            WHERE draw_date BETWEEN '2024-02-05' AND '2024-02-15'
            ORDER BY draw_date
        """)
        
        spring_2024_data = cursor.fetchall()
        for data in spring_2024_data:
            draw_date, natural_year, lunar_year, special_num, zodiac, wuxing = data
            marker = "🎊" if draw_date >= "2024-02-10" else "📅"
            print(f"  {marker} {draw_date}: 自然年{natural_year}/农历年{lunar_year} → {special_num}({zodiac}/{wuxing})")
        
        # 检查2025年春节前后的数据
        print("\n🔍 2025年春节(2025-01-29)前后数据:")
        cursor.execute("""
            SELECT draw_date, natural_year, lunar_year, special_number, 
                   special_zodiac, special_wuxing 
            FROM lottery_data 
            WHERE draw_date BETWEEN '2025-01-25' AND '2025-02-05'
            ORDER BY draw_date
        """)
        
        spring_2025_data = cursor.fetchall()
        for data in spring_2025_data:
            draw_date, natural_year, lunar_year, special_num, zodiac, wuxing = data
            marker = "🎊" if draw_date >= "2025-01-29" else "📅"
            print(f"  {marker} {draw_date}: 自然年{natural_year}/农历年{lunar_year} → {special_num}({zodiac}/{wuxing})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def check_prediction_modules():
    """检查预测模块是否正确处理年份映射"""
    print("\n🔮 预测模块年份处理检查")
    print("=" * 60)
    
    # 检查主要预测模块文件
    modules_to_check = [
        "src/independent_modules/zodiac_extended_module.py",
        "src/independent_modules/special_zodiac_module.py", 
        "src/independent_modules/ml_module.py",
        "src/independent_modules/traditional_module.py",
        "src/perfect_prediction_system.py",
        "historical_backtest.py",
        "enhanced_hit_rate_optimizer.py"
    ]
    
    for module_path in modules_to_check:
        try:
            with open(module_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            print(f"\n📄 检查模块: {module_path}")
            
            # 检查是否有农历年份处理
            has_lunar_year = "lunar_year" in content or "农历年" in content
            has_spring_festival = "spring_festival" in content or "春节" in content
            has_year_mapping = "year_mapping" in content or "年份映射" in content
            
            print(f"  农历年份处理: {'✅' if has_lunar_year else '❌'}")
            print(f"  春节日期处理: {'✅' if has_spring_festival else '❌'}")
            print(f"  年份映射处理: {'✅' if has_year_mapping else '❌'}")
            
            # 检查是否有日期转换函数
            has_date_conversion = any(keyword in content for keyword in [
                "get_lunar_year", "convert_date", "date_to_lunar", 
                "_get_zodiac_mapping", "_get_wuxing_mapping"
            ])
            
            print(f"  日期转换功能: {'✅' if has_date_conversion else '❌'}")
            
        except FileNotFoundError:
            print(f"❌ 文件不存在: {module_path}")
        except Exception as e:
            print(f"❌ 检查失败: {e}")

def test_date_mapping_function():
    """测试日期映射功能"""
    print("\n🧪 日期映射功能测试")
    print("=" * 60)
    
    # 测试关键日期的映射
    test_dates = [
        "2024-02-09",  # 2024年春节前一天 (应该是兔年)
        "2024-02-10",  # 2024年春节当天 (应该是龙年)
        "2024-02-11",  # 2024年春节后一天 (应该是龙年)
        "2025-01-28",  # 2025年春节前一天 (应该是龙年)
        "2025-01-29",  # 2025年春节当天 (应该是蛇年)
        "2025-01-30",  # 2025年春节后一天 (应该是蛇年)
    ]
    
    try:
        conn = sqlite3.connect("data/lottery.db")
        cursor = conn.cursor()
        
        print("📅 关键日期的农历年份映射:")
        for test_date in test_dates:
            # 查询该日期对应的农历年份
            cursor.execute("""
                SELECT lunar_year, zodiac_name 
                FROM lunar_year_mapping 
                WHERE ? BETWEEN year_start_date AND year_end_date
            """, (test_date,))
            
            result = cursor.fetchone()
            if result:
                lunar_year, zodiac = result
                print(f"  {test_date}: {lunar_year}年{zodiac}年")
            else:
                print(f"  {test_date}: ❌ 未找到对应的农历年份")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def check_backtest_consistency():
    """检查回测系统的一致性"""
    print("\n📊 回测系统一致性检查")
    print("=" * 60)
    
    # 检查回测相关文件
    backtest_files = [
        "historical_backtest.py",
        "enhanced_hit_rate_optimizer.py",
        "optimal_pattern_selector.py"
    ]
    
    for file_path in backtest_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📄 检查回测文件: {file_path}")
            
            # 检查是否正确处理历史数据的年份映射
            checks = {
                "历史数据查询": any(keyword in content for keyword in [
                    "lunar_year", "农历年", "year_mapping"
                ]),
                "日期范围处理": any(keyword in content for keyword in [
                    "date_range", "start_date", "end_date", "draw_date"
                ]),
                "生肖映射": any(keyword in content for keyword in [
                    "zodiac_mapping", "special_zodiac", "生肖映射"
                ]),
                "五行映射": any(keyword in content for keyword in [
                    "wuxing_mapping", "special_wuxing", "五行映射"
                ])
            }
            
            for check_name, result in checks.items():
                print(f"  {check_name}: {'✅' if result else '❌'}")
                
        except FileNotFoundError:
            print(f"❌ 文件不存在: {file_path}")
        except Exception as e:
            print(f"❌ 检查失败: {e}")

def generate_mapping_recommendations():
    """生成映射改进建议"""
    print("\n💡 映射系统改进建议")
    print("=" * 60)
    
    recommendations = [
        "1. 确保所有预测模块都使用农历年份进行生肖和五行映射",
        "2. 在回测时，根据开奖日期自动确定对应的农历年份",
        "3. 创建统一的日期转换函数，避免各模块重复实现",
        "4. 验证春节前后数据的映射切换是否正确",
        "5. 在增强回测中考虑跨年数据的特殊处理",
        "6. 建立映射数据的一致性检查机制",
        "7. 为预测结果添加年份标识，确保使用正确的映射"
    ]
    
    for recommendation in recommendations:
        print(f"  {recommendation}")
    
    print(f"\n🎯 关键要点:")
    print("  • 春节当天开始使用新年的生肖和五行映射")
    print("  • 回测历史数据时必须使用当时的正确映射")
    print("  • 预测未来时使用目标日期对应的农历年份映射")
    print("  • 所有模块必须保持映射的一致性")

def main():
    """主函数"""
    print("🎯 年份映射系统全面检查")
    print("=" * 80)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项检查
    check_spring_festival_mapping()
    check_cross_year_data()
    check_prediction_modules()
    test_date_mapping_function()
    check_backtest_consistency()
    generate_mapping_recommendations()
    
    print(f"\n🎉 检查完成!")
    print(f"\n📋 总结:")
    print("  ✅ 数据库中有完整的春节日期映射")
    print("  ✅ 跨年数据处理基本正确")
    print("  ⚠️ 部分预测模块可能需要改进年份处理")
    print("  ⚠️ 回测系统需要验证映射一致性")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
