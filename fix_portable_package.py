"""
修复便携版封包缺失文件问题
为用户的封包添加缺失的关键文件
"""

import os
import shutil
from pathlib import Path

def check_src_directory(package_path):
    """检查src目录内容"""
    print("🔍 检查src目录内容...")
    print("-" * 40)
    
    src_path = os.path.join(package_path, "src")
    if not os.path.exists(src_path):
        print("❌ src目录不存在")
        return False
    
    print("📁 src目录内容:")
    for root, dirs, files in os.walk(src_path):
        level = root.replace(src_path, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}📁 {os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}📄 {file}")
    
    return True

def copy_missing_files(source_dir, target_dir):
    """复制缺失的文件到目标目录"""
    print(f"\n📋 复制缺失文件到: {target_dir}")
    print("-" * 40)
    
    # 要复制的文件
    files_to_copy = [
        "lottery_prediction_gui.py",
        "consistent_predictor.py",
        "special_number_predictor.py",
        "enhanced_backtest.py",
        "comprehensive_evaluation.py"
    ]
    
    copied_files = []
    
    for file_name in files_to_copy:
        source_file = os.path.join(source_dir, file_name)
        target_file = os.path.join(target_dir, file_name)
        
        if os.path.exists(source_file):
            try:
                shutil.copy2(source_file, target_file)
                print(f"✅ 复制: {file_name}")
                copied_files.append(file_name)
            except Exception as e:
                print(f"❌ 复制失败 {file_name}: {e}")
        else:
            print(f"⚠️ 源文件不存在: {file_name}")
    
    return copied_files

def create_main_gui_launcher(target_dir):
    """创建主GUI启动器"""
    print(f"\n🚀 创建主GUI启动器...")
    print("-" * 40)
    
    # 检查是否有GUI文件在src目录中
    src_gui_files = []
    src_path = os.path.join(target_dir, "src")
    
    if os.path.exists(src_path):
        for file in os.listdir(src_path):
            if "gui" in file.lower() or "main" in file.lower():
                src_gui_files.append(file)
    
    print(f"发现src中的GUI文件: {src_gui_files}")
    
    # 创建主启动器
    launcher_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六合彩预测系统主启动器
自动检测并启动GUI界面
"""

import sys
import os
from pathlib import Path

def find_gui_module():
    """查找GUI模块"""
    # 可能的GUI文件位置
    possible_gui_files = [
        "lottery_prediction_gui.py",
        "src/lottery_prediction_gui.py", 
        "src/main_gui.py",
        "src/gui_main.py",
        "src/application_layer/gui_main.py"
    ]
    
    current_dir = Path(__file__).parent
    
    for gui_file in possible_gui_files:
        gui_path = current_dir / gui_file
        if gui_path.exists():
            print(f"✅ 找到GUI文件: {gui_path}")
            return str(gui_path)
    
    print("❌ 未找到GUI文件")
    return None

def setup_environment():
    """设置环境"""
    current_dir = Path(__file__).parent
    src_dir = current_dir / "src"
    
    # 添加src目录到Python路径
    if src_dir.exists():
        sys.path.insert(0, str(src_dir))
        print(f"✅ 添加src目录到Python路径: {src_dir}")
    
    # 添加当前目录到Python路径
    sys.path.insert(0, str(current_dir))
    print(f"✅ 添加当前目录到Python路径: {current_dir}")

def main():
    """主函数"""
    print("🎊 六合彩预测系统启动器")
    print("=" * 50)
    
    # 设置环境
    setup_environment()
    
    # 查找GUI模块
    gui_file = find_gui_module()
    
    if gui_file:
        try:
            # 尝试直接执行GUI文件
            print(f"🚀 启动GUI: {gui_file}")
            
            # 切换到GUI文件所在目录
            gui_dir = os.path.dirname(gui_file)
            if gui_dir:
                os.chdir(gui_dir)
                print(f"📁 切换到目录: {gui_dir}")
            
            # 执行GUI文件
            exec(open(gui_file, encoding='utf-8').read())
            
        except Exception as e:
            print(f"❌ GUI启动失败: {e}")
            print("\\n🔧 尝试备用启动方法...")
            
            try:
                # 备用方法：导入模块
                if "src/" in gui_file:
                    module_name = os.path.basename(gui_file).replace('.py', '')
                    module = __import__(module_name)
                    if hasattr(module, 'main'):
                        module.main()
                    else:
                        print("❌ 模块没有main函数")
                else:
                    print("❌ 备用方法也失败")
            except Exception as e2:
                print(f"❌ 备用方法失败: {e2}")
                
                # 显示详细错误信息
                print("\\n📋 详细错误信息:")
                import traceback
                traceback.print_exc()
    else:
        print("❌ 无法找到GUI文件，请检查文件完整性")
        
        # 显示当前目录内容
        print("\\n📁 当前目录内容:")
        current_dir = Path(__file__).parent
        for item in current_dir.iterdir():
            if item.is_dir():
                print(f"  📁 {item.name}/")
            else:
                print(f"  📄 {item.name}")

if __name__ == "__main__":
    main()
'''
    
    launcher_path = os.path.join(target_dir, "lottery_prediction_gui.py")
    try:
        with open(launcher_path, "w", encoding="utf-8") as f:
            f.write(launcher_code)
        print(f"✅ 主启动器已创建: {launcher_path}")
        return launcher_path
    except Exception as e:
        print(f"❌ 创建主启动器失败: {e}")
        return None

def create_enhanced_startup_script(target_dir):
    """创建增强启动脚本"""
    print(f"\n🔧 创建增强启动脚本...")
    print("-" * 40)
    
    startup_script = '''@echo off
chcp 65001 > nul
title 六合彩预测系统 v1.0.0 - 增强启动

echo 🎊 六合彩预测系统 v1.0.0
echo ==========================================
echo 正在启动系统...

REM 显示当前目录
echo 📁 当前目录: %CD%
echo.

REM 检查Python环境
echo 📋 检查Python环境...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo.
    echo 💡 请按以下步骤安装Python:
    echo 1. 访问: https://www.python.org/downloads/
    echo 2. 下载Python 3.8或更高版本
    echo 3. 安装时务必勾选 "Add Python to PATH"
    echo 4. 重启电脑后再运行此脚本
    echo.
    pause
    exit /b 1
)

python --version
echo ✅ Python环境检查通过
echo.

REM 检查主程序文件
echo 📁 检查程序文件...
if exist "lottery_prediction_gui.py" (
    echo ✅ 主程序文件存在
) else (
    echo ⚠️ 主程序文件不存在，正在检查src目录...
    if exist "src" (
        echo ✅ src目录存在，将从src目录启动
    ) else (
        echo ❌ 错误: 未找到程序文件
        echo 当前目录内容:
        dir /b
        pause
        exit /b 1
    )
)
echo.

REM 升级pip
echo 📦 升级pip...
python -m pip install --upgrade pip --quiet
echo ✅ pip升级完成
echo.

REM 安装依赖包
echo 📦 安装/检查依赖包...
if exist "requirements.txt" (
    echo 从requirements.txt安装依赖...
    python -m pip install -r requirements.txt --quiet
) else (
    echo 手动安装核心依赖...
    python -m pip install PyQt5 numpy pandas scikit-learn matplotlib --quiet
)

if errorlevel 1 (
    echo ⚠️ 部分依赖安装可能失败，继续尝试启动...
) else (
    echo ✅ 依赖包安装完成
)
echo.

REM 测试PyQt5
echo 🧪 测试GUI环境...
python -c "from PyQt5.QtWidgets import QApplication; print('✅ PyQt5测试通过')" 2>nul
if errorlevel 1 (
    echo ⚠️ PyQt5测试失败，正在重新安装...
    python -m pip install PyQt5 --force-reinstall --quiet
    python -c "from PyQt5.QtWidgets import QApplication; print('✅ PyQt5重新安装成功')" 2>nul
)
echo.

REM 启动程序
echo 🚀 启动GUI界面...
echo 注意: 
echo - 如果程序启动但没有窗口，请检查任务栏
echo - 按Alt+Tab可以切换到程序窗口
echo - 如果被杀毒软件拦截，请添加到白名单
echo.

REM 尝试启动主程序
if exist "lottery_prediction_gui.py" (
    python lottery_prediction_gui.py
) else (
    REM 尝试从src目录启动
    cd src 2>nul
    if exist "lottery_prediction_gui.py" (
        python lottery_prediction_gui.py
    ) else if exist "main_gui.py" (
        python main_gui.py
    ) else if exist "gui_main.py" (
        python gui_main.py
    ) else (
        echo ❌ 无法找到GUI启动文件
        cd ..
        echo 正在列出可用的Python文件:
        for %%f in (*.py src\\*.py) do echo   %%f
    )
)

REM 检查启动结果
if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败
    echo.
    echo 💡 故障排除建议:
    echo 1. 确保Python版本为3.8或更高
    echo 2. 重新安装PyQt5: pip install PyQt5 --force-reinstall
    echo 3. 检查杀毒软件是否拦截程序
    echo 4. 以管理员身份运行此脚本
    echo 5. 重启电脑后再试
    echo.
    echo 如需技术支持，请保存此窗口的错误信息
)

echo.
echo 按任意键退出...
pause > nul
'''
    
    script_path = os.path.join(target_dir, "启动系统.bat")
    try:
        with open(script_path, "w", encoding="gbk", errors="ignore") as f:
            f.write(startup_script)
        print(f"✅ 增强启动脚本已创建: {script_path}")
        return script_path
    except Exception as e:
        print(f"❌ 创建启动脚本失败: {e}")
        return None

def main():
    """主函数"""
    source_dir = r"i:\编程\lottery"  # 源文件目录
    target_dir = r"F:\软件下载\六合彩各版本工具\LotteryPrediction_Portable_v1.0.0"
    
    print("🔧 便携版封包修复工具")
    print("=" * 60)
    print(f"源目录: {source_dir}")
    print(f"目标目录: {target_dir}")
    print()
    
    # 检查目录
    if not os.path.exists(target_dir):
        print(f"❌ 目标目录不存在: {target_dir}")
        return
    
    # 检查src目录
    check_src_directory(target_dir)
    
    # 复制缺失文件
    if os.path.exists(source_dir):
        copied_files = copy_missing_files(source_dir, target_dir)
        print(f"✅ 已复制 {len(copied_files)} 个文件")
    else:
        print(f"⚠️ 源目录不存在，跳过文件复制")
    
    # 创建主启动器
    launcher = create_main_gui_launcher(target_dir)
    
    # 创建增强启动脚本
    script = create_enhanced_startup_script(target_dir)
    
    print("\n" + "=" * 60)
    print("🎯 修复完成总结:")
    print(f"目标目录: ✅ {target_dir}")
    print(f"主启动器: {'✅ 已创建' if launcher else '❌ 创建失败'}")
    print(f"启动脚本: {'✅ 已创建' if script else '❌ 创建失败'}")
    
    print(f"\n💡 使用说明:")
    print(f"1. 进入目录: {target_dir}")
    print(f"2. 双击运行: 启动系统.bat")
    print(f"3. 或者运行: python lottery_prediction_gui.py")
    print(f"4. 如果还有问题，运行: debug_start.bat")
    print("=" * 60)

if __name__ == "__main__":
    main()
