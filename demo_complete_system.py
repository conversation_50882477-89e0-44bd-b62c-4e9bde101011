"""
六合彩预测系统 - 完整演示脚本
展示从数据导入到最终预测的完整流程
"""
import sys
sys.path.insert(0, 'src')

from datetime import date, datetime
from src.data_layer.database.models import create_database_engine, get_session
from src.data_layer.database.init_db import initialize_database
from src.data_layer.year_mapping.lunar_year_manager import LunarYearManager
from src.data_layer.year_mapping.number_attribute_mapper import NumberAttributeMapper
from src.algorithm_layer.model_fusion.cross_validation_fusion import CrossValidationFusion
from loguru import logger

def main_demo():
    """主演示函数"""
    print("="*80)
    print("🎯 澳门六合彩预测系统 - 完整演示")
    print("="*80)
    print("核心思路：")
    print("• 预测澳门六合彩特码")
    print("• 初选范围：16-24个号码")
    print("• 交叉验证后推荐：12-16个号码")
    print("• 生肖维度分析：预测4个最高得分生肖")
    print("="*80)
    
    # 1. 初始化系统
    print("\n🔧 步骤1：初始化系统...")
    if not initialize_database():
        print("❌ 数据库初始化失败")
        return
    print("✅ 数据库初始化完成")
    
    # 2. 创建连接和管理器
    print("\n🔗 步骤2：创建系统连接...")
    engine = create_database_engine()
    session = get_session(engine)
    lunar_manager = LunarYearManager(session)
    attr_mapper = NumberAttributeMapper()
    fusion_system = CrossValidationFusion(session, lunar_manager, attr_mapper)
    print("✅ 系统连接建立完成")
    
    # 3. 执行综合预测
    print("\n🎯 步骤3：执行综合预测分析...")
    print("   正在运行生肖维度扩展分析...")
    print("   正在运行传统统计分析...")
    print("   正在运行机器学习预测...")
    print("   正在进行交叉验证与融合...")
    
    comprehensive_result = fusion_system.comprehensive_prediction(
        recent_periods=50,
        target_date=date(2024, 2, 15)
    )
    
    # 4. 展示预测结果
    print("\n" + "="*80)
    print("🎯 预测结果")
    print("="*80)
    
    final_numbers = comprehensive_result['final_recommended_numbers']
    print(f"📊 系统状态：")
    print(f"   • 最终推荐号码数量：{len(final_numbers)}个")
    print(f"   • 整体置信度：{comprehensive_result['confidence_level']:.1%}")
    print(f"   • 预测时间：{comprehensive_result['prediction_time']}")
    
    print(f"\n🎯 最终推荐号码：")
    for i, number in enumerate(final_numbers, 1):
        score = comprehensive_result['number_scores'][number]
        print(f"   {i:2d}. {number:2d} (得分: {score:.3f})")
    
    print(f"\n📋 号码列表：{final_numbers}")
    
    # 5. 各模块详细结果
    analysis_results = comprehensive_result['analysis_results']
    print(f"\n📊 各模块分析详情：")
    
    if analysis_results.get('zodiac_analysis'):
        zodiac_result = analysis_results['zodiac_analysis']
        print(f"   🐲 生肖维度分析：")
        print(f"      • 推荐生肖：{zodiac_result['predicted_zodiac']}")
        print(f"      • 置信度：{zodiac_result['confidence_level']:.1%}")
        print(f"      • 分析期数：{zodiac_result['data_periods']}")
    
    if analysis_results.get('traditional_analysis'):
        traditional_result = analysis_results['traditional_analysis']
        print(f"   📈 传统统计分析：")
        print(f"      • 推荐号码数：{len(traditional_result['recommended_numbers'])}")
        print(f"      • 置信度：{traditional_result['confidence_level']:.1%}")
        print(f"      • 分析期数：{traditional_result['data_periods']}")
    
    if analysis_results.get('ml_prediction'):
        ml_result = analysis_results['ml_prediction']
        print(f"   🤖 机器学习预测：")
        print(f"      • 推荐号码数：{len(ml_result['predicted_numbers'])}")
        print(f"      • 置信度：{ml_result['confidence_level']:.1%}")
        print(f"      • 训练样本数：{ml_result['training_data_size']}")
        print(f"      • 使用模型：{', '.join(ml_result['models_used'])}")
    
    # 6. 交叉验证结果
    validation_results = comprehensive_result['validation_results']
    consensus = validation_results['consensus_analysis']
    
    print(f"\n🔍 交叉验证结果：")
    print(f"   • 一致推荐号码：{len(consensus['common_numbers'])}个")
    print(f"   • 号码重叠率：{validation_results['consistency_metrics']['number_overlap_rate']:.1%}")
    print(f"   • 整体一致性：{validation_results['consistency_metrics']['overall_consistency']:.1%}")
    
    # 7. 系统配置
    print(f"\n⚖️ 系统配置：")
    print(f"   • 模块权重：")
    for module, weight in comprehensive_result['module_weights'].items():
        print(f"     - {module}: {weight:.1%}")
    
    # 8. 生成完整报告
    print(f"\n📄 生成完整预测报告...")
    report = fusion_system.get_comprehensive_report(comprehensive_result)
    
    # 保存报告到文件
    with open("prediction_report.txt", "w", encoding="utf-8") as f:
        f.write(report)
    print(f"✅ 预测报告已保存到 prediction_report.txt")
    
    # 9. 总结
    print("\n" + "="*80)
    print("🎊 预测完成总结")
    print("="*80)
    print(f"✅ 成功完成澳门六合彩特码预测")
    print(f"✅ 生肖维度分析：预测了4个最高得分生肖")
    print(f"✅ 传统统计分析：基于多种传统方法")
    print(f"✅ 机器学习预测：使用5种ML算法")
    print(f"✅ 交叉验证融合：智能整合所有结果")
    print(f"✅ 最终推荐：{len(final_numbers)}个号码，置信度{comprehensive_result['confidence_level']:.1%}")
    print(f"")
    print(f"🎯 推荐号码：{final_numbers}")
    print(f"")
    print(f"🍀 祝您好运！")
    print("="*80)
    
    session.close()

if __name__ == "__main__":
    main_demo()
