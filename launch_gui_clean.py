"""
澳门六合彩预测系统 GUI 清洁启动脚本
解决PyQt5弃用警告问题
"""
import sys
import os
import warnings

def suppress_warnings():
    """抑制PyQt5相关的弃用警告"""
    # 抑制所有PyQt5/sip相关的弃用警告
    warnings.filterwarnings("ignore", category=DeprecationWarning)
    warnings.filterwarnings("ignore", message=".*sipPyTypeDict.*")
    warnings.filterwarnings("ignore", module=".*sip.*")
    
    # 抑制其他可能的Qt相关警告
    warnings.filterwarnings("ignore", message=".*Qt.*")
    warnings.filterwarnings("ignore", message=".*QApplication.*")

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 6):
        print("❌ 需要Python 3.6或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def check_pyqt5():
    """检查PyQt5是否可用"""
    try:
        import PyQt5
        print(f"✅ PyQt5版本: {PyQt5.Qt.PYQT_VERSION_STR}")
        return True
    except ImportError:
        print("❌ PyQt5未安装")
        print("请运行: pip install PyQt5")
        return False

def check_dependencies():
    """检查其他依赖"""
    required_packages = [
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
    ]
    
    missing = []
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name}")
        except ImportError:
            print(f"⚠️ {package_name} - 可选依赖，建议安装")
            missing.append(package_name)
    
    return missing

def launch_gui():
    """启动GUI应用程序"""
    try:
        print("🚀 启动GUI应用程序...")
        
        # 设置环境变量来抑制Qt警告
        os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'
        
        # 导入并启动GUI
        from lottery_prediction_gui import main
        main()
        
    except ImportError as e:
        print(f"❌ 导入GUI模块失败: {e}")
        print("请确保 lottery_prediction_gui.py 文件存在")
        return False
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🎯 澳门六合彩预测系统 - 清洁启动器")
    print("=" * 60)
    
    # 首先抑制警告
    suppress_warnings()
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    # 检查PyQt5
    if not check_pyqt5():
        input("按回车键退出...")
        return
    
    # 检查其他依赖
    print("\n🔍 检查依赖库...")
    missing = check_dependencies()
    
    if missing:
        print(f"\n⚠️ 缺少可选依赖: {', '.join(missing)}")
        print("这些依赖不是必需的，但建议安装以获得完整功能")
        response = input("是否继续启动? (y/n): ")
        if response.lower() != 'y':
            return
    
    print("\n✅ 所有检查通过")
    
    # 启动GUI
    if not launch_gui():
        input("按回车键退出...")

if __name__ == "__main__":
    main()
