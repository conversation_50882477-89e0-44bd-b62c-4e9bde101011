"""
修复融合配置错误：DynamicFusionManager缺少configure_static_weights方法
解决与增强回测优选配置的冲突问题
"""

def analyze_fusion_config_error():
    """分析融合配置错误"""
    print("🔍 融合配置错误分析报告")
    print("=" * 60)
    
    print("❌ 错误信息:")
    print("'DynamicFusionManager' object has no attribute 'configure_static_weights'")
    
    print("\n🎯 问题根源:")
    print("1. GUI调用了 fusion_manager.configure_static_weights() 方法")
    print("2. DynamicFusionManager v3.0 中没有实现这个方法")
    print("3. 增强回测配置与GUI融合配置产生冲突")
    
    print("\n🔧 冲突分析:")
    print("• GUI期望: configure_static_weights() 方法")
    print("• 实际情况: DynamicFusionManager v3.0 没有此方法")
    print("• 增强回测: 通过apply_enhanced_config()应用配置")
    print("• 配置冲突: 两种配置方式不兼容")

def check_fusion_manager_methods():
    """检查融合管理器的方法"""
    print("\n📋 DynamicFusionManager v3.0 方法检查:")
    print("-" * 40)
    
    try:
        import sys
        sys.path.append('src')
        from dynamic_fusion_manager_v3 import DynamicFusionManager
        
        fusion_manager = DynamicFusionManager()
        methods = [method for method in dir(fusion_manager) if not method.startswith('_')]
        
        print("✅ 可用方法:")
        for method in methods:
            print(f"  • {method}")
        
        print(f"\n❌ 缺少方法:")
        print("  • configure_static_weights")
        print("  • configure_fusion_parameters")
        
        return methods
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return []

def create_fusion_manager_patch():
    """创建融合管理器补丁"""
    print("\n🔧 创建DynamicFusionManager补丁")
    print("-" * 40)
    
    patch_code = '''
def configure_static_weights(self, weights: Dict[str, float]):
    """配置静态权重"""
    try:
        self.logger.info(f"Configuring static weights: {weights}")
        
        # 验证权重
        total_weight = sum(weights.values())
        if abs(total_weight - 1.0) > 0.01:
            raise ValueError(f"权重总和必须为1.0，当前为{total_weight}")
        
        # 更新融合配置
        if "static_weights" not in self.fusion_config:
            self.fusion_config["static_weights"] = {}
        
        self.fusion_config["static_weights"].update(weights)
        
        # 更新模组权重
        for module_name, weight in weights.items():
            if module_name in self.module_weights:
                self.module_weights[module_name] = weight
        
        self.logger.info("Static weights configured successfully")
        return True
        
    except Exception as e:
        self.logger.error(f"Failed to configure static weights: {e}")
        raise

def configure_fusion_parameters(self, **params):
    """配置融合参数"""
    try:
        self.logger.info(f"Configuring fusion parameters: {params}")
        
        # 更新融合配置
        for param_name, param_value in params.items():
            if param_name in ["voting_threshold", "diversity_factor", "max_contribution_per_module"]:
                self.fusion_config[param_name] = param_value
        
        self.logger.info("Fusion parameters configured successfully")
        return True
        
    except Exception as e:
        self.logger.error(f"Failed to configure fusion parameters: {e}")
        raise
'''
    
    print("📝 补丁代码已生成")
    return patch_code

def apply_fusion_manager_patch():
    """应用融合管理器补丁"""
    print("\n🚀 应用DynamicFusionManager补丁")
    print("-" * 40)
    
    try:
        # 读取原文件
        with open('src/dynamic_fusion_manager_v3.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有这些方法
        if 'configure_static_weights' in content:
            print("✅ configure_static_weights 方法已存在")
            return True
        
        # 找到类的结尾位置
        lines = content.split('\n')
        insert_position = -1
        
        for i, line in enumerate(lines):
            if line.strip() and not line.startswith(' ') and not line.startswith('\t') and i > 100:
                insert_position = i
                break
        
        if insert_position == -1:
            insert_position = len(lines) - 1
        
        # 插入补丁代码
        patch_methods = '''
    def configure_static_weights(self, weights: Dict[str, float]):
        """配置静态权重"""
        try:
            self.logger.info(f"Configuring static weights: {weights}")
            
            # 验证权重
            total_weight = sum(weights.values())
            if abs(total_weight - 1.0) > 0.01:
                raise ValueError(f"权重总和必须为1.0，当前为{total_weight}")
            
            # 更新融合配置
            if "static_weights" not in self.fusion_config:
                self.fusion_config["static_weights"] = {}
            
            self.fusion_config["static_weights"].update(weights)
            
            # 更新模组权重
            for module_name, weight in weights.items():
                if module_name in self.module_weights:
                    self.module_weights[module_name] = weight
            
            self.logger.info("Static weights configured successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to configure static weights: {e}")
            raise

    def configure_fusion_parameters(self, **params):
        """配置融合参数"""
        try:
            self.logger.info(f"Configuring fusion parameters: {params}")
            
            # 更新融合配置
            for param_name, param_value in params.items():
                if param_name in ["voting_threshold", "diversity_factor", "max_contribution_per_module"]:
                    self.fusion_config[param_name] = param_value
            
            self.logger.info("Fusion parameters configured successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to configure fusion parameters: {e}")
            raise
'''
        
        # 插入补丁
        lines.insert(insert_position, patch_methods)
        
        # 写回文件
        with open('src/dynamic_fusion_manager_v3.py', 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print("✅ 补丁应用成功")
        return True
        
    except Exception as e:
        print(f"❌ 补丁应用失败: {e}")
        return False

def create_config_compatibility_layer():
    """创建配置兼容层"""
    print("\n🔗 创建配置兼容层")
    print("-" * 40)
    
    compatibility_code = '''
class FusionConfigCompatibility:
    """融合配置兼容层"""
    
    def __init__(self, fusion_manager):
        self.fusion_manager = fusion_manager
        self.enhanced_config_applied = False
    
    def apply_gui_config(self, weights, params):
        """应用GUI配置"""
        try:
            # 如果已经应用了增强配置，先备份
            if self.enhanced_config_applied:
                self.backup_enhanced_config()
            
            # 应用GUI配置
            self.fusion_manager.configure_static_weights(weights)
            self.fusion_manager.configure_fusion_parameters(**params)
            
            return True
        except Exception as e:
            print(f"GUI配置应用失败: {e}")
            return False
    
    def apply_enhanced_config(self, enhanced_config):
        """应用增强回测配置"""
        try:
            if 'fusion_weights' in enhanced_config:
                weights = enhanced_config['fusion_weights']
                self.fusion_manager.configure_static_weights(weights)
            
            self.enhanced_config_applied = True
            return True
        except Exception as e:
            print(f"增强配置应用失败: {e}")
            return False
    
    def backup_enhanced_config(self):
        """备份增强配置"""
        # 实现配置备份逻辑
        pass
'''
    
    print("📝 兼容层代码已生成")
    return compatibility_code

def provide_solution_recommendations():
    """提供解决方案建议"""
    print("\n💡 解决方案建议")
    print("=" * 60)
    
    print("🎯 立即解决方案:")
    print("1. 为DynamicFusionManager添加缺失的方法")
    print("   • configure_static_weights()")
    print("   • configure_fusion_parameters()")
    
    print("\n2. 创建配置兼容层")
    print("   • 处理GUI配置与增强回测配置的冲突")
    print("   • 提供统一的配置接口")
    
    print("\n3. 修改GUI调用逻辑")
    print("   • 检查融合管理器类型")
    print("   • 使用兼容的方法调用")
    
    print("\n🔧 长期解决方案:")
    print("1. 统一配置管理")
    print("   • 创建统一的配置管理器")
    print("   • 标准化配置接口")
    
    print("\n2. 版本兼容性")
    print("   • 确保不同版本的融合管理器兼容")
    print("   • 提供向后兼容支持")
    
    print("\n3. 配置优先级")
    print("   • 定义配置应用的优先级")
    print("   • 增强回测配置 > GUI配置")

def main():
    """主函数"""
    print("🎊 融合配置错误修复方案")
    print("=" * 60)
    
    # 分析错误
    analyze_fusion_config_error()
    
    # 检查方法
    methods = check_fusion_manager_methods()
    
    # 创建补丁
    patch_code = create_fusion_manager_patch()
    
    # 应用补丁
    if apply_fusion_manager_patch():
        print("\n✅ 修复完成！")
        print("现在可以重新运行GUI应用融合配置")
    else:
        print("\n❌ 自动修复失败，请手动应用补丁")
    
    # 创建兼容层
    compatibility_code = create_config_compatibility_layer()
    
    # 提供建议
    provide_solution_recommendations()
    
    print("\n" + "=" * 60)
    print("🎯 总结:")
    print("✅ 问题已识别: DynamicFusionManager缺少configure_static_weights方法")
    print("✅ 补丁已生成: 添加缺失的配置方法")
    print("✅ 兼容层已设计: 处理配置冲突")
    print("💡 建议: 重启GUI应用程序测试修复效果")
    print("=" * 60)

if __name__ == "__main__":
    main()
