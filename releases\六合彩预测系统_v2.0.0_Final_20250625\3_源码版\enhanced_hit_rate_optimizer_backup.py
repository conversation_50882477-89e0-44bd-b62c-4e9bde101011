#!/usr/bin/env python3
"""
命中率优化器 - 专注提升六合彩特码预测命中率
"""

import numpy as np
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import sqlite3
import json

class HitRateOptimizer:
    """命中率优化器"""

    def __init__(self, db_path="data/lottery.db"):
        self.db_path = db_path

        # 核心优化策略
        self.optimization_strategies = {
            "hot_cold_balance": self.hot_cold_balance_strategy,
            "pattern_recognition": self.pattern_recognition_strategy,
            "frequency_clustering": self.frequency_clustering_strategy,
            "trend_following": self.trend_following_strategy,
            "zodiac_correlation": self.zodiac_correlation_strategy
        }

        # 历史命中率统计
        self.hit_rate_history = {}

    def optimize_number_selection(self, historical_data, target_date, base_predictions):
        """优化号码选择以提高命中率"""
        print("🎯 开始命中率优化...")

        try:
            # 分析历史命中模式
            patterns = self._analyze_hit_patterns(historical_data)

            # 应用优化策略
            optimized_numbers = []
            strategy_results = {}

            for strategy_name, strategy_func in self.optimization_strategies.items():
                try:
                    strategy_result = strategy_func(historical_data, base_predictions, patterns)
                    strategy_results[strategy_name] = strategy_result
                    optimized_numbers.extend(strategy_result.get("numbers", []))
                except Exception as e:
                    print(f"  策略 {strategy_name} 失败: {e}")
                    strategy_results[strategy_name] = {"numbers": [], "confidence": 0}

            # 融合策略结果
            final_numbers = self._fuse_strategy_results(strategy_results, base_predictions)

            # 计算置信度
            confidence = self._calculate_confidence(strategy_results, final_numbers)

            result = {
                "optimized_numbers": final_numbers,
                "confidence": confidence,
                "strategy_results": strategy_results,
                "optimization_applied": True
            }

            print(f"✅ 命中率优化完成: {len(final_numbers)}个号码, 置信度{confidence:.1%}")

            return result

        except Exception as e:
            print(f"❌ 命中率优化失败: {e}")
            # 返回基础预测作为备用
            return {
                "optimized_numbers": base_predictions[:16],
                "confidence": 0.5,
                "strategy_results": {},
                "optimization_applied": False,
                "error": str(e)
            }

    def _analyze_hit_patterns(self, historical_data):
        """分析历史命中模式"""
        patterns = {
            "hot_numbers": self._find_hot_numbers(historical_data),
            "cold_numbers": self._find_cold_numbers(historical_data),
            "consecutive_patterns": self._find_consecutive_patterns(historical_data),
            "gap_patterns": self._find_gap_patterns(historical_data),
            "zodiac_patterns": self._find_zodiac_patterns(historical_data)
        }
        return patterns

    def _find_hot_numbers(self, historical_data, window=30):
        """找出热号"""
        from collections import Counter
        freq = Counter()
        for record in historical_data[:window]:
            special = record.get('special_number', 0)
            if special > 0:
                freq[special] += 1
        return [num for num, count in freq.most_common()]

    def _find_cold_numbers(self, historical_data, window=50):
        """找出冷号"""
        all_numbers = set(range(1, 50))
        appeared = set()

        for record in historical_data[:window]:
            special = record.get('special_number', 0)
            if special > 0:
                appeared.add(special)

        cold_numbers = list(all_numbers - appeared)

        # 如果冷号不足，选择出现频率最低的
        if len(cold_numbers) < 10:
            from collections import Counter
            freq = Counter()
            for record in historical_data[:window]:
                special = record.get('special_number', 0)
                if special > 0:
                    freq[special] += 1

            # 补充低频号码
            low_freq = [num for num, count in freq.most_common()[-20:]]
            cold_numbers.extend(low_freq)

        return cold_numbers

    def _find_consecutive_patterns(self, historical_data):
        """找出连号模式"""
        consecutive_pairs = []
        for record in historical_data[:20]:
            special = record.get('special_number', 0)
            if special > 0:
                # 检查前后连号
                if special > 1:
                    consecutive_pairs.append(special - 1)
                if special < 49:
                    consecutive_pairs.append(special + 1)
        return list(set(consecutive_pairs))

    def _find_gap_patterns(self, historical_data):
        """找出间隔模式"""
        gap_candidates = []
        for record in historical_data[:15]:
            special = record.get('special_number', 0)
            if special > 0:
                # 常见间隔
                for gap in [3, 5, 7, 10]:
                    if special + gap <= 49:
                        gap_candidates.append(special + gap)
                    if special - gap >= 1:
                        gap_candidates.append(special - gap)
        return list(set(gap_candidates))

    def _find_zodiac_patterns(self, historical_data):
        """找出生肖模式"""
        # 简化实现
        return {}

    def _hot_cold_balance_strategy(self, historical_data, base_predictions, patterns):
        """热冷平衡策略"""
        hot_numbers = patterns.get("hot_numbers", [])[:8]
        cold_numbers = patterns.get("cold_numbers", [])[:8]

        balanced_numbers = hot_numbers + cold_numbers
        return {
            "numbers": balanced_numbers[:16],
            "confidence": 0.7,
            "method": "hot_cold_balance"
        }

    def _pattern_recognition_strategy(self, historical_data, base_predictions, patterns):
        """模式识别策略"""
        consecutive = patterns.get("consecutive_patterns", [])[:8]
        gaps = patterns.get("gap_patterns", [])[:8]

        pattern_numbers = consecutive + gaps
        return {
            "numbers": pattern_numbers[:16],
            "confidence": 0.65,
            "method": "pattern_recognition"
        }

    def _frequency_clustering_strategy(self, historical_data, base_predictions, patterns):
        """频率聚类策略"""
        from collections import Counter

        # 分析频率分布
        freq = Counter()
        for record in historical_data[:30]:
            special = record.get('special_number', 0)
            if special > 0:
                freq[special] += 1

        # 选择中等频率的号码
        sorted_freq = sorted(freq.items(), key=lambda x: x[1])
        mid_range = len(sorted_freq) // 3
        cluster_numbers = [num for num, count in sorted_freq[mid_range:mid_range*2]]

        return {
            "numbers": cluster_numbers[:16],
            "confidence": 0.6,
            "method": "frequency_clustering"
        }

    def _trend_following_strategy(self, historical_data, base_predictions, patterns):
        """趋势跟踪策略"""
        # 分析短期趋势
        recent_numbers = []
        for record in historical_data[:5]:
            special = record.get('special_number', 0)
            if special > 0:
                recent_numbers.append(special)

        # 基于趋势预测
        trend_numbers = []
        for num in recent_numbers:
            # 趋势延续
            if num + 1 <= 49:
                trend_numbers.append(num + 1)
            if num - 1 >= 1:
                trend_numbers.append(num - 1)

        # 补充基础预测
        trend_numbers.extend(base_predictions[:16-len(trend_numbers)])

        return {
            "numbers": trend_numbers[:16],
            "confidence": 0.55,
            "method": "trend_following"
        }

    def _zodiac_correlation_strategy(self, historical_data, base_predictions, patterns):
        """生肖关联策略"""
        # 简化实现，使用基础预测
        return {
            "numbers": base_predictions[:16],
            "confidence": 0.5,
            "method": "zodiac_correlation"
        }

    def _fuse_strategy_results(self, strategy_results, base_predictions):
        """融合策略结果"""
        from collections import Counter

        # 统计号码投票
        number_votes = Counter()

        for strategy_name, result in strategy_results.items():
            numbers = result.get("numbers", [])
            confidence = result.get("confidence", 0)

            # 根据置信度加权投票
            weight = int(confidence * 10)
            for num in numbers:
                if 1 <= num <= 49:
                    number_votes[num] += weight

        # 选择得票最高的16个号码
        top_numbers = [num for num, votes in number_votes.most_common(16)]

        # 如果不足16个，用基础预测补充
        if len(top_numbers) < 16:
            for num in base_predictions:
                if num not in top_numbers:
                    top_numbers.append(num)
                    if len(top_numbers) >= 16:
                        break

        return top_numbers[:16]

    def _calculate_confidence(self, strategy_results, final_numbers):
        """计算最终置信度"""
        if not strategy_results:
            return 0.5

        # 基于策略一致性计算置信度
        total_confidence = 0
        strategy_count = 0

        for strategy_name, result in strategy_results.items():
            strategy_confidence = result.get("confidence", 0)
            total_confidence += strategy_confidence
            strategy_count += 1

        avg_confidence = total_confidence / strategy_count if strategy_count > 0 else 0.5

        # 基于号码重叠度调整置信度
        overlap_bonus = min(0.2, len(final_numbers) / 16 * 0.2)

        final_confidence = min(0.95, avg_confidence + overlap_bonus)

        return final_confidence

class EnhancedHitRateOptimizer:
    """增强命中率优化器 - 支持回测功能"""

    def __init__(self, db_path="data/lottery.db"):
        self.db_path = db_path

        # 回测配置
        self.backtest_config = {
            "default_iterations": 10,
            "min_data_points": 5,  # 降低最小数据点要求
            "confidence_threshold": 0.6,
            "hit_rate_threshold": 0.3
        }

        # 核心优化策略
        self.optimization_strategies = {
            "hot_cold_balance": self._hot_cold_balance_strategy,
            "pattern_recognition": self._pattern_recognition_strategy,
            "frequency_clustering": self._frequency_clustering_strategy,
            "trend_following": self._trend_following_strategy,
            "zodiac_correlation": self._zodiac_correlation_strategy
        }

        # 历史命中率统计
        self.hit_rate_history = {}

        print(f"✅ 增强命中率优化器初始化完成")

    def run_enhanced_backtest(self, start_date: str, end_date: str, iterations: int = 10):
        """运行增强回测"""
        print(f"🔧 开始增强回测: {start_date} 到 {end_date}, {iterations}次迭代")

        try:
            # 获取回测期间的数据
            backtest_data = self._get_backtest_data(start_date, end_date)

            if len(backtest_data) < self.backtest_config["min_data_points"]:
                print(f"⚠️ 数据不足，需要至少{self.backtest_config['min_data_points']}条记录")
                return None

            iteration_results = []
            best_hit_rate = 0
            best_config = {}

            for i in range(iterations):
                print(f"  迭代 {i+1}/{iterations}")

                # 生成随机配置
                config = self._generate_random_config(i)

                # 执行回测
                hit_rate = self._run_single_backtest(backtest_data, config)

                iteration_result = {
                    "iteration": i + 1,
                    "config": config,
                    "hit_rate": hit_rate
                }
                iteration_results.append(iteration_result)

                # 更新最佳结果
                if hit_rate > best_hit_rate:
                    best_hit_rate = hit_rate
                    best_config = config.copy()

                print(f"    命中率: {hit_rate:.1%}")

            result = {
                "best_hit_rate": best_hit_rate,
                "best_config": best_config,
                "iteration_results": iteration_results,
                "backtest_period": {"start": start_date, "end": end_date},
                "total_iterations": iterations
            }

            print(f"✅ 增强回测完成，最佳命中率: {best_hit_rate:.1%}")

            return result

        except Exception as e:
            print(f"❌ 增强回测失败: {e}")
            return None

    def _get_backtest_data(self, start_date: str, end_date: str):
        """获取回测数据"""
        import sqlite3

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        query = """
        SELECT draw_date, special_number, period_number
        FROM lottery_results
        WHERE draw_date >= ? AND draw_date <= ?
        ORDER BY draw_date DESC
        """

        cursor.execute(query, (start_date, end_date))
        rows = cursor.fetchall()

        data = []
        for row in rows:
            data.append({
                "draw_date": row[0],
                "special_number": row[1],
                "period_number": row[2]
            })

        conn.close()
        return data

    def _generate_random_config(self, iteration: int):
        """生成随机配置"""
        import random

        # 设置确定性种子
        random.seed(42 + iteration)

        config = {
            "fusion_weights": {
                "hot_cold_balance": random.uniform(0.1, 0.4),
                "pattern_recognition": random.uniform(0.1, 0.4),
                "frequency_clustering": random.uniform(0.1, 0.4),
                "trend_following": random.uniform(0.1, 0.3),
                "zodiac_correlation": random.uniform(0.1, 0.3)
            },
            "selection_strategy": random.choice(["balanced", "aggressive", "conservative"]),
            "confidence_threshold": random.uniform(0.6, 0.9),
            "window_size": random.choice([10, 20, 30, 50])
        }

        # 归一化权重
        total_weight = sum(config["fusion_weights"].values())
        for key in config["fusion_weights"]:
            config["fusion_weights"][key] /= total_weight

        return config

    def _run_single_backtest(self, backtest_data, config):
        """运行单次回测"""
        hits = 0
        total_predictions = 0

        # 使用滑动窗口进行回测
        window_size = min(config.get("window_size", 10), len(backtest_data) - 1)

        if window_size <= 0:
            return 0.0

        for i in range(1, min(len(backtest_data), window_size + 1)):
            # 获取目标数据
            target_record = backtest_data[i - 1]
            actual_number = target_record["special_number"]
            target_date = target_record["draw_date"]

            # 获取历史数据
            historical_data = backtest_data[i:]

            if len(historical_data) < 5:  # 至少需要5条历史数据
                continue

            # 简化的预测逻辑
            try:
                # 使用简单的频率分析进行预测
                predicted_numbers = self._simple_frequency_prediction(historical_data)

                # 检查命中
                if actual_number in predicted_numbers:
                    hits += 1

                total_predictions += 1

            except Exception as e:
                print(f"    预测失败: {e}")
                continue

        # 计算命中率
        hit_rate = hits / total_predictions if total_predictions > 0 else 0
        return hit_rate

    def _simple_frequency_prediction(self, historical_data):
        """简单的频率预测"""
        from collections import defaultdict

        frequency = defaultdict(int)

        for record in historical_data[-20:]:  # 使用最近20期数据
            special = record.get("special_number", 0)
            if 1 <= special <= 49:
                frequency[special] += 1

        # 选择频率最高的16个号码
        sorted_numbers = sorted(frequency.items(), key=lambda x: x[1], reverse=True)
        predicted_numbers = [num for num, freq in sorted_numbers[:16]]

        # 如果不足16个，补充其他号码
        if len(predicted_numbers) < 16:
            all_numbers = set(range(1, 50))
            remaining = list(all_numbers - set(predicted_numbers))
            predicted_numbers.extend(remaining[:16-len(predicted_numbers)])

        return predicted_numbers[:16]

    def _analyze_hit_patterns(self, historical_data):
        """分析历史命中模式"""
        patterns = {
            "hot_numbers": self._find_hot_numbers(historical_data),
            "cold_numbers": self._find_cold_numbers(historical_data),
            "consecutive_patterns": self._find_consecutive_patterns(historical_data),
            "gap_patterns": self._find_gap_patterns(historical_data),
            "zodiac_patterns": self._find_zodiac_patterns(historical_data)
        }
        return patterns

    def _find_hot_numbers(self, historical_data, window=30):
        """找出热号"""
        from collections import Counter
        freq = Counter()
        for record in historical_data[:window]:
            special = record.get('special_number', 0)
            if special > 0:
                freq[special] += 1
        return [num for num, count in freq.most_common()]

    def _find_cold_numbers(self, historical_data, window=50):
        """找出冷号"""
        all_numbers = set(range(1, 50))
        appeared = set()

        for record in historical_data[:window]:
            special = record.get('special_number', 0)
            if special > 0:
                appeared.add(special)

        cold_numbers = list(all_numbers - appeared)

        # 如果冷号不足，选择出现频率最低的
        if len(cold_numbers) < 10:
            from collections import Counter
            freq = Counter()
            for record in historical_data[:window]:
                special = record.get('special_number', 0)
                if special > 0:
                    freq[special] += 1

            # 补充低频号码
            low_freq = [num for num, count in freq.most_common()[-20:]]
            cold_numbers.extend(low_freq)

        return cold_numbers

    def _find_consecutive_patterns(self, historical_data):
        """找出连号模式"""
        consecutive_pairs = []
        for record in historical_data[:20]:
            special = record.get('special_number', 0)
            if special > 0:
                # 检查前后连号
                if special > 1:
                    consecutive_pairs.append(special - 1)
                if special < 49:
                    consecutive_pairs.append(special + 1)
        return list(set(consecutive_pairs))

    def _find_gap_patterns(self, historical_data):
        """找出间隔模式"""
        gap_candidates = []
        for record in historical_data[:15]:
            special = record.get('special_number', 0)
            if special > 0:
                # 常见间隔
                for gap in [3, 5, 7, 10]:
                    if special + gap <= 49:
                        gap_candidates.append(special + gap)
                    if special - gap >= 1:
                        gap_candidates.append(special - gap)
        return list(set(gap_candidates))

    def _find_zodiac_patterns(self, historical_data):
        """找出生肖模式"""
        # 简化实现
        return {}

# 原始HitRateOptimizer类的方法继续
class HitRateOptimizerMethods:
    """命中率优化器的方法集合"""

    def __init__(self, db_path="data/lottery.db"):
        self.db_path = db_path

        # 核心优化策略
        self.optimization_strategies = {
            "hot_cold_balance": self._hot_cold_balance_strategy,
            "pattern_recognition": self._pattern_recognition_strategy,
            "frequency_clustering": self._frequency_clustering_strategy,
            "trend_following": self._trend_following_strategy,
            "zodiac_correlation": self._zodiac_correlation_strategy
        }

        # 历史命中率统计
        self.hit_rate_history = {}
        
    def optimize_number_selection(self, historical_data, target_date, base_predictions):
        """优化号码选择以提高命中率"""
        print("🎯 开始命中率优化...")
        
        # 分析历史命中模式
        hit_patterns = self._analyze_hit_patterns(historical_data)
        
        # 应用多种优化策略
        optimized_results = {}
        
        for strategy_name, strategy_func in self.optimization_strategies.items():
            try:
                result = strategy_func(historical_data, target_date, base_predictions, hit_patterns)
                optimized_results[strategy_name] = result
                print(f"✅ {strategy_name}: {len(result['numbers'])}个号码")
            except Exception as e:
                print(f"❌ {strategy_name}失败: {e}")
        
        # 融合优化结果
        final_optimized = self._fuse_optimization_results(optimized_results)
        
        return final_optimized
    
    def _analyze_hit_patterns(self, historical_data):
        """分析历史命中模式"""
        patterns = {
            "hot_numbers": self._find_hot_numbers(historical_data),
            "cold_numbers": self._find_cold_numbers(historical_data),
            "consecutive_patterns": self._find_consecutive_patterns(historical_data),
            "gap_patterns": self._find_gap_patterns(historical_data),
            "zodiac_patterns": self._find_zodiac_patterns(historical_data)
        }
        
        return patterns
    
    def _hot_cold_balance_strategy(self, historical_data, target_date, base_predictions, patterns):
        """热冷号平衡策略"""
        hot_numbers = patterns["hot_numbers"][:8]  # 前8个热号
        cold_numbers = patterns["cold_numbers"][:8]  # 前8个冷号
        
        # 平衡选择：6个热号 + 6个冷号 + 4个中温号
        selected_numbers = []
        
        # 选择热号（优先选择基础预测中的热号）
        hot_in_base = [n for n in hot_numbers if n in base_predictions]
        selected_numbers.extend(hot_in_base[:6])
        if len(selected_numbers) < 6:
            selected_numbers.extend([n for n in hot_numbers if n not in selected_numbers][:6-len(selected_numbers)])
        
        # 选择冷号（回补策略）
        cold_candidates = [n for n in cold_numbers if n not in selected_numbers]
        selected_numbers.extend(cold_candidates[:6])
        
        # 选择中温号
        all_numbers = set(range(1, 50))
        hot_set = set(hot_numbers)
        cold_set = set(cold_numbers)
        medium_numbers = list(all_numbers - hot_set - cold_set - set(selected_numbers))
        selected_numbers.extend(medium_numbers[:4])
        
        return {
            "numbers": selected_numbers[:16],
            "strategy": "hot_cold_balance",
            "confidence": 0.78,
            "reasoning": f"热号{len(hot_in_base)}个，冷号{min(6, len(cold_candidates))}个"
        }
    
    def _pattern_recognition_strategy(self, historical_data, target_date, base_predictions, patterns):
        """模式识别策略"""
        # 分析最近10期的号码模式
        recent_10 = historical_data[:10]
        
        # 连号模式分析
        consecutive_freq = defaultdict(int)
        for record in recent_10:
            special = record.get('special_number', 0)
            if special > 0:
                # 检查前后连号
                for offset in [-2, -1, 1, 2]:
                    candidate = special + offset
                    if 1 <= candidate <= 49:
                        consecutive_freq[candidate] += 1
        
        # 间隔模式分析
        gap_freq = defaultdict(int)
        for record in recent_10:
            special = record.get('special_number', 0)
            if special > 0:
                # 检查常见间隔
                for gap in [3, 5, 7, 10, 12]:
                    for direction in [-1, 1]:
                        candidate = special + gap * direction
                        if 1 <= candidate <= 49:
                            gap_freq[candidate] += 1
        
        # 合并模式候选
        pattern_candidates = {}
        for num, freq in consecutive_freq.items():
            pattern_candidates[num] = pattern_candidates.get(num, 0) + freq * 2  # 连号权重更高
        
        for num, freq in gap_freq.items():
            pattern_candidates[num] = pattern_candidates.get(num, 0) + freq
        
        # 选择得分最高的16个
        sorted_candidates = sorted(pattern_candidates.items(), key=lambda x: x[1], reverse=True)
        selected_numbers = [num for num, score in sorted_candidates[:16]]
        
        # 如果不足16个，从基础预测中补充
        if len(selected_numbers) < 16:
            for num in base_predictions:
                if num not in selected_numbers:
                    selected_numbers.append(num)
                    if len(selected_numbers) >= 16:
                        break
        
        return {
            "numbers": selected_numbers[:16],
            "strategy": "pattern_recognition",
            "confidence": 0.72,
            "reasoning": f"连号模式{len(consecutive_freq)}个，间隔模式{len(gap_freq)}个"
        }
    
    def _frequency_clustering_strategy(self, historical_data, target_date, base_predictions, patterns):
        """频率聚类策略"""
        # 分析不同时间窗口的频率
        windows = [10, 20, 50, 100]
        frequency_scores = defaultdict(float)
        
        for window in windows:
            window_data = historical_data[:min(window, len(historical_data))]
            window_freq = Counter()
            
            for record in window_data:
                special = record.get('special_number', 0)
                if special > 0:
                    window_freq[special] += 1
            
            # 计算频率得分（考虑时间衰减）
            total_records = len(window_data)
            for num, freq in window_freq.items():
                # 时间衰减权重：近期权重更高
                time_weight = 1.0 / (windows.index(window) + 1)
                frequency_scores[num] += (freq / total_records) * time_weight
        
        # 聚类分析：将号码按频率分组
        sorted_by_freq = sorted(frequency_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 分层选择策略
        high_freq = [num for num, score in sorted_by_freq[:12]]  # 高频12个
        medium_freq = [num for num, score in sorted_by_freq[12:25]]  # 中频13个
        low_freq = [num for num, score in sorted_by_freq[25:]]  # 低频其余
        
        # 平衡选择：8高频 + 6中频 + 2低频
        selected_numbers = []
        selected_numbers.extend(high_freq[:8])
        selected_numbers.extend(medium_freq[:6])
        selected_numbers.extend(low_freq[:2])
        
        return {
            "numbers": selected_numbers[:16],
            "strategy": "frequency_clustering",
            "confidence": 0.75,
            "reasoning": f"高频{min(8, len(high_freq))}个，中频{min(6, len(medium_freq))}个"
        }
    
    def _trend_following_strategy(self, historical_data, target_date, base_predictions, patterns):
        """趋势跟踪策略"""
        # 设置确定性种子
        import hashlib
        seed_string = f"trend_following_{target_date}"
        seed = int(hashlib.md5(seed_string.encode()).hexdigest()[:8], 16) % 100000
        np.random.seed(seed)

        # 分析号码趋势
        trend_scores = defaultdict(float)

        # 短期趋势（最近5期）
        recent_5 = historical_data[:5]
        for i, record in enumerate(recent_5):
            special = record.get('special_number', 0)
            if special > 0:
                # 越近期权重越高
                weight = (5 - i) / 5.0
                trend_scores[special] += weight * 2  # 短期趋势权重高

        # 中期趋势（最近20期）
        recent_20 = historical_data[:20]
        medium_freq = Counter()
        for record in recent_20:
            special = record.get('special_number', 0)
            if special > 0:
                medium_freq[special] += 1

        for num, freq in medium_freq.items():
            trend_scores[num] += freq / 20.0

        # 趋势变化分析
        for num in range(1, 50):
            # 计算号码在不同时期的出现频率变化
            recent_freq = sum(1 for r in recent_5 if r.get('special_number') == num) / 5
            medium_freq_rate = medium_freq.get(num, 0) / 20

            # 如果近期频率明显高于中期，给予趋势加分
            if recent_freq > medium_freq_rate * 1.5:
                trend_scores[num] += 0.5  # 上升趋势加分

        # 选择趋势得分最高的号码，确保确定性
        sorted_trends = sorted(trend_scores.items(), key=lambda x: (x[1], x[0]), reverse=True)
        selected_numbers = [num for num, score in sorted_trends[:16]]

        # 如果不足16个，用确定性方法补充
        if len(selected_numbers) < 16:
            all_numbers = list(range(1, 50))
            remaining = [n for n in all_numbers if n not in selected_numbers]
            # 使用确定性排序
            remaining.sort(key=lambda x: (x % 7, x))  # 确定性排序规则
            selected_numbers.extend(remaining[:16-len(selected_numbers)])

        return {
            "numbers": selected_numbers[:16],
            "strategy": "trend_following",
            "confidence": 0.70,
            "reasoning": f"短期趋势{len(recent_5)}期，中期趋势{len(recent_20)}期"
        }
    
    def _zodiac_correlation_strategy(self, historical_data, target_date, base_predictions, patterns):
        """生肖关联策略"""
        # 生肖号码映射（简化版）
        zodiac_numbers = {
            1: [1, 13, 25, 37, 49],  # 鼠
            2: [2, 14, 26, 38],      # 牛
            3: [3, 15, 27, 39],      # 虎
            4: [4, 16, 28, 40],      # 兔
            5: [5, 17, 29, 41],      # 龙
            6: [6, 18, 30, 42],      # 蛇
            7: [7, 19, 31, 43],      # 马
            8: [8, 20, 32, 44],      # 羊
            9: [9, 21, 33, 45],      # 猴
            10: [10, 22, 34, 46],    # 鸡
            11: [11, 23, 35, 47],    # 狗
            12: [12, 24, 36, 48]     # 猪
        }
        
        # 分析生肖出现频率
        zodiac_freq = defaultdict(int)
        recent_20 = historical_data[:20]
        
        for record in recent_20:
            special = record.get('special_number', 0)
            if special > 0:
                # 找到对应生肖
                for zodiac, numbers in zodiac_numbers.items():
                    if special in numbers:
                        zodiac_freq[zodiac] += 1
                        break
        
        # 选择频率较高的生肖
        sorted_zodiacs = sorted(zodiac_freq.items(), key=lambda x: x[1], reverse=True)
        top_zodiacs = [zodiac for zodiac, freq in sorted_zodiacs[:4]]  # 选择前4个生肖
        
        # 从这些生肖中选择号码
        selected_numbers = []
        for zodiac in top_zodiacs:
            zodiac_nums = zodiac_numbers[zodiac]
            # 每个生肖选择4个号码
            selected_numbers.extend(zodiac_nums[:4])
        
        return {
            "numbers": selected_numbers[:16],
            "strategy": "zodiac_correlation",
            "confidence": 0.68,
            "reasoning": f"选择{len(top_zodiacs)}个热门生肖的号码"
        }
    
    def _fuse_optimization_results(self, optimization_results):
        """融合优化结果"""
        # 统计每个号码的出现次数和权重得分
        number_scores = defaultdict(float)

        for strategy_name, result in optimization_results.items():
            numbers = result["numbers"]
            confidence = result["confidence"]

            for i, num in enumerate(numbers):
                # 位置权重：排名越前权重越高
                position_weight = (16 - i) / 16.0
                # 策略权重：置信度越高权重越高
                strategy_weight = confidence

                number_scores[num] += position_weight * strategy_weight

        # 确定性排序：先按得分降序，再按号码升序（确保一致性）
        sorted_numbers = sorted(number_scores.items(), key=lambda x: (-x[1], x[0]))
        final_numbers = [num for num, score in sorted_numbers[:16]]

        # 计算融合置信度
        if optimization_results:
            avg_confidence = np.mean([result["confidence"] for result in optimization_results.values()])
            fusion_confidence = min(avg_confidence * 1.1, 0.95)  # 融合提升但不超过95%
        else:
            fusion_confidence = 0.5

        return {
            "optimized_numbers": final_numbers,
            "fusion_confidence": fusion_confidence,
            "strategy_count": len(optimization_results),
            "individual_results": optimization_results,
            "number_scores": dict(number_scores)
        }
    
    def _find_hot_numbers(self, historical_data, window=30):
        """找出热号"""
        freq = Counter()
        for record in historical_data[:window]:
            special = record.get('special_number', 0)
            if special > 0:
                freq[special] += 1
        
        return [num for num, count in freq.most_common()]
    
    def _find_cold_numbers(self, historical_data, window=50):
        """找出冷号"""
        all_numbers = set(range(1, 50))
        appeared = set()
        
        for record in historical_data[:window]:
            special = record.get('special_number', 0)
            if special > 0:
                appeared.add(special)
        
        cold_numbers = list(all_numbers - appeared)
        
        # 如果冷号不足，选择出现频率最低的
        if len(cold_numbers) < 10:
            freq = Counter()
            for record in historical_data[:window]:
                special = record.get('special_number', 0)
                if special > 0:
                    freq[special] += 1
            
            # 补充低频号码
            low_freq = [num for num, count in freq.most_common()[-20:]]
            cold_numbers.extend(low_freq)
        
        return cold_numbers
    
    def _find_consecutive_patterns(self, historical_data):
        """找出连号模式"""
        consecutive_pairs = []
        for record in historical_data[:20]:
            special = record.get('special_number', 0)
            if special > 0:
                # 检查前后连号
                if special > 1:
                    consecutive_pairs.append(special - 1)
                if special < 49:
                    consecutive_pairs.append(special + 1)
        
        return list(set(consecutive_pairs))
    
    def _find_gap_patterns(self, historical_data):
        """找出间隔模式"""
        gap_candidates = []
        for record in historical_data[:15]:
            special = record.get('special_number', 0)
            if special > 0:
                # 常见间隔
                for gap in [3, 5, 7, 10]:
                    if special + gap <= 49:
                        gap_candidates.append(special + gap)
                    if special - gap >= 1:
                        gap_candidates.append(special - gap)
        
        return list(set(gap_candidates))
    
    def _find_zodiac_patterns(self, historical_data):
        """找出生肖模式"""
        # 简化实现
        return {}

if __name__ == "__main__":
    # 测试命中率优化器
    optimizer = HitRateOptimizer()
    print("🚀 命中率优化器测试")
