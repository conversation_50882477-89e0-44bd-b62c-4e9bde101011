"""
测试完美预测系统状态
"""

import sys
import os
from pathlib import Path

# 添加路径
sys.path.insert(0, str(Path.cwd()))
sys.path.insert(0, str(Path.cwd() / "src"))

def test_system():
    print("🧪 测试完美预测系统状态...")
    
    try:
        # 测试导入
        from special_number_predictor import SpecialNumberPredictor
        from consistent_predictor import ConsistentSpecialNumberPredictor
        from historical_backtest import HistoricalBacktestSystem
        from consistency_test import test_prediction_consistency
        from src.perfect_prediction_system import PerfectPredictionSystem
        from src.fusion_manager import FusionManager
        
        print("✅ 所有模块导入成功")
        
        # 测试初始化
        system = PerfectPredictionSystem()
        system.initialize_modules()
        
        print("✅ 完美预测系统初始化成功")
        print("✅ 融合管理器可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_system()
    if success:
        print("\n🎉 系统状态正常！")
    else:
        print("\n❌ 系统有问题！")
    
    input("\n按回车键退出...")
