"""
检查2024年10月的数据是否与真实开奖数据匹配
"""
import sqlite3

def check_2024_october_data():
    """检查2024年10月的数据"""
    print("🔍 检查2024年10月的数据")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        # 查询2024年10月的数据
        cursor.execute('''
            SELECT draw_date, period_number, regular_1, regular_2, regular_3, 
                   regular_4, regular_5, regular_6, special_number
            FROM lottery_results 
            WHERE draw_date LIKE "2024-10%" 
            ORDER BY draw_date DESC
            LIMIT 10
        ''')
        
        rows = cursor.fetchall()
        
        print(f"数据库中2024年10月的数据 ({len(rows)}条):")
        print("-" * 60)
        
        # 用户提供的真实数据
        real_data = {
            "2024-10-21": {"period": "2024295", "regular": [14,49,18,9,38,24], "special": 48},
            "2024-10-20": {"period": "2024294", "regular": [13,11,15,26,27,48], "special": 36},
            "2024-10-19": {"period": "2024293", "regular": [48,37,18,46,5,30], "special": 10},
            "2024-10-18": {"period": "2024292", "regular": [26,6,19,35,48,44], "special": 31},
            "2024-10-17": {"period": "2024291", "regular": [1,29,31,38,8,14], "special": 11},
        }
        
        for row in rows:
            draw_date, period_number, r1, r2, r3, r4, r5, r6, special_number = row
            regular_numbers = [r1, r2, r3, r4, r5, r6]
            
            print(f"日期: {draw_date}")
            print(f"  数据库期号: {period_number}")
            print(f"  数据库正码: {regular_numbers}")
            print(f"  数据库特码: {special_number}")
            
            # 检查是否与真实数据匹配
            if draw_date in real_data:
                real = real_data[draw_date]
                print(f"  真实期号: {real['period']}")
                print(f"  真实正码: {real['regular']}")
                print(f"  真实特码: {real['special']}")
                
                # 比较数据
                period_match = period_number == real['period']
                regular_match = set(regular_numbers) == set(real['regular'])
                special_match = special_number == real['special']
                
                print(f"  期号匹配: {'✅' if period_match else '❌'}")
                print(f"  正码匹配: {'✅' if regular_match else '❌'}")
                print(f"  特码匹配: {'✅' if special_match else '❌'}")
                
                if not (period_match and regular_match and special_match):
                    print(f"  ⚠️ 数据不匹配！")
            else:
                print(f"  ⚠️ 没有对应的真实数据进行比较")
            
            print("-" * 40)
        
        conn.close()
        
    except Exception as e:
        print(f"检查失败: {e}")

def check_specific_date():
    """检查特定日期的数据"""
    print("\n🔍 检查2024-10-21的详细数据")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        # 查询2024-10-21的数据
        cursor.execute('''
            SELECT * FROM lottery_results 
            WHERE draw_date = "2024-10-21"
        ''')
        
        row = cursor.fetchone()
        
        if row:
            print("数据库中的完整记录:")
            cursor.execute('PRAGMA table_info(lottery_results)')
            columns = [col[1] for col in cursor.fetchall()]
            
            for i, col in enumerate(columns):
                print(f"  {col}: {row[i]}")
            
            # 提取关键数据
            regular_numbers = [row[3], row[4], row[5], row[6], row[7], row[8]]  # regular_1 到 regular_6
            special_number = row[9]  # special_number
            
            print(f"\n解析后的数据:")
            print(f"  正码: {regular_numbers}")
            print(f"  特码: {special_number}")
            
            # 与真实数据比较
            real_regular = [14,49,18,9,38,24]
            real_special = 48
            
            print(f"\n真实数据:")
            print(f"  正码: {real_regular}")
            print(f"  特码: {real_special}")
            
            print(f"\n比较结果:")
            print(f"  正码匹配: {'✅' if set(regular_numbers) == set(real_regular) else '❌'}")
            print(f"  特码匹配: {'✅' if special_number == real_special else '❌'}")
            
        else:
            print("❌ 数据库中没有找到2024-10-21的数据")
        
        conn.close()
        
    except Exception as e:
        print(f"检查失败: {e}")

def main():
    """主函数"""
    print("🔧 检查数据库数据与真实开奖数据的匹配性")
    print("=" * 70)
    
    check_2024_october_data()
    check_specific_date()
    
    print("\n" + "=" * 70)
    print("🎯 数据匹配性检查完成")
    
    print("\n💡 如果发现数据不匹配，可能的原因:")
    print("  1. 数据库中的数据不是最新的真实开奖数据")
    print("  2. 数据导入时格式转换错误")
    print("  3. 期号映射错误")
    print("  4. 需要更新数据库中的数据")

if __name__ == "__main__":
    main()
