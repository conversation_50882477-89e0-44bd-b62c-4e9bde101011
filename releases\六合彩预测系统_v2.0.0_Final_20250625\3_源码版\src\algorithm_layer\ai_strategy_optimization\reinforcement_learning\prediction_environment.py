"""
预测环境 - 强化学习的环境定义
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import gym
from gym import spaces
import json

class LotteryPredictionEnvironment(gym.Env):
    """澳门六合彩预测环境"""
    
    def __init__(self, 
                 historical_data: pd.DataFrame,
                 prediction_horizon: int = 1,
                 max_episode_length: int = 100,
                 reward_config: Dict = None):
        """
        初始化预测环境
        
        Args:
            historical_data: 历史开奖数据
            prediction_horizon: 预测时间跨度
            max_episode_length: 最大回合长度
            reward_config: 奖励配置
        """
        super().__init__()
        
        self.historical_data = historical_data
        self.prediction_horizon = prediction_horizon
        self.max_episode_length = max_episode_length
        self.reward_config = reward_config or self._default_reward_config()
        
        # 环境状态
        self.current_step = 0
        self.current_data_index = 0
        self.episode_predictions = []
        self.episode_rewards = []
        
        # 定义状态空间和动作空间
        self._define_spaces()
        
        # 初始化环境
        self.reset()
    
    def _default_reward_config(self) -> Dict:
        """默认奖励配置"""
        return {
            'correct_prediction': 10.0,      # 正确预测奖励
            'partial_match': 2.0,            # 部分匹配奖励
            'wrong_prediction': -1.0,        # 错误预测惩罚
            'consistency_bonus': 1.0,        # 一致性奖励
            'diversity_penalty': -0.5,       # 多样性惩罚
            'confidence_weight': 0.5         # 置信度权重
        }
    
    def _define_spaces(self):
        """定义状态空间和动作空间"""
        # 状态空间：历史数据特征
        # 包括：最近N期的开奖号码、统计特征、趋势特征等
        state_dim = 200  # 状态维度
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf, 
            shape=(state_dim,), dtype=np.float32
        )
        
        # 动作空间：预测决策
        # 包括：选择的号码、置信度、策略参数等
        # 使用多离散动作空间：49个号码的选择概率
        self.action_space = spaces.Box(
            low=0.0, high=1.0,
            shape=(49,), dtype=np.float32
        )
    
    def reset(self) -> np.ndarray:
        """重置环境"""
        self.current_step = 0
        self.current_data_index = np.random.randint(
            50, len(self.historical_data) - self.max_episode_length
        )
        self.episode_predictions = []
        self.episode_rewards = []
        
        return self._get_state()
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, Dict]:
        """执行一步动作"""
        # 解析动作
        prediction_probs = action
        predicted_numbers = self._action_to_prediction(prediction_probs)
        
        # 获取真实结果
        true_numbers = self._get_true_numbers()
        
        # 计算奖励
        reward = self._calculate_reward(predicted_numbers, true_numbers, prediction_probs)
        
        # 更新状态
        self.current_step += 1
        self.current_data_index += 1
        
        # 记录预测
        self.episode_predictions.append({
            'step': self.current_step,
            'predicted': predicted_numbers,
            'true': true_numbers,
            'reward': reward,
            'action_probs': prediction_probs.tolist()
        })
        self.episode_rewards.append(reward)
        
        # 检查是否结束
        done = (self.current_step >= self.max_episode_length or 
                self.current_data_index >= len(self.historical_data) - 1)
        
        # 获取新状态
        next_state = self._get_state() if not done else np.zeros_like(self._get_state())
        
        # 信息字典
        info = {
            'step': self.current_step,
            'predicted_numbers': predicted_numbers,
            'true_numbers': true_numbers,
            'accuracy': self._calculate_accuracy(predicted_numbers, true_numbers),
            'episode_reward': sum(self.episode_rewards)
        }
        
        return next_state, reward, done, info
    
    def _get_state(self) -> np.ndarray:
        """获取当前状态"""
        if self.current_data_index < 50:
            # 如果数据不足，用零填充
            return np.zeros(self.observation_space.shape[0], dtype=np.float32)
        
        # 获取历史数据窗口
        window_size = 50
        start_idx = max(0, self.current_data_index - window_size)
        end_idx = self.current_data_index
        
        window_data = self.historical_data.iloc[start_idx:end_idx]
        
        # 提取特征
        features = []
        
        # 1. 最近期号码特征
        if len(window_data) > 0:
            latest_numbers = json.loads(window_data.iloc[-1]['regular_numbers'])
            special_number = window_data.iloc[-1]['special_number']
            
            # 号码one-hot编码
            number_features = np.zeros(49)
            for num in latest_numbers + [special_number]:
                if 1 <= num <= 49:
                    number_features[num-1] = 1
            features.extend(number_features)
        else:
            features.extend(np.zeros(49))
        
        # 2. 统计特征
        if len(window_data) > 0:
            # 号码频率统计
            all_numbers = []
            for _, row in window_data.iterrows():
                regular_nums = json.loads(row['regular_numbers'])
                all_numbers.extend(regular_nums + [row['special_number']])
            
            freq_features = np.zeros(49)
            for num in all_numbers:
                if 1 <= num <= 49:
                    freq_features[num-1] += 1
            
            # 归一化
            if freq_features.sum() > 0:
                freq_features = freq_features / freq_features.sum()
            
            features.extend(freq_features)
        else:
            features.extend(np.zeros(49))
        
        # 3. 趋势特征
        trend_features = self._extract_trend_features(window_data)
        features.extend(trend_features)
        
        # 4. 时间特征
        time_features = self._extract_time_features()
        features.extend(time_features)
        
        # 确保特征维度正确
        features = np.array(features, dtype=np.float32)
        if len(features) < self.observation_space.shape[0]:
            # 补零
            padding = np.zeros(self.observation_space.shape[0] - len(features))
            features = np.concatenate([features, padding])
        elif len(features) > self.observation_space.shape[0]:
            # 截断
            features = features[:self.observation_space.shape[0]]
        
        return features
    
    def _extract_trend_features(self, window_data: pd.DataFrame) -> List[float]:
        """提取趋势特征"""
        features = []
        
        if len(window_data) < 5:
            return [0.0] * 20  # 返回固定长度的零特征
        
        # 和值趋势
        sums = []
        for _, row in window_data.iterrows():
            regular_nums = json.loads(row['regular_numbers'])
            total_sum = sum(regular_nums) + row['special_number']
            sums.append(total_sum)
        
        if len(sums) > 1:
            # 和值变化趋势
            sum_diff = np.diff(sums)
            features.extend([
                np.mean(sum_diff),
                np.std(sum_diff),
                np.max(sum_diff),
                np.min(sum_diff)
            ])
        else:
            features.extend([0.0, 0.0, 0.0, 0.0])
        
        # 奇偶比例趋势
        odd_ratios = []
        for _, row in window_data.iterrows():
            regular_nums = json.loads(row['regular_numbers'])
            all_nums = regular_nums + [row['special_number']]
            odd_count = sum(1 for num in all_nums if num % 2 == 1)
            odd_ratios.append(odd_count / len(all_nums))
        
        if len(odd_ratios) > 1:
            features.extend([
                np.mean(odd_ratios),
                np.std(odd_ratios),
                odd_ratios[-1]  # 最新比例
            ])
        else:
            features.extend([0.0, 0.0, 0.0])
        
        # 大小比例趋势
        big_ratios = []
        for _, row in window_data.iterrows():
            regular_nums = json.loads(row['regular_numbers'])
            all_nums = regular_nums + [row['special_number']]
            big_count = sum(1 for num in all_nums if num > 24)
            big_ratios.append(big_count / len(all_nums))
        
        if len(big_ratios) > 1:
            features.extend([
                np.mean(big_ratios),
                np.std(big_ratios),
                big_ratios[-1]  # 最新比例
            ])
        else:
            features.extend([0.0, 0.0, 0.0])
        
        # 补充特征到固定长度
        while len(features) < 20:
            features.append(0.0)
        
        return features[:20]
    
    def _extract_time_features(self) -> List[float]:
        """提取时间特征"""
        if self.current_data_index < len(self.historical_data):
            current_date = pd.to_datetime(self.historical_data.iloc[self.current_data_index]['draw_date'])
            
            # 周期性特征
            day_of_week = current_date.dayofweek / 6.0  # 归一化到[0,1]
            day_of_month = current_date.day / 31.0
            month = current_date.month / 12.0
            
            return [day_of_week, day_of_month, month]
        else:
            return [0.0, 0.0, 0.0]
    
    def _action_to_prediction(self, action_probs: np.ndarray) -> List[int]:
        """将动作转换为预测号码"""
        # 选择概率最高的7个号码（6个正码+1个特码）
        top_indices = np.argsort(action_probs)[-7:]
        predicted_numbers = [idx + 1 for idx in top_indices]  # 转换为1-49的号码
        return sorted(predicted_numbers)
    
    def _get_true_numbers(self) -> List[int]:
        """获取真实开奖号码"""
        if self.current_data_index < len(self.historical_data):
            row = self.historical_data.iloc[self.current_data_index]
            regular_nums = json.loads(row['regular_numbers'])
            special_num = row['special_number']
            return sorted(regular_nums + [special_num])
        else:
            return []
    
    def _calculate_reward(self, predicted: List[int], true: List[int], action_probs: np.ndarray) -> float:
        """计算奖励"""
        if not predicted or not true:
            return 0.0
        
        # 基础匹配奖励
        matches = len(set(predicted) & set(true))
        total_numbers = len(true)
        
        if matches == total_numbers:
            reward = self.reward_config['correct_prediction']
        elif matches > 0:
            reward = self.reward_config['partial_match'] * (matches / total_numbers)
        else:
            reward = self.reward_config['wrong_prediction']
        
        # 置信度奖励
        confidence = np.max(action_probs)
        confidence_bonus = self.reward_config['confidence_weight'] * confidence
        
        # 多样性惩罚（避免总是选择相同号码）
        diversity_penalty = 0.0
        if len(self.episode_predictions) > 0:
            last_predicted = self.episode_predictions[-1]['predicted']
            overlap = len(set(predicted) & set(last_predicted))
            if overlap > 5:  # 如果重叠太多
                diversity_penalty = self.reward_config['diversity_penalty']
        
        total_reward = reward + confidence_bonus + diversity_penalty
        
        return total_reward
    
    def _calculate_accuracy(self, predicted: List[int], true: List[int]) -> float:
        """计算准确率"""
        if not predicted or not true:
            return 0.0
        
        matches = len(set(predicted) & set(true))
        return matches / len(true)
    
    def get_episode_summary(self) -> Dict:
        """获取回合总结"""
        if not self.episode_predictions:
            return {}
        
        total_reward = sum(self.episode_rewards)
        accuracies = [pred.get('accuracy', 0) for pred in self.episode_predictions]
        avg_accuracy = np.mean(accuracies) if accuracies else 0
        
        return {
            'total_steps': self.current_step,
            'total_reward': total_reward,
            'average_reward': total_reward / max(1, self.current_step),
            'average_accuracy': avg_accuracy,
            'best_accuracy': max(accuracies) if accuracies else 0,
            'predictions': self.episode_predictions
        }
    
    def render(self, mode='human'):
        """渲染环境（可选）"""
        if mode == 'human':
            print(f"Step: {self.current_step}, Data Index: {self.current_data_index}")
            if self.episode_predictions:
                last_pred = self.episode_predictions[-1]
                print(f"Last Prediction: {last_pred['predicted']}")
                print(f"True Numbers: {last_pred['true']}")
                print(f"Reward: {last_pred['reward']:.2f}")
