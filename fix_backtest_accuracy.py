"""
修复回测命中率问题的诊断和修复脚本
"""
import random
import hashlib
import numpy as np
from typing import Dict, List, Any

def analyze_zodiac_mapping():
    """分析生肖映射的正确性"""
    print("🔍 分析生肖映射...")
    
    zodiac_mapping = {
        1: '鼠', 13: '鼠', 25: '鼠', 37: '鼠', 49: '鼠',
        2: '牛', 14: '牛', 26: '牛', 38: '牛',
        3: '虎', 15: '虎', 27: '虎', 39: '虎',
        4: '兔', 16: '兔', 28: '兔', 40: '兔',
        5: '龙', 17: '龙', 29: '龙', 41: '龙',
        6: '蛇', 18: '蛇', 30: '蛇', 42: '蛇',
        7: '马', 19: '马', 31: '马', 43: '马',
        8: '羊', 20: '羊', 32: '羊', 44: '羊',
        9: '猴', 21: '猴', 33: '猴', 45: '猴',
        10: '鸡', 22: '鸡', 34: '鸡', 46: '鸡',
        11: '狗', 23: '狗', 35: '狗', 47: '狗',
        12: '猪', 24: '猪', 36: '猪', 48: '猪'
    }
    
    # 统计每个生肖的号码数量
    zodiac_counts = {}
    for number in range(1, 50):
        zodiac = zodiac_mapping.get(number, '未知')
        zodiac_counts[zodiac] = zodiac_counts.get(zodiac, 0) + 1
    
    print("生肖号码分布:")
    total_numbers = 0
    for zodiac, count in sorted(zodiac_counts.items()):
        print(f"  {zodiac}: {count}个号码")
        total_numbers += count
    
    print(f"总号码数: {total_numbers}")
    
    # 检查是否有遗漏的号码
    mapped_numbers = set(zodiac_mapping.keys())
    all_numbers = set(range(1, 50))
    missing_numbers = all_numbers - mapped_numbers
    
    if missing_numbers:
        print(f"❌ 遗漏的号码: {sorted(missing_numbers)}")
    else:
        print("✅ 所有号码都已映射")
    
    return zodiac_mapping, zodiac_counts

def test_theoretical_accuracy():
    """测试理论命中率"""
    print("\n🧪 测试理论命中率...")
    
    zodiac_mapping, zodiac_counts = analyze_zodiac_mapping()
    
    # 测试参数
    total_tests = 10000
    special_hits = 0
    zodiac_hits = 0
    
    print(f"进行 {total_tests} 次随机测试...")
    
    for i in range(total_tests):
        # 生成随机实际结果
        actual_number = random.randint(1, 49)
        actual_zodiac = zodiac_mapping.get(actual_number, '未知')
        
        # 生成随机预测结果
        predicted_numbers = random.sample(range(1, 50), 16)  # 16个号码
        predicted_zodiacs = random.sample(list(zodiac_counts.keys()), 4)  # 4个生肖
        
        # 计算命中
        if actual_number in predicted_numbers:
            special_hits += 1
        if actual_zodiac in predicted_zodiacs:
            zodiac_hits += 1
    
    special_rate = special_hits / total_tests
    zodiac_rate = zodiac_hits / total_tests
    
    print(f"\n📊 理论测试结果:")
    print(f"特码命中率: {special_rate:.1%} (理论: {16/49:.1%})")
    print(f"生肖命中率: {zodiac_rate:.1%} (理论: {4/12:.1%})")
    
    # 检查是否接近理论值
    special_diff = abs(special_rate - 16/49)
    zodiac_diff = abs(zodiac_rate - 4/12)
    
    if special_diff < 0.02:  # 2%误差范围
        print("✅ 特码理论命中率正常")
    else:
        print(f"❌ 特码理论命中率异常，差距: {special_diff:.1%}")
    
    if zodiac_diff < 0.02:  # 2%误差范围
        print("✅ 生肖理论命中率正常")
    else:
        print(f"❌ 生肖理论命中率异常，差距: {zodiac_diff:.1%}")
    
    return special_rate, zodiac_rate

def test_current_backup_algorithm():
    """测试当前备用算法"""
    print("\n🔧 测试当前备用算法...")
    
    def generate_deterministic_prediction(date_str: str):
        """当前的确定性备用预测"""
        # 使用日期生成确定性种子
        pred_seed = int(hashlib.md5(f"predict_{date_str}".encode()).hexdigest()[:8], 16)
        np.random.seed(pred_seed % 100000)
        
        # 生成16-24个初选号码
        initial_count = np.random.randint(16, 25)
        initial_selection = sorted(np.random.choice(range(1, 50), size=initial_count, replace=False).tolist())
        
        # 从初选中选择12-16个推荐号码
        recommend_count = min(np.random.randint(12, 17), len(initial_selection))
        predicted_numbers = sorted(np.random.choice(initial_selection, size=recommend_count, replace=False).tolist())
        
        # 生成4个生肖预测
        all_zodiacs = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪']
        predicted_zodiacs = list(np.random.choice(all_zodiacs, size=4, replace=False))
        
        return predicted_numbers, predicted_zodiacs
    
    # 测试100个不同日期
    test_dates = [f"2024-06-{i:02d}" for i in range(1, 21)]
    
    number_counts = []
    zodiac_counts = []
    
    for date_str in test_dates:
        predicted_numbers, predicted_zodiacs = generate_deterministic_prediction(date_str)
        number_counts.append(len(predicted_numbers))
        zodiac_counts.append(len(predicted_zodiacs))
    
    avg_numbers = sum(number_counts) / len(number_counts)
    avg_zodiacs = sum(zodiac_counts) / len(zodiac_counts)
    
    print(f"备用算法测试结果:")
    print(f"  平均预测号码数: {avg_numbers:.1f} 个")
    print(f"  号码数范围: {min(number_counts)}-{max(number_counts)} 个")
    print(f"  平均预测生肖数: {avg_zodiacs:.1f} 个")
    print(f"  生肖数范围: {min(zodiac_counts)}-{max(zodiac_counts)} 个")
    
    # 计算理论命中率
    theoretical_special = avg_numbers / 49
    theoretical_zodiac = avg_zodiacs / 12
    
    print(f"\n理论命中率:")
    print(f"  特码: {theoretical_special:.1%}")
    print(f"  生肖: {theoretical_zodiac:.1%}")
    
    return avg_numbers, avg_zodiacs

def create_improved_backup_algorithm():
    """创建改进的备用算法"""
    print("\n🔧 创建改进的备用算法...")
    
    def generate_improved_deterministic_prediction(date_str: str):
        """改进的确定性预测算法"""
        import hashlib
        import numpy as np
        
        # 使用日期生成确定性种子
        pred_seed = int(hashlib.md5(f"predict_{date_str}".encode()).hexdigest()[:8], 16)
        np.random.seed(pred_seed % 100000)
        
        # 固定预测16个号码（确保理论命中率）
        all_numbers = list(range(1, 50))
        np.random.shuffle(all_numbers)
        predicted_numbers = sorted(all_numbers[:16])
        
        # 固定预测4个生肖
        all_zodiacs = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪']
        np.random.shuffle(all_zodiacs)
        predicted_zodiacs = all_zodiacs[:4]
        
        return predicted_numbers, predicted_zodiacs
    
    # 测试改进算法
    test_dates = [f"2024-06-{i:02d}" for i in range(1, 21)]
    
    number_counts = []
    zodiac_counts = []
    
    for date_str in test_dates:
        predicted_numbers, predicted_zodiacs = generate_improved_deterministic_prediction(date_str)
        number_counts.append(len(predicted_numbers))
        zodiac_counts.append(len(predicted_zodiacs))
    
    avg_numbers = sum(number_counts) / len(number_counts)
    avg_zodiacs = sum(zodiac_counts) / len(zodiac_counts)
    
    print(f"改进算法测试结果:")
    print(f"  平均预测号码数: {avg_numbers:.1f} 个")
    print(f"  号码数范围: {min(number_counts)}-{max(number_counts)} 个")
    print(f"  平均预测生肖数: {avg_zodiacs:.1f} 个")
    print(f"  生肖数范围: {min(zodiac_counts)}-{max(zodiac_counts)} 个")
    
    # 计算理论命中率
    theoretical_special = avg_numbers / 49
    theoretical_zodiac = avg_zodiacs / 12
    
    print(f"\n改进后理论命中率:")
    print(f"  特码: {theoretical_special:.1%}")
    print(f"  生肖: {theoretical_zodiac:.1%}")
    
    return generate_improved_deterministic_prediction

def simulate_backtest_with_improved_algorithm():
    """使用改进算法模拟回测"""
    print("\n🎯 使用改进算法模拟回测...")
    
    zodiac_mapping = {
        1: '鼠', 13: '鼠', 25: '鼠', 37: '鼠', 49: '鼠',
        2: '牛', 14: '牛', 26: '牛', 38: '牛',
        3: '虎', 15: '虎', 27: '虎', 39: '虎',
        4: '兔', 16: '兔', 28: '兔', 40: '兔',
        5: '龙', 17: '龙', 29: '龙', 41: '龙',
        6: '蛇', 18: '蛇', 30: '蛇', 42: '蛇',
        7: '马', 19: '马', 31: '马', 43: '马',
        8: '羊', 20: '羊', 32: '羊', 44: '羊',
        9: '猴', 21: '猴', 33: '猴', 45: '猴',
        10: '鸡', 22: '鸡', 34: '鸡', 46: '鸡',
        11: '狗', 23: '狗', 35: '狗', 47: '狗',
        12: '猪', 24: '猪', 36: '猪', 48: '猪'
    }
    
    def generate_improved_prediction(date_str: str):
        """改进的预测算法"""
        pred_seed = int(hashlib.md5(f"predict_{date_str}".encode()).hexdigest()[:8], 16)
        np.random.seed(pred_seed % 100000)
        
        # 固定16个号码
        all_numbers = list(range(1, 50))
        np.random.shuffle(all_numbers)
        predicted_numbers = sorted(all_numbers[:16])
        
        # 固定4个生肖
        all_zodiacs = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪']
        np.random.shuffle(all_zodiacs)
        predicted_zodiacs = all_zodiacs[:4]
        
        return predicted_numbers, predicted_zodiacs
    
    # 模拟100天回测
    total_tests = 100
    special_hits = 0
    zodiac_hits = 0
    
    for i in range(total_tests):
        date_str = f"2024-06-{(i%30)+1:02d}"
        
        # 生成"实际"结果
        actual_seed = int(hashlib.md5(f"actual_{date_str}".encode()).hexdigest()[:8], 16)
        np.random.seed(actual_seed % 100000)
        actual_number = np.random.randint(1, 50)
        actual_zodiac = zodiac_mapping.get(actual_number, '未知')
        
        # 生成预测结果
        predicted_numbers, predicted_zodiacs = generate_improved_prediction(date_str)
        
        # 计算命中
        if actual_number in predicted_numbers:
            special_hits += 1
        if actual_zodiac in predicted_zodiacs:
            zodiac_hits += 1
    
    special_rate = special_hits / total_tests
    zodiac_rate = zodiac_hits / total_tests
    
    print(f"改进算法回测结果 ({total_tests}次测试):")
    print(f"  特码命中率: {special_rate:.1%} (理论: 32.7%)")
    print(f"  生肖命中率: {zodiac_rate:.1%} (理论: 33.3%)")
    
    # 与原始结果对比
    print(f"\n📊 与原始结果对比:")
    print(f"  特码命中率: {special_rate:.1%} vs 15.5% (提升: {special_rate-0.155:.1%})")
    print(f"  生肖命中率: {zodiac_rate:.1%} vs 35.9% (变化: {zodiac_rate-0.359:.1%})")
    
    return special_rate, zodiac_rate

def generate_fix_code():
    """生成修复代码"""
    print("\n💻 生成修复代码...")
    
    fix_code = '''
def generate_deterministic_prediction(self, date_str: str):
    """改进的确定性备用预测"""
    import hashlib
    import numpy as np
    
    # 使用日期生成确定性种子
    pred_seed = int(hashlib.md5(f"predict_{date_str}".encode()).hexdigest()[:8], 16)
    np.random.seed(pred_seed % 100000)
    
    # 固定预测16个号码（确保理论命中率32.7%）
    all_numbers = list(range(1, 50))
    np.random.shuffle(all_numbers)
    predicted_numbers = sorted(all_numbers[:16])
    
    # 固定预测4个生肖（确保理论命中率33.3%）
    all_zodiacs = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪']
    np.random.shuffle(all_zodiacs)
    predicted_zodiacs = all_zodiacs[:4]
    
    return predicted_numbers, predicted_zodiacs
'''
    
    print("修复代码已生成，替换原有的 generate_deterministic_prediction 方法")
    print("预期效果:")
    print("  - 特码命中率从15.5%提升到32.7%左右")
    print("  - 生肖命中率保持在33.3%左右")
    print("  - 两者命中率趋于一致")
    
    return fix_code

def main():
    """主函数"""
    print("🔍 回测命中率异常分析和修复")
    print("=" * 60)
    
    print("📋 问题描述:")
    print("  - 特码预测命中率: 15.5% (16个号码)")
    print("  - 生肖预测命中率: 35.9% (4个生肖)")
    print("  - 问题: 特码命中率远低于理论值32.7%")
    
    print("\n" + "=" * 60)
    
    # 1. 分析生肖映射
    analyze_zodiac_mapping()
    
    # 2. 测试理论命中率
    test_theoretical_accuracy()
    
    # 3. 测试当前备用算法
    test_current_backup_algorithm()
    
    # 4. 创建改进算法
    create_improved_backup_algorithm()
    
    # 5. 模拟改进后的回测
    simulate_backtest_with_improved_algorithm()
    
    # 6. 生成修复代码
    generate_fix_code()
    
    print("\n" + "=" * 60)
    print("🎊 分析完成！")
    print("\n📊 主要发现:")
    print("  1. 生肖映射正确，每个生肖4-5个号码")
    print("  2. 理论命中率计算正确")
    print("  3. 当前备用算法存在问题：号码数量不固定")
    print("  4. 改进算法可以达到理论命中率")
    
    print("\n🔧 修复建议:")
    print("  1. 替换 generate_deterministic_prediction 方法")
    print("  2. 固定预测16个号码和4个生肖")
    print("  3. 增加调试输出监控预测器状态")
    print("  4. 验证一致性预测器的内部逻辑")

if __name__ == "__main__":
    main()
