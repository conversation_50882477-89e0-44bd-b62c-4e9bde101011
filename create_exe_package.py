"""
六合彩预测系统 EXE 打包脚本
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def create_exe_package():
    """创建EXE打包"""
    print("🚀 开始创建六合彩预测系统 EXE 打包")
    print("=" * 60)
    
    # 设置路径
    source_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    if not source_dir.exists():
        print("❌ 源目录不存在")
        return False
    
    print(f"📁 源目录: {source_dir}")
    
    # 切换到源目录
    original_dir = Path.cwd()
    os.chdir(source_dir)
    
    try:
        # 1. 检查PyInstaller
        print("\n1️⃣ 检查PyInstaller...")
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "show", "pyinstaller"], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print("📦 安装PyInstaller...")
                subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("✅ PyInstaller 已准备就绪")
        except Exception as e:
            print(f"❌ PyInstaller 安装失败: {e}")
            return False
        
        # 2. 创建spec文件
        print("\n2️⃣ 创建PyInstaller spec文件...")
        create_spec_file()
        print("✅ spec文件创建完成")
        
        # 3. 准备资源文件
        print("\n3️⃣ 准备资源文件...")
        prepare_resources()
        print("✅ 资源文件准备完成")
        
        # 4. 执行打包
        print("\n4️⃣ 执行PyInstaller打包...")
        try:
            cmd = [sys.executable, "-m", "PyInstaller", "--clean", "lottery_system.spec"]
            print(f"🔧 执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ PyInstaller打包成功")
                print("📄 打包日志:")
                print(result.stdout[-500:])  # 显示最后500字符
            else:
                print("❌ PyInstaller打包失败")
                print("📄 错误信息:")
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ 打包过程出错: {e}")
            return False
        
        # 5. 后处理
        print("\n5️⃣ 后处理...")
        post_process()
        print("✅ 后处理完成")
        
        # 6. 验证EXE
        print("\n6️⃣ 验证EXE文件...")
        exe_path = Path("dist/六合彩预测系统_v2.0.0.exe")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"✅ EXE文件创建成功")
            print(f"📁 文件路径: {exe_path.absolute()}")
            print(f"📊 文件大小: {size_mb:.1f} MB")
            
            # 创建发布包
            create_release_package()
            
            return True
        else:
            print("❌ EXE文件未找到")
            return False
            
    finally:
        os.chdir(original_dir)

def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 添加源码路径
sys.path.insert(0, str(Path.cwd()))
sys.path.insert(0, str(Path.cwd() / "src"))

block_cipher = None

# 数据文件列表
datas = [
    ('src', 'src'),
    ('data', 'data'),
    ('config', 'config'),
    ('logs', 'logs'),
    ('*.json', '.'),
    ('*.py', '.'),
    ('README.md', '.'),
    ('*.png', '.'),
]

# 隐藏导入
hiddenimports = [
    'src.lottery_prediction_system',
    'src.perfect_prediction_system',
    'src.dynamic_fusion_manager_v3',
    'src.fusion_manager',
    'src.enhanced_feature_engineering_v2',
    'src.enhanced_ml_integration',
    'src.stability_optimizer_v3',
    'src.unified_config_manager',
    'src.resource_path_handler',
    'src.prediction_coordinator',
    'src.module_manager',
    'src.optimized_system_integrator',
    'src.ml_module_adapter',
    'src.auto_feature_selector',
    'src.consistency_predictor',
    'src.historical_simulation_predictor',
    'src.special_number_predictor',
    'special_number_predictor',
    'consistent_predictor',
    'historical_backtest',
    'enhanced_hit_rate_optimizer',
    'optimal_pattern_selector',
    'adaptive_optimizer',
    'performance_monitor',
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'sqlite3',
    'pandas',
    'numpy',
    'scikit-learn',
    'joblib',
    'datetime',
    'json',
    'random',
    'math',
    'statistics',
    'collections',
    'itertools',
    'functools',
    'pathlib',
    'logging',
    'warnings',
]

a = Analysis(
    ['lottery_prediction_gui.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='六合彩预测系统_v2.0.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 设置为False隐藏控制台
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='六合彩预测系统_v2.0.0',
)
'''
    
    with open("lottery_system.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)

def prepare_resources():
    """准备资源文件"""
    # 确保必要的目录存在
    dirs_to_create = ["data", "config", "logs", "src"]
    for dir_name in dirs_to_create:
        Path(dir_name).mkdir(exist_ok=True)
    
    # 创建启动脚本
    create_startup_script()
    
    # 创建配置文件
    create_default_configs()

def create_startup_script():
    """创建启动脚本"""
    startup_content = '''"""
六合彩预测系统启动脚本
"""

import sys
import os
from pathlib import Path

def setup_environment():
    """设置运行环境"""
    # 获取可执行文件目录
    if getattr(sys, 'frozen', False):
        # 打包后的环境
        base_path = Path(sys._MEIPASS)
        app_path = Path(sys.executable).parent
    else:
        # 开发环境
        base_path = Path(__file__).parent
        app_path = base_path
    
    # 添加路径
    sys.path.insert(0, str(base_path))
    sys.path.insert(0, str(base_path / "src"))
    
    # 设置工作目录
    os.chdir(app_path)
    
    return base_path, app_path

if __name__ == "__main__":
    try:
        # 设置环境
        base_path, app_path = setup_environment()
        
        # 启动主程序
        from lottery_prediction_gui import LotteryPredictionGUI
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        window = LotteryPredictionGUI()
        window.show()
        
        sys.exit(app.exec_())
        
    except Exception as e:
        import traceback
        error_msg = f"启动失败: {e}\\n\\n详细错误:\\n{traceback.format_exc()}"
        
        try:
            from PyQt5.QtWidgets import QApplication, QMessageBox
            app = QApplication(sys.argv)
            QMessageBox.critical(None, "启动错误", error_msg)
        except:
            print(error_msg)
            input("按回车键退出...")
'''
    
    with open("startup.py", "w", encoding="utf-8") as f:
        f.write(startup_content)

def create_default_configs():
    """创建默认配置文件"""
    # 创建基础配置
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    # 主配置文件
    main_config = {
        "version": "2.0.0",
        "app_name": "六合彩预测系统",
        "database_path": "data/lottery.db",
        "log_level": "INFO",
        "auto_backup": True,
        "backup_interval": 7
    }
    
    import json
    with open(config_dir / "main_config.json", "w", encoding="utf-8") as f:
        json.dump(main_config, f, ensure_ascii=False, indent=2)

def post_process():
    """后处理"""
    dist_dir = Path("dist/六合彩预测系统_v2.0.0")
    
    if dist_dir.exists():
        # 复制额外的文件
        extra_files = [
            "README.md",
            "强制启动.bat",
            "启动系统.bat"
        ]
        
        for file_name in extra_files:
            if Path(file_name).exists():
                shutil.copy2(file_name, dist_dir)
        
        # 创建快捷启动脚本
        create_launcher_scripts(dist_dir)

def create_launcher_scripts(dist_dir):
    """创建启动脚本"""
    # Windows批处理文件 (使用ASCII字符)
    bat_content = '''@echo off
chcp 65001 > nul
echo Starting Lottery Prediction System...
echo.

REM Check if EXE file exists
if not exist "六合彩预测系统_v2.0.0.exe" (
    echo Error: Main program file not found
    pause
    exit /b 1
)

REM Start program
echo Starting program...
start "" "六合彩预测系统_v2.0.0.exe"

REM Wait for program to start
timeout /t 2 /nobreak > nul
echo Program started successfully
'''

    with open(dist_dir / "启动系统.bat", "w", encoding="utf-8") as f:
        f.write(bat_content)
    
    # PowerShell脚本
    ps1_content = '''# 六合彩预测系统启动脚本
Write-Host "🚀 启动六合彩预测系统..." -ForegroundColor Green

# 检查EXE文件
if (-not (Test-Path "六合彩预测系统_v2.0.0.exe")) {
    Write-Host "❌ 找不到主程序文件" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# 启动程序
Write-Host "✅ 正在启动程序..." -ForegroundColor Green
Start-Process "六合彩预测系统_v2.0.0.exe"

Write-Host "✅ 程序已启动" -ForegroundColor Green
Start-Sleep -Seconds 2
'''
    
    with open(dist_dir / "启动系统.ps1", "w", encoding="utf-8") as f:
        f.write(ps1_content)

def create_release_package():
    """创建发布包"""
    print("\n📦 创建发布包...")
    
    dist_dir = Path("dist/六合彩预测系统_v2.0.0")
    if not dist_dir.exists():
        print("❌ 发布目录不存在")
        return
    
    # 创建发布说明
    readme_content = '''# 六合彩预测系统 v2.0.0

## 🎉 功能特点

✅ **特码预测**: 智能预测特码号码
✅ **完美预测系统**: 多模组融合预测
✅ **性能监控**: 实时监控各模组性能
✅ **增强回测**: 最优模式选择和自适应优化
✅ **数据管理**: 完整的数据导入导出功能
✅ **融合配置**: 灵活的融合策略配置

## 🚀 使用方法

1. **启动程序**:
   - 双击 `启动系统.bat` (推荐)
   - 或双击 `六合彩预测系统_v2.0.0.exe`

2. **导入数据**:
   - 切换到 "📊 数据管理" 标签页
   - 点击 "📂 选择文件" 选择CSV数据文件
   - 点击 "📥 导入到数据库"

3. **运行预测**:
   - 切换到 "🎯 特码预测" 或 "⭐ 完美预测系统" 标签页
   - 设置预测参数
   - 点击相应的预测按钮

## 📋 系统要求

- Windows 10/11 (64位)
- 至少 4GB 内存
- 至少 1GB 可用磁盘空间

## 🔧 故障排除

如果程序无法启动:
1. 以管理员身份运行
2. 检查防病毒软件是否阻止
3. 确保系统已安装 Visual C++ 运行库

## 📞 技术支持

如有问题请联系技术支持。

---
版本: v2.0.0
构建日期: 2025-06-24
'''
    
    with open(dist_dir / "使用说明.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print(f"✅ 发布包创建完成: {dist_dir.absolute()}")

def main():
    """主函数"""
    print("🎯 六合彩预测系统 EXE 打包工具")
    print("=" * 70)
    
    success = create_exe_package()
    
    if success:
        print("\n🎉 EXE打包完成！")
        print("📁 输出目录: dist/六合彩预测系统_v2.0.0/")
        print("🚀 可执行文件: 六合彩预测系统_v2.0.0.exe")
        print("\n📋 下一步:")
        print("  1. 测试EXE文件是否正常运行")
        print("  2. 检查所有功能是否正常")
        print("  3. 创建安装包或压缩包分发")
    else:
        print("\n❌ EXE打包失败")
        print("请检查错误信息并重试")
    
    return success

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
