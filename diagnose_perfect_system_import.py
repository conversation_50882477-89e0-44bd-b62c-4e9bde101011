"""
诊断完美预测系统导入问题
"""

import sys
import os
from pathlib import Path

def diagnose_import_issues():
    """诊断导入问题"""
    print("🔍 诊断完美预测系统导入问题")
    print("=" * 50)
    
    # 检查当前工作目录
    current_dir = Path.cwd()
    print(f"📁 当前工作目录: {current_dir}")
    
    # 检查Windows版本目录
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    if windows_dir.exists():
        print(f"✅ Windows版本目录存在: {windows_dir}")
        
        # 切换到Windows版本目录进行测试
        os.chdir(windows_dir)
        print(f"📁 切换到目录: {windows_dir}")
        
        # 添加路径到sys.path
        sys.path.insert(0, str(Path.cwd()))
        sys.path.insert(0, str(Path.cwd() / "src"))
        
        print(f"📋 Python路径:")
        for i, path in enumerate(sys.path[:5]):
            print(f"  {i}: {path}")
        
    else:
        print(f"❌ Windows版本目录不存在: {windows_dir}")
        return False
    
    print("\n" + "=" * 50)
    print("🧪 测试模块导入")
    print("=" * 50)
    
    # 测试各个模块的导入
    modules_to_test = [
        ("special_number_predictor", "SpecialNumberPredictor"),
        ("consistent_predictor", "ConsistentSpecialNumberPredictor"),
        ("historical_backtest", "HistoricalBacktestSystem"),
        ("consistency_test", "test_prediction_consistency"),
        ("src.perfect_prediction_system", "PerfectPredictionSystem"),
        ("src.fusion_manager", "FusionManager")
    ]
    
    import_results = {}
    
    for module_name, class_name in modules_to_test:
        print(f"\n🔧 测试导入: {module_name}.{class_name}")
        try:
            # 尝试导入模块
            module = __import__(module_name, fromlist=[class_name])
            
            # 尝试获取类
            cls = getattr(module, class_name)
            
            print(f"✅ 成功导入: {module_name}.{class_name}")
            import_results[module_name] = True
            
        except ImportError as e:
            print(f"❌ 导入失败: {module_name} - ImportError: {e}")
            import_results[module_name] = False
            
        except AttributeError as e:
            print(f"❌ 属性错误: {module_name} - AttributeError: {e}")
            import_results[module_name] = False
            
        except Exception as e:
            print(f"❌ 其他错误: {module_name} - {type(e).__name__}: {e}")
            import_results[module_name] = False
    
    print("\n" + "=" * 50)
    print("📊 导入结果总结")
    print("=" * 50)
    
    success_count = sum(import_results.values())
    total_count = len(import_results)
    
    for module_name, success in import_results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {module_name}: {status}")
    
    print(f"\n📈 总体结果: {success_count}/{total_count} 模块导入成功")
    
    # 检查关键文件是否存在
    print("\n" + "=" * 50)
    print("📁 检查关键文件")
    print("=" * 50)
    
    key_files = [
        "special_number_predictor.py",
        "consistent_predictor.py", 
        "historical_backtest.py",
        "consistency_test.py",
        "src/perfect_prediction_system.py",
        "src/fusion_manager.py"
    ]
    
    for file_path in key_files:
        file_obj = Path(file_path)
        if file_obj.exists():
            size = file_obj.stat().st_size
            print(f"✅ {file_path}: 存在 ({size} bytes)")
        else:
            print(f"❌ {file_path}: 不存在")
    
    # 如果有导入失败，尝试修复
    if success_count < total_count:
        print("\n" + "=" * 50)
        print("🔧 尝试修复导入问题")
        print("=" * 50)
        
        # 检查缺失的文件
        missing_files = []
        for file_path in key_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            print("缺失的文件:")
            for file_path in missing_files:
                print(f"  ❌ {file_path}")
            
            # 尝试从主目录复制
            print("\n🔄 尝试从主目录复制缺失文件...")
            main_dir = Path("../../")  # 回到主目录
            
            for file_path in missing_files:
                source_file = main_dir / file_path
                target_file = Path(file_path)
                
                if source_file.exists():
                    try:
                        # 确保目标目录存在
                        target_file.parent.mkdir(parents=True, exist_ok=True)
                        
                        # 复制文件
                        import shutil
                        shutil.copy2(source_file, target_file)
                        print(f"✅ 复制成功: {file_path}")
                        
                    except Exception as e:
                        print(f"❌ 复制失败: {file_path} - {e}")
                else:
                    print(f"⚠️ 源文件不存在: {source_file}")
    
    return success_count == total_count

def test_perfect_system_initialization():
    """测试完美预测系统初始化"""
    print("\n" + "=" * 50)
    print("🚀 测试完美预测系统初始化")
    print("=" * 50)
    
    try:
        from src.perfect_prediction_system import PerfectPredictionSystem
        
        print("✅ 成功导入 PerfectPredictionSystem")
        
        # 创建实例
        system = PerfectPredictionSystem()
        print("✅ 成功创建 PerfectPredictionSystem 实例")
        
        # 初始化模块
        system.initialize_modules()
        print("✅ 成功初始化所有模块")
        
        return True
        
    except Exception as e:
        print(f"❌ 完美预测系统初始化失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🔍 完美预测系统导入问题诊断工具")
    print("=" * 60)
    
    # 诊断导入问题
    import_success = diagnose_import_issues()
    
    if import_success:
        print("\n🎉 所有模块导入成功！")
        
        # 测试完美预测系统初始化
        init_success = test_perfect_system_initialization()
        
        if init_success:
            print("\n🎉 完美预测系统完全正常！")
            print("\n📝 建议:")
            print("  1. 重新启动GUI程序")
            print("  2. 检查完美预测系统是否可用")
            return True
        else:
            print("\n⚠️ 模块导入成功，但初始化失败")
            return False
    else:
        print("\n❌ 部分模块导入失败")
        print("\n📝 建议:")
        print("  1. 检查缺失的文件")
        print("  2. 重新运行修复脚本")
        print("  3. 确保所有依赖已安装")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*60}")
    if success:
        print("✅ 诊断完成：问题已解决！")
    else:
        print("❌ 诊断完成：仍有问题需要解决")
    
    input("\n按回车键退出...")
