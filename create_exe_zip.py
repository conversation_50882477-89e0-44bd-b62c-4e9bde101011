"""
创建EXE封包的ZIP压缩包
"""

import zipfile
import os
from pathlib import Path

def create_exe_zip():
    """创建EXE的ZIP压缩包"""
    print("📦 创建EXE封包ZIP压缩包...")
    
    # 源目录和目标文件
    source_dir = Path("releases/complete_exe/LotteryPredictionSystem_Complete_v2.0.0_20250624")
    zip_file = Path("releases/LotteryPredictionSystem_Complete_EXE_v2.0.0_20250624.zip")
    
    if not source_dir.exists():
        print(f"❌ 源目录不存在: {source_dir}")
        return False
    
    print(f"📁 源目录: {source_dir}")
    print(f"📦 目标文件: {zip_file}")
    
    try:
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in source_dir.rglob('*'):
                if file_path.is_file():
                    # 计算相对路径
                    arcname = file_path.relative_to(source_dir.parent)
                    zipf.write(file_path, arcname)
                    print(f"  ✅ 添加: {file_path.name}")
        
        # 获取压缩包大小
        zip_size = zip_file.stat().st_size / (1024 * 1024)
        print(f"✅ ZIP压缩包已创建: {zip_file}")
        print(f"📦 压缩包大小: {zip_size:.1f}MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建ZIP失败: {e}")
        return False

def create_installation_guide():
    """创建安装使用指南"""
    print("\n📖 创建安装使用指南...")
    
    guide_content = '''# 六合彩预测系统 v2.0.0 - EXE完整版安装使用指南

## 🎊 欢迎使用六合彩预测系统 EXE完整版

### 📋 版本信息
- **版本号**: v2.0.0
- **发布日期**: 2025年6月24日
- **包类型**: EXE完整版（集成所有依赖）
- **文件大小**: 约190MB
- **系统要求**: Windows 7/8/10/11 (64位)

## 🚀 快速安装使用

### 步骤1：下载解压
1. 下载 `LotteryPredictionSystem_Complete_EXE_v2.0.0_20250624.zip`
2. 解压到任意目录（建议：`C:\\LotteryPrediction\\`）
3. 确保解压后的目录包含以下文件：
   - `LotteryPredictionSystem_Complete_v2.0.0_20250624.exe` (主程序)
   - `启动系统.bat` (启动脚本)
   - `README.md` (说明文档)
   - `卸载系统.bat` (卸载脚本)

### 步骤2：启动程序
**方法1：使用启动脚本（推荐）**
- 双击 `启动系统.bat`
- 等待程序启动

**方法2：直接运行EXE**
- 双击 `LotteryPredictionSystem_Complete_v2.0.0_20250624.exe`
- 首次启动可能需要1-2分钟

### 步骤3：开始使用
1. 程序启动后会显示主界面
2. 首次使用建议先导入历史数据
3. 选择预测功能开始使用

## 🔧 主要功能特性

### ✅ 预测功能
- **特码预测**: 推荐16个最有可能的特码号码
- **一致性预测**: 使用确定性算法保证结果一致
- **完美预测系统**: 融合4大分析模组的综合预测
- **生肖预测**: 多维度生肖分析和预测

### ✅ 分析模组
- **传统统计分析**: 频率分析、趋势分析、遗漏分析
- **机器学习模组**: 集成5种机器学习算法
- **多维生肖扩展**: 生肖、五行、季节等多维度分析
- **特码生肖专项**: 专门针对特码的生肖分析

### ✅ 回测功能
- **历史回测**: 验证预测算法的历史表现
- **增强回测**: 自动优化配置参数
- **综合评估**: 多维度性能评估和报告
- **配置优化**: 智能寻找最优参数组合

### ✅ 数据管理
- **数据导入**: 支持CSV格式的历史开奖数据导入
- **数据清洗**: 自动检测和修复数据异常
- **数据预览**: 完整的数据展示和统计
- **数据备份**: 自动备份重要数据

## 📊 技术特点

### 🤖 机器学习集成
- **5种算法**: RandomForest, GradientBoosting, SVM, KNN, NaiveBayes
- **50个特征**: 综合特征工程提取
- **动态融合**: 智能权重优化
- **性能监控**: 实时算法性能跟踪

### 🔄 动态融合管理
- **多层融合**: 静态权重、动态评分、投票机制
- **智能优化**: 命中率优化器、智能筛选器
- **反馈学习**: 基于历史表现的持续改进
- **配置管理**: 灵活的参数调整系统

## ⚠️ 重要注意事项

### 🛡️ 安全提醒
- **杀毒软件**: 可能被误报为病毒，请添加到白名单
- **防火墙**: 如有网络访问提示，请允许
- **管理员权限**: 如遇权限问题，请以管理员身份运行

### 💾 系统要求
- **操作系统**: Windows 7/8/10/11 (64位)
- **内存**: 至少4GB可用内存（推荐8GB）
- **存储**: 至少1GB可用磁盘空间
- **处理器**: 双核或更高（推荐四核）

### 🚀 性能优化
- **首次启动**: 需要1-2分钟解压和初始化
- **后续启动**: 通常30秒内完成
- **内存使用**: 运行时占用约500MB-1GB内存
- **CPU使用**: 预测计算时会短暂占用较高CPU

## 🔧 故障排除

### 问题1：程序无法启动
**可能原因**: 杀毒软件拦截、权限不足、文件损坏
**解决方案**:
1. 关闭杀毒软件或添加到白名单
2. 以管理员身份运行
3. 重新下载解压文件
4. 检查Windows系统更新

### 问题2：启动缓慢
**可能原因**: 首次启动正常现象
**解决方案**:
1. 耐心等待1-2分钟
2. 确保有足够的磁盘空间
3. 关闭其他占用内存的程序

### 问题3：界面显示异常
**可能原因**: 显示设置、分辨率问题
**解决方案**:
1. 调整Windows显示缩放设置
2. 更新显卡驱动
3. 尝试兼容性模式运行

### 问题4：预测功能异常
**可能原因**: 数据问题、配置错误
**解决方案**:
1. 检查导入的数据格式
2. 重新导入历史数据
3. 重置系统配置

## 📞 技术支持

### 🆘 获取帮助
- **问题反馈**: 详细描述问题和错误信息
- **系统信息**: 提供Windows版本和系统配置
- **错误截图**: 如有错误界面请提供截图

### 📝 使用建议
- **定期备份**: 建议定期备份预测结果和配置
- **数据更新**: 定期更新历史开奖数据
- **理性使用**: 预测结果仅供参考，请理性对待

## 🗑️ 卸载说明

### 完全卸载
1. 运行 `卸载系统.bat`
2. 或手动删除整个程序目录
3. 清理可能的临时文件

### 保留数据卸载
1. 备份程序目录中的数据文件
2. 执行卸载操作
3. 重新安装时恢复数据文件

---

## 📄 版权声明
© 2025 六合彩预测系统 v2.0.0 - EXE完整版
本软件仅供学习研究使用，预测结果仅供参考。

---

**祝您使用愉快！** 🎊✨
'''
    
    guide_file = Path("releases/六合彩预测系统_EXE版_安装使用指南.md")
    with open(guide_file, "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print(f"✅ 安装使用指南已创建: {guide_file}")

def main():
    """主函数"""
    print("🎊 EXE封包ZIP压缩工具")
    print("=" * 50)
    
    # 创建ZIP压缩包
    zip_success = create_exe_zip()
    
    # 创建安装指南
    create_installation_guide()
    
    if zip_success:
        print("\n" + "=" * 50)
        print("🎉 EXE封包ZIP创建完成！")
        print("=" * 50)
        print("📦 发布文件:")
        print("  📄 LotteryPredictionSystem_Complete_EXE_v2.0.0_20250624.zip")
        print("  📖 六合彩预测系统_EXE版_安装使用指南.md")
        print("\n💡 分发说明:")
        print("1. 将ZIP文件和安装指南一起提供给用户")
        print("2. 用户下载后解压ZIP文件")
        print("3. 按照安装指南操作即可")
        print("=" * 50)
    else:
        print("\n❌ ZIP创建失败")

if __name__ == "__main__":
    main()
