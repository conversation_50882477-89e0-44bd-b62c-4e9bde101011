"""
五行特征映射器
"""
from typing import Dict, List
from loguru import logger

class WuxingMapper:
    def __init__(self):
        # 五行基础映射（按年份会有变化，这里用简化版本）
        self.wuxing_mapping = {
            "金": [4, 5, 12, 13, 20, 21, 28, 29, 36, 37, 44, 45],
            "木": [1, 8, 9, 16, 17, 24, 25, 32, 33, 40, 41, 48, 49],
            "水": [6, 7, 14, 15, 22, 23, 30, 31, 38, 39, 46, 47],
            "火": [2, 3, 10, 11, 18, 19, 26, 27, 34, 35, 42, 43],
            "土": []  # 土通常为特殊情况
        }
        
        # 五行相生相克关系
        self.wuxing_relations = {
            "相生": {"金": "水", "水": "木", "木": "火", "火": "土", "土": "金"},
            "相克": {"金": "木", "木": "土", "土": "水", "水": "火", "火": "金"}
        }
    
    def get_wuxing_by_number(self, number: int) -> str:
        """根据号码获取五行"""
        for wuxing, numbers in self.wuxing_mapping.items():
            if number in numbers:
                return wuxing
        return "土"  # 默认为土
    
    def extract_wuxing_features(self, numbers: List[int], special: int) -> Dict:
        """提取五行特征"""
        features = {}
        
        # 正码五行分布
        wuxing_count = {"金": 0, "木": 0, "水": 0, "火": 0, "土": 0}
        
        for number in numbers:
            wuxing = self.get_wuxing_by_number(number)
            wuxing_count[wuxing] += 1
        
        # 五行分布特征
        for wuxing, count in wuxing_count.items():
            features[f'regular_wuxing_{wuxing}'] = count
        
        # 特码五行
        special_wuxing = self.get_wuxing_by_number(special)
        features['special_wuxing'] = special_wuxing
        
        # 五行平衡度
        wuxing_values = list(wuxing_count.values())
        features['wuxing_balance'] = 1 - (max(wuxing_values) - min(wuxing_values)) / len(numbers)
        
        # 五行多样性
        unique_wuxing = sum(1 for count in wuxing_count.values() if count > 0)
        features['wuxing_diversity'] = unique_wuxing
        
        # 相生相克关系
        features['wuxing_harmony'] = self._calculate_wuxing_harmony(wuxing_count)
        
        return features
    
    def _calculate_wuxing_harmony(self, wuxing_count: Dict[str, int]) -> float:
        """计算五行和谐度"""
        harmony_score = 0
        total_pairs = 0
        
        wuxing_list = ["金", "木", "水", "火", "土"]
        
        for i, wuxing1 in enumerate(wuxing_list):
            for j, wuxing2 in enumerate(wuxing_list):
                if i != j and wuxing_count[wuxing1] > 0 and wuxing_count[wuxing2] > 0:
                    total_pairs += 1
                    
                    # 相生关系加分
                    if self.wuxing_relations["相生"].get(wuxing1) == wuxing2:
                        harmony_score += 1
                    # 相克关系减分
                    elif self.wuxing_relations["相克"].get(wuxing1) == wuxing2:
                        harmony_score -= 0.5
        
        return harmony_score / total_pairs if total_pairs > 0 else 0
