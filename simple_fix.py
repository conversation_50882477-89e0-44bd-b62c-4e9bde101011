"""
简化数据库修复脚本
"""
import sys
sys.path.insert(0, 'src')

print("🔧 简化数据库修复")
print("=" * 60)

try:
    # 1. 测试数据库初始化
    print("📊 测试数据库初始化...")
    from src.data_layer.database.init_db import initialize_database
    
    result = initialize_database()
    if result:
        print("✅ 数据库初始化成功")
    else:
        print("❌ 数据库初始化失败")
        exit()
    
    # 2. 测试系统组件
    print("📊 测试系统组件...")
    from src.data_layer.database.models import create_database_engine, get_session
    
    engine = create_database_engine()
    session = get_session(engine)
    print("✅ 数据库连接成功")
    
    # 3. 检查表结构
    print("📊 检查表结构...")
    from src.data_layer.database.models import LotteryData
    
    # 查询表结构
    import sqlite3
    conn = sqlite3.connect("data/lottery.db")
    cursor = conn.cursor()
    
    cursor.execute("PRAGMA table_info(lottery_data)")
    columns = cursor.fetchall()
    
    print("✅ 表结构检查:")
    for col in columns:
        print(f"   {col[1]} ({col[2]})")
    
    conn.close()
    session.close()
    
    print("\n🎊 数据库结构检查完成！")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
