"""
数据格式校验器
"""
import pandas as pd
from typing import Dict
from loguru import logger

class DataValidator:
    def __init__(self):
        pass
    
    def validate_lottery_data(self, df: pd.DataFrame) -> Dict:
        validation_result = {
            'is_valid': True,
            'errors': [],
            'summary': {}
        }
        
        # 检查必需列
        required_columns = ['issue', 'date', 'numbers', 'special']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            validation_result['is_valid'] = False
            validation_result['errors'].append(f"缺少必需列: {missing_columns}")
            return validation_result
        
        # 简单验证
        validation_result['summary'] = {
            'total_records': len(df),
            'error_count': len(validation_result['errors'])
        }
        
        return validation_result
    
    def _validate_numbers(self, value) -> tuple:
        try:
            numbers_str = str(value).replace('"', '').strip()
            numbers = [int(x.strip()) for x in numbers_str.split(',')]
            
            if len(numbers) != 6:
                return False, f"正码数量错误"
            
            if not all(1 <= n <= 49 for n in numbers):
                return False, "正码范围错误"
            
            return True, "正码格式正确"
        except:
            return False, "正码格式无效"
