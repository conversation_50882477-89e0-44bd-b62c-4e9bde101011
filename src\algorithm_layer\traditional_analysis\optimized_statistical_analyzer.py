"""
优化的统计分析器 - 增强的统计分析功能
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from scipy import stats
from scipy.stats import chi2_contingency, kstest, normaltest
import json
from datetime import datetime, timedelta
import warnings

class OptimizedStatisticalAnalyzer:
    """优化的统计分析器"""
    
    def __init__(self):
        """初始化优化的统计分析器"""
        self.analysis_cache = {}
        self.analysis_history = []
        
        # 生肖映射（优化版）
        self.zodiac_mapping = {
            2024: {1: '鼠', 13: '牛', 25: '虎', 37: '兔', 49: '龙', 12: '蛇',
                   24: '马', 36: '羊', 48: '猴', 11: '鸡', 23: '狗', 35: '猪',
                   2: '鼠', 14: '牛', 26: '虎', 38: '兔', 3: '龙', 15: '蛇',
                   27: '马', 39: '羊', 4: '猴', 16: '鸡', 28: '狗', 40: '猪',
                   5: '鼠', 17: '牛', 29: '虎', 41: '兔', 6: '龙', 18: '蛇',
                   30: '马', 42: '羊', 7: '猴', 19: '鸡', 31: '狗', 43: '猪',
                   8: '鼠', 20: '牛', 32: '虎', 44: '兔', 9: '龙', 21: '蛇',
                   33: '马', 45: '羊', 10: '猴', 22: '鸡', 34: '狗', 46: '猪',
                   47: '鼠'}
        }
        
        # 五行映射
        self.wuxing_mapping = {
            '金': [4, 5, 18, 19, 32, 33, 46, 47],
            '木': [1, 8, 15, 22, 29, 36, 43],
            '水': [6, 7, 20, 21, 34, 35, 48, 49],
            '火': [2, 9, 16, 23, 30, 37, 44],
            '土': [3, 10, 11, 17, 24, 25, 31, 38, 39, 45]
        }
    
    def comprehensive_frequency_analysis(self, 
                                       data: pd.DataFrame,
                                       analysis_periods: List[int] = [10, 30, 50, 100]) -> Dict[str, Any]:
        """
        综合频率分析
        
        Args:
            data: 历史数据
            analysis_periods: 分析周期列表
        """
        
        print("📊 执行综合频率分析...")
        
        results = {
            'analysis_date': datetime.now().isoformat(),
            'total_records': len(data),
            'period_analysis': {},
            'trend_analysis': {},
            'statistical_tests': {}
        }
        
        # 提取所有号码
        all_numbers = self._extract_all_numbers(data)
        
        for period in analysis_periods:
            if len(data) < period:
                continue
            
            recent_data = data.tail(period)
            recent_numbers = self._extract_all_numbers(recent_data)
            
            # 基础频率统计
            freq_stats = self._calculate_frequency_stats(recent_numbers)
            
            # 分布检验
            distribution_tests = self._test_distributions(recent_numbers)
            
            # 趋势分析
            trend_analysis = self._analyze_trends(recent_data)
            
            results['period_analysis'][f'last_{period}'] = {
                'frequency_stats': freq_stats,
                'distribution_tests': distribution_tests,
                'trend_analysis': trend_analysis
            }
        
        # 整体趋势分析
        results['trend_analysis'] = self._comprehensive_trend_analysis(data)
        
        # 统计检验
        results['statistical_tests'] = self._comprehensive_statistical_tests(data)
        
        # 缓存结果
        cache_key = f"freq_analysis_{len(data)}_{hash(str(analysis_periods))}"
        self.analysis_cache[cache_key] = results
        
        return results
    
    def _extract_all_numbers(self, data: pd.DataFrame) -> List[int]:
        """提取所有号码"""
        all_numbers = []
        
        for _, row in data.iterrows():
            try:
                regular_numbers = json.loads(row['regular_numbers']) if isinstance(row['regular_numbers'], str) else row['regular_numbers']
                special_number = row['special_number']
                
                all_numbers.extend(regular_numbers + [special_number])
            except:
                continue
        
        return all_numbers
    
    def _calculate_frequency_stats(self, numbers: List[int]) -> Dict[str, Any]:
        """计算频率统计"""
        
        if not numbers:
            return {}
        
        # 基础频率统计
        freq_counter = {}
        for num in numbers:
            freq_counter[num] = freq_counter.get(num, 0) + 1
        
        # 统计指标
        frequencies = list(freq_counter.values())
        
        stats_result = {
            'total_numbers': len(numbers),
            'unique_numbers': len(freq_counter),
            'frequency_distribution': freq_counter,
            'most_frequent': max(freq_counter.items(), key=lambda x: x[1]) if freq_counter else None,
            'least_frequent': min(freq_counter.items(), key=lambda x: x[1]) if freq_counter else None,
            'frequency_stats': {
                'mean': np.mean(frequencies),
                'std': np.std(frequencies),
                'min': np.min(frequencies),
                'max': np.max(frequencies),
                'median': np.median(frequencies)
            }
        }
        
        # 热号和冷号
        sorted_freq = sorted(freq_counter.items(), key=lambda x: x[1], reverse=True)
        hot_numbers = [num for num, freq in sorted_freq[:10]]
        cold_numbers = [num for num, freq in sorted_freq[-10:]]
        
        stats_result['hot_numbers'] = hot_numbers
        stats_result['cold_numbers'] = cold_numbers
        
        return stats_result
    
    def _test_distributions(self, numbers: List[int]) -> Dict[str, Any]:
        """分布检验"""
        
        if len(numbers) < 8:
            return {'error': '数据不足'}
        
        results = {}
        
        # 均匀分布检验
        try:
            # 期望每个号码出现的频率
            expected_freq = len(numbers) / 49
            observed_freq = [numbers.count(i) for i in range(1, 50)]
            
            # 卡方检验
            chi2_stat, chi2_p = stats.chisquare(observed_freq)
            
            results['uniformity_test'] = {
                'chi2_statistic': chi2_stat,
                'p_value': chi2_p,
                'is_uniform': chi2_p > 0.05,
                'interpretation': '均匀分布' if chi2_p > 0.05 else '非均匀分布'
            }
        except Exception as e:
            results['uniformity_test'] = {'error': str(e)}
        
        # 正态性检验
        try:
            stat, p_value = normaltest(numbers)
            results['normality_test'] = {
                'statistic': stat,
                'p_value': p_value,
                'is_normal': p_value > 0.05,
                'interpretation': '正态分布' if p_value > 0.05 else '非正态分布'
            }
        except Exception as e:
            results['normality_test'] = {'error': str(e)}
        
        # 随机性检验
        try:
            # 游程检验
            median_val = np.median(numbers)
            runs = []
            current_run = 1
            
            for i in range(1, len(numbers)):
                if (numbers[i] > median_val) == (numbers[i-1] > median_val):
                    current_run += 1
                else:
                    runs.append(current_run)
                    current_run = 1
            runs.append(current_run)
            
            # 期望游程数
            n1 = sum(1 for x in numbers if x > median_val)
            n2 = len(numbers) - n1
            expected_runs = (2 * n1 * n2) / (n1 + n2) + 1
            
            results['randomness_test'] = {
                'observed_runs': len(runs),
                'expected_runs': expected_runs,
                'is_random': abs(len(runs) - expected_runs) < 2 * np.sqrt(expected_runs),
                'interpretation': '随机' if abs(len(runs) - expected_runs) < 2 * np.sqrt(expected_runs) else '非随机'
            }
        except Exception as e:
            results['randomness_test'] = {'error': str(e)}
        
        return results
    
    def _analyze_trends(self, data: pd.DataFrame) -> Dict[str, Any]:
        """趋势分析"""
        
        if len(data) < 5:
            return {'error': '数据不足'}
        
        trends = {}
        
        # 和值趋势
        sums = []
        for _, row in data.iterrows():
            try:
                regular_numbers = json.loads(row['regular_numbers']) if isinstance(row['regular_numbers'], str) else row['regular_numbers']
                special_number = row['special_number']
                total_sum = sum(regular_numbers) + special_number
                sums.append(total_sum)
            except:
                continue
        
        if sums:
            trends['sum_trend'] = {
                'values': sums,
                'mean': np.mean(sums),
                'std': np.std(sums),
                'trend_direction': 'increasing' if sums[-1] > sums[0] else 'decreasing',
                'correlation_with_time': np.corrcoef(range(len(sums)), sums)[0, 1] if len(sums) > 1 else 0
            }
        
        # 奇偶比例趋势
        odd_ratios = []
        for _, row in data.iterrows():
            try:
                regular_numbers = json.loads(row['regular_numbers']) if isinstance(row['regular_numbers'], str) else row['regular_numbers']
                special_number = row['special_number']
                all_nums = regular_numbers + [special_number]
                odd_count = sum(1 for num in all_nums if num % 2 == 1)
                odd_ratios.append(odd_count / len(all_nums))
            except:
                continue
        
        if odd_ratios:
            trends['odd_even_trend'] = {
                'odd_ratios': odd_ratios,
                'mean_odd_ratio': np.mean(odd_ratios),
                'std_odd_ratio': np.std(odd_ratios),
                'trend_direction': 'more_odd' if odd_ratios[-1] > 0.5 else 'more_even'
            }
        
        return trends
    
    def _comprehensive_trend_analysis(self, data: pd.DataFrame) -> Dict[str, Any]:
        """综合趋势分析"""
        
        trends = {
            'long_term_patterns': {},
            'cyclical_patterns': {},
            'seasonal_patterns': {}
        }
        
        # 长期模式
        if len(data) >= 100:
            # 分段分析
            segment_size = len(data) // 4
            segments = []
            
            for i in range(4):
                start_idx = i * segment_size
                end_idx = (i + 1) * segment_size if i < 3 else len(data)
                segment_data = data.iloc[start_idx:end_idx]
                segment_numbers = self._extract_all_numbers(segment_data)
                segment_stats = self._calculate_frequency_stats(segment_numbers)
                segments.append(segment_stats)
            
            trends['long_term_patterns']['segments'] = segments
        
        # 周期性模式
        if len(data) >= 50:
            # 按星期分析
            data_with_weekday = data.copy()
            data_with_weekday['draw_date'] = pd.to_datetime(data_with_weekday['draw_date'])
            data_with_weekday['weekday'] = data_with_weekday['draw_date'].dt.dayofweek
            
            weekday_patterns = {}
            for weekday in range(7):
                weekday_data = data_with_weekday[data_with_weekday['weekday'] == weekday]
                if len(weekday_data) > 0:
                    weekday_numbers = self._extract_all_numbers(weekday_data)
                    weekday_patterns[weekday] = self._calculate_frequency_stats(weekday_numbers)
            
            trends['cyclical_patterns']['weekday_patterns'] = weekday_patterns
        
        return trends
    
    def _comprehensive_statistical_tests(self, data: pd.DataFrame) -> Dict[str, Any]:
        """综合统计检验"""
        
        tests = {}
        
        # 独立性检验
        if len(data) >= 30:
            try:
                # 构建连续开奖的列联表
                consecutive_pairs = []
                for i in range(len(data) - 1):
                    current_numbers = self._extract_single_draw_numbers(data.iloc[i])
                    next_numbers = self._extract_single_draw_numbers(data.iloc[i + 1])
                    
                    if current_numbers and next_numbers:
                        # 简化：检查是否有重复号码
                        has_repeat = len(set(current_numbers) & set(next_numbers)) > 0
                        consecutive_pairs.append(has_repeat)
                
                if consecutive_pairs:
                    repeat_count = sum(consecutive_pairs)
                    no_repeat_count = len(consecutive_pairs) - repeat_count
                    
                    # 期望值（假设独立）
                    expected_repeat = len(consecutive_pairs) * 0.5  # 简化假设
                    
                    tests['independence_test'] = {
                        'observed_repeats': repeat_count,
                        'expected_repeats': expected_repeat,
                        'is_independent': abs(repeat_count - expected_repeat) < np.sqrt(expected_repeat),
                        'interpretation': '独立' if abs(repeat_count - expected_repeat) < np.sqrt(expected_repeat) else '相关'
                    }
            except Exception as e:
                tests['independence_test'] = {'error': str(e)}
        
        return tests
    
    def _extract_single_draw_numbers(self, row: pd.Series) -> List[int]:
        """提取单次开奖号码"""
        try:
            regular_numbers = json.loads(row['regular_numbers']) if isinstance(row['regular_numbers'], str) else row['regular_numbers']
            special_number = row['special_number']
            return regular_numbers + [special_number]
        except:
            return []
    
    def advanced_pattern_analysis(self, data: pd.DataFrame) -> Dict[str, Any]:
        """高级模式分析"""
        
        print("🔍 执行高级模式分析...")
        
        patterns = {
            'number_combinations': {},
            'sequence_patterns': {},
            'gap_analysis': {},
            'zodiac_analysis': {},
            'wuxing_analysis': {}
        }
        
        # 号码组合分析
        patterns['number_combinations'] = self._analyze_number_combinations(data)
        
        # 序列模式分析
        patterns['sequence_patterns'] = self._analyze_sequence_patterns(data)
        
        # 间隔分析
        patterns['gap_analysis'] = self._analyze_number_gaps(data)
        
        # 生肖分析
        patterns['zodiac_analysis'] = self._analyze_zodiac_patterns(data)
        
        # 五行分析
        patterns['wuxing_analysis'] = self._analyze_wuxing_patterns(data)
        
        return patterns
    
    def _analyze_number_combinations(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析号码组合"""
        
        combinations = {
            'pair_frequency': {},
            'triplet_frequency': {},
            'common_combinations': []
        }
        
        # 分析号码对
        pair_freq = {}
        for _, row in data.iterrows():
            numbers = self._extract_single_draw_numbers(row)
            if len(numbers) >= 2:
                for i in range(len(numbers)):
                    for j in range(i + 1, len(numbers)):
                        pair = tuple(sorted([numbers[i], numbers[j]]))
                        pair_freq[pair] = pair_freq.get(pair, 0) + 1
        
        # 最常见的号码对
        top_pairs = sorted(pair_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        combinations['pair_frequency'] = dict(top_pairs)
        
        return combinations
    
    def _analyze_sequence_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析序列模式"""
        
        patterns = {
            'consecutive_numbers': {},
            'arithmetic_sequences': {},
            'geometric_patterns': {}
        }
        
        # 连续号码分析
        consecutive_counts = {}
        for _, row in data.iterrows():
            numbers = self._extract_single_draw_numbers(row)
            if numbers:
                sorted_numbers = sorted(numbers)
                consecutive_count = 0
                
                for i in range(len(sorted_numbers) - 1):
                    if sorted_numbers[i + 1] - sorted_numbers[i] == 1:
                        consecutive_count += 1
                
                consecutive_counts[consecutive_count] = consecutive_counts.get(consecutive_count, 0) + 1
        
        patterns['consecutive_numbers'] = consecutive_counts
        
        return patterns
    
    def _analyze_number_gaps(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析号码间隔"""
        
        gaps = {
            'number_gaps': {},
            'gap_statistics': {}
        }
        
        # 计算每个号码的出现间隔
        number_positions = {}
        for idx, row in data.iterrows():
            numbers = self._extract_single_draw_numbers(row)
            for num in numbers:
                if num not in number_positions:
                    number_positions[num] = []
                number_positions[num].append(idx)
        
        # 计算间隔统计
        gap_stats = {}
        for num, positions in number_positions.items():
            if len(positions) > 1:
                gaps_list = [positions[i] - positions[i-1] for i in range(1, len(positions))]
                gap_stats[num] = {
                    'mean_gap': np.mean(gaps_list),
                    'std_gap': np.std(gaps_list),
                    'min_gap': np.min(gaps_list),
                    'max_gap': np.max(gaps_list)
                }
        
        gaps['gap_statistics'] = gap_stats
        
        return gaps
    
    def _analyze_zodiac_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析生肖模式"""
        
        zodiac_analysis = {
            'zodiac_frequency': {},
            'zodiac_trends': {}
        }
        
        # 生肖频率统计
        zodiac_freq = {}
        for _, row in data.iterrows():
            numbers = self._extract_single_draw_numbers(row)
            for num in numbers:
                zodiac = self.zodiac_mapping.get(2024, {}).get(num, '未知')
                zodiac_freq[zodiac] = zodiac_freq.get(zodiac, 0) + 1
        
        zodiac_analysis['zodiac_frequency'] = zodiac_freq
        
        return zodiac_analysis
    
    def _analyze_wuxing_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析五行模式"""
        
        wuxing_analysis = {
            'wuxing_frequency': {},
            'wuxing_balance': {}
        }
        
        # 五行频率统计
        wuxing_freq = {element: 0 for element in self.wuxing_mapping.keys()}
        
        for _, row in data.iterrows():
            numbers = self._extract_single_draw_numbers(row)
            for num in numbers:
                for element, element_numbers in self.wuxing_mapping.items():
                    if num in element_numbers:
                        wuxing_freq[element] += 1
                        break
        
        wuxing_analysis['wuxing_frequency'] = wuxing_freq
        
        # 五行平衡分析
        total_count = sum(wuxing_freq.values())
        if total_count > 0:
            wuxing_balance = {
                element: count / total_count 
                for element, count in wuxing_freq.items()
            }
            wuxing_analysis['wuxing_balance'] = wuxing_balance
        
        return wuxing_analysis
    
    def generate_analysis_report(self, data: pd.DataFrame) -> Dict[str, Any]:
        """生成分析报告"""
        
        print("📋 生成综合分析报告...")
        
        # 执行各种分析
        frequency_analysis = self.comprehensive_frequency_analysis(data)
        pattern_analysis = self.advanced_pattern_analysis(data)
        
        # 生成报告
        report = {
            'report_metadata': {
                'generated_at': datetime.now().isoformat(),
                'data_period': {
                    'start_date': data['draw_date'].min(),
                    'end_date': data['draw_date'].max(),
                    'total_records': len(data)
                },
                'analysis_version': '2.0'
            },
            'frequency_analysis': frequency_analysis,
            'pattern_analysis': pattern_analysis,
            'recommendations': self._generate_recommendations(frequency_analysis, pattern_analysis)
        }
        
        # 记录分析历史
        self.analysis_history.append({
            'timestamp': datetime.now().isoformat(),
            'data_records': len(data),
            'analysis_type': 'comprehensive'
        })
        
        return report
    
    def _generate_recommendations(self, 
                                frequency_analysis: Dict, 
                                pattern_analysis: Dict) -> List[str]:
        """生成分析建议"""
        
        recommendations = []
        
        # 基于频率分析的建议
        if 'period_analysis' in frequency_analysis:
            for period, analysis in frequency_analysis['period_analysis'].items():
                if 'frequency_stats' in analysis:
                    hot_numbers = analysis['frequency_stats'].get('hot_numbers', [])
                    if hot_numbers:
                        recommendations.append(f"最近{period}期热号: {hot_numbers[:5]}")
        
        # 基于模式分析的建议
        if 'zodiac_analysis' in pattern_analysis:
            zodiac_freq = pattern_analysis['zodiac_analysis'].get('zodiac_frequency', {})
            if zodiac_freq:
                top_zodiac = max(zodiac_freq.items(), key=lambda x: x[1])
                recommendations.append(f"出现频率最高的生肖: {top_zodiac[0]}")
        
        # 基于统计检验的建议
        if 'statistical_tests' in frequency_analysis:
            tests = frequency_analysis['statistical_tests']
            if 'independence_test' in tests:
                independence = tests['independence_test']
                if independence.get('interpretation') == '相关':
                    recommendations.append("号码间存在相关性，建议考虑历史模式")
        
        return recommendations
