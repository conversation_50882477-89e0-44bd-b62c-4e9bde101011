"""
数据质量指标
"""
import pandas as pd
import numpy as np
from typing import Dict, List
from datetime import datetime
from loguru import logger

class QualityMetrics:
    def __init__(self):
        self.metrics = {
            'completeness': self._calculate_completeness,
            'accuracy': self._calculate_accuracy,
            'consistency': self._calculate_consistency,
            'timeliness': self._calculate_timeliness
        }
    
    def calculate_all_metrics(self, df: pd.DataFrame) -> Dict:
        """计算所有质量指标"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'total_records': len(df),
            'metrics': {}
        }
        
        for metric_name, metric_func in self.metrics.items():
            try:
                results['metrics'][metric_name] = metric_func(df)
            except Exception as e:
                logger.error(f"计算指标 {metric_name} 失败: {e}")
                results['metrics'][metric_name] = {'score': 0, 'error': str(e)}
        
        # 计算总体质量分数
        scores = [m.get('score', 0) for m in results['metrics'].values() if 'score' in m]
        results['overall_score'] = np.mean(scores) if scores else 0
        
        return results
    
    def _calculate_completeness(self, df: pd.DataFrame) -> Dict:
        """计算完整性指标"""
        total_cells = df.size
        missing_cells = df.isnull().sum().sum()
        completeness_score = ((total_cells - missing_cells) / total_cells) * 100
        
        return {
            'score': completeness_score,
            'total_cells': total_cells,
            'missing_cells': missing_cells,
            'description': '数据完整性百分比'
        }
    
    def _calculate_accuracy(self, df: pd.DataFrame) -> Dict:
        """计算准确性指标"""
        accuracy_issues = 0
        total_records = len(df)
        
        # 检查数据格式准确性
        for index, row in df.iterrows():
            # 检查特码范围
            if 'special' in row:
                try:
                    special = int(row['special'])
                    if not (1 <= special <= 49):
                        accuracy_issues += 1
                except:
                    accuracy_issues += 1
            
            # 检查正码格式
            if 'numbers' in row:
                try:
                    numbers_str = str(row['numbers']).replace('"', '').strip()
                    numbers = [int(x.strip()) for x in numbers_str.split(',')]
                    if len(numbers) != 6 or not all(1 <= n <= 49 for n in numbers):
                        accuracy_issues += 1
                except:
                    accuracy_issues += 1
        
        accuracy_score = ((total_records - accuracy_issues) / total_records) * 100 if total_records > 0 else 0
        
        return {
            'score': accuracy_score,
            'total_records': total_records,
            'accuracy_issues': accuracy_issues,
            'description': '数据准确性百分比'
        }
    
    def _calculate_consistency(self, df: pd.DataFrame) -> Dict:
        """计算一致性指标"""
        consistency_issues = 0
        total_records = len(df)
        
        # 检查期号一致性
        if 'issue' in df.columns:
            duplicate_issues = df['issue'].duplicated().sum()
            consistency_issues += duplicate_issues
        
        # 检查日期一致性
        if 'date' in df.columns:
            duplicate_dates = df['date'].duplicated().sum()
            consistency_issues += duplicate_dates
        
        consistency_score = ((total_records - consistency_issues) / total_records) * 100 if total_records > 0 else 0
        
        return {
            'score': consistency_score,
            'total_records': total_records,
            'consistency_issues': consistency_issues,
            'description': '数据一致性百分比'
        }
    
    def _calculate_timeliness(self, df: pd.DataFrame) -> Dict:
        """计算时效性指标"""
        if 'date' not in df.columns:
            return {'score': 0, 'description': '无日期列，无法计算时效性'}
        
        try:
            dates = pd.to_datetime(df['date'])
            latest_date = dates.max()
            current_date = pd.Timestamp.now()
            
            # 计算最新数据的时效性（天数）
            days_old = (current_date - latest_date).days
            
            # 时效性评分：30天内为满分，超过30天递减
            if days_old <= 30:
                timeliness_score = 100
            elif days_old <= 90:
                timeliness_score = 100 - ((days_old - 30) * 1.5)
            else:
                timeliness_score = 10
            
            timeliness_score = max(0, timeliness_score)
            
            return {
                'score': timeliness_score,
                'latest_date': latest_date.strftime('%Y-%m-%d'),
                'days_old': days_old,
                'description': '数据时效性评分'
            }
        except Exception as e:
            return {'score': 0, 'error': str(e), 'description': '时效性计算失败'}
