"""
格式标准化器
"""
import pandas as pd
from datetime import datetime
from loguru import logger

class FormatStandardizer:
    def __init__(self):
        self.date_formats = ['%Y-%m-%d', '%Y/%m/%d', '%d/%m/%Y', '%d-%m-%Y']
    
    def standardize_lottery_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化彩票数据格式"""
        df_copy = df.copy()
        
        # 标准化日期格式
        if 'date' in df_copy.columns:
            df_copy['date'] = self._standardize_dates(df_copy['date'])
        
        # 标准化号码格式
        if 'numbers' in df_copy.columns:
            df_copy['numbers'] = self._standardize_numbers(df_copy['numbers'])
        
        # 标准化期号格式
        if 'issue' in df_copy.columns:
            df_copy['issue'] = self._standardize_issues(df_copy['issue'])
        
        return df_copy
    
    def _standardize_dates(self, date_series):
        """标准化日期格式"""
        standardized_dates = []
        for date_val in date_series:
            standardized_date = self._parse_date(date_val)
            standardized_dates.append(standardized_date)
        return pd.Series(standardized_dates)
    
    def _parse_date(self, date_val):
        """解析日期"""
        if pd.isna(date_val):
            return None
        
        date_str = str(date_val).strip()
        for fmt in self.date_formats:
            try:
                return datetime.strptime(date_str, fmt).date()
            except ValueError:
                continue
        
        logger.warning(f"无法解析日期: {date_val}")
        return None
    
    def _standardize_numbers(self, numbers_series):
        """标准化号码格式"""
        standardized_numbers = []
        for numbers_val in numbers_series:
            if pd.isna(numbers_val):
                standardized_numbers.append(None)
                continue
            
            # 清理号码字符串
            numbers_str = str(numbers_val).replace('"', '').replace("'", '').strip()
            try:
                numbers = [int(x.strip()) for x in numbers_str.split(',')]
                standardized_numbers.append(','.join(map(str, sorted(numbers))))
            except:
                logger.warning(f"无法解析号码: {numbers_val}")
                standardized_numbers.append(None)
        
        return pd.Series(standardized_numbers)
    
    def _standardize_issues(self, issue_series):
        """标准化期号格式"""
        return issue_series.astype(str).str.strip()
