
"""
增强的机器学习模组集成
支持真实ML模组与增强回测配置的协作
"""
import logging
from typing import Dict, List, Any, Optional

class EnhancedMLIntegration:
    """增强的ML模组集成"""
    
    def __init__(self, config_manager=None):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        self.performance_history = []
    
    def create_ml_module(self, db_session=None, number_attribute_mapper=None, lunar_year_manager=None):
        """创建ML模组实例"""
        try:
            # 尝试创建真实ML模组
            if db_session and number_attribute_mapper and lunar_year_manager:
                from src.algorithm_layer.ml_models.ml_predictor import MachineLearningPredictor
                from src.ml_module_adapter import MLModuleAdapter
                
                # 创建真实ML预测器
                ml_predictor = MachineLearningPredictor(
                    db_session, number_attribute_mapper, lunar_year_manager
                )
                
                # 使用适配器包装
                ml_module = MLModuleAdapter(ml_predictor, self.logger)
                
                # 应用增强回测配置
                if self.config_manager and hasattr(self.config_manager, 'optimal_config'):
                    self._apply_ml_config(ml_module)
                
                self.logger.info("Real ML module created successfully")
                return ml_module, "real"
                
            else:
                # 降级到Mock模组
                from src.perfect_prediction_system import MockMLModule
                ml_module = MockMLModule()
                self.logger.warning("Using Mock ML module due to missing dependencies")
                return ml_module, "mock"
                
        except Exception as e:
            self.logger.error(f"Failed to create ML module: {e}")
            # 最终降级
            from src.perfect_prediction_system import MockMLModule
            return MockMLModule(), "mock"
    
    def _apply_ml_config(self, ml_module):
        """应用ML模组的增强配置"""
        try:
            if self.config_manager and self.config_manager.optimal_config:
                config = self.config_manager.optimal_config
                
                # 应用ML特定配置
                if 'ml_parameters' in config:
                    ml_params = config['ml_parameters']
                    
                    # 设置置信度阈值
                    if hasattr(ml_module, 'set_confidence_threshold'):
                        threshold = ml_params.get('confidence_threshold', 0.75)
                        ml_module.set_confidence_threshold(threshold)
                    
                    # 设置模型权重
                    if hasattr(ml_module, 'set_model_weights'):
                        weights = ml_params.get('model_weights', {})
                        ml_module.set_model_weights(weights)
                
                self.logger.info("ML configuration applied from enhanced backtest")
                
        except Exception as e:
            self.logger.error(f"Failed to apply ML config: {e}")
    
    def monitor_ml_performance(self, ml_module, prediction_result):
        """监控ML模组性能"""
        try:
            performance_data = {
                'timestamp': __import__('datetime').datetime.now().isoformat(),
                'confidence': prediction_result.get('confidence', 0),
                'numbers_count': len(prediction_result.get('predicted_numbers', [])),
                'method': prediction_result.get('method', 'unknown'),
                'module_type': 'real' if 'real' in prediction_result.get('method', '') else 'mock'
            }
            
            self.performance_history.append(performance_data)
            
            # 保持最近100条记录
            if len(self.performance_history) > 100:
                self.performance_history = self.performance_history[-100:]
            
            self.logger.info(f"ML performance recorded: confidence={performance_data['confidence']}")
            
        except Exception as e:
            self.logger.error(f"Failed to monitor ML performance: {e}")
    
    def get_performance_report(self):
        """获取性能报告"""
        if not self.performance_history:
            return {"error": "No performance data available"}
        
        try:
            recent_data = self.performance_history[-10:]  # 最近10次
            
            avg_confidence = sum(d['confidence'] for d in recent_data) / len(recent_data)
            real_module_usage = sum(1 for d in recent_data if d['module_type'] == 'real')
            
            return {
                'total_predictions': len(self.performance_history),
                'recent_average_confidence': avg_confidence,
                'real_module_usage_rate': real_module_usage / len(recent_data),
                'latest_performance': recent_data[-1] if recent_data else None
            }
            
        except Exception as e:
            self.logger.error(f"Failed to generate performance report: {e}")
            return {"error": str(e)}
