"""
统计显著性检验 - 验证模型性能的统计显著性
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from scipy import stats
from scipy.stats import ttest_rel, ttest_ind, wilcoxon, mannwhit<PERSON>u
from scipy.stats import chi2_contingency, fisher_exact
import warnings

class StatisticalTester:
    """统计显著性检验器"""
    
    def __init__(self, alpha: float = 0.05):
        """
        初始化统计检验器
        
        Args:
            alpha: 显著性水平，默认0.05
        """
        self.alpha = alpha
        self.test_results = {}
    
    def paired_t_test(self, 
                     predictions_a: List[Dict], 
                     predictions_b: List[Dict],
                     actual_results: List[Dict],
                     metric: str = 'hit_rate') -> Dict[str, Any]:
        """
        配对t检验 - 比较两个模型的性能差异
        
        Args:
            predictions_a: 模型A的预测结果
            predictions_b: 模型B的预测结果
            actual_results: 实际结果
            metric: 比较的指标
        """
        
        print(f"🔬 执行配对t检验 - 指标: {metric}")
        
        # 计算每个预测的指标值
        scores_a = self._calculate_metric_scores(predictions_a, actual_results, metric)
        scores_b = self._calculate_metric_scores(predictions_b, actual_results, metric)
        
        if len(scores_a) != len(scores_b) or len(scores_a) < 2:
            return {'error': '数据不足或长度不匹配'}
        
        # 执行配对t检验
        statistic, p_value = ttest_rel(scores_a, scores_b)
        
        # 计算效应大小 (Cohen's d)
        differences = np.array(scores_a) - np.array(scores_b)
        cohens_d = np.mean(differences) / np.std(differences) if np.std(differences) > 0 else 0
        
        # 置信区间
        confidence_level = 1 - self.alpha
        t_critical = stats.t.ppf(1 - self.alpha/2, len(differences) - 1)
        margin_error = t_critical * (np.std(differences) / np.sqrt(len(differences)))
        mean_diff = np.mean(differences)
        
        result = {
            'test_type': 'paired_t_test',
            'metric': metric,
            'sample_size': len(scores_a),
            'mean_score_a': np.mean(scores_a),
            'mean_score_b': np.mean(scores_b),
            'mean_difference': mean_diff,
            't_statistic': statistic,
            'p_value': p_value,
            'is_significant': p_value < self.alpha,
            'cohens_d': cohens_d,
            'effect_size_interpretation': self._interpret_effect_size(abs(cohens_d)),
            'confidence_interval': [mean_diff - margin_error, mean_diff + margin_error],
            'confidence_level': confidence_level,
            'better_model': 'A' if mean_diff > 0 else 'B' if mean_diff < 0 else 'Equal'
        }
        
        return result
    
    def independent_t_test(self, 
                          predictions_a: List[Dict], 
                          predictions_b: List[Dict],
                          actual_results_a: List[Dict],
                          actual_results_b: List[Dict],
                          metric: str = 'hit_rate') -> Dict[str, Any]:
        """
        独立样本t检验 - 比较两个独立模型的性能
        """
        
        print(f"🔬 执行独立样本t检验 - 指标: {metric}")
        
        scores_a = self._calculate_metric_scores(predictions_a, actual_results_a, metric)
        scores_b = self._calculate_metric_scores(predictions_b, actual_results_b, metric)
        
        if len(scores_a) < 2 or len(scores_b) < 2:
            return {'error': '数据不足'}
        
        # 执行独立样本t检验
        statistic, p_value = ttest_ind(scores_a, scores_b)
        
        # 计算效应大小
        pooled_std = np.sqrt(((len(scores_a) - 1) * np.var(scores_a, ddof=1) + 
                             (len(scores_b) - 1) * np.var(scores_b, ddof=1)) / 
                            (len(scores_a) + len(scores_b) - 2))
        cohens_d = (np.mean(scores_a) - np.mean(scores_b)) / pooled_std if pooled_std > 0 else 0
        
        result = {
            'test_type': 'independent_t_test',
            'metric': metric,
            'sample_size_a': len(scores_a),
            'sample_size_b': len(scores_b),
            'mean_score_a': np.mean(scores_a),
            'mean_score_b': np.mean(scores_b),
            'std_score_a': np.std(scores_a),
            'std_score_b': np.std(scores_b),
            't_statistic': statistic,
            'p_value': p_value,
            'is_significant': p_value < self.alpha,
            'cohens_d': cohens_d,
            'effect_size_interpretation': self._interpret_effect_size(abs(cohens_d)),
            'better_model': 'A' if np.mean(scores_a) > np.mean(scores_b) else 'B'
        }
        
        return result
    
    def wilcoxon_signed_rank_test(self, 
                                 predictions_a: List[Dict], 
                                 predictions_b: List[Dict],
                                 actual_results: List[Dict],
                                 metric: str = 'hit_rate') -> Dict[str, Any]:
        """
        Wilcoxon符号秩检验 - 非参数配对检验
        """
        
        print(f"🔬 执行Wilcoxon符号秩检验 - 指标: {metric}")
        
        scores_a = self._calculate_metric_scores(predictions_a, actual_results, metric)
        scores_b = self._calculate_metric_scores(predictions_b, actual_results, metric)
        
        if len(scores_a) != len(scores_b) or len(scores_a) < 6:
            return {'error': '数据不足或长度不匹配（至少需要6个样本）'}
        
        # 执行Wilcoxon符号秩检验
        try:
            statistic, p_value = wilcoxon(scores_a, scores_b)
        except ValueError as e:
            return {'error': f'检验失败: {e}'}
        
        result = {
            'test_type': 'wilcoxon_signed_rank_test',
            'metric': metric,
            'sample_size': len(scores_a),
            'median_score_a': np.median(scores_a),
            'median_score_b': np.median(scores_b),
            'median_difference': np.median(np.array(scores_a) - np.array(scores_b)),
            'w_statistic': statistic,
            'p_value': p_value,
            'is_significant': p_value < self.alpha,
            'better_model': 'A' if np.median(scores_a) > np.median(scores_b) else 'B'
        }
        
        return result
    
    def mann_whitney_u_test(self, 
                           predictions_a: List[Dict], 
                           predictions_b: List[Dict],
                           actual_results_a: List[Dict],
                           actual_results_b: List[Dict],
                           metric: str = 'hit_rate') -> Dict[str, Any]:
        """
        Mann-Whitney U检验 - 非参数独立样本检验
        """
        
        print(f"🔬 执行Mann-Whitney U检验 - 指标: {metric}")
        
        scores_a = self._calculate_metric_scores(predictions_a, actual_results_a, metric)
        scores_b = self._calculate_metric_scores(predictions_b, actual_results_b, metric)
        
        if len(scores_a) < 3 or len(scores_b) < 3:
            return {'error': '数据不足（每组至少需要3个样本）'}
        
        # 执行Mann-Whitney U检验
        statistic, p_value = mannwhitneyu(scores_a, scores_b, alternative='two-sided')
        
        result = {
            'test_type': 'mann_whitney_u_test',
            'metric': metric,
            'sample_size_a': len(scores_a),
            'sample_size_b': len(scores_b),
            'median_score_a': np.median(scores_a),
            'median_score_b': np.median(scores_b),
            'u_statistic': statistic,
            'p_value': p_value,
            'is_significant': p_value < self.alpha,
            'better_model': 'A' if np.median(scores_a) > np.median(scores_b) else 'B'
        }
        
        return result
    
    def chi_square_test(self, 
                       predictions_a: List[Dict], 
                       predictions_b: List[Dict],
                       actual_results: List[Dict]) -> Dict[str, Any]:
        """
        卡方检验 - 检验预测准确性的独立性
        """
        
        print("🔬 执行卡方检验")
        
        # 构建列联表
        correct_a = 0
        incorrect_a = 0
        correct_b = 0
        incorrect_b = 0
        
        for pred_a, pred_b, actual in zip(predictions_a, predictions_b, actual_results):
            if 'error' in actual:
                continue
            
            pred_nums_a = pred_a.get('predicted_numbers', [])
            pred_nums_b = pred_b.get('predicted_numbers', [])
            actual_nums = actual.get('all_numbers', [])
            
            if not pred_nums_a or not pred_nums_b or not actual_nums:
                continue
            
            # 检查是否有命中
            hit_a = len(set(pred_nums_a) & set(actual_nums)) > 0
            hit_b = len(set(pred_nums_b) & set(actual_nums)) > 0
            
            if hit_a:
                correct_a += 1
            else:
                incorrect_a += 1
            
            if hit_b:
                correct_b += 1
            else:
                incorrect_b += 1
        
        # 构建2x2列联表
        contingency_table = np.array([[correct_a, incorrect_a],
                                     [correct_b, incorrect_b]])
        
        if np.any(contingency_table < 5):
            # 如果期望频数小于5，使用Fisher精确检验
            return self._fisher_exact_test(contingency_table)
        
        # 执行卡方检验
        chi2_stat, p_value, dof, expected = chi2_contingency(contingency_table)
        
        result = {
            'test_type': 'chi_square_test',
            'contingency_table': contingency_table.tolist(),
            'expected_frequencies': expected.tolist(),
            'chi2_statistic': chi2_stat,
            'p_value': p_value,
            'degrees_of_freedom': dof,
            'is_significant': p_value < self.alpha,
            'accuracy_a': correct_a / (correct_a + incorrect_a) if (correct_a + incorrect_a) > 0 else 0,
            'accuracy_b': correct_b / (correct_b + incorrect_b) if (correct_b + incorrect_b) > 0 else 0
        }
        
        return result
    
    def _fisher_exact_test(self, contingency_table: np.ndarray) -> Dict[str, Any]:
        """Fisher精确检验"""
        
        print("🔬 执行Fisher精确检验（样本量较小）")
        
        odds_ratio, p_value = fisher_exact(contingency_table)
        
        result = {
            'test_type': 'fisher_exact_test',
            'contingency_table': contingency_table.tolist(),
            'odds_ratio': odds_ratio,
            'p_value': p_value,
            'is_significant': p_value < self.alpha,
            'note': '由于期望频数小于5，使用Fisher精确检验'
        }
        
        return result
    
    def bootstrap_test(self, 
                      predictions_a: List[Dict], 
                      predictions_b: List[Dict],
                      actual_results: List[Dict],
                      metric: str = 'hit_rate',
                      n_bootstrap: int = 1000) -> Dict[str, Any]:
        """
        Bootstrap检验 - 非参数重采样检验
        """
        
        print(f"🔬 执行Bootstrap检验 - 指标: {metric}, 重采样次数: {n_bootstrap}")
        
        scores_a = self._calculate_metric_scores(predictions_a, actual_results, metric)
        scores_b = self._calculate_metric_scores(predictions_b, actual_results, metric)
        
        if len(scores_a) != len(scores_b) or len(scores_a) < 10:
            return {'error': '数据不足或长度不匹配（至少需要10个样本）'}
        
        # 观察到的差异
        observed_diff = np.mean(scores_a) - np.mean(scores_b)
        
        # Bootstrap重采样
        bootstrap_diffs = []
        n_samples = len(scores_a)
        
        for _ in range(n_bootstrap):
            # 重采样
            indices = np.random.choice(n_samples, n_samples, replace=True)
            bootstrap_a = [scores_a[i] for i in indices]
            bootstrap_b = [scores_b[i] for i in indices]
            
            # 计算差异
            bootstrap_diff = np.mean(bootstrap_a) - np.mean(bootstrap_b)
            bootstrap_diffs.append(bootstrap_diff)
        
        bootstrap_diffs = np.array(bootstrap_diffs)
        
        # 计算p值（双侧检验）
        p_value = 2 * min(np.mean(bootstrap_diffs >= observed_diff),
                         np.mean(bootstrap_diffs <= observed_diff))
        
        # 置信区间
        confidence_level = 1 - self.alpha
        lower_percentile = (self.alpha / 2) * 100
        upper_percentile = (1 - self.alpha / 2) * 100
        
        ci_lower = np.percentile(bootstrap_diffs, lower_percentile)
        ci_upper = np.percentile(bootstrap_diffs, upper_percentile)
        
        result = {
            'test_type': 'bootstrap_test',
            'metric': metric,
            'sample_size': len(scores_a),
            'n_bootstrap': n_bootstrap,
            'observed_difference': observed_diff,
            'bootstrap_mean': np.mean(bootstrap_diffs),
            'bootstrap_std': np.std(bootstrap_diffs),
            'p_value': p_value,
            'is_significant': p_value < self.alpha,
            'confidence_interval': [ci_lower, ci_upper],
            'confidence_level': confidence_level,
            'better_model': 'A' if observed_diff > 0 else 'B' if observed_diff < 0 else 'Equal'
        }
        
        return result
    
    def _calculate_metric_scores(self, 
                                predictions: List[Dict], 
                                actual_results: List[Dict],
                                metric: str) -> List[float]:
        """计算指标分数"""
        
        scores = []
        
        for pred, actual in zip(predictions, actual_results):
            if 'error' in actual:
                continue
            
            pred_numbers = pred.get('predicted_numbers', [])
            actual_numbers = actual.get('all_numbers', [])
            
            if not pred_numbers or not actual_numbers:
                continue
            
            if metric == 'hit_rate':
                matches = len(set(pred_numbers) & set(actual_numbers))
                score = matches / len(actual_numbers)
            elif metric == 'precision':
                matches = len(set(pred_numbers) & set(actual_numbers))
                score = matches / len(pred_numbers) if pred_numbers else 0
            elif metric == 'recall':
                matches = len(set(pred_numbers) & set(actual_numbers))
                score = matches / len(actual_numbers) if actual_numbers else 0
            elif metric == 'exact_match':
                score = 1.0 if set(pred_numbers) == set(actual_numbers) else 0.0
            else:
                # 默认使用命中率
                matches = len(set(pred_numbers) & set(actual_numbers))
                score = matches / len(actual_numbers)
            
            scores.append(score)
        
        return scores
    
    def _interpret_effect_size(self, cohens_d: float) -> str:
        """解释效应大小"""
        if cohens_d < 0.2:
            return "微小效应"
        elif cohens_d < 0.5:
            return "小效应"
        elif cohens_d < 0.8:
            return "中等效应"
        else:
            return "大效应"
    
    def comprehensive_comparison(self, 
                               predictions_a: List[Dict], 
                               predictions_b: List[Dict],
                               actual_results: List[Dict],
                               model_name_a: str = "Model A",
                               model_name_b: str = "Model B") -> Dict[str, Any]:
        """
        综合比较两个模型
        """
        
        print(f"�� 综合比较: {model_name_a} vs {model_name_b}")
        
        comparison_results = {
            'model_a': model_name_a,
            'model_b': model_name_b,
            'tests': {}
        }
        
        # 执行多种检验
        tests_to_run = [
            ('paired_t_test', 'hit_rate'),
            ('wilcoxon_signed_rank_test', 'hit_rate'),
            ('paired_t_test', 'precision'),
            ('paired_t_test', 'recall'),
            ('bootstrap_test', 'hit_rate')
        ]
        
        for test_name, metric in tests_to_run:
            try:
                if test_name == 'paired_t_test':
                    result = self.paired_t_test(predictions_a, predictions_b, actual_results, metric)
                elif test_name == 'wilcoxon_signed_rank_test':
                    result = self.wilcoxon_signed_rank_test(predictions_a, predictions_b, actual_results, metric)
                elif test_name == 'bootstrap_test':
                    result = self.bootstrap_test(predictions_a, predictions_b, actual_results, metric)
                
                comparison_results['tests'][f"{test_name}_{metric}"] = result
                
            except Exception as e:
                comparison_results['tests'][f"{test_name}_{metric}"] = {'error': str(e)}
        
        # 卡方检验
        try:
            chi2_result = self.chi_square_test(predictions_a, predictions_b, actual_results)
            comparison_results['tests']['chi_square_test'] = chi2_result
        except Exception as e:
            comparison_results['tests']['chi_square_test'] = {'error': str(e)}
        
        # 生成综合结论
        comparison_results['summary'] = self._generate_comparison_summary(comparison_results['tests'])
        
        return comparison_results
    
    def _generate_comparison_summary(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成比较总结"""
        
        summary = {
            'significant_tests': [],
            'non_significant_tests': [],
            'consistent_winner': None,
            'recommendation': ""
        }
        
        model_a_wins = 0
        model_b_wins = 0
        total_tests = 0
        
        for test_name, result in test_results.items():
            if 'error' in result:
                continue
            
            total_tests += 1
            
            if result.get('is_significant', False):
                summary['significant_tests'].append(test_name)
                
                better_model = result.get('better_model', 'Equal')
                if better_model == 'A':
                    model_a_wins += 1
                elif better_model == 'B':
                    model_b_wins += 1
            else:
                summary['non_significant_tests'].append(test_name)
        
        # 确定一致的获胜者
        if model_a_wins > model_b_wins:
            summary['consistent_winner'] = 'A'
            summary['recommendation'] = f"模型A在{model_a_wins}/{total_tests}个检验中表现更好"
        elif model_b_wins > model_a_wins:
            summary['consistent_winner'] = 'B'
            summary['recommendation'] = f"模型B在{model_b_wins}/{total_tests}个检验中表现更好"
        else:
            summary['consistent_winner'] = 'Equal'
            summary['recommendation'] = "两个模型的性能没有显著差异"
        
        summary['total_tests'] = total_tests
        summary['significant_count'] = len(summary['significant_tests'])
        summary['significance_ratio'] = len(summary['significant_tests']) / total_tests if total_tests > 0 else 0
        
        return summary
    
    def multiple_model_comparison(self, 
                                 model_predictions: Dict[str, List[Dict]],
                                 actual_results: List[Dict],
                                 metric: str = 'hit_rate') -> Dict[str, Any]:
        """
        多模型比较（使用方差分析）
        """
        
        print(f"🔬 多模型比较 - 指标: {metric}")
        
        model_names = list(model_predictions.keys())
        if len(model_names) < 3:
            return {'error': '至少需要3个模型进行多模型比较'}
        
        # 计算每个模型的分数
        all_scores = []
        group_labels = []
        
        for model_name, predictions in model_predictions.items():
            scores = self._calculate_metric_scores(predictions, actual_results, metric)
            all_scores.extend(scores)
            group_labels.extend([model_name] * len(scores))
        
        if len(all_scores) < 10:
            return {'error': '数据不足，无法进行多模型比较'}
        
        # 执行单因素方差分析
        groups = [self._calculate_metric_scores(predictions, actual_results, metric) 
                 for predictions in model_predictions.values()]
        
        try:
            f_statistic, p_value = stats.f_oneway(*groups)
        except Exception as e:
            return {'error': f'方差分析失败: {e}'}
        
        # 计算每个模型的统计量
        model_stats = {}
        for model_name, predictions in model_predictions.items():
            scores = self._calculate_metric_scores(predictions, actual_results, metric)
            model_stats[model_name] = {
                'mean': np.mean(scores),
                'std': np.std(scores),
                'median': np.median(scores),
                'sample_size': len(scores)
            }
        
        result = {
            'test_type': 'one_way_anova',
            'metric': metric,
            'model_count': len(model_names),
            'total_samples': len(all_scores),
            'f_statistic': f_statistic,
            'p_value': p_value,
            'is_significant': p_value < self.alpha,
            'model_statistics': model_stats,
            'best_model': max(model_stats.items(), key=lambda x: x[1]['mean'])[0],
            'worst_model': min(model_stats.items(), key=lambda x: x[1]['mean'])[0]
        }
        
        return result
