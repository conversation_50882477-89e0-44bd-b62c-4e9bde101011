"""
手动输入界面
"""
from typing import Dict, List, Optional
from datetime import date, datetime
from loguru import logger

class ManualInput:
    def __init__(self):
        self.input_history = []
    
    def input_single_record(self) -> Optional[Dict]:
        try:
            issue = input("请输入期号: ").strip()
            date_str = input("请输入开奖日期 (YYYY-MM-DD): ").strip()
            numbers_str = input("请输入6个正码 (用逗号分隔): ").strip()
            special_str = input("请输入特码: ").strip()
            
            draw_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            numbers = [int(x.strip()) for x in numbers_str.split(',')]
            special = int(special_str)
            
            record = {
                'issue': issue,
                'date': draw_date,
                'numbers': numbers,
                'special': special
            }
            
            self.input_history.append(record)
            return record
        except Exception as e:
            logger.error(f"输入错误: {e}")
            return None
