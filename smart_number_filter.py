#!/usr/bin/env python3
"""
智能号码筛选器 - 基于历史命中率数据优化号码选择
"""

import numpy as np
from collections import defaultdict, Counter
import sqlite3
from datetime import datetime, timedelta

class SmartNumberFilter:
    """智能号码筛选器"""
    
    def __init__(self, db_path="data/lottery.db"):
        self.db_path = db_path
        
        # 筛选策略权重
        self.filter_weights = {
            "hit_rate_filter": 0.35,      # 历史命中率筛选
            "frequency_filter": 0.25,     # 频率分析筛选
            "pattern_filter": 0.20,       # 模式识别筛选
            "exclusion_filter": 0.20      # 排除法筛选
        }
        
    def filter_numbers(self, candidate_numbers, historical_data, target_date):
        """智能筛选号码"""
        print(f"🔍 开始智能筛选 {len(candidate_numbers)} 个候选号码...")
        
        # 应用多重筛选策略
        filtered_results = {}
        
        # 1. 历史命中率筛选
        hit_rate_filtered = self._hit_rate_filter(candidate_numbers, historical_data)
        filtered_results["hit_rate"] = hit_rate_filtered
        
        # 2. 频率分析筛选
        frequency_filtered = self._frequency_filter(candidate_numbers, historical_data)
        filtered_results["frequency"] = frequency_filtered
        
        # 3. 模式识别筛选
        pattern_filtered = self._pattern_filter(candidate_numbers, historical_data)
        filtered_results["pattern"] = pattern_filtered
        
        # 4. 排除法筛选
        exclusion_filtered = self._exclusion_filter(candidate_numbers, historical_data)
        filtered_results["exclusion"] = exclusion_filtered
        
        # 融合筛选结果
        final_filtered = self._fuse_filter_results(filtered_results, candidate_numbers)
        
        print(f"✅ 筛选完成，从 {len(candidate_numbers)} 个减少到 {len(final_filtered)} 个")
        
        return final_filtered
    
    def _hit_rate_filter(self, candidates, historical_data):
        """基于历史命中率的筛选"""
        print("  📊 执行命中率筛选...")
        
        # 计算每个号码的历史命中率
        hit_rates = {}
        
        for num in candidates:
            # 统计该号码在历史中的表现
            appearances = 0
            total_periods = min(100, len(historical_data))  # 分析最近100期
            
            for record in historical_data[:total_periods]:
                if record.get('special_number') == num:
                    appearances += 1
            
            hit_rate = appearances / total_periods if total_periods > 0 else 0
            hit_rates[num] = hit_rate
        
        # 选择命中率较高的号码
        avg_hit_rate = np.mean(list(hit_rates.values())) if hit_rates else 0
        threshold = max(avg_hit_rate * 0.8, 0.01)  # 动态阈值
        
        filtered_numbers = [num for num, rate in hit_rates.items() if rate >= threshold]
        
        # 确保至少保留一定数量
        if len(filtered_numbers) < 8:
            sorted_by_rate = sorted(hit_rates.items(), key=lambda x: x[1], reverse=True)
            filtered_numbers = [num for num, rate in sorted_by_rate[:12]]
        
        return {
            "numbers": filtered_numbers,
            "hit_rates": hit_rates,
            "threshold": threshold,
            "avg_hit_rate": avg_hit_rate
        }
    
    def _frequency_filter(self, candidates, historical_data):
        """基于频率分析的筛选"""
        print("  📈 执行频率筛选...")
        
        # 多时间窗口频率分析
        windows = [10, 30, 50]
        frequency_scores = defaultdict(float)
        
        for window in windows:
            window_data = historical_data[:min(window, len(historical_data))]
            window_freq = Counter()
            
            for record in window_data:
                special = record.get('special_number', 0)
                if special in candidates:
                    window_freq[special] += 1
            
            # 计算标准化频率
            total_in_window = sum(window_freq.values())
            for num in candidates:
                freq = window_freq.get(num, 0)
                normalized_freq = freq / total_in_window if total_in_window > 0 else 0
                
                # 不同窗口的权重
                window_weight = 1.0 / (windows.index(window) + 1)
                frequency_scores[num] += normalized_freq * window_weight
        
        # 频率平衡策略：选择高频和适中频率的号码
        sorted_by_freq = sorted(frequency_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 选择策略：前50%的高频号码 + 中等频率号码
        high_freq_count = max(len(sorted_by_freq) // 2, 6)
        high_freq_numbers = [num for num, score in sorted_by_freq[:high_freq_count]]
        
        # 选择一些中等频率号码（避免全选热号）
        medium_start = high_freq_count
        medium_end = min(medium_start + 6, len(sorted_by_freq))
        medium_freq_numbers = [num for num, score in sorted_by_freq[medium_start:medium_end]]
        
        filtered_numbers = high_freq_numbers + medium_freq_numbers
        
        return {
            "numbers": filtered_numbers,
            "frequency_scores": dict(frequency_scores),
            "high_freq_count": len(high_freq_numbers),
            "medium_freq_count": len(medium_freq_numbers)
        }
    
    def _pattern_filter(self, candidates, historical_data):
        """基于模式识别的筛选"""
        print("  🔍 执行模式筛选...")
        
        pattern_scores = defaultdict(float)
        recent_data = historical_data[:20]  # 分析最近20期
        
        # 1. 连号模式分析
        consecutive_bonus = defaultdict(float)
        for record in recent_data:
            special = record.get('special_number', 0)
            if special > 0:
                # 给相邻号码加分
                for offset in [-1, 1]:
                    neighbor = special + offset
                    if neighbor in candidates:
                        consecutive_bonus[neighbor] += 0.3
        
        # 2. 间隔模式分析
        gap_bonus = defaultdict(float)
        for record in recent_data:
            special = record.get('special_number', 0)
            if special > 0:
                # 常见间隔模式
                for gap in [3, 5, 7, 10, 12]:
                    for direction in [-1, 1]:
                        target = special + gap * direction
                        if target in candidates:
                            gap_bonus[target] += 0.2
        
        # 3. 周期模式分析
        cycle_bonus = defaultdict(float)
        for i, record in enumerate(recent_data):
            special = record.get('special_number', 0)
            if special > 0 and special in candidates:
                # 根据出现的周期性给分
                cycle_weight = 1.0 / (i + 1)  # 越近期权重越高
                cycle_bonus[special] += cycle_weight
        
        # 合并模式得分
        for num in candidates:
            pattern_scores[num] = (consecutive_bonus[num] + 
                                 gap_bonus[num] + 
                                 cycle_bonus[num])
        
        # 选择模式得分较高的号码
        sorted_by_pattern = sorted(pattern_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 选择前70%的号码
        select_count = max(len(sorted_by_pattern) * 7 // 10, 8)
        filtered_numbers = [num for num, score in sorted_by_pattern[:select_count]]
        
        return {
            "numbers": filtered_numbers,
            "pattern_scores": dict(pattern_scores),
            "consecutive_bonus": dict(consecutive_bonus),
            "gap_bonus": dict(gap_bonus),
            "cycle_bonus": dict(cycle_bonus)
        }
    
    def _exclusion_filter(self, candidates, historical_data):
        """排除法筛选"""
        print("  🔍 执行排除筛选...")
        
        exclusion_scores = defaultdict(float)
        
        # 1. 排除过于频繁出现的号码
        recent_5 = historical_data[:5]
        recent_specials = [r.get('special_number', 0) for r in recent_5]
        
        for num in candidates:
            # 如果最近5期出现过，降低分数
            recent_count = recent_specials.count(num)
            if recent_count > 0:
                exclusion_scores[num] -= recent_count * 0.5
        
        # 2. 排除长期冷门号码
        long_term_data = historical_data[:100]
        long_term_freq = Counter()
        for record in long_term_data:
            special = record.get('special_number', 0)
            if special > 0:
                long_term_freq[special] += 1
        
        avg_freq = np.mean(list(long_term_freq.values())) if long_term_freq else 0
        
        for num in candidates:
            freq = long_term_freq.get(num, 0)
            if freq < avg_freq * 0.3:  # 频率过低
                exclusion_scores[num] -= 0.3
        
        # 3. 排除异常模式号码
        for num in candidates:
            # 检查是否有异常的聚集模式
            positions = []
            for i, record in enumerate(historical_data[:50]):
                if record.get('special_number') == num:
                    positions.append(i)
            
            if len(positions) >= 2:
                # 计算间隔的方差，方差过大说明不规律
                intervals = [positions[i] - positions[i+1] for i in range(len(positions)-1)]
                if len(intervals) > 1:
                    interval_var = np.var(intervals)
                    if interval_var > 100:  # 间隔过于不规律
                        exclusion_scores[num] -= 0.2
        
        # 应用排除筛选
        filtered_numbers = []
        for num in candidates:
            if exclusion_scores[num] > -0.8:  # 排除得分过低的
                filtered_numbers.append(num)
        
        # 确保不会过度排除
        if len(filtered_numbers) < len(candidates) * 0.6:
            # 如果排除过多，选择排除得分最高的
            sorted_by_exclusion = sorted(exclusion_scores.items(), key=lambda x: x[1], reverse=True)
            keep_count = max(len(candidates) * 6 // 10, 8)
            filtered_numbers = [num for num, score in sorted_by_exclusion[:keep_count]]
        
        return {
            "numbers": filtered_numbers,
            "exclusion_scores": dict(exclusion_scores),
            "excluded_count": len(candidates) - len(filtered_numbers)
        }
    
    def _fuse_filter_results(self, filter_results, original_candidates):
        """融合筛选结果"""
        print("  🔀 融合筛选结果...")
        
        # 计算每个号码的综合得分
        final_scores = defaultdict(float)
        
        for filter_name, result in filter_results.items():
            filtered_numbers = result["numbers"]
            weight = self.filter_weights.get(filter_name, 0.25)
            
            for num in original_candidates:
                if num in filtered_numbers:
                    # 在筛选中保留的号码得分
                    position = filtered_numbers.index(num) if num in filtered_numbers else len(filtered_numbers)
                    position_score = (len(filtered_numbers) - position) / len(filtered_numbers)
                    final_scores[num] += weight * position_score
                else:
                    # 被筛选掉的号码得0分
                    final_scores[num] += 0
        
        # 选择综合得分最高的16个号码
        sorted_final = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        final_numbers = [num for num, score in sorted_final[:16]]
        
        # 计算筛选效果统计
        filter_stats = {
            "original_count": len(original_candidates),
            "final_count": len(final_numbers),
            "reduction_rate": 1 - len(final_numbers) / len(original_candidates),
            "filter_effectiveness": {
                name: len(result["numbers"]) / len(original_candidates)
                for name, result in filter_results.items()
            }
        }
        
        return {
            "filtered_numbers": final_numbers,
            "final_scores": dict(final_scores),
            "filter_stats": filter_stats,
            "individual_filters": filter_results
        }
    
    def evaluate_filter_performance(self, historical_predictions, actual_results):
        """评估筛选器性能"""
        print("📊 评估筛选器性能...")
        
        performance_metrics = {
            "total_predictions": len(historical_predictions),
            "hit_count": 0,
            "hit_rate": 0.0,
            "average_prediction_size": 0.0,
            "efficiency_score": 0.0
        }
        
        hit_count = 0
        total_prediction_size = 0
        
        for pred, actual in zip(historical_predictions, actual_results):
            predicted_numbers = pred.get("filtered_numbers", [])
            actual_number = actual.get("special_number", 0)
            
            if actual_number in predicted_numbers:
                hit_count += 1
            
            total_prediction_size += len(predicted_numbers)
        
        if len(historical_predictions) > 0:
            performance_metrics["hit_count"] = hit_count
            performance_metrics["hit_rate"] = hit_count / len(historical_predictions)
            performance_metrics["average_prediction_size"] = total_prediction_size / len(historical_predictions)
            
            # 效率得分：命中率 / 平均预测数量
            if performance_metrics["average_prediction_size"] > 0:
                performance_metrics["efficiency_score"] = (
                    performance_metrics["hit_rate"] / 
                    (performance_metrics["average_prediction_size"] / 49.0)
                )
        
        return performance_metrics

if __name__ == "__main__":
    # 测试智能筛选器
    filter_system = SmartNumberFilter()
    print("🔍 智能号码筛选器测试")
