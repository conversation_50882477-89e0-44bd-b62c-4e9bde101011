#!/usr/bin/env python3
"""
简化的增强回测系统
专注于核心回测功能的实现
"""

import sqlite3
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter

class SimpleEnhancedBacktest:
    """简化的增强回测系统"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        
        # 回测配置
        self.config = {
            "min_data_points": 5,
            "default_iterations": 5,
            "prediction_count": 16,
            "confidence_threshold": 0.6
        }
        
        print(f"✅ 简化增强回测系统初始化完成")
    
    def run_enhanced_backtest(self, start_date: str, end_date: str, iterations: int = 5) -> Dict[str, Any]:
        """运行增强回测"""
        print(f"🔧 开始增强回测: {start_date} 到 {end_date}, {iterations}次迭代")
        
        try:
            # 获取回测数据
            backtest_data = self._get_backtest_data(start_date, end_date)
            
            if len(backtest_data) < self.config["min_data_points"]:
                print(f"⚠️ 数据不足，需要至少{self.config['min_data_points']}条记录，实际{len(backtest_data)}条")
                return {
                    "best_hit_rate": 0.0,
                    "best_config": {},
                    "iteration_results": [],
                    "error": "数据不足"
                }
            
            iteration_results = []
            best_hit_rate = 0
            best_config = {}
            
            for i in range(iterations):
                print(f"  迭代 {i+1}/{iterations}")
                
                # 生成配置
                config = self._generate_config(i)
                
                # 执行回测
                hit_rate = self._run_single_backtest(backtest_data, config)
                
                iteration_result = {
                    "iteration": i + 1,
                    "config": config,
                    "hit_rate": hit_rate
                }
                iteration_results.append(iteration_result)
                
                # 更新最佳结果
                if hit_rate > best_hit_rate:
                    best_hit_rate = hit_rate
                    best_config = config.copy()
                
                print(f"    命中率: {hit_rate:.1%}")
            
            result = {
                "best_hit_rate": best_hit_rate,
                "best_config": best_config,
                "iteration_results": iteration_results,
                "backtest_period": {"start": start_date, "end": end_date},
                "total_iterations": iterations,
                "data_points": len(backtest_data)
            }
            
            print(f"✅ 增强回测完成，最佳命中率: {best_hit_rate:.1%}")
            
            return result
            
        except Exception as e:
            print(f"❌ 增强回测失败: {e}")
            return {
                "best_hit_rate": 0.0,
                "best_config": {},
                "iteration_results": [],
                "error": str(e)
            }
    
    def _get_backtest_data(self, start_date: str, end_date: str) -> List[Dict]:
        """获取回测数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = """
        SELECT draw_date, special_number, period_number
        FROM lottery_results 
        WHERE draw_date >= ? AND draw_date <= ?
        ORDER BY draw_date ASC
        """
        
        cursor.execute(query, (start_date, end_date))
        rows = cursor.fetchall()
        
        data = []
        for row in rows:
            data.append({
                "draw_date": row[0],
                "special_number": row[1],
                "period_number": row[2]
            })
        
        conn.close()
        print(f"📊 获取回测数据: {len(data)}条记录")
        return data
    
    def _generate_config(self, iteration: int) -> Dict[str, Any]:
        """生成配置"""
        import random
        
        # 设置确定性种子
        random.seed(42 + iteration)
        
        config = {
            "method": random.choice(["frequency", "pattern", "trend", "mixed"]),
            "window_size": random.choice([5, 10, 15, 20]),
            "confidence_threshold": random.uniform(0.5, 0.9),
            "prediction_strategy": random.choice(["conservative", "balanced", "aggressive"])
        }
        
        return config
    
    def _run_single_backtest(self, backtest_data: List[Dict], config: Dict) -> float:
        """运行单次回测"""
        hits = 0
        total_predictions = 0
        
        # 使用滑动窗口进行回测
        window_size = min(config.get("window_size", 10), len(backtest_data) - 1)
        
        if window_size <= 0:
            return 0.0
        
        for i in range(1, min(len(backtest_data), window_size + 1)):
            # 获取目标数据
            target_record = backtest_data[i - 1]
            actual_number = target_record["special_number"]
            
            # 获取历史数据
            historical_data = backtest_data[i:]
            
            if len(historical_data) < 3:  # 至少需要3条历史数据
                continue
            
            try:
                # 根据配置选择预测方法
                method = config.get("method", "frequency")
                predicted_numbers = self._predict_numbers(historical_data, method)
                
                # 检查命中
                if actual_number in predicted_numbers:
                    hits += 1
                
                total_predictions += 1
                
            except Exception as e:
                print(f"    预测失败: {e}")
                continue
        
        # 计算命中率
        hit_rate = hits / total_predictions if total_predictions > 0 else 0
        return hit_rate
    
    def _predict_numbers(self, historical_data: List[Dict], method: str) -> List[int]:
        """根据方法预测号码"""
        if method == "frequency":
            return self._frequency_prediction(historical_data)
        elif method == "pattern":
            return self._pattern_prediction(historical_data)
        elif method == "trend":
            return self._trend_prediction(historical_data)
        elif method == "mixed":
            return self._mixed_prediction(historical_data)
        else:
            return self._frequency_prediction(historical_data)
    
    def _frequency_prediction(self, historical_data: List[Dict]) -> List[int]:
        """频率预测"""
        frequency = Counter()
        
        for record in historical_data[-20:]:  # 使用最近20期数据
            special = record.get("special_number", 0)
            if 1 <= special <= 49:
                frequency[special] += 1
        
        # 选择频率最高的16个号码
        sorted_numbers = sorted(frequency.items(), key=lambda x: x[1], reverse=True)
        predicted_numbers = [num for num, freq in sorted_numbers[:16]]
        
        # 如果不足16个，补充其他号码
        if len(predicted_numbers) < 16:
            all_numbers = set(range(1, 50))
            remaining = list(all_numbers - set(predicted_numbers))
            predicted_numbers.extend(remaining[:16-len(predicted_numbers)])
        
        return predicted_numbers[:16]
    
    def _pattern_prediction(self, historical_data: List[Dict]) -> List[int]:
        """模式预测"""
        pattern_candidates = set()
        
        # 分析最近10期的模式
        recent_data = historical_data[-10:]
        
        for record in recent_data:
            special = record.get("special_number", 0)
            if 1 <= special <= 49:
                # 连号模式
                if special > 1:
                    pattern_candidates.add(special - 1)
                if special < 49:
                    pattern_candidates.add(special + 1)
                
                # 间隔模式
                for gap in [3, 5, 7]:
                    if special + gap <= 49:
                        pattern_candidates.add(special + gap)
                    if special - gap >= 1:
                        pattern_candidates.add(special - gap)
        
        predicted_numbers = list(pattern_candidates)[:16]
        
        # 如果不足16个，用频率补充
        if len(predicted_numbers) < 16:
            freq_result = self._frequency_prediction(historical_data)
            for num in freq_result:
                if num not in predicted_numbers:
                    predicted_numbers.append(num)
                    if len(predicted_numbers) >= 16:
                        break
        
        return predicted_numbers[:16]
    
    def _trend_prediction(self, historical_data: List[Dict]) -> List[int]:
        """趋势预测"""
        # 分析短期和长期趋势
        short_term = historical_data[-5:]
        long_term = historical_data[-15:]
        
        trend_scores = defaultdict(float)
        
        # 短期趋势权重更高
        for i, record in enumerate(short_term):
            special = record.get("special_number", 0)
            if 1 <= special <= 49:
                weight = (i + 1) / len(short_term)  # 越近期权重越高
                trend_scores[special] += weight * 2
        
        # 长期趋势
        for record in long_term:
            special = record.get("special_number", 0)
            if 1 <= special <= 49:
                trend_scores[special] += 0.5
        
        # 选择得分最高的号码
        sorted_trends = sorted(trend_scores.items(), key=lambda x: x[1], reverse=True)
        predicted_numbers = [num for num, score in sorted_trends[:16]]
        
        # 如果不足16个，随机补充
        if len(predicted_numbers) < 16:
            all_numbers = list(range(1, 50))
            remaining = [n for n in all_numbers if n not in predicted_numbers]
            predicted_numbers.extend(remaining[:16-len(predicted_numbers)])
        
        return predicted_numbers[:16]
    
    def _mixed_prediction(self, historical_data: List[Dict]) -> List[int]:
        """混合预测"""
        # 获取各种方法的预测
        freq_pred = self._frequency_prediction(historical_data)
        pattern_pred = self._pattern_prediction(historical_data)
        trend_pred = self._trend_prediction(historical_data)
        
        # 统计号码出现次数
        number_votes = defaultdict(int)
        
        for num in freq_pred[:8]:  # 频率预测前8个
            number_votes[num] += 3
        
        for num in pattern_pred[:8]:  # 模式预测前8个
            number_votes[num] += 2
        
        for num in trend_pred[:8]:  # 趋势预测前8个
            number_votes[num] += 1
        
        # 选择得票最高的16个号码
        sorted_votes = sorted(number_votes.items(), key=lambda x: x[1], reverse=True)
        predicted_numbers = [num for num, votes in sorted_votes[:16]]
        
        # 如果不足16个，从频率预测中补充
        if len(predicted_numbers) < 16:
            for num in freq_pred:
                if num not in predicted_numbers:
                    predicted_numbers.append(num)
                    if len(predicted_numbers) >= 16:
                        break
        
        return predicted_numbers[:16]

def test_simple_enhanced_backtest():
    """测试简化增强回测系统"""
    print("🧪 测试简化增强回测系统")
    print("=" * 50)
    
    try:
        # 初始化系统
        backtest_system = SimpleEnhancedBacktest()
        
        # 运行回测
        start_date = "2025-01-01"
        end_date = "2025-01-10"
        iterations = 3
        
        result = backtest_system.run_enhanced_backtest(start_date, end_date, iterations)
        
        if result and "best_config" in result:
            print(f"✅ 简化增强回测测试成功")
            print(f"📊 最佳命中率: {result['best_hit_rate']:.1%}")
            print(f"📊 迭代次数: {len(result['iteration_results'])}")
            print(f"📊 数据点数: {result.get('data_points', 0)}")
            
            return True, result
        else:
            print(f"❌ 简化增强回测测试失败")
            print(f"结果: {result}")
            return False, result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, {"error": str(e)}

if __name__ == "__main__":
    success, result = test_simple_enhanced_backtest()
    print(f"\n{'✅ 测试通过' if success else '❌ 测试失败'}")
    if success:
        print(f"最佳命中率: {result['best_hit_rate']:.1%}")
        print(f"数据点数: {result.get('data_points', 0)}")
