"""
检查lottery_results表的结构和数据
"""
import sqlite3

def check_table_structure():
    """检查表结构"""
    print("🔍 检查lottery_results表结构")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute('PRAGMA table_info(lottery_results)')
        columns = cursor.fetchall()
        print("表结构:")
        for col in columns:
            print(f"  {col}")
        
        # 获取列名
        column_names = [col[1] for col in columns]
        print(f"\n列名: {column_names}")
        
        # 显示前5条数据
        cursor.execute('SELECT * FROM lottery_results LIMIT 5')
        rows = cursor.fetchall()
        print(f"\n前5条数据:")
        for i, row in enumerate(rows):
            print(f"  {i+1}. {row}")
        
        # 如果有日期相关的列，查找2025-01-31
        date_columns = [col for col in column_names if 'date' in col.lower() or 'time' in col.lower()]
        print(f"\n日期相关列: {date_columns}")
        
        if date_columns:
            date_col = date_columns[0]
            cursor.execute(f'SELECT * FROM lottery_results WHERE {date_col} LIKE "%2025-01-31%" LIMIT 5')
            jan_31_data = cursor.fetchall()
            print(f"\n2025-01-31相关数据 (使用{date_col}列):")
            for row in jan_31_data:
                print(f"  {row}")
        
        # 查找最新的数据
        cursor.execute('SELECT * FROM lottery_results ORDER BY rowid DESC LIMIT 10')
        latest = cursor.fetchall()
        print(f"\n最新10条数据:")
        for i, row in enumerate(latest):
            print(f"  {i+1}. {row}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查失败: {e}")

if __name__ == "__main__":
    check_table_structure()
