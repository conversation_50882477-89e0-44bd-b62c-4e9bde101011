"""
数据导入处理器
"""
import pandas as pd
import json
from datetime import datetime, date
from typing import List, Dict, Any, Optional
from pathlib import Path
from loguru import logger

class LotteryDataProcessor:
    """开奖数据处理器"""
    
    def __init__(self, lunar_year_manager, number_attribute_mapper, db_session):
        self.lunar_manager = lunar_year_manager
        self.attr_mapper = number_attribute_mapper
        self.db = db_session
    
    def import_from_csv(self, file_path: str) -> int:
        """从CSV文件导入数据"""
        try:
            df = pd.read_csv(file_path, encoding='utf-8')
            return self._process_dataframe(df)
        except Exception as e:
            logger.error(f"CSV导入失败: {e}")
            raise
    
    def import_from_excel(self, file_path: str, sheet_name: str = 0) -> int:
        """从Excel文件导入数据"""
        try:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            return self._process_dataframe(df)
        except Exception as e:
            logger.error(f"Excel导入失败: {e}")
            raise
    
    def _process_dataframe(self, df: pd.DataFrame) -> int:
        """处理DataFrame数据"""
        logger.info(f"开始处理数据，共 {len(df)} 条记录")
        
        # 数据清洗和验证
        df_cleaned = self._clean_dataframe(df)
        
        # 批量处理记录
        processed_count = 0
        for _, row in df_cleaned.iterrows():
            try:
                if self._process_single_record(row):
                    processed_count += 1
            except Exception as e:
                logger.error(f"处理记录失败: {row.to_dict()}, 错误: {e}")
                continue
        
        # 提交事务
        self.db.commit()
        logger.info(f"数据处理完成，成功处理 {processed_count} 条记录")
        return processed_count
    
    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗DataFrame数据"""
        # 标准化列名
        column_mapping = {
            '期号': 'period_number',
            '开奖日期': 'draw_date', 
            '日期': 'draw_date',
            '正码': 'regular_numbers',
            '特码': 'special_number',
            '特别号码': 'special_number'
        }
        
        df_renamed = df.rename(columns=column_mapping)
        
        # 确保必要列存在
        required_columns = ['period_number', 'draw_date', 'special_number']
        for col in required_columns:
            if col not in df_renamed.columns:
                raise ValueError(f"缺少必要列: {col}")
        
        # 数据类型转换
        df_cleaned = df_renamed.copy()
        
        # 处理日期
        df_cleaned['draw_date'] = pd.to_datetime(df_cleaned['draw_date']).dt.date
        
        # 处理特码
        df_cleaned['special_number'] = pd.to_numeric(df_cleaned['special_number'], errors='coerce')
        
        # 处理正码
        if 'regular_numbers' in df_cleaned.columns:
            df_cleaned['regular_numbers'] = df_cleaned['regular_numbers'].apply(self._parse_regular_numbers)
        else:
            # 如果没有正码列，创建空列表
            df_cleaned['regular_numbers'] = [[] for _ in range(len(df_cleaned))]
        
        # 删除无效记录
        df_cleaned = df_cleaned.dropna(subset=['period_number', 'draw_date', 'special_number'])
        
        # 验证特码范围
        df_cleaned = df_cleaned[
            (df_cleaned['special_number'] >= 1) & 
            (df_cleaned['special_number'] <= 49)
        ]
        
        return df_cleaned
    
    def _parse_regular_numbers(self, regular_str: Any) -> List[int]:
        """解析正码字符串"""
        if pd.isna(regular_str):
            return []
        
        try:
            # 尝试不同的分隔符
            separators = [',', '，', ' ', '+', '-', '|']
            numbers_str = str(regular_str).strip()
            
            for sep in separators:
                if sep in numbers_str:
                    parts = numbers_str.split(sep)
                    numbers = []
                    for part in parts:
                        part = part.strip()
                        if part.isdigit():
                            num = int(part)
                            if 1 <= num <= 49:
                                numbers.append(num)
                    if numbers:
                        return numbers
            
            # 如果没有分隔符，尝试直接转换
            if numbers_str.isdigit():
                num = int(numbers_str)
                if 1 <= num <= 49:
                    return [num]
            
            return []
        except:
            return []
    
    def _process_single_record(self, row: pd.Series) -> bool:
        """处理单条记录"""
        from src.data_layer.database.models import LotteryData
        
        period_number = str(row['period_number']).strip()
        draw_date = row['draw_date']
        special_number = int(row['special_number'])
        regular_numbers = row.get('regular_numbers', [])
        
        # 检查记录是否已存在
        existing = self.db.query(LotteryData).filter_by(period_number=period_number).first()
        if existing:
            logger.debug(f"期号 {period_number} 已存在，跳过")
            return False
        
        # 获取农历年份信息
        lunar_year_info = self.lunar_manager.get_lunar_year_by_date(draw_date)
        
        # 获取号码生肖映射
        zodiac_mapping = self.lunar_manager.get_zodiac_mapping_by_date(draw_date)
        special_zodiac = zodiac_mapping[special_number]['zodiac']
        special_wuxing = zodiac_mapping[special_number]['wuxing']
        
        # 获取号码属性
        number_attrs = self.attr_mapper.get_number_attributes(special_number, special_zodiac)
        zodiac_attrs = self.attr_mapper.get_all_zodiac_attributes(special_zodiac)
        
        # 创建数据库记录
        lottery_record = LotteryData(
            period_number=period_number,
            draw_date=draw_date,
            regular_numbers=json.dumps(regular_numbers),
            special_number=special_number,
            natural_year=draw_date.year,
            lunar_year=lunar_year_info['lunar_year'],
            special_zodiac=special_zodiac,
            special_wuxing=special_wuxing,
            
            # 扩展生肖属性
            zodiac_color_wave=zodiac_attrs.get('波色分类', ''),
            zodiac_season=zodiac_attrs.get('四季分类', ''),
            zodiac_day_night=zodiac_attrs.get('昼夜分类', ''),
            zodiac_left_right=zodiac_attrs.get('左右分类', ''),
            zodiac_yin_yang=zodiac_attrs.get('阴阳分类', ''),
            zodiac_courage=zodiac_attrs.get('胆量分类', ''),
            zodiac_fortune=zodiac_attrs.get('吉凶分类', ''),
            zodiac_group=zodiac_attrs.get('琴棋书画', ''),
            zodiac_origin=zodiac_attrs.get('家野分类', ''),
            zodiac_heaven_earth=zodiac_attrs.get('天地分类', ''),
            zodiac_gender=zodiac_attrs.get('男女分类', ''),
            zodiac_position=zodiac_attrs.get('前后分类', ''),
            zodiac_strokes=zodiac_attrs.get('笔画分类', ''),
            zodiac_combine=zodiac_attrs.get('合独分类', ''),
            
            # 号码基础属性
            special_size=number_attrs.get('size', ''),
            special_parity=number_attrs.get('parity', ''),
            special_prime=number_attrs.get('prime', ''),
            special_tail=number_attrs.get('tail', 0),
            special_wave_color=number_attrs.get('wave_color', ''),
            special_positive_negative=number_attrs.get('positive_negative', ''),
            special_segment=number_attrs.get('segment', 0)
        )
        
        self.db.add(lottery_record)
        return True
    
    def manual_input_record(self, period_number: str, draw_date: date, 
                           regular_numbers: List[int], special_number: int) -> bool:
        """手动输入单条记录"""
        try:
            # 创建临时DataFrame
            data = {
                'period_number': [period_number],
                'draw_date': [draw_date],
                'regular_numbers': [regular_numbers],
                'special_number': [special_number]
            }
            df = pd.DataFrame(data)
            
            # 处理记录
            success = self._process_single_record(df.iloc[0])
            if success:
                self.db.commit()
                logger.info(f"手动输入记录成功: {period_number}")
            
            return success
        except Exception as e:
            logger.error(f"手动输入记录失败: {e}")
            self.db.rollback()
            return False
    
    def validate_data_consistency(self) -> Dict[str, Any]:
        """验证数据一致性"""
        from src.data_layer.database.models import LotteryData
        
        logger.info("开始验证数据一致性")
        
        # 获取所有记录
        records = self.db.query(LotteryData).all()
        
        validation_results = {
            'total_records': len(records),
            'inconsistent_records': [],
            'missing_attributes': [],
            'invalid_numbers': [],
            'year_mismatches': []
        }
        
        for record in records:
            # 验证特码范围
            if not (1 <= record.special_number <= 49):
                validation_results['invalid_numbers'].append({
                    'period': record.period_number,
                    'special_number': record.special_number
                })
            
            # 验证年份一致性
            expected_lunar_year = self.lunar_manager.get_lunar_year_by_date(record.draw_date)
            if record.lunar_year != expected_lunar_year['lunar_year']:
                validation_results['year_mismatches'].append({
                    'period': record.period_number,
                    'recorded_year': record.lunar_year,
                    'expected_year': expected_lunar_year['lunar_year']
                })
            
            # 验证属性完整性
            if not record.special_zodiac or not record.special_wuxing:
                validation_results['missing_attributes'].append({
                    'period': record.period_number,
                    'missing': {
                        'zodiac': not record.special_zodiac,
                        'wuxing': not record.special_wuxing
                    }
                })
        
        logger.info(f"数据一致性验证完成: {validation_results}")
        return validation_results
    
    def repair_data_inconsistencies(self) -> int:
        """修复数据不一致问题"""
        from src.data_layer.database.models import LotteryData
        
        logger.info("开始修复数据不一致问题")
        
        records = self.db.query(LotteryData).all()
        repaired_count = 0
        
        for record in records:
            updated = False
            
            # 重新计算农历年份
            lunar_year_info = self.lunar_manager.get_lunar_year_by_date(record.draw_date)
            if record.lunar_year != lunar_year_info['lunar_year']:
                record.lunar_year = lunar_year_info['lunar_year']
                updated = True
            
            # 重新计算生肖属性
            zodiac_mapping = self.lunar_manager.get_zodiac_mapping_by_date(record.draw_date)
            expected_zodiac = zodiac_mapping[record.special_number]['zodiac']
            expected_wuxing = zodiac_mapping[record.special_number]['wuxing']
            
            if record.special_zodiac != expected_zodiac:
                record.special_zodiac = expected_zodiac
                updated = True
            
            if record.special_wuxing != expected_wuxing:
                record.special_wuxing = expected_wuxing
                updated = True
            
            # 重新计算扩展属性
            if updated or not record.zodiac_color_wave:
                zodiac_attrs = self.attr_mapper.get_all_zodiac_attributes(expected_zodiac)
                number_attrs = self.attr_mapper.get_number_attributes(record.special_number, expected_zodiac)
                
                # 更新扩展生肖属性
                record.zodiac_color_wave = zodiac_attrs.get('波色分类', '')
                record.zodiac_season = zodiac_attrs.get('四季分类', '')
                record.zodiac_day_night = zodiac_attrs.get('昼夜分类', '')
                # ... 其他属性
                
                # 更新号码基础属性
                record.special_size = number_attrs.get('size', '')
                record.special_parity = number_attrs.get('parity', '')
                # ... 其他属性
                
                updated = True
            
            if updated:
                repaired_count += 1
        
        self.db.commit()
        logger.info(f"数据修复完成，共修复 {repaired_count} 条记录")
        return repaired_count

    def process_single_record(self, record_data):
        """
        处理单条记录
        
        Args:
            record_data: 包含开奖数据的字典
            
        Returns:
            bool: 处理成功返回True，失败返回False
        """
        try:
            # 验证必要字段
            required_fields = ['period_number', 'draw_date', 'regular_numbers', 'special_number']
            for field in required_fields:
                if field not in record_data:
                    print(f"缺少必要字段: {field}")
                    return False
            
            # 检查记录是否已存在
            existing_record = self.session.query(LotteryData).filter_by(
                period_number=record_data['period_number']
            ).first()
            
            if existing_record:
                print(f"记录已存在: {record_data['period_number']}")
                return False
            
            # 处理日期
            if isinstance(record_data['draw_date'], str):
                try:
                    draw_date = datetime.strptime(record_data['draw_date'], '%Y-%m-%d').date()
                except ValueError:
                    try:
                        draw_date = datetime.strptime(record_data['draw_date'], '%Y/%m/%d').date()
                    except ValueError:
                        print(f"日期格式错误: {record_data['draw_date']}")
                        return False
            else:
                draw_date = record_data['draw_date']
            
            # 处理正码
            if isinstance(record_data['regular_numbers'], str):
                # 如果是字符串，尝试解析
                regular_numbers = []
                for num_str in record_data['regular_numbers'].replace('[', '').replace(']', '').split(','):
                    try:
                        num = int(num_str.strip())
                        if 1 <= num <= 49:
                            regular_numbers.append(num)
                    except ValueError:
                        continue
            else:
                regular_numbers = record_data['regular_numbers']
            
            # 验证正码
            if len(regular_numbers) != 6:
                print(f"正码数量错误: {len(regular_numbers)}")
                return False
            
            # 处理特码
            try:
                special_number = int(record_data['special_number'])
                if not (1 <= special_number <= 49):
                    print(f"特码范围错误: {special_number}")
                    return False
            except ValueError:
                print(f"特码格式错误: {record_data['special_number']}")
                return False
            
            # 获取农历年份
            lunar_year = self.lunar_manager.get_lunar_year(draw_date)
            
            # 获取号码属性
            special_zodiac = self.attr_mapper.get_zodiac(special_number, lunar_year)
            special_element = self.attr_mapper.get_element(special_number)
            special_color = self.attr_mapper.get_color(special_number)
            
            # 计算其他字段
            regular_sum = sum(regular_numbers)
            total_sum = regular_sum + special_number
            
            # 创建数据库记录
            lottery_record = LotteryData(
                period_number=record_data['period_number'],
                draw_date=draw_date,
                natural_year=draw_date.year,
                lunar_year=lunar_year,
                regular_numbers=json.dumps(regular_numbers),
                special_number=special_number,
                special_zodiac=special_zodiac,
                special_element=special_element,
                special_color=special_color,
                regular_sum=regular_sum,
                total_sum=total_sum,
                odd_count=sum(1 for num in regular_numbers + [special_number] if num % 2 == 1),
                even_count=sum(1 for num in regular_numbers + [special_number] if num % 2 == 0),
                big_count=sum(1 for num in regular_numbers + [special_number] if num > 24),
                small_count=sum(1 for num in regular_numbers + [special_number] if num <= 24),
                sum_tail=total_sum % 10
            )
            
            # 添加到数据库
            self.session.add(lottery_record)
            
            return True
            
        except Exception as e:
            print(f"处理单条记录失败: {e}")
            return False
