"""
快速测试16个号码修复
验证一致性预测器是否现在生成16个号码
"""

def test_consistent_predictor_16_numbers():
    """测试一致性预测器16个号码"""
    print("🔧 快速测试一致性预测器16个号码")
    print("=" * 50)
    
    try:
        from consistent_predictor import ConsistentSpecialNumberPredictor
        
        # 创建预测器
        predictor = ConsistentSpecialNumberPredictor()
        print("✅ 一致性预测器初始化成功")
        
        # 运行预测
        result = predictor.run_consistent_prediction("2025-06-22")
        
        # 检查特码预测
        special_pred = result['special_number_prediction']
        final_recommendations = special_pred['final_recommendations']
        
        print(f"\n📊 特码预测结果:")
        print(f"   推荐号码数量: {len(final_recommendations)}")
        
        if len(final_recommendations) == 16:
            print("   ✅ 特码预测现在生成16个号码！")
        else:
            print(f"   ❌ 特码预测还是只有{len(final_recommendations)}个号码")
        
        # 检查融合预测
        fusion_pred = result['fusion_prediction']
        fusion_numbers = fusion_pred['recommended_numbers']
        
        print(f"\n📊 融合预测结果:")
        print(f"   融合号码数量: {len(fusion_numbers)}")
        
        if len(fusion_numbers) == 16:
            print("   ✅ 融合预测生成16个号码！")
        else:
            print(f"   ❌ 融合预测只有{len(fusion_numbers)}个号码")
        
        print("\n✅ 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_consistent_predictor_16_numbers()
