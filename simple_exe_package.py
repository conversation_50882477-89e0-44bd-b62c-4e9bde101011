"""
简化的六合彩预测系统 EXE 打包脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def create_simple_exe():
    """创建简化的EXE打包"""
    print("🚀 简化EXE打包开始")
    print("=" * 50)
    
    # 设置路径
    source_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    if not source_dir.exists():
        print("❌ 源目录不存在")
        return False
    
    print(f"📁 源目录: {source_dir}")
    
    # 切换到源目录
    original_dir = Path.cwd()
    os.chdir(source_dir)
    
    try:
        # 1. 检查PyInstaller
        print("\n1️⃣ 检查PyInstaller...")
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "show", "pyinstaller"], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print("📦 安装PyInstaller...")
                subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("✅ PyInstaller 已准备就绪")
        except Exception as e:
            print(f"❌ PyInstaller 安装失败: {e}")
            return False
        
        # 2. 创建简化的spec文件
        print("\n2️⃣ 创建spec文件...")
        create_simple_spec()
        print("✅ spec文件创建完成")
        
        # 3. 执行打包
        print("\n3️⃣ 执行PyInstaller打包...")
        try:
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--onedir",  # 使用onedir模式
                "--windowed",  # 无控制台窗口
                "--name=六合彩预测系统_v2.0.0",
                "--add-data=src;src",
                "--add-data=data;data", 
                "--add-data=config;config",
                "--add-data=*.json;.",
                "--hidden-import=PyQt5.QtCore",
                "--hidden-import=PyQt5.QtGui", 
                "--hidden-import=PyQt5.QtWidgets",
                "--hidden-import=sqlite3",
                "--hidden-import=pandas",
                "--hidden-import=numpy",
                "--hidden-import=scikit-learn",
                "lottery_prediction_gui.py"
            ]
            
            print(f"🔧 执行命令: PyInstaller...")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ PyInstaller打包成功")
            else:
                print("❌ PyInstaller打包失败")
                print("📄 错误信息:")
                print(result.stderr[-1000:])  # 显示最后1000字符
                return False
                
        except Exception as e:
            print(f"❌ 打包过程出错: {e}")
            return False
        
        # 4. 验证EXE
        print("\n4️⃣ 验证EXE文件...")
        exe_path = Path("dist/六合彩预测系统_v2.0.0/六合彩预测系统_v2.0.0.exe")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"✅ EXE文件创建成功")
            print(f"📁 文件路径: {exe_path.absolute()}")
            print(f"📊 文件大小: {size_mb:.1f} MB")
            
            # 创建启动脚本
            create_simple_launcher(exe_path.parent)
            
            return True
        else:
            print("❌ EXE文件未找到")
            return False
            
    finally:
        os.chdir(original_dir)

def create_simple_spec():
    """创建简化的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['lottery_prediction_gui.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('data', 'data'),
        ('config', 'config'),
        ('*.json', '.'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'sqlite3',
        'pandas',
        'numpy',
        'scikit-learn',
        'joblib',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='六合彩预测系统_v2.0.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='六合彩预测系统_v2.0.0',
)
'''
    
    with open("simple_lottery.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)

def create_simple_launcher(dist_dir):
    """创建简单的启动脚本"""
    # 简单的批处理文件
    bat_content = '''@echo off
echo Starting Lottery Prediction System...
start "" "六合彩预测系统_v2.0.0.exe"
'''
    
    with open(dist_dir / "start.bat", "w", encoding="utf-8") as f:
        f.write(bat_content)
    
    # 创建使用说明
    readme_content = '''六合彩预测系统 v2.0.0

使用方法:
1. 双击 "六合彩预测系统_v2.0.0.exe" 启动程序
2. 或双击 "start.bat" 启动程序

功能特点:
- 特码预测
- 完美预测系统  
- 性能监控
- 增强回测
- 数据管理

系统要求:
- Windows 10/11
- 4GB+ 内存
- 1GB+ 磁盘空间

如有问题请联系技术支持。
'''
    
    with open(dist_dir / "README.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)

def main():
    """主函数"""
    print("🎯 简化EXE打包工具")
    print("=" * 50)
    
    success = create_simple_exe()
    
    if success:
        print("\n🎉 EXE打包完成！")
        print("📁 输出目录: releases/六合彩预测系统_v2.0.0_20250624_Windows版/dist/六合彩预测系统_v2.0.0/")
        print("🚀 可执行文件: 六合彩预测系统_v2.0.0.exe")
        print("\n📋 测试步骤:")
        print("  1. 进入输出目录")
        print("  2. 双击EXE文件测试")
        print("  3. 检查所有功能是否正常")
    else:
        print("\n❌ EXE打包失败")
        print("请检查错误信息并重试")
    
    return success

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
