# 强制启动六合彩预测系统 PowerShell脚本

Write-Host "🔄 强制启动六合彩预测系统..." -ForegroundColor Green
Write-Host "================================" -ForegroundColor Cyan

# 清理Python缓存
Write-Host "清理Python缓存..." -ForegroundColor Yellow
if (Test-Path "__pycache__") {
    Remove-Item "__pycache__" -Recurse -Force
    Write-Host "✅ 清理 __pycache__" -ForegroundColor Green
}
if (Test-Path "src\__pycache__") {
    Remove-Item "src\__pycache__" -Recurse -Force
    Write-Host "✅ 清理 src\__pycache__" -ForegroundColor Green
}

# 删除.pyc文件
Get-ChildItem -Recurse -Filter "*.pyc" | Remove-Item -Force
Write-Host "✅ 清理.pyc文件" -ForegroundColor Green

# 设置环境变量
$env:PYTHONDONTWRITEBYTECODE = "1"
$env:PYTHONPATH = "$PWD;$PWD\src"

Write-Host "启动GUI程序..." -ForegroundColor Yellow

try {
    if (Test-Path "lottery_prediction_gui.py") {
        python lottery_prediction_gui.py
    } elseif (Test-Path "六合彩预测系统_v2.0.0.exe") {
        Write-Host "启动EXE版本..." -ForegroundColor Yellow
        Start-Process "六合彩预测系统_v2.0.0.exe"
    } else {
        Write-Host "未找到可执行文件" -ForegroundColor Red
    }
} catch {
    Write-Host "启动失败: $_" -ForegroundColor Red
}

Write-Host "程序已启动" -ForegroundColor Green
Read-Host "按回车键退出"
