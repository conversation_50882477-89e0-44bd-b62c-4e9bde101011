"""
集成构建器 - 构建各种类型的模型集成
"""
import numpy as np
from typing import List, Dict, Any, Optional
from sklearn.ensemble import VotingClassifier, BaggingClassifier, AdaBoostClassifier
from sklearn.model_selection import cross_val_score, TimeSeriesSplit
from sklearn.base import BaseEstimator, ClassifierMixin
from sklearn.linear_model import LogisticRegression
import joblib

class EnsembleBuilder:
    def __init__(self):
        """初始化集成构建器"""
        self.ensemble_models = {}
        self.ensemble_history = []
    
    def build_voting_ensemble(self,
                            models: List[tuple],
                            voting: str = 'soft',
                            weights: List[float] = None) -> VotingClassifier:
        """构建投票集成"""
        
        ensemble = VotingClassifier(
            estimators=models,
            voting=voting,
            weights=weights
        )
        
        ensemble_info = {
            'type': 'voting',
            'voting_type': voting,
            'models': [name for name, _ in models],
            'weights': weights
        }
        
        self.ensemble_models['voting'] = {
            'model': ensemble,
            'info': ensemble_info
        }
        
        return ensemble
    
    def build_bagging_ensemble(self,
                             base_estimator: Any,
                             n_estimators: int = 10,
                             max_samples: float = 1.0,
                             max_features: float = 1.0,
                             random_state: int = 42) -> BaggingClassifier:
        """构建Bagging集成"""
        
        ensemble = BaggingClassifier(
            base_estimator=base_estimator,
            n_estimators=n_estimators,
            max_samples=max_samples,
            max_features=max_features,
            random_state=random_state
        )
        
        ensemble_info = {
            'type': 'bagging',
            'base_estimator': type(base_estimator).__name__,
            'n_estimators': n_estimators,
            'max_samples': max_samples,
            'max_features': max_features
        }
        
        self.ensemble_models['bagging'] = {
            'model': ensemble,
            'info': ensemble_info
        }
        
        return ensemble
    
    def build_boosting_ensemble(self,
                              base_estimator: Any,
                              n_estimators: int = 50,
                              learning_rate: float = 1.0,
                              random_state: int = 42) -> AdaBoostClassifier:
        """构建Boosting集成"""
        
        ensemble = AdaBoostClassifier(
            base_estimator=base_estimator,
            n_estimators=n_estimators,
            learning_rate=learning_rate,
            random_state=random_state
        )
        
        ensemble_info = {
            'type': 'boosting',
            'base_estimator': type(base_estimator).__name__,
            'n_estimators': n_estimators,
            'learning_rate': learning_rate
        }
        
        self.ensemble_models['boosting'] = {
            'model': ensemble,
            'info': ensemble_info
        }
        
        return ensemble
    
    def build_stacking_ensemble(self,
                              base_models: List[tuple],
                              meta_classifier: Any = None,
                              cv_folds: int = 5) -> 'StackingClassifier':
        """构建Stacking集成"""
        
        if meta_classifier is None:
            meta_classifier = LogisticRegression()
        
        ensemble = StackingClassifier(
            base_models=base_models,
            meta_classifier=meta_classifier,
            cv_folds=cv_folds
        )
        
        ensemble_info = {
            'type': 'stacking',
            'base_models': [name for name, _ in base_models],
            'meta_classifier': type(meta_classifier).__name__,
            'cv_folds': cv_folds
        }
        
        self.ensemble_models['stacking'] = {
            'model': ensemble,
            'info': ensemble_info
        }
        
        return ensemble
    
    def evaluate_ensemble(self,
                         ensemble: Any,
                         X: np.ndarray,
                         y: np.ndarray,
                         cv_folds: int = 5) -> Dict:
        """评估集成模型"""
        
        # 时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=cv_folds)
        
        # 计算交叉验证分数
        cv_scores = cross_val_score(ensemble, X, y, cv=tscv, scoring='accuracy')
        
        evaluation_result = {
            'cv_scores': cv_scores.tolist(),
            'mean_cv_score': cv_scores.mean(),
            'std_cv_score': cv_scores.std(),
            'min_cv_score': cv_scores.min(),
            'max_cv_score': cv_scores.max()
        }
        
        return evaluation_result
    
    def compare_ensembles(self,
                         X: np.ndarray,
                         y: np.ndarray,
                         cv_folds: int = 5) -> Dict:
        """比较不同集成方法"""
        
        comparison_results = {}
        
        for ensemble_name, ensemble_data in self.ensemble_models.items():
            ensemble = ensemble_data['model']
            
            try:
                # 训练集成模型
                ensemble.fit(X, y)
                
                # 评估集成模型
                evaluation = self.evaluate_ensemble(ensemble, X, y, cv_folds)
                
                comparison_results[ensemble_name] = {
                    'info': ensemble_data['info'],
                    'evaluation': evaluation
                }
                
            except Exception as e:
                comparison_results[ensemble_name] = {
                    'info': ensemble_data['info'],
                    'error': str(e)
                }
        
        # 找出最佳集成
        best_ensemble = None
        best_score = -1
        
        for name, result in comparison_results.items():
            if 'evaluation' in result:
                score = result['evaluation']['mean_cv_score']
                if score > best_score:
                    best_score = score
                    best_ensemble = name
        
        comparison_results['best_ensemble'] = best_ensemble
        comparison_results['best_score'] = best_score
        
        return comparison_results


class StackingClassifier(BaseEstimator, ClassifierMixin):
    """自定义Stacking分类器"""
    
    def __init__(self, base_models, meta_classifier, cv_folds=5):
        self.base_models = base_models
        self.meta_classifier = meta_classifier
        self.cv_folds = cv_folds
        self.fitted_base_models = []
        self.fitted_meta_classifier = None
    
    def fit(self, X, y):
        """训练Stacking集成"""
        
        # 第一层：训练基础模型并生成元特征
        meta_features = np.zeros((X.shape[0], len(self.base_models)))
        
        # 时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=self.cv_folds)
        
        for i, (name, model) in enumerate(self.base_models):
            fold_predictions = np.zeros(X.shape[0])
            
            for train_idx, val_idx in tscv.split(X):
                X_train_fold, X_val_fold = X[train_idx], X[val_idx]
                y_train_fold = y[train_idx]
                
                # 训练基础模型
                model_copy = type(model)(**model.get_params())
                model_copy.fit(X_train_fold, y_train_fold)
                
                # 预测验证集
                fold_predictions[val_idx] = model_copy.predict_proba(X_val_fold)[:, 1]
            
            meta_features[:, i] = fold_predictions
        
        # 在全部数据上训练基础模型
        self.fitted_base_models = []
        for name, model in self.base_models:
            model_copy = type(model)(**model.get_params())
            model_copy.fit(X, y)
            self.fitted_base_models.append((name, model_copy))
        
        # 第二层：训练元分类器
        self.fitted_meta_classifier = type(self.meta_classifier)(**self.meta_classifier.get_params())
        self.fitted_meta_classifier.fit(meta_features, y)
        
        return self
    
    def predict(self, X):
        """预测"""
        meta_features = self._get_meta_features(X)
        return self.fitted_meta_classifier.predict(meta_features)
    
    def predict_proba(self, X):
        """预测概率"""
        meta_features = self._get_meta_features(X)
        return self.fitted_meta_classifier.predict_proba(meta_features)
    
    def _get_meta_features(self, X):
        """获取元特征"""
        meta_features = np.zeros((X.shape[0], len(self.fitted_base_models)))
        
        for i, (name, model) in enumerate(self.fitted_base_models):
            if hasattr(model, 'predict_proba'):
                meta_features[:, i] = model.predict_proba(X)[:, 1]
            else:
                meta_features[:, i] = model.predict(X)
        
        return meta_features
