# 完整EXE打包流程 PowerShell脚本
# 按照最佳实践进行EXE打包

Write-Host "🔧 完整EXE打包流程启动" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Cyan

# 检查Python环境
Write-Host "检查Python环境..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python版本: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python未安装或不在PATH中" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# 检查PyInstaller
Write-Host "检查PyInstaller..." -ForegroundColor Yellow
try {
    $pyinstallerVersion = python -m PyInstaller --version 2>&1
    Write-Host "✅ PyInstaller版本: $pyinstallerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ PyInstaller未安装" -ForegroundColor Red
    Write-Host "正在安装PyInstaller..." -ForegroundColor Yellow
    python -m pip install pyinstaller
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ PyInstaller安装失败" -ForegroundColor Red
        Read-Host "按回车键退出"
        exit 1
    }
    Write-Host "✅ PyInstaller安装成功" -ForegroundColor Green
}

# 检查项目文件
Write-Host "检查项目文件..." -ForegroundColor Yellow
$requiredFiles = @(
    "lottery_prediction_gui.py",
    "complete_exe_package_builder.py"
)

$missingFiles = @()
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file: 存在" -ForegroundColor Green
    } else {
        Write-Host "❌ $file: 不存在" -ForegroundColor Red
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "缺少必要文件: $($missingFiles -join ', ')" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# 运行完整打包流程
Write-Host "`n🚀 开始EXE打包流程..." -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Cyan

try {
    python build_complete_exe.py
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n🎉 EXE打包成功完成！" -ForegroundColor Green
        Write-Host "📁 请查看 releases/ 目录中的发布包" -ForegroundColor Cyan
        
        # 检查生成的文件
        if (Test-Path "releases") {
            Write-Host "`n📋 生成的发布包:" -ForegroundColor Yellow
            Get-ChildItem "releases" -Directory | ForEach-Object {
                Write-Host "  📁 $($_.Name)" -ForegroundColor Cyan
            }
            Get-ChildItem "releases" -File -Filter "*.zip" | ForEach-Object {
                $size = [math]::Round($_.Length / 1MB, 1)
                Write-Host "  📦 $($_.Name) ($size MB)" -ForegroundColor Cyan
            }
        }
        
        Write-Host "`n🔧 下一步操作:" -ForegroundColor Yellow
        Write-Host "  1. 进入 releases/ 目录" -ForegroundColor White
        Write-Host "  2. 解压ZIP文件" -ForegroundColor White
        Write-Host "  3. 运行 debug_start.bat 测试EXE" -ForegroundColor White
        
    } else {
        Write-Host "`n❌ EXE打包失败" -ForegroundColor Red
        Write-Host "📋 请查看 exe_build.log 了解详细信息" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "`n❌ 打包过程发生异常: $_" -ForegroundColor Red
}

Write-Host "`n" -NoNewline
Read-Host "按回车键退出"
