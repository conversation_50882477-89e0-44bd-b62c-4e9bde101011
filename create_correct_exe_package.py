"""
创建正确的EXE封包
使用原始的lottery_prediction_gui.py界面
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
from datetime import datetime

class CorrectEXEPackager:
    """正确的EXE封包器"""
    
    def __init__(self):
        self.version = "v2.0.0"
        self.release_date = datetime.now().strftime("%Y%m%d")
        self.package_name = f"LotteryPredictionSystem_Correct_{self.version}_{self.release_date}"
        
        # 工作目录
        self.work_dir = Path("exe_correct_build")
        self.work_dir.mkdir(exist_ok=True)
        
        # 输出目录
        self.output_dir = Path("releases") / "correct_exe"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🎯 正确GUI界面EXE封包工具")
        print(f"📦 版本: {self.version}")
        print(f"🔨 工作目录: {self.work_dir}")
    
    def prepare_all_files(self):
        """准备所有必需文件"""
        print("\n📋 准备所有必需文件...")
        print("-" * 50)
        
        # 核心Python文件
        core_files = [
            "lottery_prediction_gui.py",
            "special_number_predictor.py",
            "consistent_predictor.py",
            "historical_backtest.py",
            "consistency_test.py",
            "enhanced_backtest.py",
            "comprehensive_evaluation.py"
        ]
        
        copied_files = []
        for file_name in core_files:
            if os.path.exists(file_name):
                shutil.copy2(file_name, self.work_dir)
                print(f"✅ 复制: {file_name}")
                copied_files.append(file_name)
            else:
                print(f"⚠️ 缺失: {file_name}")
        
        # 复制src目录
        if os.path.exists("src"):
            if (self.work_dir / "src").exists():
                shutil.rmtree(self.work_dir / "src")
            shutil.copytree("src", self.work_dir / "src")
            print(f"✅ 复制: src/")
        
        # 复制data目录
        if os.path.exists("data"):
            if (self.work_dir / "data").exists():
                shutil.rmtree(self.work_dir / "data")
            shutil.copytree("data", self.work_dir / "data")
            print(f"✅ 复制: data/")
        else:
            # 创建空的data目录
            (self.work_dir / "data").mkdir(exist_ok=True)
            print(f"✅ 创建: data/")
        
        # 复制配置文件
        config_files = ["*.json", "*.txt", "*.ini", "*.cfg"]
        for pattern in config_files:
            for file_path in Path(".").glob(pattern):
                if file_path.name not in ["version_info.json"]:
                    try:
                        shutil.copy2(file_path, self.work_dir)
                        print(f"✅ 复制: {file_path.name}")
                    except:
                        pass
        
        return len(copied_files) > 0
    
    def create_main_launcher(self):
        """创建主启动器"""
        print("\n🚀 创建主启动器...")
        print("-" * 50)
        
        launcher_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六合彩预测系统主启动器
确保使用原始GUI界面
"""

import sys
import os
from pathlib import Path
import warnings

# 忽略警告
warnings.filterwarnings("ignore")

def setup_environment():
    """设置环境"""
    # 获取程序目录
    if getattr(sys, 'frozen', False):
        # PyInstaller环境
        app_dir = Path(sys.executable).parent
    else:
        # 开发环境
        app_dir = Path(__file__).parent
    
    # 设置工作目录
    os.chdir(app_dir)
    
    # 添加路径到sys.path
    sys.path.insert(0, str(app_dir))
    sys.path.insert(0, str(app_dir / "src"))
    
    print(f"🏠 程序目录: {app_dir}")
    print(f"📁 工作目录: {os.getcwd()}")
    
    return app_dir

def check_gui_file():
    """检查GUI文件"""
    gui_file = "lottery_prediction_gui.py"
    if os.path.exists(gui_file):
        print(f"✅ 找到GUI文件: {gui_file}")
        return True
    else:
        print(f"❌ 未找到GUI文件: {gui_file}")
        return False

def start_original_gui():
    """启动原始GUI"""
    print("🚀 启动原始GUI界面...")
    
    try:
        # 直接导入并运行原始GUI
        import lottery_prediction_gui
        
        # 检查是否有main函数
        if hasattr(lottery_prediction_gui, 'main'):
            print("✅ 调用main函数")
            lottery_prediction_gui.main()
        else:
            print("✅ 直接执行GUI模块")
            # 如果没有main函数，直接执行文件内容
            with open('lottery_prediction_gui.py', 'r', encoding='utf-8') as f:
                gui_code = f.read()
            exec(gui_code)
            
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        
        # 显示详细错误
        import traceback
        print("\\n📋 详细错误信息:")
        traceback.print_exc()
        
        # 尝试显示错误对话框
        try:
            from PyQt5.QtWidgets import QApplication, QMessageBox
            
            app = QApplication(sys.argv)
            
            msg = QMessageBox()
            msg.setWindowTitle("启动失败")
            msg.setText(f"GUI启动失败:\\n{str(e)}")
            msg.setDetailedText(traceback.format_exc())
            msg.setIcon(QMessageBox.Critical)
            msg.exec_()
            
        except Exception as e2:
            print(f"❌ 无法显示错误对话框: {e2}")
            input("按回车键退出...")

def main():
    """主函数"""
    print("🎊 六合彩预测系统 - 原始GUI版")
    print("=" * 60)
    
    # 设置环境
    app_dir = setup_environment()
    
    # 检查GUI文件
    if not check_gui_file():
        print("❌ GUI文件缺失，无法启动")
        input("按回车键退出...")
        return
    
    # 启动原始GUI
    start_original_gui()

if __name__ == "__main__":
    main()
'''
        
        launcher_file = self.work_dir / "main.py"
        with open(launcher_file, "w", encoding="utf-8") as f:
            f.write(launcher_code)
        
        print(f"✅ 主启动器已创建: {launcher_file}")
        return launcher_file
    
    def create_comprehensive_spec(self, main_file):
        """创建全面的spec文件"""
        print("\n📝 创建全面的spec文件...")
        print("-" * 50)
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 获取当前目录
current_dir = Path.cwd()

# 数据文件列表
datas = []

# 添加src目录
if (current_dir / 'src').exists():
    datas.append((str(current_dir / 'src'), 'src'))

# 添加data目录
if (current_dir / 'data').exists():
    datas.append((str(current_dir / 'data'), 'data'))

# 添加Python文件
python_files = [
    'lottery_prediction_gui.py',
    'special_number_predictor.py', 
    'consistent_predictor.py',
    'historical_backtest.py',
    'consistency_test.py',
    'enhanced_backtest.py',
    'comprehensive_evaluation.py'
]

for py_file in python_files:
    if (current_dir / py_file).exists():
        datas.append((str(current_dir / py_file), '.'))

# 添加配置文件
config_patterns = ['*.json', '*.txt', '*.ini', '*.cfg']
for pattern in config_patterns:
    for file_path in current_dir.glob(pattern):
        if file_path.name not in ['version_info.json']:
            datas.append((str(file_path), '.'))

# 隐藏导入 - 包含所有可能需要的模块
hiddenimports = [
    # PyQt5核心
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'PyQt5.sip',
    
    # 数据处理
    'numpy',
    'pandas',
    'sqlite3',
    
    # 机器学习
    'sklearn',
    'sklearn.ensemble',
    'sklearn.svm', 
    'sklearn.neighbors',
    'sklearn.naive_bayes',
    'sklearn.model_selection',
    'sklearn.preprocessing',
    'sklearn.metrics',
    'sklearn.tree',
    'sklearn.linear_model',
    
    # 可视化
    'matplotlib',
    'matplotlib.pyplot',
    'seaborn',
    
    # 系统模块
    'datetime',
    'pathlib',
    'collections',
    'hashlib',
    'random',
    'warnings',
    'logging',
    'json',
    'csv',
    'os',
    'sys',
    'typing',
    
    # 项目模块
    'special_number_predictor',
    'consistent_predictor', 
    'historical_backtest',
    'consistency_test',
    'enhanced_backtest',
    'comprehensive_evaluation',
    
    # src目录下的模块
    'src.perfect_prediction_system',
    'src.fusion_manager',
    'src.dynamic_fusion_manager_v3',
    'src.algorithm_layer.ml_models.ml_predictor',
    'src.independent_modules.traditional_module',
    'src.independent_modules.ml_module', 
    'src.independent_modules.zodiac_extended_module',
    'src.independent_modules.special_zodiac_module',
    'src.application_layer.gui_main',
    'src.data_layer.database_manager',
    'src.data_layer.data_processor'
]

# 排除不需要的模块
excludes = [
    'tkinter',
    'unittest',
    'test',
    'distutils',
    'setuptools',
    'pip',
    'wheel'
]

block_cipher = None

a = Analysis(
    ['{main_file.name}'],
    pathex=[str(current_dir)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.package_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
        
        spec_file = self.work_dir / f"{self.package_name}.spec"
        with open(spec_file, "w", encoding="utf-8") as f:
            f.write(spec_content)
        
        print(f"✅ 全面spec文件已创建: {spec_file}")
        return spec_file
    
    def build_exe(self, spec_file):
        """构建EXE"""
        print("\n🔨 构建EXE文件...")
        print("-" * 50)
        
        try:
            # 切换到工作目录
            original_cwd = os.getcwd()
            os.chdir(self.work_dir)
            
            # 运行PyInstaller
            cmd = [sys.executable, "-m", "PyInstaller", "--clean", str(spec_file.name)]
            print(f"执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            # 恢复目录
            os.chdir(original_cwd)
            
            if result.returncode == 0:
                print("✅ EXE构建成功")
                
                # 查找EXE文件
                dist_dir = self.work_dir / "dist"
                exe_file = dist_dir / f"{self.package_name}.exe"
                
                if exe_file.exists():
                    print(f"✅ EXE文件: {exe_file}")
                    return exe_file
                else:
                    print("❌ 未找到EXE文件")
                    return None
            else:
                print(f"❌ 构建失败:")
                print(f"stdout: {result.stdout}")
                print(f"stderr: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"❌ 构建出错: {e}")
            return None
    
    def create_final_package(self, exe_file):
        """创建最终包"""
        print("\n📦 创建最终包...")
        print("-" * 50)
        
        if not exe_file or not exe_file.exists():
            print("❌ EXE文件不存在")
            return False
        
        # 创建最终目录
        final_dir = self.output_dir / self.package_name
        if final_dir.exists():
            shutil.rmtree(final_dir)
        final_dir.mkdir(parents=True)
        
        # 复制EXE
        final_exe = final_dir / f"{self.package_name}.exe"
        shutil.copy2(exe_file, final_exe)
        print(f"✅ EXE文件: {final_exe.name}")
        
        # 创建启动脚本
        self._create_launcher(final_dir)
        
        # 创建说明
        self._create_readme(final_dir)
        
        # 获取大小
        exe_size = final_exe.stat().st_size / (1024 * 1024)
        print(f"✅ 文件大小: {exe_size:.1f}MB")
        
        return final_dir
    
    def _create_launcher(self, target_dir):
        """创建启动脚本"""
        launcher_content = f'''@echo off
chcp 65001 > nul
title 六合彩预测系统 {self.version} - 原始GUI版

echo 🎯 六合彩预测系统 {self.version} - 原始GUI版
echo ==========================================
echo 正在启动系统...

echo 🚀 启动原始GUI界面...
echo 注意: 首次启动可能需要较长时间
echo 如果没有窗口显示，请检查任务栏或按Alt+Tab

start "" "{self.package_name}.exe"

echo ✅ 系统已启动
timeout /t 3 > nul
'''
        
        with open(target_dir / "启动系统.bat", "w", encoding="gbk", errors="ignore") as f:
            f.write(launcher_content)
        
        print("✅ 启动脚本已创建")
    
    def _create_readme(self, target_dir):
        """创建说明"""
        readme_content = f'''# 六合彩预测系统 {self.version} - 原始GUI版

## 版本信息
- 版本: {self.version}
- 日期: {self.release_date}
- 类型: 原始GUI界面EXE版

## 使用说明
1. 双击 `启动系统.bat` 或直接运行EXE文件
2. 等待原始GUI界面启动
3. 使用完整的预测功能

## 功能特性
- ✅ 完整的原始GUI界面
- ✅ 特码预测功能
- ✅ 一致性预测
- ✅ 完美预测系统
- ✅ 历史回测
- ✅ 数据管理
- ✅ 所有原始功能

## 注意事项
- 首次启动可能较慢
- 如被杀毒软件拦截请添加白名单
- 预测结果仅供参考
- 包含完整的原始GUI界面

---
© 2025 六合彩预测系统原始GUI版
'''
        
        with open(target_dir / "README.md", "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        print("✅ 说明文档已创建")
    
    def create_package(self):
        """创建封包"""
        print("\n🚀 开始创建原始GUI EXE封包...")
        print("=" * 60)
        
        try:
            # 1. 准备文件
            if not self.prepare_all_files():
                print("❌ 文件准备失败")
                return False
            
            # 2. 创建启动器
            main_file = self.create_main_launcher()
            
            # 3. 创建spec文件
            spec_file = self.create_comprehensive_spec(main_file)
            
            # 4. 构建EXE
            exe_file = self.build_exe(spec_file)
            
            # 5. 创建最终包
            final_package = self.create_final_package(exe_file)
            
            if final_package:
                print("\n" + "=" * 60)
                print("🎉 原始GUI EXE封包创建成功！")
                print("=" * 60)
                print(f"📁 位置: {final_package}")
                print("💡 这个版本使用原始的lottery_prediction_gui.py界面")
                print("=" * 60)
                return True
            else:
                print("\n❌ 封包创建失败")
                return False
                
        except Exception as e:
            print(f"\n❌ 封包出错: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    packager = CorrectEXEPackager()
    success = packager.create_package()
    
    if success:
        print("\n🎊 原始GUI EXE封包完成！")
        print("💡 这个版本使用完整的原始GUI界面")
    else:
        print("\n❌ 封包失败")

if __name__ == "__main__":
    main()
