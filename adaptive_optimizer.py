"""
自适应参数优化器
真正的自动调优各个子模块，产生不同的命中率
"""
import json
import sqlite3
import random
import statistics
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple
from collections import defaultdict

class AdaptiveOptimizer:
    """自适应参数优化器"""

    def __init__(self, gui_instance=None):
        self.gui = gui_instance
        self.optimization_results = []
        self.best_configuration = None

        # 真实的子模块调优参数范围
        self.parameter_ranges = {
            'traditional_analysis': {
                'hot_cold_threshold': (0.1, 0.4),      # 冷热号阈值
                'pattern_weight': (0.5, 2.0),          # 模式权重
                'recent_bias': (0.3, 0.8),             # 近期偏向
                'frequency_factor': (0.6, 1.4),        # 频率因子
                'trend_sensitivity': (0.4, 1.2)        # 趋势敏感度
            },
            'machine_learning': {
                'learning_rate': (0.01, 0.2),          # 学习率
                'feature_importance': (0.5, 1.5),      # 特征重要性
                'regularization': (0.001, 0.1),        # 正则化
                'ensemble_size': (3, 15),              # 集成大小
                'prediction_confidence': (0.6, 0.95)   # 预测置信度
            },
            'zodiac_extended': {
                'zodiac_cycle_weight': (0.4, 1.6),     # 生肖周期权重
                'seasonal_factor': (0.3, 1.2),         # 季节因子
                'element_bias': (0.5, 1.3),            # 五行偏向
                'category_balance': (0.6, 1.4),        # 分类平衡
                'distance_threshold': (0.2, 0.8)       # 距离阈值
            },
            'special_zodiac': {
                'hot_cold_weight': (0.2, 0.4),         # 冷热度权重
                'distance_weight': (0.25, 0.35),       # 远近度权重
                'cycle_weight': (0.2, 0.3),            # 周期性权重
                'category_weight': (0.15, 0.25),       # 分类权重
                'prediction_scope': (12, 18)           # 预测范围
            }
        }

    def generate_adaptive_parameters(self, iteration: int) -> Dict[str, Any]:
        """生成自适应参数"""
        # 使用不同的随机种子确保参数变化
        random.seed(iteration * 12345 + 67890)
        np.random.seed(iteration * 54321 + 98765)

        parameters = {}

        for module, param_ranges in self.parameter_ranges.items():
            module_params = {}
            for param_name, (min_val, max_val) in param_ranges.items():
                if isinstance(min_val, int) and isinstance(max_val, int):
                    # 整数参数
                    value = random.randint(min_val, max_val)
                else:
                    # 浮点数参数
                    value = random.uniform(min_val, max_val)
                module_params[param_name] = value
            parameters[module] = module_params

        return parameters

    def run_adaptive_optimization(self, start_date: str, end_date: str,
                                iterations: int = 15) -> Dict[str, Any]:
        """运行自适应优化"""
        print(f"🔄 开始自适应参数优化...")
        print(f"📅 优化期间: {start_date} 到 {end_date}")
        print(f"🔧 优化轮次: {iterations}次")

        self.optimization_results = []

        # 获取真实的历史数据
        historical_data = self.get_historical_lottery_data(start_date, end_date)

        if not historical_data:
            print("⚠️ 没有找到历史数据，使用模拟数据")
            historical_data = self.generate_simulation_data(start_date, end_date)

        print(f"📊 数据量: {len(historical_data)} 条记录")

        for i in range(iterations):
            print(f"\n🔧 第{i+1}/{iterations}轮优化...")

            # 生成本轮参数
            params = self.generate_adaptive_parameters(i)

            # 运行优化测试
            result = self.run_optimization_test(historical_data, params, i+1)

            if result:
                self.optimization_results.append(result)
                hit_rate = result.get('overall_hit_rate', 0)
                print(f"   📊 本轮命中率: {hit_rate:.1%}")

                # 显示关键参数
                self.display_key_parameters(params, result)
            else:
                print(f"   ❌ 第{i+1}轮优化失败")

        # 分析结果并选择最优配置
        best_result = self.analyze_optimization_results()

        return best_result

    def get_historical_lottery_data(self, start_date: str, end_date: str) -> List[Dict]:
        """获取真实的历史彩票数据"""
        try:
            import sqlite3
            conn = sqlite3.connect('data/lottery.db')
            cursor = conn.cursor()

            query = """
            SELECT date, period, regular_1, regular_2, regular_3,
                   regular_4, regular_5, regular_6, special_number
            FROM lottery_results
            WHERE date BETWEEN ? AND ?
            ORDER BY date
            """

            cursor.execute(query, (start_date, end_date))
            rows = cursor.fetchall()

            historical_data = []
            for row in rows:
                date, period, r1, r2, r3, r4, r5, r6, special = row
                historical_data.append({
                    'date': date,
                    'period': period,
                    'regular_numbers': [r1, r2, r3, r4, r5, r6],
                    'special_number': special
                })

            conn.close()
            return historical_data

        except Exception as e:
            print(f"获取历史数据失败: {e}")
            return []

    def generate_simulation_data(self, start_date: str, end_date: str) -> List[Dict]:
        """生成模拟数据（当没有真实数据时）"""
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')

        simulation_data = []
        current_date = start_dt
        period = 1

        while current_date <= end_dt:
            # 使用日期作为种子，确保数据一致但有变化
            date_seed = hash(current_date.strftime('%Y-%m-%d')) % 100000
            random.seed(date_seed)

            # 生成6个正码 + 1个特码
            all_numbers = list(range(1, 50))
            random.shuffle(all_numbers)

            regular_numbers = sorted(all_numbers[:6])
            special_number = all_numbers[6]

            simulation_data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'period': period,
                'regular_numbers': regular_numbers,
                'special_number': special_number
            })

            current_date += timedelta(days=1)
            period += 1

        return simulation_data

    def run_optimization_test(self, historical_data: List[Dict],
                            params: Dict[str, Any], iteration: int) -> Dict[str, Any]:
        """运行优化测试"""
        try:
            # 为每个模块生成预测结果
            module_results = {}
            total_hits = 0
            total_predictions = 0

            modules = ['traditional_analysis', 'machine_learning', 'zodiac_extended', 'special_zodiac']

            for module in modules:
                module_params = params.get(module, {})

                # 根据参数调整预测策略
                predictions = self.generate_adaptive_predictions(
                    historical_data, module, module_params
                )

                # 计算命中率
                hits = 0
                for i, data in enumerate(historical_data):
                    if i < len(predictions):
                        predicted_numbers = predictions[i]
                        actual_special = data['special_number']

                        if actual_special in predicted_numbers:
                            hits += 1

                hit_rate = hits / len(historical_data) if historical_data else 0

                module_results[module] = {
                    'hit_rate': hit_rate,
                    'hits': hits,
                    'total': len(historical_data),
                    'parameters': module_params
                }

                total_hits += hits
                total_predictions += len(historical_data)

            # 计算整体性能
            overall_hit_rate = total_hits / total_predictions if total_predictions > 0 else 0

            # 计算稳定性（各模块命中率的标准差）
            hit_rates = [result['hit_rate'] for result in module_results.values()]
            stability = 1.0 - (statistics.stdev(hit_rates) if len(hit_rates) > 1 else 0)

            # 计算综合评分
            performance_score = overall_hit_rate * 0.7 + stability * 0.3

            return {
                'iteration': iteration,
                'parameters': params,
                'module_results': module_results,
                'overall_hit_rate': overall_hit_rate,
                'stability': stability,
                'performance_score': performance_score,
                'total_data_points': len(historical_data)
            }

        except Exception as e:
            print(f"优化测试失败: {e}")
            return None

    def generate_adaptive_predictions(self, historical_data: List[Dict],
                                    module: str, params: Dict[str, Any]) -> List[List[int]]:
        """根据参数生成自适应预测"""
        predictions = []

        for i, data in enumerate(historical_data):
            # 根据不同模块和参数生成不同的预测策略
            if module == 'traditional_analysis':
                predicted_numbers = self.traditional_analysis_predict(
                    historical_data[:i], params
                )
            elif module == 'machine_learning':
                predicted_numbers = self.machine_learning_predict(
                    historical_data[:i], params
                )
            elif module == 'zodiac_extended':
                predicted_numbers = self.zodiac_extended_predict(
                    historical_data[:i], params
                )
            elif module == 'special_zodiac':
                predicted_numbers = self.special_zodiac_predict(
                    historical_data[:i], params
                )
            else:
                predicted_numbers = self.default_predict(historical_data[:i])

            predictions.append(predicted_numbers)

        return predictions

    def traditional_analysis_predict(self, history: List[Dict], params: Dict[str, Any]) -> List[int]:
        """传统分析预测（根据参数调整）"""
        if not history:
            return random.sample(range(1, 50), 16)

        # 提取参数
        hot_cold_threshold = params.get('hot_cold_threshold', 0.25)
        pattern_weight = params.get('pattern_weight', 1.0)
        recent_bias = params.get('recent_bias', 0.5)
        frequency_factor = params.get('frequency_factor', 1.0)

        # 统计号码频率
        number_freq = defaultdict(int)
        recent_numbers = []

        for i, data in enumerate(history):
            weight = 1.0
            if i >= len(history) - 10:  # 最近10期
                weight = 1.0 + recent_bias

            # 统计特码
            number_freq[data['special_number']] += weight * frequency_factor

            if i >= len(history) - 5:  # 最近5期
                recent_numbers.append(data['special_number'])

        # 计算冷热号
        avg_freq = sum(number_freq.values()) / len(number_freq) if number_freq else 0
        cold_numbers = [num for num, freq in number_freq.items()
                       if freq < avg_freq * (1 - hot_cold_threshold)]
        hot_numbers = [num for num, freq in number_freq.items()
                      if freq > avg_freq * (1 + hot_cold_threshold)]

        # 根据参数调整选号策略
        candidates = []

        # 冷号策略（传统分析偏向冷号）
        if cold_numbers:
            candidates.extend(random.sample(cold_numbers,
                                          min(8, len(cold_numbers))))

        # 模式分析
        if len(recent_numbers) >= 3:
            pattern_candidates = self.analyze_number_patterns(recent_numbers, pattern_weight)
            candidates.extend(pattern_candidates[:4])

        # 补充随机号码
        remaining = [i for i in range(1, 50) if i not in candidates]
        needed = 16 - len(candidates)
        if needed > 0 and remaining:
            candidates.extend(random.sample(remaining, min(needed, len(remaining))))

        return sorted(candidates[:16])

    def machine_learning_predict(self, history: List[Dict], params: Dict[str, Any]) -> List[int]:
        """机器学习预测（根据参数调整）"""
        if len(history) < 5:
            return random.sample(range(1, 50), 16)

        # 提取参数
        learning_rate = params.get('learning_rate', 0.1)
        feature_importance = params.get('feature_importance', 1.0)
        ensemble_size = int(params.get('ensemble_size', 5))
        confidence = params.get('prediction_confidence', 0.8)

        # 特征工程
        features = self.extract_ml_features(history, feature_importance)

        # 模拟集成学习
        ensemble_predictions = []

        for i in range(ensemble_size):
            # 每个模型使用不同的随机种子
            model_seed = hash(f"ml_{i}_{learning_rate}") % 100000
            random.seed(model_seed)

            # 基于特征的预测
            prediction_scores = {}

            for num in range(1, 50):
                # 计算号码得分（基于特征）
                score = 0.0

                # 频率特征
                freq_score = features.get('frequency', {}).get(num, 0) * feature_importance
                score += freq_score * learning_rate

                # 趋势特征
                trend_score = features.get('trend', {}).get(num, 0) * feature_importance
                score += trend_score * learning_rate * 0.8

                # 周期特征
                cycle_score = features.get('cycle', {}).get(num, 0) * feature_importance
                score += cycle_score * learning_rate * 0.6

                # 添加随机噪声（模拟模型不确定性）
                noise = random.uniform(-0.1, 0.1) * (1 - confidence)
                score += noise

                prediction_scores[num] = score

            # 选择得分最高的16个号码
            top_numbers = sorted(prediction_scores.keys(),
                               key=lambda x: prediction_scores[x], reverse=True)[:16]
            ensemble_predictions.append(top_numbers)

        # 集成投票
        vote_counts = defaultdict(int)
        for prediction in ensemble_predictions:
            for num in prediction:
                vote_counts[num] += 1

        # 选择得票最多的16个号码
        final_prediction = sorted(vote_counts.keys(),
                                key=lambda x: vote_counts[x], reverse=True)[:16]

        return sorted(final_prediction)

    def zodiac_extended_predict(self, history: List[Dict], params: Dict[str, Any]) -> List[int]:
        """生肖扩展预测（根据参数调整）"""
        if not history:
            return random.sample(range(1, 50), 16)

        # 提取参数
        cycle_weight = params.get('zodiac_cycle_weight', 1.0)
        seasonal_factor = params.get('seasonal_factor', 0.8)
        element_bias = params.get('element_bias', 1.0)

        # 生肖映射（简化版）
        zodiac_mapping = {
            1: '蛇', 13: '蛇', 25: '蛇', 37: '蛇', 49: '蛇',
            2: '马', 14: '马', 26: '马', 38: '马',
            3: '羊', 15: '羊', 27: '羊', 39: '羊',
            4: '猴', 16: '猴', 28: '猴', 40: '猴',
            5: '鸡', 17: '鸡', 29: '鸡', 41: '鸡',
            6: '狗', 18: '狗', 30: '狗', 42: '狗',
            7: '猪', 19: '猪', 31: '猪', 43: '猪',
            8: '鼠', 20: '鼠', 32: '鼠', 44: '鼠',
            9: '牛', 21: '牛', 33: '牛', 45: '牛',
            10: '虎', 22: '虎', 34: '虎', 46: '虎',
            11: '兔', 23: '兔', 35: '兔', 47: '兔',
            12: '龙', 24: '龙', 36: '龙', 48: '龙'
        }

        # 统计生肖出现频率
        zodiac_freq = defaultdict(int)
        for data in history:
            zodiac = zodiac_mapping.get(data['special_number'], '未知')
            zodiac_freq[zodiac] += 1

        # 根据参数调整生肖选择策略
        selected_zodiacs = []

        # 周期性分析
        if len(history) >= 12:
            cycle_analysis = self.analyze_zodiac_cycles(history, zodiac_mapping, cycle_weight)
            selected_zodiacs.extend(cycle_analysis[:2])

        # 季节因子
        current_month = datetime.now().month
        seasonal_zodiacs = self.get_seasonal_zodiacs(current_month, seasonal_factor)
        selected_zodiacs.extend(seasonal_zodiacs[:2])

        # 去重并限制数量
        selected_zodiacs = list(set(selected_zodiacs))[:3]

        # 转换为号码
        candidates = []
        for zodiac in selected_zodiacs:
            zodiac_numbers = [num for num, z in zodiac_mapping.items() if z == zodiac]
            candidates.extend(zodiac_numbers)

        # 补充到16个号码
        remaining = [i for i in range(1, 50) if i not in candidates]
        needed = 16 - len(candidates)
        if needed > 0 and remaining:
            candidates.extend(random.sample(remaining, min(needed, len(remaining))))

        return sorted(candidates[:16])

    def special_zodiac_predict(self, history: List[Dict], params: Dict[str, Any]) -> List[int]:
        """特码生肖预测（根据参数调整）"""
        if not history:
            return random.sample(range(1, 50), 16)

        # 提取参数
        hot_cold_weight = params.get('hot_cold_weight', 0.25)
        distance_weight = params.get('distance_weight', 0.3)
        cycle_weight = params.get('cycle_weight', 0.25)
        category_weight = params.get('category_weight', 0.2)
        prediction_scope = int(params.get('prediction_scope', 15))

        # 简化的特码生肖分析
        special_numbers = [data['special_number'] for data in history]

        # 冷热度分析
        number_freq = defaultdict(int)
        for num in special_numbers:
            number_freq[num] += 1

        avg_freq = sum(number_freq.values()) / len(number_freq) if number_freq else 0
        cold_numbers = [num for num, freq in number_freq.items()
                       if freq < avg_freq * (1 - hot_cold_weight)]

        # 距离分析
        recent_numbers = special_numbers[-10:] if len(special_numbers) >= 10 else special_numbers
        distant_numbers = [i for i in range(1, 50) if i not in recent_numbers]

        # 综合评分
        candidates = []

        # 冷号 + 远期号码
        cold_distant = list(set(cold_numbers) & set(distant_numbers))
        candidates.extend(cold_distant[:8])

        # 补充其他号码
        remaining = [i for i in range(1, 50) if i not in candidates]
        needed = prediction_scope - len(candidates)
        if needed > 0 and remaining:
            candidates.extend(random.sample(remaining, min(needed, len(remaining))))

        return sorted(candidates[:prediction_scope])

    def default_predict(self, history: List[Dict]) -> List[int]:
        """默认预测"""
        return random.sample(range(1, 50), 16)

    def extract_ml_features(self, history: List[Dict], importance: float) -> Dict[str, Dict]:
        """提取机器学习特征"""
        features = {
            'frequency': defaultdict(float),
            'trend': defaultdict(float),
            'cycle': defaultdict(float)
        }

        if not history:
            return features

        # 频率特征
        for data in history:
            features['frequency'][data['special_number']] += importance

        # 趋势特征（最近的权重更高）
        for i, data in enumerate(history):
            weight = (i + 1) / len(history) * importance
            features['trend'][data['special_number']] += weight

        # 周期特征（简化）
        if len(history) >= 7:
            for i in range(len(history) - 7):
                current_num = history[i]['special_number']
                future_num = history[i + 7]['special_number']
                features['cycle'][future_num] += importance * 0.5

        return features

    def analyze_number_patterns(self, recent_numbers: List[int], weight: float) -> List[int]:
        """分析号码模式"""
        patterns = []

        if len(recent_numbers) >= 3:
            # 连号模式
            for i in range(len(recent_numbers) - 1):
                diff = recent_numbers[i+1] - recent_numbers[i]
                if abs(diff) <= 3:  # 相近号码
                    next_candidate = recent_numbers[i+1] + diff
                    if 1 <= next_candidate <= 49:
                        patterns.append(next_candidate)

        # 根据权重调整模式强度
        pattern_count = int(len(patterns) * weight)
        return patterns[:pattern_count] if patterns else []

    def analyze_zodiac_cycles(self, history: List[Dict], zodiac_mapping: Dict, weight: float) -> List[str]:
        """分析生肖周期"""
        zodiac_positions = defaultdict(list)

        for i, data in enumerate(history):
            zodiac = zodiac_mapping.get(data['special_number'], '未知')
            zodiac_positions[zodiac].append(i)

        # 计算平均间隔
        cycle_scores = {}
        for zodiac, positions in zodiac_positions.items():
            if len(positions) >= 2:
                intervals = [positions[i+1] - positions[i] for i in range(len(positions)-1)]
                avg_interval = sum(intervals) / len(intervals)
                last_position = positions[-1]
                expected_next = last_position + avg_interval

                # 距离当前位置越近，得分越高
                distance_to_now = abs(len(history) - expected_next)
                score = weight / (1 + distance_to_now * 0.1)
                cycle_scores[zodiac] = score

        # 按得分排序
        sorted_zodiacs = sorted(cycle_scores.keys(), key=lambda x: cycle_scores[x], reverse=True)
        return sorted_zodiacs

    def get_seasonal_zodiacs(self, month: int, factor: float) -> List[str]:
        """获取季节性生肖"""
        seasonal_mapping = {
            1: ['牛', '虎'], 2: ['虎', '兔'], 3: ['兔', '龙'],
            4: ['龙', '蛇'], 5: ['蛇', '马'], 6: ['马', '羊'],
            7: ['羊', '猴'], 8: ['猴', '鸡'], 9: ['鸡', '狗'],
            10: ['狗', '猪'], 11: ['猪', '鼠'], 12: ['鼠', '牛']
        }

        base_zodiacs = seasonal_mapping.get(month, ['鼠', '牛'])

        # 根据因子调整
        if factor > 1.0:
            # 扩展季节影响
            extended = []
            for zodiac in base_zodiacs:
                extended.append(zodiac)
            return extended
        else:
            # 减少季节影响
            return base_zodiacs[:1]

    def display_key_parameters(self, params: Dict[str, Any], result: Dict[str, Any]):
        """显示关键参数"""
        print(f"   🔧 关键参数:")

        # 显示每个模块的关键参数
        for module, module_params in params.items():
            if module in result['module_results']:
                hit_rate = result['module_results'][module]['hit_rate']
                print(f"      {module}: 命中率{hit_rate:.1%}")

                # 显示1-2个关键参数
                key_params = list(module_params.items())[:2]
                for param_name, param_value in key_params:
                    if isinstance(param_value, float):
                        print(f"        {param_name}: {param_value:.3f}")
                    else:
                        print(f"        {param_name}: {param_value}")

    def analyze_optimization_results(self) -> Dict[str, Any]:
        """分析优化结果"""
        if not self.optimization_results:
            return {'error': '没有优化结果'}

        print(f"\n📊 分析{len(self.optimization_results)}轮优化结果...")

        # 按性能评分排序
        sorted_results = sorted(self.optimization_results,
                              key=lambda x: x['performance_score'], reverse=True)

        best_result = sorted_results[0]
        self.best_configuration = best_result

        # 计算统计信息
        hit_rates = [r['overall_hit_rate'] for r in self.optimization_results]
        performance_scores = [r['performance_score'] for r in self.optimization_results]

        analysis = {
            'total_iterations': len(self.optimization_results),
            'best_iteration': best_result['iteration'],
            'best_hit_rate': best_result['overall_hit_rate'],
            'best_performance_score': best_result['performance_score'],
            'best_parameters': best_result['parameters'],
            'hit_rate_range': {
                'min': min(hit_rates),
                'max': max(hit_rates),
                'avg': statistics.mean(hit_rates),
                'std': statistics.stdev(hit_rates) if len(hit_rates) > 1 else 0
            },
            'performance_improvement': {
                'best_vs_avg': best_result['performance_score'] - statistics.mean(performance_scores),
                'improvement_percentage': ((best_result['performance_score'] - statistics.mean(performance_scores)) / statistics.mean(performance_scores)) * 100 if statistics.mean(performance_scores) > 0 else 0
            },
            'module_analysis': self.analyze_module_performance(sorted_results),
            'parameter_insights': self.extract_parameter_insights(sorted_results)
        }

        # 显示结果
        self.display_optimization_results(analysis)

        return analysis

    def analyze_module_performance(self, sorted_results: List[Dict]) -> Dict[str, Any]:
        """分析模块性能"""
        module_stats = defaultdict(list)

        for result in sorted_results:
            for module, module_result in result['module_results'].items():
                module_stats[module].append(module_result['hit_rate'])

        module_analysis = {}
        for module, hit_rates in module_stats.items():
            module_analysis[module] = {
                'avg_hit_rate': statistics.mean(hit_rates),
                'best_hit_rate': max(hit_rates),
                'worst_hit_rate': min(hit_rates),
                'stability': 1.0 - (statistics.stdev(hit_rates) if len(hit_rates) > 1 else 0)
            }

        return module_analysis

    def extract_parameter_insights(self, sorted_results: List[Dict]) -> Dict[str, Any]:
        """提取参数洞察"""
        insights = {}

        # 分析最佳结果的参数特征
        best_params = sorted_results[0]['parameters']

        for module, params in best_params.items():
            module_insights = {}
            for param_name, param_value in params.items():
                module_insights[param_name] = {
                    'best_value': param_value,
                    'recommendation': self.get_parameter_recommendation(param_name, param_value)
                }
            insights[module] = module_insights

        return insights

    def get_parameter_recommendation(self, param_name: str, value: float) -> str:
        """获取参数建议"""
        if 'threshold' in param_name or 'factor' in param_name:
            if value > 1.0:
                return "偏向激进策略"
            elif value < 0.5:
                return "偏向保守策略"
            else:
                return "平衡策略"
        elif 'weight' in param_name:
            if value > 1.2:
                return "高权重，重要性强"
            elif value < 0.8:
                return "低权重，影响较小"
            else:
                return "标准权重"
        else:
            return "参数正常"

    def display_optimization_results(self, analysis: Dict[str, Any]):
        """显示优化结果"""
        print(f"\n🎯 自适应优化完成！")
        print(f"📊 总优化轮次: {analysis['total_iterations']}")
        print(f"🏆 最佳轮次: 第{analysis['best_iteration']}轮")
        print(f"🎪 最佳命中率: {analysis['best_hit_rate']:.1%}")
        print(f"⭐ 最佳性能评分: {analysis['best_performance_score']:.3f}")

        # 显示命中率范围
        hit_range = analysis['hit_rate_range']
        print(f"\n📈 命中率分析:")
        print(f"   最高: {hit_range['max']:.1%}")
        print(f"   最低: {hit_range['min']:.1%}")
        print(f"   平均: {hit_range['avg']:.1%}")
        print(f"   标准差: {hit_range['std']:.3f}")

        # 显示改进效果
        improvement = analysis['performance_improvement']
        print(f"\n📊 改进效果:")
        print(f"   相比平均提升: {improvement['improvement_percentage']:.1f}%")

        # 显示模块分析
        print(f"\n🔧 模块性能分析:")
        module_analysis = analysis['module_analysis']
        for module, stats in module_analysis.items():
            print(f"   {module}:")
            print(f"     平均命中率: {stats['avg_hit_rate']:.1%}")
            print(f"     最佳命中率: {stats['best_hit_rate']:.1%}")
            print(f"     稳定性: {stats['stability']:.3f}")

    def save_best_configuration(self) -> bool:
        """保存最佳配置"""
        if not self.best_configuration:
            print("❌ 没有最佳配置可保存")
            return False

        try:
            config = {
                'timestamp': datetime.now().isoformat(),
                'optimization_type': 'adaptive_parameter_optimization',
                'best_configuration': self.best_configuration,
                'performance_metrics': {
                    'hit_rate': self.best_configuration['overall_hit_rate'],
                    'performance_score': self.best_configuration['performance_score'],
                    'stability': self.best_configuration['stability']
                }
            }

            with open('adaptive_optimal_config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            print("📄 最佳配置已保存到: adaptive_optimal_config.json")
            return True

        except Exception as e:
            print(f"保存配置失败: {e}")
            return False