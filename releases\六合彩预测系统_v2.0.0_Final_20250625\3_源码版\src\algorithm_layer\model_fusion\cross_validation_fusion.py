"""
交叉验证与融合模块
整合生肖分析、传统统计分析、机器学习预测的结果
最终推荐12-16个号码
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from datetime import date, datetime
from collections import Counter
from loguru import logger
import json

class CrossValidationFusion:
    """交叉验证与融合器"""
    
    def __init__(self, db_session, lunar_year_manager, number_attribute_mapper):
        self.db = db_session
        self.lunar_manager = lunar_year_manager
        self.attr_mapper = number_attribute_mapper
        
        # 模块权重配置
        self.module_weights = {
            'zodiac_analysis': 0.30,      # 生肖维度分析权重
            'traditional_analysis': 0.35,  # 传统统计分析权重
            'ml_prediction': 0.35         # 机器学习预测权重
        }
        
        # 融合策略配置
        self.fusion_config = {
            'min_final_numbers': 12,      # 最少推荐号码
            'max_final_numbers': 16,      # 最多推荐号码
            'confidence_threshold': 0.6,  # 置信度阈值
            'consensus_weight': 0.3,      # 一致性权重
            'diversity_weight': 0.2       # 多样性权重
        }
    
    def comprehensive_prediction(self, recent_periods: int = 50, target_date: date = None) -> Dict[str, Any]:
        """综合预测分析"""
        logger.info("开始综合预测分析")
        
        if target_date is None:
            target_date = date.today()
        
        # 1. 执行各模块分析
        analysis_results = self._execute_all_analyses(recent_periods, target_date)
        
        # 2. 交叉验证
        validation_results = self._cross_validation(analysis_results)
        
        # 3. 结果融合
        fusion_results = self._fusion_analysis(analysis_results, validation_results)
        
        # 4. 最终号码选择
        final_numbers = self._select_final_numbers(fusion_results)
        
        # 5. 生成综合报告
        comprehensive_result = {
            "final_recommended_numbers": final_numbers,
            "number_scores": fusion_results['final_scores'],
            "analysis_results": analysis_results,
            "validation_results": validation_results,
            "fusion_details": fusion_results,
            "confidence_level": self._calculate_overall_confidence(fusion_results, final_numbers),
            "prediction_time": datetime.now(),
            "module_weights": self.module_weights,
            "fusion_config": self.fusion_config
        }
        
        logger.info(f"综合预测完成，最终推荐 {len(final_numbers)} 个号码")
        return comprehensive_result
    
    def _execute_all_analyses(self, recent_periods: int, target_date: date) -> Dict[str, Any]:
        """执行所有分析模块"""
        logger.info("执行所有分析模块")
        
        analysis_results = {}
        
        try:
            # 1. 生肖维度扩展分析
            from src.algorithm_layer.zodiac_extended.zodiac_analysis import ZodiacExtendedAnalysis
            zodiac_analyzer = ZodiacExtendedAnalysis(self.db, self.attr_mapper)
            zodiac_result = zodiac_analyzer.predict_next_zodiac_range(recent_periods, target_date)
            analysis_results['zodiac_analysis'] = zodiac_result
            logger.info("生肖维度分析完成")
            
        except Exception as e:
            logger.error(f"生肖维度分析失败: {e}")
            analysis_results['zodiac_analysis'] = None
        
        try:
            # 2. 传统统计分析
            from src.algorithm_layer.traditional_analysis.traditional_statistical import TraditionalStatisticalAnalysis
            traditional_analyzer = TraditionalStatisticalAnalysis(self.db, self.attr_mapper)
            traditional_result = traditional_analyzer.comprehensive_analysis(recent_periods, target_date)
            analysis_results['traditional_analysis'] = traditional_result
            logger.info("传统统计分析完成")
            
        except Exception as e:
            logger.error(f"传统统计分析失败: {e}")
            analysis_results['traditional_analysis'] = None
        
        try:
            # 3. 机器学习预测
            from src.algorithm_layer.ml_models.ml_predictor import MachineLearningPredictor
            ml_predictor = MachineLearningPredictor(self.db, self.attr_mapper, self.lunar_manager)
            ml_result = ml_predictor.predict_numbers(recent_periods, target_date, 16, 24)
            analysis_results['ml_prediction'] = ml_result
            logger.info("机器学习预测完成")
            
        except Exception as e:
            logger.error(f"机器学习预测失败: {e}")
            analysis_results['ml_prediction'] = None
        
        return analysis_results
    
    def _cross_validation(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """交叉验证分析"""
        logger.info("开始交叉验证分析")
        
        validation_results = {
            'consensus_analysis': {},
            'conflict_analysis': {},
            'reliability_scores': {},
            'consistency_metrics': {}
        }
        
        # 1. 一致性分析
        validation_results['consensus_analysis'] = self._analyze_consensus(analysis_results)
        
        # 2. 冲突分析
        validation_results['conflict_analysis'] = self._analyze_conflicts(analysis_results)
        
        # 3. 可靠性评分
        validation_results['reliability_scores'] = self._calculate_reliability_scores(analysis_results)
        
        # 4. 一致性指标
        validation_results['consistency_metrics'] = self._calculate_consistency_metrics(analysis_results)
        
        return validation_results
    
    def _analyze_consensus(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析各模块的一致性"""
        consensus = {
            'common_numbers': [],
            'zodiac_consensus': [],
            'confidence_consensus': 0.0
        }
        
        # 收集各模块推荐的号码
        all_recommendations = {}
        
        # 生肖分析推荐的号码（基于生肖转换为号码）
        if analysis_results.get('zodiac_analysis'):
            zodiac_numbers = self._convert_zodiac_to_numbers(
                analysis_results['zodiac_analysis']['predicted_zodiac']
            )
            all_recommendations['zodiac'] = zodiac_numbers
        
        # 传统分析推荐的号码
        if analysis_results.get('traditional_analysis'):
            all_recommendations['traditional'] = analysis_results['traditional_analysis']['recommended_numbers']
        
        # 机器学习推荐的号码
        if analysis_results.get('ml_prediction'):
            all_recommendations['ml'] = analysis_results['ml_prediction']['predicted_numbers']
        
        # 找出共同推荐的号码
        if len(all_recommendations) >= 2:
            number_votes = Counter()
            for module, numbers in all_recommendations.items():
                for number in numbers:
                    number_votes[number] += 1
            
            # 至少被2个模块推荐的号码
            consensus['common_numbers'] = [
                number for number, votes in number_votes.items() 
                if votes >= 2
            ]
        
        # 生肖一致性
        if analysis_results.get('zodiac_analysis'):
            consensus['zodiac_consensus'] = analysis_results['zodiac_analysis']['predicted_zodiac']
        
        # 置信度一致性
        confidences = []
        for module, result in analysis_results.items():
            if result and 'confidence_level' in result:
                confidences.append(result['confidence_level'])
        
        if confidences:
            consensus['confidence_consensus'] = np.mean(confidences)
        
        return consensus
    
    def _analyze_conflicts(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析各模块的冲突"""
        conflicts = {
            'conflicting_numbers': [],
            'confidence_conflicts': [],
            'strategy_conflicts': []
        }
        
        # 收集各模块的推荐
        recommendations = {}
        for module, result in analysis_results.items():
            if result:
                if module == 'zodiac_analysis':
                    recommendations[module] = self._convert_zodiac_to_numbers(result['predicted_zodiac'])
                elif 'recommended_numbers' in result:
                    recommendations[module] = result['recommended_numbers']
                elif 'predicted_numbers' in result:
                    recommendations[module] = result['predicted_numbers']
        
        # 找出冲突的号码（只被一个模块推荐）
        all_numbers = set()
        for numbers in recommendations.values():
            all_numbers.update(numbers)
        
        for number in all_numbers:
            recommending_modules = [
                module for module, numbers in recommendations.items() 
                if number in numbers
            ]
            if len(recommending_modules) == 1:
                conflicts['conflicting_numbers'].append({
                    'number': number,
                    'module': recommending_modules[0]
                })
        
        # 置信度冲突
        confidences = {}
        for module, result in analysis_results.items():
            if result and 'confidence_level' in result:
                confidences[module] = result['confidence_level']
        
        if len(confidences) >= 2:
            conf_values = list(confidences.values())
            if max(conf_values) - min(conf_values) > 0.3:  # 置信度差异超过30%
                conflicts['confidence_conflicts'] = confidences
        
        return conflicts
    
    def _calculate_reliability_scores(self, analysis_results: Dict[str, Any]) -> Dict[str, float]:
        """计算各模块的可靠性评分"""
        reliability_scores = {}
        
        for module, result in analysis_results.items():
            if not result:
                reliability_scores[module] = 0.0
                continue
            
            score = 0.5  # 基础分数
            
            # 基于置信度调整
            if 'confidence_level' in result:
                confidence = result['confidence_level']
                score += confidence * 0.3
            
            # 基于数据量调整
            if module == 'zodiac_analysis' and 'data_periods' in result:
                data_ratio = min(1.0, result['data_periods'] / 50)
                score += data_ratio * 0.1
            elif module == 'traditional_analysis' and 'data_periods' in result:
                data_ratio = min(1.0, result['data_periods'] / 50)
                score += data_ratio * 0.1
            elif module == 'ml_prediction' and 'training_data_size' in result:
                data_ratio = min(1.0, result['training_data_size'] / 30)
                score += data_ratio * 0.1
            
            # 基于推荐数量的合理性调整
            if module == 'zodiac_analysis':
                if len(result.get('predicted_zodiac', [])) == 4:
                    score += 0.1
            elif 'recommended_numbers' in result:
                num_count = len(result['recommended_numbers'])
                if 12 <= num_count <= 24:
                    score += 0.1
            elif 'predicted_numbers' in result:
                num_count = len(result['predicted_numbers'])
                if 12 <= num_count <= 24:
                    score += 0.1
            
            reliability_scores[module] = min(1.0, score)
        
        return reliability_scores
    
    def _calculate_consistency_metrics(self, analysis_results: Dict[str, Any]) -> Dict[str, float]:
        """计算一致性指标"""
        metrics = {
            'overall_consistency': 0.0,
            'number_overlap_rate': 0.0,
            'confidence_variance': 0.0
        }
        
        # 收集所有推荐号码
        all_recommendations = []
        for module, result in analysis_results.items():
            if result:
                if module == 'zodiac_analysis':
                    numbers = self._convert_zodiac_to_numbers(result['predicted_zodiac'])
                elif 'recommended_numbers' in result:
                    numbers = result['recommended_numbers']
                elif 'predicted_numbers' in result:
                    numbers = result['predicted_numbers']
                else:
                    continue
                all_recommendations.append(set(numbers))
        
        # 计算重叠率
        if len(all_recommendations) >= 2:
            total_overlap = 0
            comparisons = 0
            
            for i in range(len(all_recommendations)):
                for j in range(i + 1, len(all_recommendations)):
                    set1, set2 = all_recommendations[i], all_recommendations[j]
                    overlap = len(set1 & set2) / len(set1 | set2) if set1 | set2 else 0
                    total_overlap += overlap
                    comparisons += 1
            
            metrics['number_overlap_rate'] = total_overlap / comparisons if comparisons > 0 else 0
        
        # 计算置信度方差
        confidences = []
        for module, result in analysis_results.items():
            if result and 'confidence_level' in result:
                confidences.append(result['confidence_level'])
        
        if len(confidences) >= 2:
            metrics['confidence_variance'] = np.var(confidences)
        
        # 综合一致性
        metrics['overall_consistency'] = (
            metrics['number_overlap_rate'] * 0.7 + 
            (1 - metrics['confidence_variance']) * 0.3
        )
        
        return metrics
    
    def _fusion_analysis(self, analysis_results: Dict[str, Any], 
                        validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """融合分析"""
        logger.info("开始融合分析")
        
        fusion_results = {
            'final_scores': {},
            'weighted_scores': {},
            'consensus_bonus': {},
            'reliability_adjustment': {}
        }
        
        # 初始化所有号码的得分
        for number in range(1, 50):
            fusion_results['final_scores'][number] = 0.0
            fusion_results['weighted_scores'][number] = 0.0
            fusion_results['consensus_bonus'][number] = 0.0
            fusion_results['reliability_adjustment'][number] = 0.0
        
        # 1. 加权融合各模块结果
        reliability_scores = validation_results['reliability_scores']
        
        for module, result in analysis_results.items():
            if not result:
                continue
            
            module_weight = self.module_weights.get(module, 0.0)
            reliability = reliability_scores.get(module, 0.5)
            effective_weight = module_weight * reliability
            
            # 获取模块的号码评分
            module_scores = self._extract_module_scores(module, result)
            
            for number, score in module_scores.items():
                if 1 <= number <= 49:
                    fusion_results['weighted_scores'][number] += score * effective_weight
        
        # 2. 一致性奖励
        consensus_numbers = validation_results['consensus_analysis']['common_numbers']
        consensus_bonus = 0.1  # 一致性奖励分数
        
        for number in consensus_numbers:
            if 1 <= number <= 49:
                fusion_results['consensus_bonus'][number] = consensus_bonus
        
        # 3. 可靠性调整
        overall_reliability = np.mean(list(reliability_scores.values())) if reliability_scores else 0.5
        reliability_factor = 0.8 + 0.4 * overall_reliability  # 0.8-1.2的调整因子
        
        for number in range(1, 50):
            fusion_results['reliability_adjustment'][number] = reliability_factor
        
        # 4. 计算最终得分
        for number in range(1, 50):
            final_score = (
                fusion_results['weighted_scores'][number] + 
                fusion_results['consensus_bonus'][number]
            ) * fusion_results['reliability_adjustment'][number]
            
            fusion_results['final_scores'][number] = final_score
        
        return fusion_results
    
    def _extract_module_scores(self, module: str, result: Dict[str, Any]) -> Dict[int, float]:
        """提取模块的号码评分"""
        scores = {}
        
        if module == 'zodiac_analysis':
            # 生肖分析：将生肖转换为号码评分
            predicted_zodiac = result.get('predicted_zodiac', [])
            zodiac_scores = result.get('zodiac_scores', {})
            
            for number in range(1, 50):
                # 获取号码对应的生肖
                zodiac_mapping = self.lunar_manager.get_zodiac_mapping_by_date(date.today())
                number_zodiac = zodiac_mapping.get(number, {}).get('zodiac', '')
                
                if number_zodiac in predicted_zodiac:
                    # 推荐生肖的号码给高分
                    zodiac_score = zodiac_scores.get(number_zodiac, 0.5)
                    scores[number] = zodiac_score
                else:
                    scores[number] = 0.1  # 非推荐生肖的号码给低分
        
        elif module == 'traditional_analysis':
            # 传统分析：直接使用号码评分
            number_scores = result.get('number_scores', {})
            recommended_numbers = result.get('recommended_numbers', [])
            
            for number in range(1, 50):
                if number in recommended_numbers:
                    scores[number] = number_scores.get(number, 0.7)
                else:
                    scores[number] = number_scores.get(number, 0.3)
        
        elif module == 'ml_prediction':
            # 机器学习：使用概率作为评分
            number_probabilities = result.get('number_probabilities', {})
            predicted_numbers = result.get('predicted_numbers', [])
            
            for number in range(1, 50):
                if number in predicted_numbers:
                    scores[number] = number_probabilities.get(number, 0.7)
                else:
                    scores[number] = number_probabilities.get(number, 0.1)
        
        return scores
    
    def _convert_zodiac_to_numbers(self, zodiac_list: List[str]) -> List[int]:
        """将生肖转换为对应的号码"""
        numbers = []
        
        try:
            # 获取当前的生肖映射
            zodiac_mapping = self.lunar_manager.get_zodiac_mapping_by_date(date.today())
            
            for number, mapping in zodiac_mapping.items():
                if mapping.get('zodiac') in zodiac_list:
                    numbers.append(number)
        
        except Exception as e:
            logger.error(f"生肖转换号码失败: {e}")
        
        return numbers
    
    def _select_final_numbers(self, fusion_results: Dict[str, Any]) -> List[int]:
        """选择最终推荐号码"""
        final_scores = fusion_results['final_scores']
        
        # 按得分排序
        sorted_numbers = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 动态选择数量
        min_count = self.fusion_config['min_final_numbers']
        max_count = self.fusion_config['max_final_numbers']
        
        # 基于得分分布选择阈值
        scores = [score for number, score in sorted_numbers]
        threshold = np.percentile(scores, 75)  # 75分位数
        
        selected = []
        for number, score in sorted_numbers:
            if score >= threshold and len(selected) < max_count:
                selected.append(number)
            elif len(selected) >= min_count:
                break
        
        # 确保数量在范围内
        if len(selected) < min_count:
            selected = [number for number, score in sorted_numbers[:min_count]]
        elif len(selected) > max_count:
            selected = selected[:max_count]
        
        return sorted(selected)
    
    def _calculate_overall_confidence(self, fusion_results: Dict[str, Any], 
                                    final_numbers: List[int]) -> float:
        """计算整体置信度"""
        if not final_numbers:
            return 0.0
        
        # 最终推荐号码的平均得分
        final_scores = fusion_results['final_scores']
        recommended_scores = [final_scores[number] for number in final_numbers]
        avg_recommended_score = np.mean(recommended_scores)
        
        # 所有号码的平均得分
        all_scores = list(final_scores.values())
        avg_all_score = np.mean(all_scores)
        
        # 基于得分差异计算置信度
        if avg_all_score > 0:
            confidence = min(0.95, max(0.1, avg_recommended_score / avg_all_score))
        else:
            confidence = 0.5
        
        return confidence
    
    def get_comprehensive_report(self, comprehensive_result: Dict[str, Any]) -> str:
        """生成综合预测报告"""
        report = []
        report.append("=" * 80)
        report.append("六合彩预测系统 - 综合预测报告")
        report.append("=" * 80)
        report.append(f"预测时间: {comprehensive_result['prediction_time']}")
        report.append(f"整体置信度: {comprehensive_result['confidence_level']:.2%}")
        report.append("")
        
        # 最终推荐号码
        final_numbers = comprehensive_result['final_recommended_numbers']
        report.append(f"🎯 最终推荐号码 ({len(final_numbers)}个):")
        for i, number in enumerate(final_numbers, 1):
            score = comprehensive_result['number_scores'][number]
            report.append(f"  {i:2d}. {number:2d} (综合得分: {score:.3f})")
        
        report.append("")
        report.append(f"📋 推荐号码列表: {final_numbers}")
        
        # 各模块分析结果
        report.append("")
        report.append("📊 各模块分析结果:")
        
        analysis_results = comprehensive_result['analysis_results']
        
        # 生肖分析结果
        if analysis_results.get('zodiac_analysis'):
            zodiac_result = analysis_results['zodiac_analysis']
            report.append(f"  🐲 生肖分析: {zodiac_result['predicted_zodiac']} (置信度: {zodiac_result['confidence_level']:.1%})")
        
        # 传统分析结果
        if analysis_results.get('traditional_analysis'):
            traditional_result = analysis_results['traditional_analysis']
            report.append(f"  📈 传统分析: {len(traditional_result['recommended_numbers'])}个号码 (置信度: {traditional_result['confidence_level']:.1%})")
        
        # 机器学习结果
        if analysis_results.get('ml_prediction'):
            ml_result = analysis_results['ml_prediction']
            report.append(f"  🤖 机器学习: {len(ml_result['predicted_numbers'])}个号码 (置信度: {ml_result['confidence_level']:.1%})")
        
        # 交叉验证结果
        validation_results = comprehensive_result['validation_results']
        consensus = validation_results['consensus_analysis']
        
        report.append("")
        report.append("🔍 交叉验证结果:")
        report.append(f"  一致推荐号码: {consensus['common_numbers']}")
        report.append(f"  号码重叠率: {validation_results['consistency_metrics']['number_overlap_rate']:.1%}")
        report.append(f"  整体一致性: {validation_results['consistency_metrics']['overall_consistency']:.1%}")
        
        # 模块权重
        report.append("")
        report.append("⚖️ 模块权重配置:")
        for module, weight in comprehensive_result['module_weights'].items():
            report.append(f"  {module}: {weight:.1%}")
        
        report.append("")
        report.append("=" * 80)
        report.append("预测完成 - 祝您好运！")
        report.append("=" * 80)
        
        return "\n".join(report)
