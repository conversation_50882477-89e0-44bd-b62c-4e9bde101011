#!/usr/bin/env python3
"""
一致性预测器 - 确保预测结果的一致性和可重现性
"""

import hashlib
import random
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging
from .lottery_prediction_system import LotteryPredictionSystem

class ConsistencyPredictor:
    """一致性预测器"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # 初始化核心预测系统
        self.lottery_system = LotteryPredictionSystem(db_path)
        
        # 确定性种子基础
        self.seed_base = "lottery_prediction_2024"
        
        self.logger.info("🔒 一致性预测器初始化完成")
        print(f"🔒 确定性种子: {self.seed_base}")
    
    def predict_with_consistency(self, target_date: str, analysis_days: int = 100) -> Dict[str, Any]:
        """执行一致性预测"""
        try:
            self.logger.info(f"开始一致性预测，目标日期: {target_date}")
            
            # 生成确定性种子
            deterministic_seed = self._generate_deterministic_seed(target_date)
            
            # 设置随机种子确保一致性
            random.seed(deterministic_seed)
            np.random.seed(deterministic_seed % 2**32)
            
            # 执行预测
            special_result = self.lottery_system.predict_special_number(target_date, analysis_days)
            zodiac_result = self.lottery_system.predict_zodiac(target_date, analysis_days)
            
            # 应用一致性增强
            enhanced_special = self._enhance_special_consistency(special_result, target_date)
            enhanced_zodiac = self._enhance_zodiac_consistency(zodiac_result, target_date)
            
            # 构建一致性结果
            result = {
                "recommended_numbers": enhanced_special["numbers"],
                "recommended_zodiacs": enhanced_zodiac["zodiacs"],
                "confidence": (enhanced_special["confidence"] + enhanced_zodiac["confidence"]) / 2,
                "consistency_info": {
                    "deterministic_seed": deterministic_seed,
                    "seed_base": self.seed_base,
                    "target_date": target_date,
                    "consistency_level": "high"
                },
                "special_analysis": enhanced_special,
                "zodiac_analysis": enhanced_zodiac,
                "prediction_method": "consistency_enhanced",
                "prediction_time": datetime.now().isoformat()
            }
            
            self.logger.info(f"一致性预测完成，推荐 {len(result['recommended_numbers'])} 个号码")
            return result
            
        except Exception as e:
            self.logger.error(f"一致性预测失败: {e}")
            return self._get_fallback_consistent_prediction(target_date)
    
    def _generate_deterministic_seed(self, target_date: str) -> int:
        """生成确定性种子"""
        # 组合种子字符串
        seed_string = f"{self.seed_base}_{target_date}"
        
        # 使用MD5哈希生成确定性种子
        hash_object = hashlib.md5(seed_string.encode())
        hash_hex = hash_object.hexdigest()
        
        # 转换为整数种子
        seed = int(hash_hex[:8], 16)
        
        self.logger.debug(f"生成确定性种子: {seed} (来源: {seed_string})")
        return seed
    
    def _enhance_special_consistency(self, special_result: Dict[str, Any], target_date: str) -> Dict[str, Any]:
        """增强特码预测的一致性"""
        original_numbers = special_result.get("recommended_numbers", [])
        
        if not original_numbers:
            # 如果没有原始预测，生成确定性预测
            seed = self._generate_deterministic_seed(target_date + "_special")
            random.seed(seed)
            consistent_numbers = sorted(random.sample(range(1, 50), 16))
        else:
            # 对原始预测应用确定性调整
            seed = self._generate_deterministic_seed(target_date + "_special_adjust")
            random.seed(seed)
            
            # 保留大部分原始号码，进行小幅调整
            consistent_numbers = original_numbers.copy()
            
            # 随机替换1-2个号码以增加变化但保持一致性
            if len(consistent_numbers) >= 16:
                replace_count = random.randint(1, 2)
                for _ in range(replace_count):
                    if consistent_numbers:
                        old_index = random.randint(0, len(consistent_numbers) - 1)
                        new_number = random.randint(1, 49)
                        while new_number in consistent_numbers:
                            new_number = random.randint(1, 49)
                        consistent_numbers[old_index] = new_number
                
                consistent_numbers.sort()
        
        # 计算增强置信度
        base_confidence = special_result.get("confidence", 0.6)
        enhanced_confidence = min(0.9, base_confidence + 0.1)  # 一致性增强提升置信度
        
        return {
            "numbers": consistent_numbers,
            "confidence": enhanced_confidence,
            "original_count": len(original_numbers),
            "enhanced_count": len(consistent_numbers),
            "consistency_applied": True,
            "enhancement_method": "deterministic_adjustment"
        }
    
    def _enhance_zodiac_consistency(self, zodiac_result: Dict[str, Any], target_date: str) -> Dict[str, Any]:
        """增强生肖预测的一致性"""
        original_zodiacs = zodiac_result.get("recommended_zodiacs", [])
        
        # 所有生肖列表
        all_zodiacs = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪']
        
        if not original_zodiacs:
            # 生成确定性生肖预测
            seed = self._generate_deterministic_seed(target_date + "_zodiac")
            random.seed(seed)
            consistent_zodiacs = random.sample(all_zodiacs, 4)
        else:
            # 对原始预测应用确定性调整
            seed = self._generate_deterministic_seed(target_date + "_zodiac_adjust")
            random.seed(seed)
            
            consistent_zodiacs = original_zodiacs.copy()
            
            # 确保有4个生肖
            while len(consistent_zodiacs) < 4:
                remaining_zodiacs = [z for z in all_zodiacs if z not in consistent_zodiacs]
                if remaining_zodiacs:
                    consistent_zodiacs.append(random.choice(remaining_zodiacs))
                else:
                    break
            
            # 如果超过4个，保留前4个
            consistent_zodiacs = consistent_zodiacs[:4]
            
            # 可能的小幅调整
            if random.random() < 0.3:  # 30%概率进行调整
                replace_index = random.randint(0, len(consistent_zodiacs) - 1)
                remaining_zodiacs = [z for z in all_zodiacs if z not in consistent_zodiacs]
                if remaining_zodiacs:
                    consistent_zodiacs[replace_index] = random.choice(remaining_zodiacs)
        
        # 计算增强置信度
        base_confidence = zodiac_result.get("confidence", 0.6)
        enhanced_confidence = min(0.9, base_confidence + 0.1)
        
        return {
            "zodiacs": consistent_zodiacs,
            "confidence": enhanced_confidence,
            "original_count": len(original_zodiacs),
            "enhanced_count": len(consistent_zodiacs),
            "consistency_applied": True,
            "enhancement_method": "deterministic_selection"
        }
    
    def _get_fallback_consistent_prediction(self, target_date: str) -> Dict[str, Any]:
        """获取备用一致性预测"""
        # 生成完全确定性的备用预测
        seed = self._generate_deterministic_seed(target_date + "_fallback")
        random.seed(seed)
        
        # 确定性号码
        consistent_numbers = sorted(random.sample(range(1, 50), 16))
        
        # 确定性生肖
        all_zodiacs = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪']
        consistent_zodiacs = random.sample(all_zodiacs, 4)
        
        return {
            "recommended_numbers": consistent_numbers,
            "recommended_zodiacs": consistent_zodiacs,
            "confidence": 0.5,
            "consistency_info": {
                "deterministic_seed": seed,
                "seed_base": self.seed_base,
                "target_date": target_date,
                "consistency_level": "fallback"
            },
            "special_analysis": {
                "numbers": consistent_numbers,
                "confidence": 0.5,
                "consistency_applied": True,
                "enhancement_method": "fallback_deterministic"
            },
            "zodiac_analysis": {
                "zodiacs": consistent_zodiacs,
                "confidence": 0.5,
                "consistency_applied": True,
                "enhancement_method": "fallback_deterministic"
            },
            "prediction_method": "fallback_consistent",
            "prediction_time": datetime.now().isoformat(),
            "note": "使用备用一致性预测"
        }
    
    def verify_consistency(self, target_date: str, test_count: int = 5) -> Dict[str, Any]:
        """验证预测一致性"""
        self.logger.info(f"开始一致性验证，测试次数: {test_count}")
        
        results = []
        for i in range(test_count):
            result = self.predict_with_consistency(target_date)
            results.append({
                "test_index": i + 1,
                "numbers": result.get("recommended_numbers", []),
                "zodiacs": result.get("recommended_zodiacs", []),
                "confidence": result.get("confidence", 0)
            })
        
        # 检查一致性
        first_numbers = results[0]["numbers"] if results else []
        first_zodiacs = results[0]["zodiacs"] if results else []
        
        numbers_consistent = all(r["numbers"] == first_numbers for r in results)
        zodiacs_consistent = all(r["zodiacs"] == first_zodiacs for r in results)
        
        consistency_report = {
            "target_date": target_date,
            "test_count": test_count,
            "numbers_consistent": numbers_consistent,
            "zodiacs_consistent": zodiacs_consistent,
            "overall_consistent": numbers_consistent and zodiacs_consistent,
            "test_results": results,
            "verification_time": datetime.now().isoformat()
        }
        
        if consistency_report["overall_consistent"]:
            self.logger.info("✅ 一致性验证通过")
        else:
            self.logger.warning("❌ 一致性验证失败")
        
        return consistency_report
    
    def get_consistency_info(self) -> Dict[str, Any]:
        """获取一致性信息"""
        return {
            "name": "一致性预测器",
            "version": "1.0.0",
            "features": [
                "确定性种子生成",
                "一致性增强",
                "预测验证",
                "备用预测"
            ],
            "seed_base": self.seed_base,
            "consistency_level": "high",
            "supported_methods": [
                "deterministic_adjustment",
                "deterministic_selection",
                "fallback_deterministic"
            ]
        }

# 便捷函数
def create_consistency_predictor(db_path: str = "data/lottery.db") -> ConsistencyPredictor:
    """创建一致性预测器实例"""
    return ConsistencyPredictor(db_path)

def predict_with_consistency(target_date: str, db_path: str = "data/lottery.db") -> Dict[str, Any]:
    """执行一致性预测的便捷函数"""
    predictor = create_consistency_predictor(db_path)
    return predictor.predict_with_consistency(target_date)

def verify_prediction_consistency(target_date: str, test_count: int = 5, db_path: str = "data/lottery.db") -> Dict[str, Any]:
    """验证预测一致性的便捷函数"""
    predictor = create_consistency_predictor(db_path)
    return predictor.verify_consistency(target_date, test_count)
