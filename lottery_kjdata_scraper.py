"""
专门针对kjdata端点的六合彩数据抓取器
基于测试发现的有效API端点
"""

import requests
import json
import pandas as pd
import sqlite3
from datetime import datetime
from typing import List, Dict, Any, Optional
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class KjdataLotteryScraper:
    """kjdata端点专用抓取器"""
    
    def __init__(self):
        self.kjdata_url = "https://yyswz.uhfasuf.com:14949/api/kjdata"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive'
        })
        self.session.verify = False
    
    def fetch_kjdata(self) -> Optional[Dict]:
        """获取kjdata数据"""
        print("📡 正在获取kjdata数据...")
        
        try:
            response = self.session.get(self.kjdata_url, timeout=30)
            
            if response.ok:
                print(f"✅ 数据获取成功")
                print(f"  响应大小: {len(response.text):,} 字节")
                
                # 解析JSON
                data = response.json()
                print(f"  数据类型: {type(data)}")
                
                if isinstance(data, dict) and 'data' in data:
                    records = data['data']
                    print(f"  记录数量: {len(records):,} 条")
                    
                    if len(records) > 0:
                        print(f"  最新记录: {records[0]}")
                        print(f"  最早记录: {records[-1]}")
                    
                    return data
                else:
                    print(f"⚠️ 数据格式异常: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                    return data
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"  错误内容: {response.text[:200]}...")
                return None
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def parse_lottery_record(self, record: Dict) -> Dict:
        """解析单条彩票记录"""
        try:
            # 解析号码字符串
            num_str = record.get('num', '')
            numbers = []
            
            if num_str:
                # 分割号码
                parts = num_str.replace(' ', '').split(',')
                numbers = [int(part) for part in parts if part.isdigit()]
            
            # 构建标准格式
            parsed = {
                'id': record.get('id'),
                'year': record.get('year'),
                'period': record.get('qishu', ''),
                'draw_date': record.get('date', ''),
                'raw_numbers': num_str,
                'all_numbers': numbers,
                'regular_numbers': numbers[:6] if len(numbers) >= 6 else numbers,
                'special_number': numbers[6] if len(numbers) > 6 else None,
                'number_count': len(numbers),
                'parse_status': 'success' if len(numbers) >= 6 else 'incomplete'
            }
            
            return parsed
            
        except Exception as e:
            return {
                'id': record.get('id'),
                'parse_status': 'error',
                'error': str(e),
                'raw_record': record
            }
    
    def process_all_data(self, raw_data: Dict) -> List[Dict]:
        """处理所有数据"""
        print("🔄 处理数据...")
        
        if not isinstance(raw_data, dict) or 'data' not in raw_data:
            print("❌ 数据格式错误")
            return []
        
        records = raw_data['data']
        processed_records = []
        
        success_count = 0
        error_count = 0
        
        for record in records:
            parsed = self.parse_lottery_record(record)
            processed_records.append(parsed)
            
            if parsed.get('parse_status') == 'success':
                success_count += 1
            else:
                error_count += 1
        
        print(f"✅ 数据处理完成")
        print(f"  总记录数: {len(processed_records):,}")
        print(f"  成功解析: {success_count:,} 条")
        print(f"  解析失败: {error_count:,} 条")
        print(f"  成功率: {success_count/len(processed_records)*100:.1f}%")
        
        return processed_records
    
    def save_to_csv(self, processed_data: List[Dict], filename: str = None) -> str:
        """保存到CSV文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"六合彩数据_kjdata_{timestamp}.csv"
        
        print(f"💾 保存数据到CSV: {filename}")
        
        try:
            # 准备CSV数据
            csv_data = []
            
            for record in processed_data:
                if record.get('parse_status') == 'success':
                    regular_nums = record.get('regular_numbers', [])
                    
                    csv_record = {
                        'ID': record.get('id'),
                        '年份': record.get('year'),
                        '期号': record.get('period'),
                        '开奖日期': record.get('draw_date'),
                        '原始号码': record.get('raw_numbers'),
                        '正码1': regular_nums[0] if len(regular_nums) > 0 else None,
                        '正码2': regular_nums[1] if len(regular_nums) > 1 else None,
                        '正码3': regular_nums[2] if len(regular_nums) > 2 else None,
                        '正码4': regular_nums[3] if len(regular_nums) > 3 else None,
                        '正码5': regular_nums[4] if len(regular_nums) > 4 else None,
                        '正码6': regular_nums[5] if len(regular_nums) > 5 else None,
                        '特码': record.get('special_number'),
                        '号码总数': record.get('number_count'),
                        '解析状态': record.get('parse_status')
                    }
                    csv_data.append(csv_record)
            
            # 创建DataFrame
            df = pd.DataFrame(csv_data)
            
            # 按日期排序
            if '开奖日期' in df.columns:
                df = df.sort_values('开奖日期')
            
            # 保存CSV
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
            print(f"✅ CSV保存成功")
            print(f"  文件名: {filename}")
            print(f"  有效记录: {len(df):,} 条")
            
            # 显示数据预览
            if len(df) > 0:
                print(f"\n📊 数据预览:")
                print(df.head().to_string())
                
                # 统计信息
                print(f"\n📈 数据统计:")
                if '年份' in df.columns:
                    year_counts = df['年份'].value_counts().sort_index()
                    print(f"  年份分布:")
                    for year, count in year_counts.items():
                        print(f"    {year}: {count} 条")
                
                if '开奖日期' in df.columns:
                    print(f"  日期范围: {df['开奖日期'].min()} 到 {df['开奖日期'].max()}")
            
            return filename
            
        except Exception as e:
            print(f"❌ CSV保存失败: {e}")
            return ""
    
    def save_to_database(self, processed_data: List[Dict], db_path: str = "data/lottery_kjdata.db") -> bool:
        """保存到数据库"""
        print(f"🗄️ 保存数据到数据库: {db_path}")
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 创建表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS lottery_results_kjdata (
                    id INTEGER PRIMARY KEY,
                    year INTEGER,
                    period TEXT,
                    draw_date TEXT,
                    raw_numbers TEXT,
                    regular_1 INTEGER,
                    regular_2 INTEGER,
                    regular_3 INTEGER,
                    regular_4 INTEGER,
                    regular_5 INTEGER,
                    regular_6 INTEGER,
                    special_number INTEGER,
                    number_count INTEGER,
                    parse_status TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 插入数据
            success_count = 0
            
            for record in processed_data:
                if record.get('parse_status') == 'success':
                    regular_nums = record.get('regular_numbers', [])
                    
                    cursor.execute('''
                        INSERT OR REPLACE INTO lottery_results_kjdata 
                        (id, year, period, draw_date, raw_numbers, 
                         regular_1, regular_2, regular_3, regular_4, regular_5, regular_6,
                         special_number, number_count, parse_status)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        record.get('id'),
                        record.get('year'),
                        record.get('period'),
                        record.get('draw_date'),
                        record.get('raw_numbers'),
                        regular_nums[0] if len(regular_nums) > 0 else None,
                        regular_nums[1] if len(regular_nums) > 1 else None,
                        regular_nums[2] if len(regular_nums) > 2 else None,
                        regular_nums[3] if len(regular_nums) > 3 else None,
                        regular_nums[4] if len(regular_nums) > 4 else None,
                        regular_nums[5] if len(regular_nums) > 5 else None,
                        record.get('special_number'),
                        record.get('number_count'),
                        record.get('parse_status')
                    ))
                    success_count += 1
            
            conn.commit()
            conn.close()
            
            print(f"✅ 数据库保存成功")
            print(f"  保存记录: {success_count:,} 条")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据库保存失败: {e}")
            return False
    
    def analyze_data_quality(self, processed_data: List[Dict]):
        """分析数据质量"""
        print("\n📊 数据质量分析")
        print("-" * 50)
        
        if not processed_data:
            print("❌ 无数据可分析")
            return
        
        total_records = len(processed_data)
        success_records = [r for r in processed_data if r.get('parse_status') == 'success']
        
        print(f"📈 基本统计:")
        print(f"  总记录数: {total_records:,}")
        print(f"  成功解析: {len(success_records):,}")
        print(f"  成功率: {len(success_records)/total_records*100:.1f}%")
        
        if success_records:
            # 年份分布
            years = {}
            for record in success_records:
                year = record.get('year')
                if year:
                    years[year] = years.get(year, 0) + 1
            
            print(f"\n📅 年份分布:")
            for year in sorted(years.keys()):
                print(f"  {year}: {years[year]:,} 条")
            
            # 号码统计
            all_numbers = []
            special_numbers = []
            
            for record in success_records:
                if record.get('regular_numbers'):
                    all_numbers.extend(record['regular_numbers'])
                if record.get('special_number'):
                    special_numbers.append(record['special_number'])
            
            if all_numbers:
                print(f"\n🔢 号码统计:")
                print(f"  正码总数: {len(all_numbers):,}")
                print(f"  号码范围: {min(all_numbers)} - {max(all_numbers)}")
                print(f"  特码总数: {len(special_numbers):,}")
                if special_numbers:
                    print(f"  特码范围: {min(special_numbers)} - {max(special_numbers)}")
    
    def comprehensive_scrape(self) -> Dict[str, Any]:
        """综合数据抓取"""
        print("🚀 开始综合数据抓取")
        print("=" * 60)
        
        # 1. 获取原始数据
        raw_data = self.fetch_kjdata()
        if not raw_data:
            print("❌ 数据获取失败")
            return {}
        
        # 2. 处理数据
        processed_data = self.process_all_data(raw_data)
        if not processed_data:
            print("❌ 数据处理失败")
            return {}
        
        # 3. 保存CSV
        csv_file = self.save_to_csv(processed_data)
        
        # 4. 保存数据库
        db_success = self.save_to_database(processed_data)
        
        # 5. 数据质量分析
        self.analyze_data_quality(processed_data)
        
        return {
            'total_records': len(processed_data),
            'success_records': len([r for r in processed_data if r.get('parse_status') == 'success']),
            'csv_file': csv_file,
            'database_saved': db_success,
            'processed_data': processed_data
        }

def main():
    """主函数"""
    print("🎯 kjdata端点专用数据抓取器")
    print("🌐 数据源: https://yyswz.uhfasuf.com:14949/api/kjdata")
    print("=" * 80)
    
    # 创建抓取器
    scraper = KjdataLotteryScraper()
    
    # 执行抓取
    results = scraper.comprehensive_scrape()
    
    print("\n🎉 抓取完成!")
    
    if results:
        print(f"\n📊 抓取结果:")
        print(f"  总记录数: {results.get('total_records', 0):,}")
        print(f"  成功记录: {results.get('success_records', 0):,}")
        print(f"  CSV文件: {results.get('csv_file', 'N/A')}")
        print(f"  数据库: {'✅ 已保存' if results.get('database_saved') else '❌ 保存失败'}")
        
        success_rate = results.get('success_records', 0) / results.get('total_records', 1) * 100
        print(f"  成功率: {success_rate:.1f}%")
        
        if success_rate > 80:
            print("\n✅ 数据质量优秀，建议集成到六合彩工具中！")
            print("💡 下一步:")
            print("  1. 验证数据准确性")
            print("  2. 设计数据更新机制")
            print("  3. 集成到现有预测系统")
        else:
            print("\n⚠️ 数据质量需要改进")
    else:
        print("\n❌ 抓取失败")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
