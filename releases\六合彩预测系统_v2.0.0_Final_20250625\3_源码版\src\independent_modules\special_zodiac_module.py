"""
特码生肖模组 - 独立预测和回测
专注于特码生肖的深度分析和预测
"""
import sqlite3
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import Counter, defaultdict
import json

class SpecialZodiacModule:
    """特码生肖模组"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.module_name = "特码生肖模组"
        
        # 生肖映射
        self.zodiac_mapping = self._get_zodiac_mapping()
        self.reverse_zodiac_mapping = self._get_reverse_zodiac_mapping()
        
        # 特码生肖专项配置
        self.config = {
            "analysis_window": 80,      # 分析窗口
            "hot_cold_threshold": 1.2,  # 冷热阈值
            "trend_window": 20,         # 趋势窗口
            "pattern_depth": 15,        # 模式深度
            "confidence_base": 0.6      # 基础置信度
        }
        
        # 生肖特殊属性
        self.zodiac_properties = {
            "琴棋书画": {
                "琴": ["鸡", "兔"],
                "棋": ["鼠", "牛"], 
                "书": ["马", "龙"],
                "画": ["蛇", "羊"]
            },
            "家野": {
                "家畜": ["牛", "马", "羊", "鸡", "狗", "猪"],
                "野兽": ["鼠", "虎", "兔", "龙", "蛇", "猴"]
            },
            "天地": {
                "天肖": ["兔", "马", "猴", "猪", "牛", "龙"],
                "地肖": ["蛇", "羊", "鸡", "狗", "鼠", "虎"]
            }
        }
    
    def predict(self, target_date: str, analysis_days: int = 100) -> Dict[str, Any]:
        """独立预测方法"""
        print(f"⭐ {self.module_name}开始预测 - 目标日期: {target_date}")
        
        # 获取历史数据
        historical_data = self._get_historical_data(target_date, analysis_days)
        
        if len(historical_data) < 30:
            return self._get_default_result()
        
        # 特码生肖深度分析
        hot_cold_analysis = self._analyze_hot_cold_zodiacs(historical_data)
        trend_analysis = self._analyze_zodiac_trends(historical_data)
        pattern_analysis = self._analyze_zodiac_patterns(historical_data)
        property_analysis = self._analyze_zodiac_properties(historical_data)
        cycle_analysis = self._analyze_zodiac_cycles(historical_data)
        
        # 综合评分
        zodiac_scores = self._calculate_comprehensive_zodiac_scores(
            hot_cold_analysis, trend_analysis, pattern_analysis, 
            property_analysis, cycle_analysis
        )
        
        # 选择推荐生肖和号码
        recommended_zodiacs = self._select_top_zodiacs(zodiac_scores)
        recommended_numbers = self._generate_zodiac_numbers(recommended_zodiacs, zodiac_scores)
        
        # 计算置信度
        confidence = self._calculate_zodiac_confidence(zodiac_scores, recommended_zodiacs)
        
        result = {
            "predicted_numbers": recommended_numbers,
            "confidence": confidence,
            "recommended_zodiacs": recommended_zodiacs,
            "zodiac_scores": zodiac_scores,
            "analysis_details": {
                "hot_cold": hot_cold_analysis,
                "trends": trend_analysis,
                "patterns": pattern_analysis,
                "properties": property_analysis,
                "cycles": cycle_analysis
            },
            "metadata": {
                "analysis_days": analysis_days,
                "data_count": len(historical_data),
                "prediction_time": datetime.now().isoformat()
            }
        }
        
        print(f"✅ {self.module_name}预测完成 - 推荐{len(recommended_numbers)}个号码")
        return result
    
    def predict_historical(self, target_date: str, window_size: int = 30) -> Dict[str, Any]:
        """历史预测方法（用于回测）"""
        end_date = datetime.strptime(target_date, "%Y-%m-%d") - timedelta(days=1)
        end_date_str = end_date.strftime("%Y-%m-%d")
        
        return self.predict(end_date_str, window_size)
    
    def run_backtest(self, start_date: str, end_date: str, window_size: int = 30) -> Dict[str, Any]:
        """运行回测"""
        print(f"🔄 {self.module_name}开始回测: {start_date} 到 {end_date}")
        
        test_dates = self._get_test_dates(start_date, end_date)
        results = []
        
        for test_date in test_dates:
            try:
                actual_number = self._get_actual_number(test_date)
                if actual_number is None:
                    continue
                
                prediction = self.predict_historical(test_date, window_size)
                predicted_numbers = prediction["predicted_numbers"]
                predicted_zodiacs = prediction["recommended_zodiacs"]
                
                # 号码命中
                hit = actual_number in predicted_numbers
                hit_rank = predicted_numbers.index(actual_number) + 1 if hit else None
                
                # 生肖命中
                actual_zodiac = self.zodiac_mapping.get(actual_number, "未知")
                zodiac_hit = actual_zodiac in predicted_zodiacs
                zodiac_rank = predicted_zodiacs.index(actual_zodiac) + 1 if zodiac_hit else None
                
                result = {
                    "test_date": test_date,
                    "actual_number": actual_number,
                    "actual_zodiac": actual_zodiac,
                    "predicted_numbers": predicted_numbers,
                    "predicted_zodiacs": predicted_zodiacs,
                    "hit": hit,
                    "zodiac_hit": zodiac_hit,
                    "hit_rank": hit_rank,
                    "zodiac_rank": zodiac_rank,
                    "confidence": prediction["confidence"],
                    "zodiac_scores": prediction["zodiac_scores"]
                }
                
                results.append(result)
                print(f"  {test_date}: 实际={actual_number}({actual_zodiac}), 号码={'✅' if hit else '❌'}, 生肖={'✅' if zodiac_hit else '❌'}")
                
            except Exception as e:
                print(f"  {test_date}: 回测失败 - {e}")
        
        # 统计结果
        hit_count = sum(1 for r in results if r["hit"])
        zodiac_hit_count = sum(1 for r in results if r["zodiac_hit"])
        hit_rate = hit_count / len(results) if results else 0
        zodiac_hit_rate = zodiac_hit_count / len(results) if results else 0
        
        print(f"✅ {self.module_name}回测完成:")
        print(f"   号码命中: {hit_count}/{len(results)} = {hit_rate:.1%}")
        print(f"   生肖命中: {zodiac_hit_count}/{len(results)} = {zodiac_hit_rate:.1%}")
        
        return {
            "results": results,
            "statistics": {
                "total_tests": len(results),
                "hit_count": hit_count,
                "zodiac_hit_count": zodiac_hit_count,
                "hit_rate": hit_rate,
                "zodiac_hit_rate": zodiac_hit_rate,
                "avg_confidence": np.mean([r["confidence"] for r in results]) if results else 0
            }
        }
    
    def _get_historical_data(self, before_date: str, days: int) -> List[Dict[str, Any]]:
        """获取历史数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT draw_date, special_number, period_number
            FROM lottery_results 
            WHERE draw_date < ? 
            ORDER BY draw_date DESC 
            LIMIT ?
        ''', (before_date, days))
        
        data = []
        for row in cursor.fetchall():
            special_number = row[1]
            zodiac = self.zodiac_mapping.get(special_number, "未知")
            
            data.append({
                "draw_date": row[0],
                "special_number": special_number,
                "zodiac": zodiac,
                "period_number": row[2]
            })
        
        conn.close()
        return data
    
    def _analyze_hot_cold_zodiacs(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """冷热生肖分析"""
        window = self.config["analysis_window"]
        recent_data = historical_data[:window]
        
        zodiac_counts = Counter(record["zodiac"] for record in recent_data)
        total_count = len(recent_data)
        avg_frequency = total_count / 12  # 12个生肖的平均频率
        
        hot_zodiacs = []
        cold_zodiacs = []
        normal_zodiacs = []
        
        zodiac_frequencies = {}
        
        for zodiac in ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]:
            count = zodiac_counts.get(zodiac, 0)
            frequency = count / total_count if total_count > 0 else 0
            zodiac_frequencies[zodiac] = frequency
            
            if count > avg_frequency * self.config["hot_cold_threshold"]:
                hot_zodiacs.append(zodiac)
            elif count < avg_frequency / self.config["hot_cold_threshold"]:
                cold_zodiacs.append(zodiac)
            else:
                normal_zodiacs.append(zodiac)
        
        return {
            "frequencies": zodiac_frequencies,
            "hot_zodiacs": hot_zodiacs,
            "cold_zodiacs": cold_zodiacs,
            "normal_zodiacs": normal_zodiacs,
            "avg_frequency": avg_frequency / total_count if total_count > 0 else 0
        }
    
    def _analyze_zodiac_trends(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """生肖趋势分析"""
        window = self.config["trend_window"]
        
        if len(historical_data) < window * 2:
            return {"rising": [], "falling": [], "stable": []}
        
        # 分为前后两段
        recent_half = historical_data[:window]
        earlier_half = historical_data[window:window*2]
        
        recent_counts = Counter(record["zodiac"] for record in recent_half)
        earlier_counts = Counter(record["zodiac"] for record in earlier_half)
        
        rising_zodiacs = []
        falling_zodiacs = []
        stable_zodiacs = []
        
        for zodiac in ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]:
            recent_freq = recent_counts.get(zodiac, 0) / len(recent_half)
            earlier_freq = earlier_counts.get(zodiac, 0) / len(earlier_half)
            
            if recent_freq > earlier_freq * 1.3:
                rising_zodiacs.append(zodiac)
            elif recent_freq < earlier_freq * 0.7:
                falling_zodiacs.append(zodiac)
            else:
                stable_zodiacs.append(zodiac)
        
        return {
            "rising": rising_zodiacs,
            "falling": falling_zodiacs,
            "stable": stable_zodiacs,
            "trend_window": window
        }
    
    def _analyze_zodiac_patterns(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """生肖模式分析"""
        depth = self.config["pattern_depth"]
        recent_zodiacs = [record["zodiac"] for record in historical_data[:depth]]
        
        # 连续出现模式
        consecutive_count = defaultdict(int)
        for i in range(len(recent_zodiacs) - 1):
            if recent_zodiacs[i] == recent_zodiacs[i + 1]:
                consecutive_count[recent_zodiacs[i]] += 1
        
        # 间隔模式
        zodiac_intervals = defaultdict(list)
        zodiac_last_pos = {}
        
        for i, zodiac in enumerate(recent_zodiacs):
            if zodiac in zodiac_last_pos:
                interval = i - zodiac_last_pos[zodiac]
                zodiac_intervals[zodiac].append(interval)
            zodiac_last_pos[zodiac] = i
        
        # 计算平均间隔
        avg_intervals = {}
        for zodiac, intervals in zodiac_intervals.items():
            avg_intervals[zodiac] = np.mean(intervals) if intervals else depth
        
        return {
            "consecutive_patterns": dict(consecutive_count),
            "avg_intervals": avg_intervals,
            "recent_sequence": recent_zodiacs[:10]
        }
    
    def _analyze_zodiac_properties(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """生肖属性分析"""
        recent_data = historical_data[:self.config["analysis_window"]]
        recent_zodiacs = [record["zodiac"] for record in recent_data]
        
        property_stats = {}
        
        for prop_name, prop_groups in self.zodiac_properties.items():
            group_counts = {}
            
            for group_name, zodiacs in prop_groups.items():
                count = sum(1 for zodiac in recent_zodiacs if zodiac in zodiacs)
                group_counts[group_name] = count / len(recent_zodiacs) if recent_zodiacs else 0
            
            property_stats[prop_name] = group_counts
        
        return property_stats
    
    def _analyze_zodiac_cycles(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """生肖周期分析"""
        # 分析每个生肖的出现周期
        zodiac_positions = defaultdict(list)
        
        for i, record in enumerate(historical_data):
            zodiac = record["zodiac"]
            zodiac_positions[zodiac].append(i)
        
        cycle_analysis = {}
        
        for zodiac, positions in zodiac_positions.items():
            if len(positions) >= 3:
                # 计算周期
                intervals = [positions[i] - positions[i+1] for i in range(len(positions)-1)]
                avg_cycle = np.mean(intervals) if intervals else 0
                cycle_stability = 1 / (np.std(intervals) + 1) if intervals else 0
                
                # 预测下次出现
                last_position = positions[0]  # 最近一次位置
                predicted_next = max(0, last_position - avg_cycle)
                
                cycle_analysis[zodiac] = {
                    "avg_cycle": avg_cycle,
                    "stability": cycle_stability,
                    "predicted_next": predicted_next,
                    "last_position": last_position
                }
            else:
                cycle_analysis[zodiac] = {
                    "avg_cycle": len(historical_data) / 2,
                    "stability": 0.5,
                    "predicted_next": len(historical_data),
                    "last_position": positions[0] if positions else len(historical_data)
                }
        
        return cycle_analysis
    
    def _calculate_comprehensive_zodiac_scores(self, hot_cold: Dict, trends: Dict, 
                                             patterns: Dict, properties: Dict, cycles: Dict) -> Dict[str, float]:
        """计算生肖综合评分"""
        scores = {}
        
        for zodiac in ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]:
            score = 0.0
            
            # 冷热得分 (30%)
            if zodiac in hot_cold["hot_zodiacs"]:
                hot_cold_score = 80
            elif zodiac in hot_cold["cold_zodiacs"]:
                hot_cold_score = 90  # 冷门生肖可能反弹
            else:
                hot_cold_score = 60
            score += hot_cold_score * 0.3
            
            # 趋势得分 (25%)
            if zodiac in trends["rising"]:
                trend_score = 85
            elif zodiac in trends["stable"]:
                trend_score = 65
            else:
                trend_score = 45
            score += trend_score * 0.25
            
            # 模式得分 (20%)
            avg_interval = patterns["avg_intervals"].get(zodiac, 10)
            pattern_score = min(100, 100 / (avg_interval + 1) * 10)
            score += pattern_score * 0.2
            
            # 周期得分 (15%)
            cycle_info = cycles.get(zodiac, {})
            predicted_next = cycle_info.get("predicted_next", 100)
            cycle_score = max(0, 100 - predicted_next * 2)  # 越接近预测时间得分越高
            score += cycle_score * 0.15
            
            # 属性得分 (10%)
            attr_score = 50  # 基础分
            for prop_name, prop_groups in self.zodiac_properties.items():
                for group_name, zodiacs in prop_groups.items():
                    if zodiac in zodiacs:
                        group_freq = properties.get(prop_name, {}).get(group_name, 0)
                        attr_score += group_freq * 20
            score += min(100, attr_score) * 0.1
            
            scores[zodiac] = score
        
        return scores
    
    def _select_top_zodiacs(self, zodiac_scores: Dict[str, float]) -> List[str]:
        """选择前3个生肖"""
        sorted_zodiacs = sorted(zodiac_scores.items(), key=lambda x: x[1], reverse=True)
        return [zodiac for zodiac, score in sorted_zodiacs[:3]]
    
    def _generate_zodiac_numbers(self, recommended_zodiacs: List[str], 
                                zodiac_scores: Dict[str, float]) -> List[int]:
        """从推荐生肖生成号码"""
        numbers = []
        
        # 按生肖得分排序，优先选择高分生肖的号码
        sorted_zodiacs = sorted(recommended_zodiacs, 
                               key=lambda z: zodiac_scores.get(z, 0), reverse=True)
        
        for zodiac in sorted_zodiacs:
            zodiac_numbers = self.reverse_zodiac_mapping.get(zodiac, [])
            # 每个生肖选择4个号码
            numbers.extend(zodiac_numbers[:4])
        
        # 确保有足够的号码
        if len(numbers) < 12:
            # 补充其他高分生肖的号码
            all_zodiacs = sorted(zodiac_scores.items(), key=lambda x: x[1], reverse=True)
            for zodiac, score in all_zodiacs:
                if zodiac not in recommended_zodiacs:
                    zodiac_numbers = self.reverse_zodiac_mapping.get(zodiac, [])
                    numbers.extend(zodiac_numbers[:2])
                    if len(numbers) >= 12:
                        break
        
        return sorted(list(set(numbers)))[:12]  # 返回12个号码
    
    def _calculate_zodiac_confidence(self, zodiac_scores: Dict[str, float], 
                                   recommended_zodiacs: List[str]) -> float:
        """计算置信度"""
        if not recommended_zodiacs:
            return self.config["confidence_base"]
        
        recommended_scores = [zodiac_scores[zodiac] for zodiac in recommended_zodiacs]
        avg_recommended_score = np.mean(recommended_scores)
        
        all_scores = list(zodiac_scores.values())
        avg_all_score = np.mean(all_scores)
        
        if avg_all_score > 0:
            confidence_ratio = avg_recommended_score / avg_all_score
            confidence = min(0.95, max(0.4, self.config["confidence_base"] + confidence_ratio * 0.3))
        else:
            confidence = self.config["confidence_base"]
        
        return confidence
    
    def _get_zodiac_mapping(self) -> Dict[int, str]:
        """获取号码到生肖的映射"""
        zodiac_mapping = {}
        zodiacs = ["蛇", "马", "羊", "猴", "鸡", "狗", "猪", "鼠", "牛", "虎", "兔", "龙"]
        
        for i in range(1, 50):
            zodiac_mapping[i] = zodiacs[(i - 1) % 12]
        
        return zodiac_mapping
    
    def _get_reverse_zodiac_mapping(self) -> Dict[str, List[int]]:
        """获取生肖到号码的映射"""
        reverse_mapping = {}
        
        for number, zodiac in self.zodiac_mapping.items():
            if zodiac not in reverse_mapping:
                reverse_mapping[zodiac] = []
            reverse_mapping[zodiac].append(number)
        
        return reverse_mapping
    
    def _get_test_dates(self, start_date: str, end_date: str) -> List[str]:
        """获取测试日期"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT draw_date FROM lottery_results 
            WHERE draw_date BETWEEN ? AND ? 
            ORDER BY draw_date
        ''', (start_date, end_date))
        
        dates = [row[0] for row in cursor.fetchall()]
        conn.close()
        return dates
    
    def _get_actual_number(self, test_date: str) -> Optional[int]:
        """获取真实开奖号码"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT special_number FROM lottery_results 
            WHERE draw_date = ?
        ''', (test_date,))
        
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None
    
    def _get_default_result(self) -> Dict[str, Any]:
        """获取默认结果"""
        default_zodiacs = ["龙", "虎", "马"]
        default_numbers = []
        
        for zodiac in default_zodiacs:
            zodiac_numbers = self.reverse_zodiac_mapping.get(zodiac, [])
            default_numbers.extend(zodiac_numbers[:4])
        
        return {
            "predicted_numbers": sorted(default_numbers[:12]),
            "confidence": 0.6,
            "recommended_zodiacs": default_zodiacs,
            "zodiac_scores": {zodiac: 60.0 for zodiac in ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]},
            "analysis_details": {},
            "metadata": {"note": "数据不足，使用默认预测"}
        }
