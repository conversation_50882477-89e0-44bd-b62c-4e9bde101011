"""
AI策略优化系统主入口 - 统一管理所有AI优化策略
"""
import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import json

# 导入各个子系统
from src.algorithm_layer.ai_strategy_optimization.reinforcement_learning.prediction_environment import LotteryPredictionEnvironment
from src.algorithm_layer.ai_strategy_optimization.reinforcement_learning.rl_agent_manager import R<PERSON><PERSON><PERSON>anager
from src.algorithm_layer.ai_strategy_optimization.genetic_algorithm.chromosome_encoding import LotteryPredictionEncoder
from src.algorithm_layer.ai_strategy_optimization.semi_supervised_learning.user_feedback_collector import UserFeedbackCollector
from src.algorithm_layer.ai_strategy_optimization.meta_learning.few_shot_learning import FewShotLearningManager

class AIStrategyOptimizer:
    """AI策略优化系统主控制器"""
    
    def __init__(self):
        """初始化AI策略优化系统"""
        
        # 初始化各个子系统
        self.rl_manager = RLAgentManager()
        self.genetic_encoder = LotteryPredictionEncoder()
        self.feedback_collector = UserFeedbackCollector()
        self.few_shot_manager = FewShotLearningManager()
        
        # 系统状态
        self.system_status = {
            'initialized_at': datetime.now(),
            'rl_agents_count': 0,
            'genetic_populations': 0,
            'feedback_samples': 0,
            'few_shot_models': 0,
            'optimization_runs': 0
        }
        
        # 优化历史
        self.optimization_history = []
        
        # 当前最佳策略
        self.best_strategies = {}
    
    def setup_reinforcement_learning(self, 
                                   historical_data: pd.DataFrame,
                                   agent_configs: List[Dict]) -> Dict:
        """设置强化学习环境和智能体"""
        
        print("🤖 设置强化学习环境...")
        
        # 创建预测环境
        self.prediction_env = LotteryPredictionEnvironment(
            historical_data=historical_data,
            prediction_horizon=1,
            max_episode_length=100
        )
        
        # 创建智能体
        state_dim = self.prediction_env.observation_space.shape[0]
        action_dim = self.prediction_env.action_space.shape[0]
        
        created_agents = []
        for i, config in enumerate(agent_configs):
            agent_id = config.get('agent_id', f'agent_{i}')
            agent_type = config.get('type', 'dqn')
            
            agent = self.rl_manager.create_agent(
                agent_id=agent_id,
                state_dim=state_dim,
                action_dim=action_dim,
                agent_type=agent_type,
                **config.get('params', {})
            )
            
            created_agents.append(agent_id)
        
        self.system_status['rl_agents_count'] = len(created_agents)
        
        return {
            'environment_created': True,
            'agents_created': created_agents,
            'state_dim': state_dim,
            'action_dim': action_dim
        }
    
    def train_reinforcement_learning(self, 
                                   episodes: int = 1000,
                                   max_steps_per_episode: int = 100) -> Dict:
        """训练强化学习智能体"""
        
        print(f"🚀 开始训练强化学习智能体 ({episodes} episodes)...")
        
        if not hasattr(self, 'prediction_env'):
            raise ValueError("请先设置强化学习环境")
        
        # 训练智能体
        training_results = self.rl_manager.train_agents(
            environment=self.prediction_env,
            episodes=episodes,
            max_steps_per_episode=max_steps_per_episode
        )
        
        # 更新系统状态
        self.system_status['optimization_runs'] += 1
        
        # 记录最佳策略
        best_agent = max(
            training_results['agent_performances'].items(),
            key=lambda x: x[1]['total_reward']
        )
        
        self.best_strategies['reinforcement_learning'] = {
            'best_agent': best_agent[0],
            'performance': best_agent[1],
            'training_date': datetime.now().isoformat()
        }
        
        return training_results
    
    def optimize_with_genetic_algorithm(self,
                                      population_size: int = 50,
                                      generations: int = 100,
                                      fitness_function: callable = None) -> Dict:
        """使用遗传算法优化策略"""
        
        print(f"🧬 开始遗传算法优化 ({generations} generations)...")
        
        # 创建初始种群
        population = []
        for _ in range(population_size):
            chromosome = self.genetic_encoder.create_random_chromosome()
            population.append(chromosome)
        
        best_fitness_history = []
        avg_fitness_history = []
        
        for generation in range(generations):
            # 评估适应度
            fitness_scores = []
            for chromosome in population:
                if fitness_function:
                    fitness = fitness_function(chromosome)
                else:
                    fitness = self._default_fitness_function(chromosome)
                fitness_scores.append(fitness)
            
            # 记录统计信息
            best_fitness = max(fitness_scores)
            avg_fitness = np.mean(fitness_scores)
            best_fitness_history.append(best_fitness)
            avg_fitness_history.append(avg_fitness)
            
            # 选择、交叉、变异（简化实现）
            new_population = []
            
            # 精英保留
            elite_count = population_size // 10
            elite_indices = np.argsort(fitness_scores)[-elite_count:]
            for idx in elite_indices:
                new_population.append(population[idx].copy())
            
            # 生成新个体
            while len(new_population) < population_size:
                # 选择父母
                parent1_idx = self._tournament_selection(fitness_scores)
                parent2_idx = self._tournament_selection(fitness_scores)
                
                # 交叉
                child = self._crossover(population[parent1_idx], population[parent2_idx])
                
                # 变异
                child = self._mutate(child)
                
                # 修复染色体
                child = self.genetic_encoder.repair_chromosome(child)
                
                new_population.append(child)
            
            population = new_population
            
            if generation % 20 == 0:
                print(f"Generation {generation}: Best={best_fitness:.4f}, Avg={avg_fitness:.4f}")
        
        # 找到最佳个体
        final_fitness_scores = [
            fitness_function(chromosome) if fitness_function else self._default_fitness_function(chromosome)
            for chromosome in population
        ]
        best_idx = np.argmax(final_fitness_scores)
        best_chromosome = population[best_idx]
        best_config = self.genetic_encoder.decode_prediction_config(best_chromosome)
        
        # 更新最佳策略
        self.best_strategies['genetic_algorithm'] = {
            'best_chromosome': best_chromosome.tolist(),
            'best_config': best_config,
            'best_fitness': final_fitness_scores[best_idx],
            'optimization_date': datetime.now().isoformat()
        }
        
        self.system_status['genetic_populations'] += 1
        self.system_status['optimization_runs'] += 1
        
        return {
            'best_chromosome': best_chromosome,
            'best_config': best_config,
            'best_fitness': final_fitness_scores[best_idx],
            'fitness_history': {
                'best': best_fitness_history,
                'average': avg_fitness_history
            },
            'final_population': population
        }
    
    def _default_fitness_function(self, chromosome: np.ndarray) -> float:
        """默认适应度函数"""
        # 简化的适应度计算
        config = self.genetic_encoder.decode_prediction_config(chromosome)
        
        # 基于配置参数计算适应度
        model_params = config.get('model', {})
        feature_params = config.get('features', {})
        strategy_params = config.get('strategy', {})
        
        # 简单的适应度计算
        fitness = 0.0
        
        # 模型参数贡献
        n_estimators = model_params.get('n_estimators', 100)
        fitness += min(1.0, n_estimators / 500) * 0.3
        
        # 特征参数贡献
        n_features = feature_params.get('n_features', 50)
        fitness += min(1.0, n_features / 100) * 0.3
        
        # 策略参数贡献
        confidence = strategy_params.get('confidence_threshold', 0.7)
        fitness += confidence * 0.4
        
        # 添加随机噪声模拟真实评估
        fitness += np.random.normal(0, 0.1)
        
        return max(0.0, fitness)
    
    def _tournament_selection(self, fitness_scores: List[float], tournament_size: int = 3) -> int:
        """锦标赛选择"""
        tournament_indices = np.random.choice(len(fitness_scores), tournament_size, replace=False)
        tournament_fitness = [fitness_scores[i] for i in tournament_indices]
        winner_idx = tournament_indices[np.argmax(tournament_fitness)]
        return winner_idx
    
    def _crossover(self, parent1: np.ndarray, parent2: np.ndarray) -> np.ndarray:
        """单点交叉"""
        crossover_point = np.random.randint(1, len(parent1))
        child = np.concatenate([parent1[:crossover_point], parent2[crossover_point:]])
        return child
    
    def _mutate(self, chromosome: np.ndarray, mutation_rate: float = 0.1) -> np.ndarray:
        """高斯变异"""
        mutation_mask = np.random.random(len(chromosome)) < mutation_rate
        noise = np.random.normal(0, 0.1, len(chromosome))
        chromosome[mutation_mask] += noise[mutation_mask]
        return np.clip(chromosome, 0, 1)
    
    def collect_user_feedback(self, 
                            user_id: str,
                            prediction_id: str,
                            feedback_data: Dict) -> int:
        """收集用户反馈"""
        
        feedback_type = feedback_data.get('type', 'rating')
        feedback_value = feedback_data.get('value')
        confidence = feedback_data.get('confidence', 1.0)
        context = feedback_data.get('context', {})
        
        if feedback_type in ['rating', 'comment', 'correction', 'preference']:
            # 显式反馈
            feedback_id = self.feedback_collector.collect_explicit_feedback(
                user_id=user_id,
                prediction_id=prediction_id,
                feedback_type=feedback_type,
                feedback_value=feedback_value,
                confidence=confidence,
                context=context
            )
        else:
            # 隐式反馈
            session_id = context.get('session_id', 'default')
            duration = feedback_data.get('duration')
            
            feedback_id = self.feedback_collector.collect_implicit_feedback(
                user_id=user_id,
                session_id=session_id,
                action_type=feedback_type,
                action_data=feedback_value,
                duration=duration,
                context=context
            )
        
        # 更新系统状态
        self.system_status['feedback_samples'] += 1
        
        return feedback_id
    
    def train_few_shot_learning(self,
                               data: np.ndarray,
                               labels: np.ndarray,
                               model_configs: List[Dict]) -> Dict:
        """训练少样本学习模型"""
        
        print("🎯 开始训练少样本学习模型...")
        
        results = {}
        
        for config in model_configs:
            model_name = config['name']
            model_type = config['type']
            input_dim = data.shape[1]
            
            # 创建模型
            model = self.few_shot_manager.create_model(
                model_type=model_type,
                model_name=model_name,
                input_dim=input_dim,
                **config.get('params', {})
            )
            
            # 训练模型
            losses = self.few_shot_manager.train_model(
                model_name=model_name,
                data=data,
                labels=labels,
                n_episodes=config.get('n_episodes', 1000),
                n_way=config.get('n_way', 5),
                k_shot=config.get('k_shot', 1)
            )
            
            # 评估模型
            evaluation = self.few_shot_manager.evaluate_model(
                model_name=model_name,
                data=data,
                labels=labels,
                n_episodes=100
            )
            
            results[model_name] = {
                'losses': losses,
                'evaluation': evaluation,
                'final_loss': losses[-1] if losses else 0
            }
        
        # 找到最佳模型
        best_model = max(results.items(), key=lambda x: x[1]['evaluation']['mean_accuracy'])
        
        self.best_strategies['few_shot_learning'] = {
            'best_model': best_model[0],
            'performance': best_model[1]['evaluation'],
            'training_date': datetime.now().isoformat()
        }
        
        self.system_status['few_shot_models'] = len(results)
        self.system_status['optimization_runs'] += 1
        
        return results
    
    def get_integrated_prediction(self, 
                                input_data: np.ndarray,
                                integration_method: str = 'ensemble') -> Dict:
        """获取集成预测结果"""
        
        predictions = {}
        
        # 强化学习预测
        if hasattr(self, 'prediction_env') and self.rl_manager.agents:
            rl_action = self.rl_manager.get_ensemble_action(input_data)
            predictions['reinforcement_learning'] = rl_action
        
        # 遗传算法优化的预测
        if 'genetic_algorithm' in self.best_strategies:
            best_config = self.best_strategies['genetic_algorithm']['best_config']
            # 这里应该使用最佳配置进行预测，简化处理
            predictions['genetic_algorithm'] = np.random.random(49)
        
        # 少样本学习预测
        if self.few_shot_manager.models:
            # 简化处理，实际应该使用训练好的模型
            predictions['few_shot_learning'] = np.random.random(49)
        
        # 集成预测
        if integration_method == 'ensemble' and predictions:
            ensemble_prediction = np.mean(list(predictions.values()), axis=0)
            predictions['ensemble'] = ensemble_prediction
        
        return predictions
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        # 更新反馈统计
        feedback_stats = self.feedback_collector.get_feedback_statistics()
        self.system_status['feedback_samples'] = (
            feedback_stats.get('total_explicit_feedback', 0) + 
            feedback_stats.get('total_implicit_feedback', 0)
        )
        
        return self.system_status
    
    def get_optimization_summary(self) -> Dict:
        """获取优化总结"""
        return {
            'system_status': self.get_system_status(),
            'best_strategies': self.best_strategies,
            'optimization_history_count': len(self.optimization_history),
            'available_methods': [
                'reinforcement_learning',
                'genetic_algorithm', 
                'semi_supervised_learning',
                'few_shot_learning'
            ]
        }


def main():
    """主函数 - 演示AI策略优化系统"""
    print("🧠 澳门六合彩预测系统 - AI策略优化系统")
    print("=" * 60)
    
    # 初始化系统
    ai_optimizer = AIStrategyOptimizer()
    
    # 显示系统状态
    status = ai_optimizer.get_system_status()
    print(f"📊 系统状态:")
    print(f"   初始化时间: {status['initialized_at']}")
    print(f"   强化学习智能体: {status['rl_agents_count']}")
    print(f"   遗传算法种群: {status['genetic_populations']}")
    print(f"   用户反馈样本: {status['feedback_samples']}")
    print(f"   少样本学习模型: {status['few_shot_models']}")
    print(f"   优化运行次数: {status['optimization_runs']}")
    
    print("\n🎊 AI策略优化系统初始化完成！")
    print("系统包含以下优化方法:")
    print("   • 强化学习优化 - 智能体策略学习")
    print("   • 遗传算法优化 - 进化计算优化")
    print("   • 半监督学习 - 用户反馈学习")
    print("   • 元学习系统 - 少样本学习")

if __name__ == "__main__":
    main()
