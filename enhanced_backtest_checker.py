#!/usr/bin/env python3
"""
增强回测功能全面检查
验证增强回测系统的各项功能是否正常
"""

import sys
import time
import json
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

class EnhancedBacktestChecker:
    """增强回测功能检查器"""
    
    def __init__(self):
        self.test_results = {}
        self.performance_metrics = {}
        
    def check_enhanced_hit_rate_optimizer(self):
        """检查增强命中率优化器"""
        print("🎯 检查增强命中率优化器")
        print("-" * 50)
        
        try:
            from enhanced_hit_rate_optimizer import EnhancedHitRateOptimizer
            
            # 初始化优化器
            optimizer = EnhancedHitRateOptimizer()
            print("✅ 增强命中率优化器初始化成功")
            
            # 测试简单回测
            start_date = "2025-01-01"
            end_date = "2025-01-05"
            iterations = 3
            
            print(f"🔧 开始增强回测: {start_date} 到 {end_date}, {iterations}次迭代")
            start_time = time.time()
            
            result = optimizer.run_enhanced_backtest(start_date, end_date, iterations)
            
            duration = time.time() - start_time
            
            if result and "best_config" in result:
                best_hit_rate = result.get("best_hit_rate", 0)
                best_config = result.get("best_config", {})
                iteration_results = result.get("iteration_results", [])
                
                print(f"✅ 增强回测完成")
                print(f"📊 最佳命中率: {best_hit_rate:.1%}")
                print(f"📊 迭代次数: {len(iteration_results)}")
                print(f"📊 执行时间: {duration:.2f}秒")
                print(f"📊 最佳配置: {len(best_config)}个参数")
                
                # 检查配置内容
                if "fusion_weights" in best_config:
                    print(f"📊 融合权重: {best_config['fusion_weights']}")
                
                return True, {
                    "success": True,
                    "best_hit_rate": best_hit_rate,
                    "iterations": len(iteration_results),
                    "duration": duration,
                    "config_params": len(best_config)
                }
            else:
                print(f"❌ 增强回测结果格式异常")
                return False, {"error": "结果格式异常"}
                
        except Exception as e:
            print(f"❌ 增强命中率优化器检查失败: {e}")
            traceback.print_exc()
            return False, {"error": str(e)}
    
    def check_historical_simulation_predictor(self):
        """检查历史模拟预测器"""
        print("\n📊 检查历史模拟预测器")
        print("-" * 50)
        
        try:
            from src.historical_simulation_predictor import HistoricalSimulationPredictor
            
            # 初始化预测器
            predictor = HistoricalSimulationPredictor()
            print("✅ 历史模拟预测器初始化成功")
            
            # 测试历史模拟
            start_date = "2025-01-01"
            end_date = "2025-01-03"
            
            print(f"🔧 开始历史模拟: {start_date} 到 {end_date}")
            start_time = time.time()
            
            result = predictor.run_historical_simulation(start_date, end_date)
            
            duration = time.time() - start_time
            
            if result and "simulation_results" in result:
                simulation_results = result.get("simulation_results", [])
                overall_hit_rate = result.get("overall_hit_rate", 0)
                summary = result.get("summary", {})
                
                print(f"✅ 历史模拟完成")
                print(f"📊 模拟结果数: {len(simulation_results)}")
                print(f"📊 整体命中率: {overall_hit_rate:.1%}")
                print(f"📊 执行时间: {duration:.2f}秒")
                
                # 检查详细结果
                if simulation_results:
                    first_result = simulation_results[0]
                    print(f"📊 单次预测号码: {len(first_result.get('predicted_numbers', []))}个")
                    print(f"📊 单次命中率: {first_result.get('hit_rate', 0):.1%}")
                
                return True, {
                    "success": True,
                    "simulation_count": len(simulation_results),
                    "overall_hit_rate": overall_hit_rate,
                    "duration": duration
                }
            else:
                print(f"❌ 历史模拟结果格式异常")
                return False, {"error": "结果格式异常"}
                
        except Exception as e:
            print(f"❌ 历史模拟预测器检查失败: {e}")
            traceback.print_exc()
            return False, {"error": str(e)}
    
    def check_backtest_configuration(self):
        """检查回测配置功能"""
        print("\n⚙️ 检查回测配置功能")
        print("-" * 50)
        
        try:
            # 检查配置文件
            import os
            config_files = [
                "optimization_config.json",
                "optimal_config.json"
            ]
            
            config_status = {}
            for config_file in config_files:
                if os.path.exists(config_file):
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config_data = json.load(f)
                        
                        config_status[config_file] = {
                            "exists": True,
                            "valid_json": True,
                            "keys": list(config_data.keys()),
                            "size": len(config_data)
                        }
                        print(f"✅ {config_file}: 存在且有效")
                        print(f"  配置项: {len(config_data)}个")
                        print(f"  主要键: {list(config_data.keys())[:5]}")
                        
                    except json.JSONDecodeError:
                        config_status[config_file] = {
                            "exists": True,
                            "valid_json": False,
                            "error": "JSON格式错误"
                        }
                        print(f"❌ {config_file}: JSON格式错误")
                else:
                    config_status[config_file] = {
                        "exists": False
                    }
                    print(f"⚠️ {config_file}: 文件不存在")
            
            return True, config_status
            
        except Exception as e:
            print(f"❌ 回测配置检查失败: {e}")
            return False, {"error": str(e)}
    
    def check_backtest_data_access(self):
        """检查回测数据访问"""
        print("\n📊 检查回测数据访问")
        print("-" * 50)
        
        try:
            import sqlite3
            
            # 连接数据库
            conn = sqlite3.connect("data/lottery.db")
            cursor = conn.cursor()
            
            # 检查数据表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            table_names = [table[0] for table in tables]
            
            print(f"✅ 数据库连接成功")
            print(f"📊 数据表数量: {len(tables)}")
            print(f"📊 表名: {table_names}")
            
            # 检查主要数据表
            if "lottery_results" in table_names:
                cursor.execute("SELECT COUNT(*) FROM lottery_results")
                total_records = cursor.fetchone()[0]
                
                cursor.execute("SELECT MIN(draw_date), MAX(draw_date) FROM lottery_results")
                date_range = cursor.fetchone()
                
                print(f"✅ lottery_results表: {total_records}条记录")
                print(f"📊 日期范围: {date_range[0]} 到 {date_range[1]}")
                
                # 检查回测期间的数据
                cursor.execute("SELECT COUNT(*) FROM lottery_results WHERE draw_date >= '2025-01-01' AND draw_date <= '2025-01-31'")
                test_period_records = cursor.fetchone()[0]
                
                print(f"📊 测试期间数据: {test_period_records}条")
                
                data_status = {
                    "total_records": total_records,
                    "date_range": date_range,
                    "test_period_records": test_period_records,
                    "tables": table_names
                }
            else:
                print(f"❌ lottery_results表不存在")
                data_status = {"error": "主数据表不存在"}
            
            conn.close()
            
            return True, data_status
            
        except Exception as e:
            print(f"❌ 回测数据访问检查失败: {e}")
            return False, {"error": str(e)}
    
    def check_backtest_performance(self):
        """检查回测性能"""
        print("\n⚡ 检查回测性能")
        print("-" * 50)
        
        try:
            from enhanced_hit_rate_optimizer import EnhancedHitRateOptimizer
            
            optimizer = EnhancedHitRateOptimizer()
            
            # 性能测试1: 短期回测
            print("🔧 性能测试1: 短期回测")
            start_time = time.time()
            
            result1 = optimizer.run_enhanced_backtest("2025-01-01", "2025-01-02", 2)
            
            duration1 = time.time() - start_time
            print(f"  短期回测耗时: {duration1:.2f}秒")
            
            # 性能测试2: 中期回测
            print("🔧 性能测试2: 中期回测")
            start_time = time.time()
            
            result2 = optimizer.run_enhanced_backtest("2025-01-01", "2025-01-05", 3)
            
            duration2 = time.time() - start_time
            print(f"  中期回测耗时: {duration2:.2f}秒")
            
            # 性能分析
            performance_metrics = {
                "short_term": {
                    "duration": duration1,
                    "success": result1 is not None,
                    "hit_rate": result1.get("best_hit_rate", 0) if result1 else 0
                },
                "medium_term": {
                    "duration": duration2,
                    "success": result2 is not None,
                    "hit_rate": result2.get("best_hit_rate", 0) if result2 else 0
                }
            }
            
            print(f"✅ 回测性能检查完成")
            print(f"📊 短期回测: {duration1:.2f}秒")
            print(f"📊 中期回测: {duration2:.2f}秒")
            
            # 性能评估
            if duration2 < 30:  # 30秒内完成中期回测
                print(f"🎊 回测性能: 优秀")
            elif duration2 < 60:
                print(f"👍 回测性能: 良好")
            else:
                print(f"⚠️ 回测性能: 需要优化")
            
            return True, performance_metrics
            
        except Exception as e:
            print(f"❌ 回测性能检查失败: {e}")
            return False, {"error": str(e)}
    
    def check_backtest_integration(self):
        """检查回测系统集成"""
        print("\n🔗 检查回测系统集成")
        print("-" * 50)
        
        try:
            # 检查与完美预测系统的集成
            from src.perfect_prediction_system import PerfectPredictionSystem
            
            system = PerfectPredictionSystem()
            print("✅ 完美预测系统初始化成功")
            
            # 检查是否加载了回测配置
            if hasattr(system, 'config') and system.config:
                config_keys = list(system.config.keys()) if isinstance(system.config, dict) else []
                print(f"✅ 配置加载成功: {len(config_keys)}个配置项")
                
                # 检查关键配置
                key_configs = ["fusion_weights", "filter_thresholds", "prediction_strategy"]
                found_configs = [key for key in key_configs if key in config_keys]
                
                print(f"📊 关键配置: {len(found_configs)}/{len(key_configs)}个")
                for config in found_configs:
                    print(f"  ✅ {config}")
                
                integration_status = {
                    "config_loaded": True,
                    "config_count": len(config_keys),
                    "key_configs": found_configs
                }
            else:
                print(f"⚠️ 配置未加载或为空")
                integration_status = {"config_loaded": False}
            
            # 检查模块初始化
            system.initialize_modules()
            print("✅ 模块初始化成功")
            
            integration_status["modules_initialized"] = True
            
            return True, integration_status
            
        except Exception as e:
            print(f"❌ 回测系统集成检查失败: {e}")
            return False, {"error": str(e)}
    
    def generate_backtest_report(self):
        """生成回测功能检查报告"""
        print("\n" + "=" * 60)
        print("📊 增强回测功能检查报告")
        print("=" * 60)
        print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 统计检查结果
        total_checks = len(self.test_results)
        successful_checks = sum(1 for result in self.test_results.values() if result.get("success", False))
        
        print(f"📊 检查统计:")
        print(f"  总检查项: {total_checks}")
        print(f"  成功项目: {successful_checks}")
        print(f"  成功率: {successful_checks/total_checks:.1%}" if total_checks > 0 else "  成功率: 0%")
        print()
        
        # 详细结果
        print("📋 详细检查结果:")
        for check_name, result in self.test_results.items():
            status = "✅" if result.get("success", False) else "❌"
            print(f"  {status} {check_name}")
            
            if result.get("success", False):
                # 显示关键指标
                if "best_hit_rate" in result:
                    print(f"    命中率: {result['best_hit_rate']:.1%}")
                if "duration" in result:
                    print(f"    执行时间: {result['duration']:.2f}秒")
                if "simulation_count" in result:
                    print(f"    模拟次数: {result['simulation_count']}")
        
        print()
        
        # 功能状态评估
        if successful_checks == total_checks:
            status = "🎊 优秀"
            description = "所有回测功能正常运行"
        elif successful_checks >= total_checks * 0.8:
            status = "👍 良好"
            description = "大部分回测功能正常"
        elif successful_checks >= total_checks * 0.6:
            status = "⚠️ 一般"
            description = "部分回测功能存在问题"
        else:
            status = "❌ 较差"
            description = "多个回测功能需要修复"
        
        print(f"🏥 回测系统状态: {status}")
        print(f"   {description}")
        print()
        
        # 性能分析
        if "backtest_performance" in self.test_results:
            perf_data = self.test_results["backtest_performance"]
            if perf_data.get("success", False):
                print("⚡ 性能分析:")
                short_duration = perf_data.get("short_term", {}).get("duration", 0)
                medium_duration = perf_data.get("medium_term", {}).get("duration", 0)
                
                print(f"  短期回测: {short_duration:.2f}秒")
                print(f"  中期回测: {medium_duration:.2f}秒")
                
                if medium_duration < 30:
                    print(f"  性能评级: 🎊 优秀")
                elif medium_duration < 60:
                    print(f"  性能评级: 👍 良好")
                else:
                    print(f"  性能评级: ⚠️ 需要优化")
        
        print()
        
        # 建议和总结
        print("💡 建议:")
        if successful_checks == total_checks:
            print("  • 所有回测功能正常，可以正常使用")
            print("  • 建议定期进行回测验证预测效果")
            print("  • 可以尝试更长期的回测来优化参数")
        else:
            failed_checks = [name for name, result in self.test_results.items() if not result.get("success", False)]
            print(f"  • 需要修复以下功能: {', '.join(failed_checks)}")
            print("  • 建议检查相关模块的依赖和配置")
        
        print()
        print("🎯 总结:")
        print("  增强回测功能检查完成")
        print(f"  系统状态: {status.split()[1]}")
        print("  回测系统已验证")

def main():
    """主检查函数"""
    print("🎊 增强回测功能全面检查开始")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    checker = EnhancedBacktestChecker()
    
    try:
        # 执行各项检查
        
        # 检查增强命中率优化器
        success1, result1 = checker.check_enhanced_hit_rate_optimizer()
        checker.test_results["enhanced_hit_rate_optimizer"] = result1
        
        # 检查历史模拟预测器
        success2, result2 = checker.check_historical_simulation_predictor()
        checker.test_results["historical_simulation_predictor"] = result2
        
        # 检查回测配置
        success3, result3 = checker.check_backtest_configuration()
        checker.test_results["backtest_configuration"] = result3
        
        # 检查回测数据访问
        success4, result4 = checker.check_backtest_data_access()
        checker.test_results["backtest_data_access"] = result4
        
        # 检查回测性能
        success5, result5 = checker.check_backtest_performance()
        checker.test_results["backtest_performance"] = result5
        
        # 检查回测系统集成
        success6, result6 = checker.check_backtest_integration()
        checker.test_results["backtest_integration"] = result6
        
        # 生成检查报告
        checker.generate_backtest_report()
        
        # 保存检查结果
        with open("enhanced_backtest_check_results.json", "w", encoding="utf-8") as f:
            json.dump({
                "test_results": checker.test_results,
                "check_time": datetime.now().isoformat()
            }, f, ensure_ascii=False, indent=2)
        
        print("📄 检查结果已保存到: enhanced_backtest_check_results.json")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
