"""
历史开奖数据导入脚本 - 增强版
将CSV文件中的901期历史数据导入到数据库
"""
import sys
sys.path.insert(0, 'src')

import pandas as pd
from datetime import datetime, date
from src.data_layer.database.models import create_database_engine, get_session
from src.data_layer.database.init_db import initialize_database
from src.data_layer.year_mapping.lunar_year_manager import LunarYearManager
from src.data_layer.year_mapping.number_attribute_mapper import NumberAttributeMapper
from src.data_layer.data_import.lottery_data_processor import LotteryDataProcessor
from loguru import logger
import json

def import_historical_data():
    """导入历史开奖数据"""
    print("=" * 80)
    print("🚀 澳门六合彩历史数据导入工具")
    print("=" * 80)
    
    logger.info("开始导入901期历史开奖数据")
    
    # 1. 初始化数据库
    print("\n📊 步骤1: 初始化数据库...")
    if not initialize_database():
        print("❌ 数据库初始化失败")
        return False
    print("✅ 数据库初始化完成")
    
    # 2. 创建连接和管理器
    print("\n📊 步骤2: 创建系统组件...")
    engine = create_database_engine()
    session = get_session(engine)
    lunar_manager = LunarYearManager(session)
    attr_mapper = NumberAttributeMapper()
    data_processor = LotteryDataProcessor(lunar_manager, attr_mapper, session)
    print("✅ 系统组件创建完成")
    
    try:
        # 3. 读取CSV文件
        print("\n📊 步骤3: 读取历史数据文件...")
        df = pd.read_csv("历史数据.csv", encoding='utf-8')
        print(f"✅ 成功读取 {len(df)} 条记录")
        print(f"📋 数据列: {list(df.columns)}")
        
        # 显示数据样本
        print(f"\n📋 数据样本:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            print(f"   {row['issue']} | {row['date']} | 正码: {row['numbers']} | 特码: {row['special']}")
        
        # 4. 数据预处理和导入
        print(f"\n📊 步骤4: 开始数据导入...")
        success_count = 0
        error_count = 0
        
        for index, row in df.iterrows():
            try:
                # 解析数据
                period_number = str(row['issue'])
                draw_date_str = str(row['date'])
                numbers_str = str(row['numbers'])
                special_number = int(row['special'])
                
                # 转换日期格式
                draw_date = datetime.strptime(draw_date_str, '%Y-%m-%d').date()
                
                # 解析正码号码
                numbers_str = numbers_str.replace('"', '').strip()
                regular_numbers = [int(x.strip()) for x in numbers_str.split(',')]
                
                # 验证数据
                if len(regular_numbers) != 6:
                    error_count += 1
                    logger.warning(f"期号 {period_number}: 正码数量错误 {len(regular_numbers)}")
                    continue
                
                if not all(1 <= n <= 49 for n in regular_numbers):
                    error_count += 1
                    logger.warning(f"期号 {period_number}: 正码范围错误")
                    continue
                
                if not (1 <= special_number <= 49):
                    error_count += 1
                    logger.warning(f"期号 {period_number}: 特码范围错误 {special_number}")
                    continue
                
                # 导入数据
                success = data_processor.manual_input_record(
                    period_number=period_number,
                    draw_date=draw_date,
                    regular_numbers=regular_numbers,
                    special_number=special_number
                )
                
                if success:
                    success_count += 1
                    if success_count % 100 == 0:
                        print(f"   已导入 {success_count} 条记录...")
                else:
                    error_count += 1
                    logger.warning(f"导入失败: {period_number}")
                    
            except Exception as e:
                error_count += 1
                logger.error(f"处理记录失败 {index}: {e}")
        
        # 5. 导入结果统计
        print("\n" + "="*80)
        print("📊 数据导入完成统计")
        print("="*80)
        print(f"✅ 成功导入: {success_count} 条")
        print(f"❌ 导入失败: {error_count} 条")
        print(f"📊 成功率: {success_count/(success_count+error_count)*100:.1f}%")
        
        # 6. 验证导入结果
        from src.data_layer.database.models import LotteryData
        total_records = session.query(LotteryData).count()
        print(f"📋 数据库中总记录数: {total_records}")
        
        # 7. 显示最新几条记录
        latest_records = session.query(LotteryData).order_by(LotteryData.draw_date.desc()).limit(5).all()
        print(f"\n📋 最新5条记录:")
        for record in latest_records:
            regular_nums = json.loads(record.regular_numbers) if record.regular_numbers else []
            print(f"   {record.period_number} | {record.draw_date} | 正码: {regular_nums} | 特码: {record.special_number}")
        
        # 8. 数据统计分析
        print(f"\n📊 数据统计分析:")
        date_range = session.query(LotteryData.draw_date).order_by(LotteryData.draw_date).all()
        if date_range:
            start_date = date_range[0][0]
            end_date = date_range[-1][0]
            print(f"   📅 数据时间范围: {start_date} 至 {end_date}")
            print(f"   📅 数据跨度: {(end_date - start_date).days} 天")
        
        session.close()
        logger.info("历史数据导入完成")
        return True
        
    except Exception as e:
        logger.error(f"导入过程发生错误: {e}")
        session.close()
        return False

if __name__ == "__main__":
    result = import_historical_data()
    
    if result:
        print("\n🎊 历史数据导入成功！")
        print("现在可以使用901期真实历史数据进行预测分析了。")
        print("建议下一步: 运行预测系统测试")
    else:
        print("\n❌ 历史数据导入失败！")
        print("请检查错误日志并重试。")
