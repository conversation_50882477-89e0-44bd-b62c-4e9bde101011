"""
重复数据检测器
"""
import pandas as pd
from typing import Dict, List
from loguru import logger

class DuplicateDetector:
    def __init__(self):
        self.duplicate_strategies = {
            'exact': self._detect_exact_duplicates,
            'issue': self._detect_issue_duplicates,
            'date': self._detect_date_duplicates
        }
    
    def detect_duplicates(self, df: pd.DataFrame, strategy: str = 'exact') -> Dict:
        """检测重复数据"""
        if strategy not in self.duplicate_strategies:
            raise ValueError(f"不支持的策略: {strategy}")
        
        detector = self.duplicate_strategies[strategy]
        return detector(df)
    
    def _detect_exact_duplicates(self, df: pd.DataFrame) -> Dict:
        """检测完全重复的记录"""
        duplicates = df.duplicated()
        duplicate_indices = df[duplicates].index.tolist()
        
        return {
            'strategy': 'exact',
            'duplicate_count': len(duplicate_indices),
            'duplicate_indices': duplicate_indices,
            'duplicate_records': df.loc[duplicate_indices].to_dict('records')
        }
    
    def _detect_issue_duplicates(self, df: pd.DataFrame) -> Dict:
        """检测期号重复的记录"""
        if 'issue' not in df.columns:
            return {'strategy': 'issue', 'duplicate_count': 0, 'duplicate_indices': []}
        
        duplicates = df.duplicated(subset=['issue'])
        duplicate_indices = df[duplicates].index.tolist()
        
        return {
            'strategy': 'issue',
            'duplicate_count': len(duplicate_indices),
            'duplicate_indices': duplicate_indices,
            'duplicate_records': df.loc[duplicate_indices].to_dict('records')
        }
    
    def _detect_date_duplicates(self, df: pd.DataFrame) -> Dict:
        """检测日期重复的记录"""
        if 'date' not in df.columns:
            return {'strategy': 'date', 'duplicate_count': 0, 'duplicate_indices': []}
        
        duplicates = df.duplicated(subset=['date'])
        duplicate_indices = df[duplicates].index.tolist()
        
        return {
            'strategy': 'date',
            'duplicate_count': len(duplicate_indices),
            'duplicate_indices': duplicate_indices,
            'duplicate_records': df.loc[duplicate_indices].to_dict('records')
        }
    
    def remove_duplicates(self, df: pd.DataFrame, strategy: str = 'exact', keep: str = 'first') -> pd.DataFrame:
        """移除重复数据"""
        if strategy == 'exact':
            return df.drop_duplicates(keep=keep)
        elif strategy == 'issue':
            return df.drop_duplicates(subset=['issue'], keep=keep)
        elif strategy == 'date':
            return df.drop_duplicates(subset=['date'], keep=keep)
        else:
            raise ValueError(f"不支持的策略: {strategy}")
