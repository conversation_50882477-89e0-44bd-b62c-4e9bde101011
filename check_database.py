"""
检查数据库中的真实历史数据
"""
import sqlite3
from datetime import datetime

def check_database():
    """检查数据库中的数据"""
    print("🔍 检查数据库中的历史数据")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"数据库中的表: {tables}")
        
        # 检查lottery_results表的数据
        try:
            cursor.execute('SELECT COUNT(*) FROM lottery_results')
            count = cursor.fetchone()[0]
            print(f"lottery_results表中的记录数: {count}")
            
            if count > 0:
                # 检查2025-01-31的数据
                cursor.execute('SELECT * FROM lottery_results WHERE date = "2025-01-31" LIMIT 1')
                result = cursor.fetchone()
                if result:
                    print(f"2025-01-31的真实数据: {result}")
                    
                    # 解析数据
                    cursor.execute('PRAGMA table_info(lottery_results)')
                    columns = [col[1] for col in cursor.fetchall()]
                    print(f"表结构: {columns}")
                    
                    # 显示具体字段
                    data_dict = dict(zip(columns, result))
                    print(f"详细数据: {data_dict}")
                else:
                    print("没有找到2025-01-31的数据")
                
                # 显示最近几条数据
                cursor.execute('SELECT * FROM lottery_results ORDER BY date DESC LIMIT 5')
                recent = cursor.fetchall()
                print("\n最近5条数据:")
                for i, row in enumerate(recent):
                    print(f"  {i+1}. {row}")
                
                # 检查2025年1月的数据
                cursor.execute('SELECT date, special_number FROM lottery_results WHERE date LIKE "2025-01%" ORDER BY date')
                jan_2025 = cursor.fetchall()
                print(f"\n2025年1月的数据 ({len(jan_2025)}条):")
                for date, special in jan_2025:
                    print(f"  {date}: 特码{special}")
                    
            else:
                print("lottery_results表为空")
                
        except Exception as e:
            print(f"查询lottery_results表出错: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"数据库连接失败: {e}")

if __name__ == "__main__":
    check_database()
