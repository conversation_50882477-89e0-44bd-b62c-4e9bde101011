"""
六合彩预测系统简化综合测试
专注于核心功能测试
"""
import sys
import os
import sqlite3
import traceback
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_core():
    """测试数据库核心功能"""
    print("🔧 测试数据库核心功能")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        # 检查数据量
        cursor.execute('SELECT COUNT(*) FROM lottery_results')
        total_records = cursor.fetchone()[0]
        print(f"✅ 数据库记录: {total_records} 条")
        
        # 验证关键数据
        cursor.execute('SELECT special_number FROM lottery_results WHERE draw_date = "2024-10-21"')
        result = cursor.fetchone()
        if result and result[0] == 48:
            print("✅ 关键数据验证通过 (2024-10-21: 特码48)")
            conn.close()
            return True
        else:
            print(f"❌ 关键数据验证失败")
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_consistent_predictor():
    """测试一致性预测器"""
    print("\n🔧 测试一致性预测器")
    print("=" * 50)
    
    try:
        from consistent_predictor import ConsistentSpecialNumberPredictor
        predictor = ConsistentSpecialNumberPredictor()
        
        # 运行预测
        result = predictor.run_consistent_prediction("2025-06-22")
        
        if result and 'special_number_prediction' in result:
            special_pred = result['special_number_prediction']
            final_recs = special_pred.get('final_recommendations', [])
            print(f"✅ 一致性预测器正常: {len(final_recs)}个推荐号码")
            
            # 检查号码有效性
            valid = all(1 <= num <= 49 for num in final_recs)
            print(f"✅ 号码有效性: {'通过' if valid else '失败'}")
            return valid
        else:
            print("❌ 一致性预测器返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ 一致性预测器测试失败: {e}")
        return False

def test_zodiac_analyzer():
    """测试生肖分析器"""
    print("\n🔧 测试生肖分析器")
    print("=" * 50)
    
    try:
        from special_zodiac_analyzer import SpecialZodiacAnalyzer
        analyzer = SpecialZodiacAnalyzer()
        
        # 运行分析
        result = analyzer.comprehensive_special_zodiac_prediction(days=50)
        
        if result and 'top_4_zodiacs' in result:
            top_zodiacs = result['top_4_zodiacs']
            print(f"✅ 生肖分析器正常: {len(top_zodiacs)}个预测生肖")
            
            # 检查生肖有效性
            valid_zodiacs = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪']
            zodiac_names = [z.get('zodiac', '') for z in top_zodiacs]
            valid = all(z in valid_zodiacs for z in zodiac_names)
            print(f"✅ 生肖有效性: {'通过' if valid else '失败'}")
            return valid
        else:
            print("❌ 生肖分析器返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ 生肖分析器测试失败: {e}")
        return False

def test_gui_data_integration():
    """测试GUI数据集成"""
    print("\n🔧 测试GUI数据集成")
    print("=" * 50)
    
    try:
        from lottery_prediction_gui import MainWindow
        gui = MainWindow()
        
        # 测试数据获取
        actual_data = gui.get_actual_lottery_data_for_date(datetime(2024, 10, 21))
        
        if actual_data and actual_data['special_number'] == 48:
            print(f"✅ GUI数据获取正常: 特码{actual_data['special_number']}")
            return True
        else:
            print("❌ GUI数据获取失败")
            return False
            
    except Exception as e:
        print(f"❌ GUI数据集成测试失败: {e}")
        return False

def test_backtest_data_flow():
    """测试回测数据流"""
    print("\n🔧 测试回测数据流")
    print("=" * 50)
    
    try:
        from lottery_prediction_gui import MainWindow
        gui = MainWindow()
        
        # 测试简化回测
        results = gui.run_simple_backtest("2024-10-21", "2024-10-21", 30)
        
        if results and len(results) > 0:
            result = results[0]
            actual_number = result.get('actual_number', 0)
            predicted_numbers = result.get('predicted_numbers', [])
            
            print(f"✅ 回测数据流正常")
            print(f"   实际号码: {actual_number}")
            print(f"   预测数量: {len(predicted_numbers)}")
            
            # 验证是否使用真实数据
            if actual_number == 48:
                print("✅ 使用真实开奖数据")
                return True
            else:
                print(f"⚠️ 可能使用模拟数据 (实际:{actual_number})")
                return False
        else:
            print("❌ 回测数据流无结果")
            return False
            
    except Exception as e:
        print(f"❌ 回测数据流测试失败: {e}")
        return False

def test_system_integration():
    """测试系统集成"""
    print("\n🔧 测试系统集成")
    print("=" * 50)
    
    try:
        # 测试各模块协同工作
        from consistent_predictor import ConsistentSpecialNumberPredictor
        from special_zodiac_analyzer import SpecialZodiacAnalyzer
        
        # 1. 预测器生成号码
        predictor = ConsistentSpecialNumberPredictor()
        pred_result = predictor.run_consistent_prediction("2025-06-22")
        
        if not pred_result:
            print("❌ 预测器无结果")
            return False
        
        # 2. 生肖分析器分析
        analyzer = SpecialZodiacAnalyzer()
        zodiac_result = analyzer.comprehensive_special_zodiac_prediction(days=50)
        
        if not zodiac_result:
            print("❌ 生肖分析器无结果")
            return False
        
        # 3. 检查结果一致性
        special_pred = pred_result.get('special_number_prediction', {})
        final_numbers = special_pred.get('final_recommendations', [])
        top_zodiacs = zodiac_result.get('top_4_zodiacs', [])
        
        print(f"✅ 系统集成正常")
        print(f"   预测号码: {len(final_numbers)}个")
        print(f"   预测生肖: {len(top_zodiacs)}个")
        
        return len(final_numbers) > 0 and len(top_zodiacs) > 0
        
    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        return False

def run_simplified_test():
    """运行简化测试"""
    print("🎯 六合彩预测系统简化综合测试")
    print("=" * 70)
    
    tests = [
        ("数据库核心", test_database_core),
        ("一致性预测器", test_consistent_predictor),
        ("生肖分析器", test_zodiac_analyzer),
        ("GUI数据集成", test_gui_data_integration),
        ("回测数据流", test_backtest_data_flow),
        ("系统集成", test_system_integration)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 70)
    print("🎯 简化测试结果汇总")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    print(f"📈 通过率: {passed/total*100:.1f}%")
    
    if passed >= total * 0.8:  # 80%通过率
        print("🎊 系统核心功能正常！")
        print("💡 主要模组交互良好，可以正常使用")
    elif passed >= total * 0.6:  # 60%通过率
        print("⚠️ 系统基本功能正常，部分模块需要优化")
    else:
        print("❌ 系统存在较多问题，需要检查修复")
    
    return results

if __name__ == "__main__":
    run_simplified_test()
