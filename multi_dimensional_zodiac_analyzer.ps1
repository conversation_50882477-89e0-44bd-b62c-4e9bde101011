# Multi-Dimensional Zodiac Analyzer - PowerShell Version
# Complete multi-dimensional analysis based on 2023-2025 data

param(
    [string]$TargetYear = "2025",
    [string]$AnalysisType = "all",
    [int]$SpecialNumber = 0
)

# Clear screen and show title
Clear-Host
Write-Host "Multi-Dimensional Zodiac Analysis Tool" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Yellow
Write-Host "Analysis Year: $TargetYear" -ForegroundColor Green
Write-Host "Analysis Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Green
Write-Host ""

# Define WuXing data (2023-2025)
$WuXingData = @{
    "2023" = @{
        "Jin" = @(1, 2, 9, 10, 23, 24, 31, 32, 39, 40)
        "Mu" = @(5, 6, 13, 14, 21, 22, 35, 36, 43, 44)
        "Shui" = @(11, 12, 19, 20, 27, 28, 41, 42, 49)
        "<PERSON><PERSON>" = @(7, 8, 15, 16, 29, 30, 37, 38, 45, 46)
        "Tu" = @(3, 4, 17, 18, 25, 26, 33, 34, 47, 48)
    }
    "2024" = @{
        "Jin" = @(2, 3, 10, 11, 24, 25, 32, 33, 40, 41)
        "Mu" = @(6, 7, 14, 15, 22, 23, 36, 37, 44, 45)
        "Shui" = @(12, 13, 20, 21, 28, 29, 42, 43)
        "Huo" = @(1, 8, 9, 16, 17, 30, 31, 38, 39, 46, 47)
        "Tu" = @(4, 5, 18, 19, 26, 27, 34, 35, 48, 49)
    }
    "2025" = @{
        "Jin" = @(3, 4, 11, 12, 25, 26, 33, 34, 41, 42)
        "Mu" = @(7, 8, 15, 16, 23, 24, 37, 38, 45, 46)
        "Shui" = @(13, 14, 21, 22, 29, 30, 43, 44)
        "Huo" = @(1, 2, 9, 10, 17, 18, 31, 32, 39, 40, 47, 48)
        "Tu" = @(5, 6, 19, 20, 27, 28, 35, 36, 49)
    }
}

# 定义生肖数据 (2023-2025年)
$ZodiacData = @{
    "2023" = @{
        "鼠" = @(4, 16, 28, 40); "牛" = @(3, 15, 27, 39); "虎" = @(2, 14, 26, 38)
        "兔" = @(1, 13, 25, 37, 49); "龙" = @(12, 24, 36, 48); "蛇" = @(11, 23, 35, 47)
        "马" = @(10, 22, 34, 46); "羊" = @(9, 21, 33, 45); "猴" = @(8, 20, 32, 44)
        "鸡" = @(7, 19, 31, 43); "狗" = @(6, 18, 30, 42); "猪" = @(5, 17, 29, 41)
    }
    "2024" = @{
        "鼠" = @(5, 17, 29, 41); "牛" = @(4, 16, 28, 40); "虎" = @(3, 15, 27, 39)
        "兔" = @(2, 14, 26, 38); "龙" = @(1, 13, 25, 37, 49); "蛇" = @(12, 24, 36, 48)
        "马" = @(11, 23, 35, 47); "羊" = @(10, 22, 34, 46); "猴" = @(9, 21, 33, 45)
        "鸡" = @(8, 20, 32, 44); "狗" = @(7, 19, 31, 43); "猪" = @(6, 18, 30, 42)
    }
    "2025" = @{
        "鼠" = @(6, 18, 30, 42); "牛" = @(5, 17, 29, 41); "虎" = @(4, 16, 28, 40)
        "兔" = @(3, 15, 27, 39); "龙" = @(2, 14, 26, 38); "蛇" = @(1, 13, 25, 37, 49)
        "马" = @(12, 24, 36, 48); "羊" = @(11, 23, 35, 47); "猴" = @(10, 22, 34, 46)
        "鸡" = @(9, 21, 33, 45); "狗" = @(8, 20, 32, 44); "猪" = @(7, 19, 31, 43)
    }
}

# 定义多维度属性
$ZodiacAttributes = @{
    "日夜" = @{
        "日肖" = @("兔", "龙", "蛇", "马", "羊", "猴")
        "夜肖" = @("鼠", "牛", "虎", "鸡", "狗", "猪")
    }
    "左右" = @{
        "左肖" = @("鼠", "牛", "龙", "蛇", "猴", "鸡")
        "右肖" = @("虎", "兔", "马", "羊", "狗", "猪")
    }
    "阴阳" = @{
        "阴肖" = @("鼠", "龙", "蛇", "马", "狗", "猪")
        "阳肖" = @("牛", "虎", "兔", "羊", "猴", "鸡")
    }
    "家野" = @{
        "家肖" = @("牛", "马", "羊", "狗", "鸡", "猪")
        "野肖" = @("鼠", "虎", "兔", "龙", "蛇", "猴")
    }
    "天地" = @{
        "天肖" = @("牛", "猴", "兔", "猪", "马", "龙")
        "地肖" = @("蛇", "虎", "羊", "鸡", "狗", "鼠")
    }
    "男女" = @{
        "男肖" = @("鼠", "牛", "虎", "龙", "马", "猴", "狗")
        "女肖" = @("兔", "蛇", "羊", "鸡", "猪")
    }
    "吉凶" = @{
        "吉肖" = @("兔", "龙", "蛇", "马", "羊", "鸡")
        "凶肖" = @("鼠", "牛", "虎", "猴", "狗", "猪")
    }
    "前后" = @{
        "前肖" = @("鼠", "牛", "虎", "兔", "龙", "蛇")
        "后肖" = @("马", "羊", "猴", "鸡", "狗", "猪")
    }
    "琴棋书画" = @{
        "琴" = @("兔", "蛇", "鸡")
        "棋" = @("鼠", "牛", "狗")
        "书" = @("马", "龙", "虎")
        "画" = @("羊", "猴", "猪")
    }
    "季节" = @{
        "春" = @("虎", "兔", "龙")
        "夏" = @("蛇", "马", "羊")
        "秋" = @("猴", "狗", "鸡")
        "冬" = @("鼠", "牛", "猪")
    }
    "波色" = @{
        "红肖" = @("鼠", "兔", "马", "鸡")
        "绿肖" = @("牛", "龙", "羊", "狗")
        "蓝肖" = @("虎", "蛇", "猴", "猪")
    }
}

# 定义号码属性
$NumberAttributes = @{
    "大小" = @{
        "大数" = @(25..49)
        "小数" = @(1..24)
    }
    "单双" = @{
        "单数" = @(1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49)
        "双数" = @(2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48)
    }
    "波色" = @{
        "红波" = @(1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46)
        "蓝波" = @(3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48)
        "绿波" = @(5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49)
    }
    "正反" = @{
        "正码" = @(5, 6, 7, 8, 9, 15, 16, 17, 18, 19, 25, 26, 27, 28, 29, 35, 36, 37, 38, 39, 45, 46, 47, 48, 49)
        "反码" = @(1, 2, 3, 4, 10, 11, 12, 13, 14, 20, 21, 22, 23, 24, 30, 31, 32, 33, 34, 40, 41, 42, 43, 44)
    }
}

function Get-NumberZodiac {
    param([int]$Number, [string]$Year)
    
    $zodiacData = $ZodiacData[$Year]
    foreach ($zodiac in $zodiacData.Keys) {
        if ($zodiacData[$zodiac] -contains $Number) {
            return $zodiac
        }
    }
    return "未知"
}

function Get-NumberWuXing {
    param([int]$Number, [string]$Year)
    
    $wuxingData = $WuXingData[$Year]
    foreach ($element in $wuxingData.Keys) {
        if ($wuxingData[$element] -contains $Number) {
            return $element
        }
    }
    return "未知"
}

function Get-ZodiacAttributes {
    param([string]$Zodiac)
    
    $attributes = @{}
    foreach ($category in $ZodiacAttributes.Keys) {
        foreach ($subcategory in $ZodiacAttributes[$category].Keys) {
            if ($ZodiacAttributes[$category][$subcategory] -contains $Zodiac) {
                $attributes[$category] = $subcategory
                break
            }
        }
    }
    return $attributes
}

function Analyze-SpecialNumber {
    param([int]$Number, [string]$Year)
    
    Write-Host "🔍 特码号码 $Number 的多维度分析 ($Year 年)" -ForegroundColor Yellow
    Write-Host "-" * 50 -ForegroundColor Gray
    
    # 基础属性
    $zodiac = Get-NumberZodiac -Number $Number -Year $Year
    $wuxing = Get-NumberWuXing -Number $Number -Year $Year
    
    Write-Host "🐲 生肖: $zodiac" -ForegroundColor Green
    Write-Host "⚡ 五行: $wuxing" -ForegroundColor Cyan
    
    # 生肖多维度属性
    $zodiacAttrs = Get-ZodiacAttributes -Zodiac $zodiac
    Write-Host ""
    Write-Host "📊 生肖多维度属性:" -ForegroundColor Magenta
    foreach ($category in $zodiacAttrs.Keys) {
        $value = $zodiacAttrs[$category]
        Write-Host "  $category : $value" -ForegroundColor White
    }
    
    # 号码属性
    Write-Host ""
    Write-Host "🔢 号码属性分析:" -ForegroundColor Magenta
    
    # 大小
    if ($Number -ge 25) {
        Write-Host "  大小: 大数 (≥25)" -ForegroundColor White
    } else {
        Write-Host "  大小: 小数 (<25)" -ForegroundColor White
    }
    
    # 单双
    if ($Number % 2 -eq 1) {
        Write-Host "  单双: 单数" -ForegroundColor White
    } else {
        Write-Host "  单双: 双数" -ForegroundColor White
    }
    
    # 波色
    foreach ($wave in $NumberAttributes["波色"].Keys) {
        if ($NumberAttributes["波色"][$wave] -contains $Number) {
            Write-Host "  波色: $wave" -ForegroundColor White
            break
        }
    }
    
    # 正反码
    foreach ($type in $NumberAttributes["正反"].Keys) {
        if ($NumberAttributes["正反"][$type] -contains $Number) {
            Write-Host "  正反: $type" -ForegroundColor White
            break
        }
    }
    
    # 合数分析
    $digitSum = [string]$Number -split '' | Where-Object { $_ -ne '' } | ForEach-Object { [int]$_ } | Measure-Object -Sum | Select-Object -ExpandProperty Sum
    Write-Host "  合数: $digitSum" -ForegroundColor White
    
    # 段位分析
    $segment = [Math]::Ceiling($Number / 7)
    Write-Host "  段位: 第 $segment 段" -ForegroundColor White
}

function Analyze-CrossDimension {
    param([string]$Year)
    
    Write-Host ""
    Write-Host "🔀 多维度交叉分析 ($Year 年)" -ForegroundColor Yellow
    Write-Host "-" * 50 -ForegroundColor Gray
    
    $zodiacData = $ZodiacData[$Year]
    $wuxingData = $WuXingData[$Year]
    
    # 生肖与五行交叉分析
    Write-Host "🌟 生肖与五行交叉分析:" -ForegroundColor Magenta
    foreach ($zodiac in $zodiacData.Keys) {
        $numbers = $zodiacData[$zodiac]
        $wuxingCount = @{}
        
        foreach ($num in $numbers) {
            $wuxing = Get-NumberWuXing -Number $num -Year $Year
            if ($wuxingCount.ContainsKey($wuxing)) {
                $wuxingCount[$wuxing]++
            } else {
                $wuxingCount[$wuxing] = 1
            }
        }
        
        $dominantWuxing = ($wuxingCount.GetEnumerator() | Sort-Object Value -Descending | Select-Object -First 1).Name
        Write-Host "  $zodiac : 主要五行 $dominantWuxing" -ForegroundColor White
    }
    
    # 季节与波色交叉分析
    Write-Host ""
    Write-Host "🌈 季节与波色交叉分析:" -ForegroundColor Magenta
    foreach ($season in $ZodiacAttributes["季节"].Keys) {
        $seasonZodiacs = $ZodiacAttributes["季节"][$season]
        $waveCount = @{"红肖" = 0; "绿肖" = 0; "蓝肖" = 0}
        
        foreach ($zodiac in $seasonZodiacs) {
            foreach ($wave in $ZodiacAttributes["波色"].Keys) {
                if ($ZodiacAttributes["波色"][$wave] -contains $zodiac) {
                    $waveCount[$wave]++
                    break
                }
            }
        }
        
        $dominantWave = ($waveCount.GetEnumerator() | Sort-Object Value -Descending | Select-Object -First 1).Name
        Write-Host "  $season 季: 主要波色 $dominantWave" -ForegroundColor White
    }
}

function Show-WuXingDistribution {
    param([string]$Year)
    
    Write-Host ""
    Write-Host "⚡ 五行分布分析 ($Year 年)" -ForegroundColor Yellow
    Write-Host "-" * 50 -ForegroundColor Gray
    
    $wuxingData = $WuXingData[$Year]
    foreach ($element in $wuxingData.Keys) {
        $numbers = $wuxingData[$element]
        $count = $numbers.Count
        Write-Host "  $element : $count 个号码 - $($numbers -join ', ')" -ForegroundColor White
    }
}

function Show-ZodiacDistribution {
    param([string]$Year)
    
    Write-Host ""
    Write-Host "🐲 生肖分布分析 ($Year 年)" -ForegroundColor Yellow
    Write-Host "-" * 50 -ForegroundColor Gray
    
    $zodiacData = $ZodiacData[$Year]
    foreach ($zodiac in $zodiacData.Keys) {
        $numbers = $zodiacData[$zodiac]
        $count = $numbers.Count
        Write-Host "  $zodiac : $count 个号码 - $($numbers -join ', ')" -ForegroundColor White
    }
}

# 主程序执行
if ($SpecialNumber -gt 0) {
    Analyze-SpecialNumber -Number $SpecialNumber -Year $TargetYear
} else {
    switch ($AnalysisType) {
        "wuxing" { Show-WuXingDistribution -Year $TargetYear }
        "zodiac" { Show-ZodiacDistribution -Year $TargetYear }
        "cross" { Analyze-CrossDimension -Year $TargetYear }
        "all" {
            Show-WuXingDistribution -Year $TargetYear
            Show-ZodiacDistribution -Year $TargetYear
            Analyze-CrossDimension -Year $TargetYear
        }
        default {
            Write-Host "❌ 未知的分析类型: $AnalysisType" -ForegroundColor Red
            Write-Host "可用类型: all, wuxing, zodiac, cross" -ForegroundColor Yellow
        }
    }
}

Write-Host ""
Write-Host "✅ 多维生肖分析完成!" -ForegroundColor Green
Write-Host ""
Write-Host "💡 使用示例:" -ForegroundColor Cyan
Write-Host "  .\multi_dimensional_zodiac_analyzer.ps1 -TargetYear 2025 -AnalysisType all" -ForegroundColor Gray
Write-Host "  .\multi_dimensional_zodiac_analyzer.ps1 -TargetYear 2025 -SpecialNumber 25" -ForegroundColor Gray
Write-Host "  .\multi_dimensional_zodiac_analyzer.ps1 -TargetYear 2024 -AnalysisType cross" -ForegroundColor Gray
