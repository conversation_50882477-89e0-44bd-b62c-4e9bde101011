=== PyInstaller 构建日志 ===
命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe -m PyInstaller --clean --noconfirm --distpath I:\编程\lottery\dist_exe --workpath I:\编程\lottery\build_exe I:\编程\lottery\LotteryPredictionSystem.spec
返回码: 0

=== 标准输出 ===
无输出
=== 标准错误 ===
427 INFO: PyInstaller: 6.3.0
427 INFO: Python: 3.11.9
435 INFO: Platform: Windows-10-10.0.26100-SP0
441 INFO: Removing temporary files and cleaning cache in C:\Users\<USER>\AppData\Local\pyinstaller
443 INFO: Extending PYTHONPATH with paths
['I:\\\\lottery', 'I:\\\\lottery']
1252 INFO: Appending 'datas' from .spec
1262 INFO: checking Analysis
1262 INFO: Building Analysis because Analysis-00.toc is non existent
1262 INFO: Initializing module dependency graph...
1265 INFO: Caching module graph hooks...
1305 INFO: Analyzing base_library.zip ...
2834 INFO: Loading module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
2922 INFO: Loading module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
4467 INFO: Loading module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
6615 INFO: Caching module dependency graph...
6689 INFO: Running Analysis Analysis-00.toc
6689 INFO: Looking for Python shared library...
6704 INFO: Using Python shared library: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python311.dll
6704 INFO: Analyzing I:\\lottery\lottery_prediction_gui.py
6861 INFO: Loading module hook 'hook-PyQt5.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
7064 INFO: Loading module hook 'hook-numpy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\_pyinstaller'...
8003 INFO: Loading module hook 'hook-platform.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
8049 INFO: Loading module hook 'hook-difflib.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
8513 INFO: Loading module hook 'hook-sysconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
8930 INFO: Loading module hook 'hook-psutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
10620 INFO: Loading module hook 'hook-pandas.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
13269 INFO: Loading module hook 'hook-pytz.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
13666 INFO: Loading module hook 'hook-pkg_resources.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
14887 INFO: Loading module hook 'hook-xml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
14912 INFO: Loading module hook 'hook-packaging.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
15088 INFO: Loading module hook 'hook-jaraco.text.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
15415 INFO: Loading module hook 'hook-backports.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
15441 INFO: Loading module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
15793 INFO: Loading module hook 'hook-multiprocessing.util.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
17686 INFO: Loading module hook 'hook-scipy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
17984 INFO: Loading module hook 'hook-pytest.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
18319 INFO: Loading module hook 'hook-py.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
18639 INFO: Loading module hook 'hook-importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
19581 INFO: Loading module hook 'hook-pygments.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
22459 INFO: Loading module hook 'hook-pycparser.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
22931 INFO: Processing pre-safe import module hook distutils from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module\\hook-distutils.py'.
24044 INFO: Loading module hook 'hook-scipy.linalg.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
25432 INFO: Loading module hook 'hook-scipy.sparse.csgraph.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
25574 INFO: Loading module hook 'hook-matplotlib.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
26109 INFO: Processing pre-safe import module hook gi from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module\\hook-gi.py'.
26326 INFO: Loading module hook 'hook-PIL.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
26391 INFO: Loading module hook 'hook-PIL.Image.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
26978 INFO: Loading module hook 'hook-xml.etree.cElementTree.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
27168 INFO: Loading module hook 'hook-PIL.ImageFilter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
28181 INFO: Loading module hook 'hook-jinja2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
30088 INFO: Loading module hook 'hook-dateutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
30175 INFO: Processing pre-safe import module hook six.moves from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module\\hook-six.moves.py'.
31481 INFO: Loading module hook 'hook-matplotlib.backends.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
32596 INFO: Loading module hook 'hook-certifi.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
34821 INFO: Loading module hook 'hook-scipy.spatial.transform.rotation.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
37903 INFO: Loading module hook 'hook-pandas.io.formats.style.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
39131 INFO: Loading module hook 'hook-pyarrow.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
40467 INFO: Processing pre-safe import module hook urllib3.packages.six.moves from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module\\hook-urllib3.packages.six.moves.py'.
41409 INFO: Loading module hook 'hook-charset_normalizer.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
43308 INFO: Loading module hook 'hook-pandas.plotting.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
43956 INFO: Loading module hook 'hook-openpyxl.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
44207 INFO: Loading module hook 'hook-lxml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
48499 INFO: Loading module hook 'hook-sqlalchemy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
52821 INFO: Loading module hook 'hook-sqlite3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
53783 INFO: Loading module hook 'hook-xml.dom.domreg.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
55248 INFO: Loading module hook 'hook-sklearn.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
56499 INFO: Loading module hook 'hook-sklearn.utils.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
57826 INFO: Loading module hook 'hook-sklearn.metrics.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
63735 INFO: Loading module hook 'hook-sklearn.metrics.cluster.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
63946 INFO: Loading module hook 'hook-sklearn.cluster.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
64034 INFO: Loading module hook 'hook-sklearn.metrics.pairwise.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
64536 INFO: Loading module hook 'hook-sklearn.neighbors.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
64672 INFO: Loading module hook 'hook-sklearn.linear_model.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
66461 INFO: Loading module hook 'hook-sklearn.tree.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
68009 INFO: Analyzing hidden import 'seaborn'
68106 INFO: Processing pre-safe import module hook win32com from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\pre_safe_import_module\\hook-win32com.py'.
68783 INFO: Loading module hook 'hook-patsy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
70590 INFO: Loading module hook 'hook-statsmodels.tsa.statespace.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
75998 INFO: Processing module hooks...
75998 INFO: Loading module hook 'hook-lxml.etree.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
76206 INFO: Loading module hook 'hook-lxml.isoschematron.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
76550 INFO: Loading module hook 'hook-matplotlib.backends.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
76550 INFO: Matplotlib backend selection method: automatic discovery of used backends
76959 INFO: Trying determine the default backend as first importable candidate from the list: ['Qt5Agg', 'Gtk3Agg', 'TkAgg', 'WxAgg']
77506 INFO: Selected matplotlib backends: ['Qt5Agg']
77536 INFO: Loading module hook 'hook-PyQt6.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
78340 INFO: Loading module hook 'hook-PIL.SpiderImagePlugin.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
78478 WARNING: Hidden import "pkg_resources._vendor.jaraco.functools" not found!
78478 WARNING: Hidden import "pkg_resources._vendor.jaraco.context" not found!
78478 WARNING: Hidden import "pkg_resources._vendor.jaraco.text" not found!
82191 INFO: Loading module hook 'hook-PyQt5.QtCore.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
82381 INFO: Loading module hook 'hook-PyQt5.QtGui.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
82531 INFO: Loading module hook 'hook-PyQt5.QtWidgets.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
82644 INFO: Loading module hook 'hook-PyQt6.QtCore.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
82809 INFO: Loading module hook 'hook-PyQt6.QtGui.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
83003 INFO: Loading module hook 'hook-PyQt6.QtWidgets.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
83215 INFO: Loading module hook 'hook-scipy.special._ellip_harm_2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
83216 INFO: Loading module hook 'hook-scipy.special._ufuncs.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
83216 INFO: Loading module hook 'hook-scipy.stats._stats.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
83220 INFO: Loading module hook 'hook-sqlalchemy.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
83524 WARNING: Hidden import "pysqlite2" not found!
83525 WARNING: Hidden import "MySQLdb" not found!
83526 WARNING: Hidden import "psycopg2" not found!
84768 INFO: Loading module hook 'hook-lxml.objectify.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
84803 INFO: Performing binary vs. data reclassification (2158 entries)
93778 INFO: Looking for ctypes DLLs
93974 INFO: Analyzing run-time hooks ...
93987 INFO: Including run-time hook 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py'
93990 INFO: Processing pre-find module path hook _pyi_rth_utils from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path\\hook-_pyi_rth_utils.py'.
94000 INFO: Loading module hook 'hook-_pyi_rth_utils.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'...
94001 INFO: Including run-time hook 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py'
94004 INFO: Including run-time hook 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py'
94013 INFO: Including run-time hook 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py'
94020 INFO: Including run-time hook 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py'
94022 INFO: Including run-time hook 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py'
94030 INFO: Including run-time hook 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py'
94128 INFO: Looking for dynamic libraries
C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\building\build_main.py:194: UserWarning: The numpy.array_api submodule is still experimental. See NEP 47.
  __import__(package)
98822 INFO: Extra DLL search directories (AddDllDirectory): ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\numpy\\.libs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\scipy.libs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\matplotlib.libs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyQt6\\Qt6\\bin', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyQt5\\Qt5\\bin']
98822 INFO: Extra DLL search directories (PATH): ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyQt5\\Qt5\\bin', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyQt6\\Qt6\\bin']
107623 INFO: Warnings written to I:\\lottery\build_exe\LotteryPredictionSystem\warn-LotteryPredictionSystem.txt
107814 INFO: Graph cross-reference written to I:\\lottery\build_exe\LotteryPredictionSystem\xref-LotteryPredictionSystem.html
107948 INFO: checking PYZ
107948 INFO: Building PYZ because PYZ-00.toc is non existent
107948 INFO: Building PYZ (ZlibArchive) I:\\lottery\build_exe\LotteryPredictionSystem\PYZ-00.pyz
110843 INFO: Building PYZ (ZlibArchive) I:\\lottery\build_exe\LotteryPredictionSystem\PYZ-00.pyz completed successfully.
110921 INFO: checking PKG
110921 INFO: Building PKG because PKG-00.toc is non existent
110921 INFO: Building PKG (CArchive) LotteryPredictionSystem.pkg
148414 INFO: Building PKG (CArchive) LotteryPredictionSystem.pkg completed successfully.
148443 INFO: Bootloader C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\bootloader\Windows-64bit-intel\run.exe
148443 INFO: checking EXE
148443 INFO: Building EXE because EXE-00.toc is non existent
148443 INFO: Building EXE from EXE-00.toc
148443 INFO: Copying bootloader EXE to I:\\lottery\dist_exe\LotteryPredictionSystem.exe
148544 INFO: Copying icon to EXE
148591 INFO: Copying 0 resources to EXE
148591 INFO: Embedding manifest in EXE
148638 INFO: Appending PKG archive to EXE
148753 INFO: Fixing EXE headers
151695 INFO: Building EXE from EXE-00.toc completed successfully.
