#!/usr/bin/env python3
"""
完美预测系统详细检查脚本
"""

import sys
import time
from datetime import datetime

def check_perfect_prediction_system():
    """检查完美预测系统"""
    print("🔍 完美预测系统详细检查")
    print("=" * 50)
    
    try:
        from src.perfect_prediction_system import PerfectPredictionSystem
        
        print("✅ 完美预测系统导入成功")
        
        # 初始化系统
        system = PerfectPredictionSystem()
        print("✅ 系统初始化成功")
        
        # 初始化模块
        system.initialize_modules()
        print("✅ 模块初始化成功")
        
        # 测试预测一致性
        target_date = "2025-06-25"
        print(f"🎯 测试目标日期: {target_date}")
        
        results = []
        for i in range(3):
            print(f"  第{i+1}次预测...")
            result = system.run_complete_prediction(target_date)
            
            if result and "final_results" in result:
                final_results = result["final_results"]
                numbers = final_results.get("recommended_16_numbers", [])
                zodiacs = final_results.get("recommended_4_zodiacs", [])
                confidence = final_results.get("overall_confidence", 0)
                stability = final_results.get("prediction_stability", 0)
                
                results.append({
                    "numbers": numbers,
                    "zodiacs": zodiacs,
                    "confidence": confidence,
                    "stability": stability
                })
                
                print(f"    📊 号码数量: {len(numbers)}")
                print(f"    🐉 生肖数量: {len(zodiacs)}")
                print(f"    💪 置信度: {confidence:.1%}")
                print(f"    🔒 稳定性: {stability:.1%}")
            else:
                print(f"    ❌ 第{i+1}次预测失败")
                results.append(None)
        
        # 检查一致性
        print()
        print("🔒 一致性检查:")
        
        valid_results = [r for r in results if r is not None]
        
        if len(valid_results) >= 2:
            first_result = valid_results[0]
            
            # 检查号码一致性
            numbers_consistent = all(r["numbers"] == first_result["numbers"] for r in valid_results)
            status = "✅ 一致" if numbers_consistent else "❌ 不一致"
            print(f"  📊 号码一致性: {status}")
            
            if not numbers_consistent:
                for i, r in enumerate(valid_results):
                    numbers_preview = r["numbers"][:5] if len(r["numbers"]) >= 5 else r["numbers"]
                    print(f"    第{i+1}次: {numbers_preview}...")
            
            # 检查生肖一致性
            zodiacs_consistent = all(r["zodiacs"] == first_result["zodiacs"] for r in valid_results)
            status = "✅ 一致" if zodiacs_consistent else "❌ 不一致"
            print(f"  🐉 生肖一致性: {status}")
            
            if not zodiacs_consistent:
                for i, r in enumerate(valid_results):
                    print(f"    第{i+1}次: {r['zodiacs']}")
            
            # 检查置信度一致性
            confidences = [r["confidence"] for r in valid_results]
            confidence_diff = max(confidences) - min(confidences)
            confidence_consistent = confidence_diff < 0.01
            status = "✅ 一致" if confidence_consistent else "❌ 不一致"
            print(f"  💪 置信度一致性: {status} (差异: {confidence_diff:.3f})")
            
            # 检查稳定性一致性
            stabilities = [r["stability"] for r in valid_results]
            stability_diff = max(stabilities) - min(stabilities)
            stability_consistent = stability_diff < 0.01
            status = "✅ 一致" if stability_consistent else "❌ 不一致"
            print(f"  🔒 稳定性一致性: {status} (差异: {stability_diff:.3f})")
            
            # 总体一致性
            overall_consistent = numbers_consistent and zodiacs_consistent and confidence_consistent and stability_consistent
            status = "✅ 通过" if overall_consistent else "❌ 失败"
            print(f"  🎯 总体一致性: {status}")
            
            if not overall_consistent:
                print()
                print("⚠️ 一致性问题分析:")
                if not numbers_consistent:
                    print("  • 号码预测结果不一致")
                if not zodiacs_consistent:
                    print("  • 生肖预测结果不一致")
                if not confidence_consistent:
                    print("  • 置信度计算不一致")
                if not stability_consistent:
                    print("  • 稳定性计算不一致")
                
                # 深入分析问题原因
                print()
                print("🔍 问题原因分析:")
                
                # 检查是否使用了随机数
                print("  1. 检查随机数种子设置...")
                
                # 检查模块配置
                print("  2. 检查模块配置一致性...")
                
                # 检查数据源
                print("  3. 检查数据源稳定性...")
                
                # 提供解决建议
                print()
                print("💡 解决建议:")
                print("  • 确保所有随机数生成都使用固定种子")
                print("  • 检查模块间的数据传递是否稳定")
                print("  • 验证配置文件的加载是否一致")
                print("  • 确保时间戳等动态因素不影响预测")
            
            return overall_consistent
            
        else:
            print("❌ 有效预测结果不足，无法进行一致性检查")
            return False

    except Exception as e:
        print(f"❌ 完美预测系统检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_individual_modules():
    """检查各个模块的一致性"""
    print()
    print("🔍 各模块一致性检查")
    print("=" * 50)
    
    try:
        # 检查传统分析模块
        print("📊 传统分析模块:")
        from src.algorithm_layer.traditional_analysis.traditional_predictor import TraditionalPredictor
        traditional = TraditionalPredictor()
        
        target_date = "2025-06-25"
        trad_results = []
        
        for i in range(3):
            result = traditional.predict(target_date)
            trad_results.append(result.get("predicted_numbers", []))
        
        trad_consistent = all(r == trad_results[0] for r in trad_results)
        print(f"  一致性: {'✅ 通过' if trad_consistent else '❌ 失败'}")
        
        # 检查机器学习模块
        print("🤖 机器学习模块:")
        from src.independent_modules.ml_module import MachineLearningModule
        ml = MachineLearningModule()
        
        ml_results = []
        for i in range(3):
            result = ml.predict(target_date)
            ml_results.append(result.get("predicted_numbers", []))
        
        ml_consistent = all(r == ml_results[0] for r in ml_results)
        print(f"  一致性: {'✅ 通过' if ml_consistent else '❌ 失败'}")
        
        # 检查生肖扩展模块
        print("🐉 生肖扩展模块:")
        from src.algorithm_layer.zodiac_analysis.zodiac_predictor import ZodiacPredictor
        zodiac = ZodiacPredictor()
        
        zodiac_results = []
        for i in range(3):
            result = zodiac.predict(target_date)
            zodiac_results.append(result.get("predicted_numbers", []))
        
        zodiac_consistent = all(r == zodiac_results[0] for r in zodiac_results)
        print(f"  一致性: {'✅ 通过' if zodiac_consistent else '❌ 失败'}")
        
        return trad_consistent and ml_consistent and zodiac_consistent
        
    except Exception as e:
        print(f"❌ 模块检查失败: {e}")
        return False

def check_fusion_manager():
    """检查融合管理器"""
    print()
    print("🔀 融合管理器检查")
    print("=" * 50)
    
    try:
        from src.fusion_layer.fusion_manager import FusionManager
        
        fusion = FusionManager()
        
        # 模拟模块预测结果
        mock_predictions = {
            "traditional_analysis": {
                "numbers": [1, 5, 12, 18, 23, 28, 34, 39, 42, 45, 47, 49, 3, 8, 15, 21],
                "confidence": 0.75
            },
            "machine_learning": {
                "numbers": [2, 6, 13, 19, 24, 29, 35, 40, 43, 46, 48, 1, 4, 9, 16, 22],
                "confidence": 0.84
            },
            "zodiac_extended": {
                "numbers": [3, 7, 14, 20, 25, 30, 36, 41, 44, 47, 49, 2, 5, 10, 17, 23],
                "confidence": 0.78
            }
        }
        
        fusion_results = []
        for i in range(3):
            result = fusion.fuse_predictions(mock_predictions)
            fusion_results.append(result.get("final_16_numbers", []))
        
        fusion_consistent = all(r == fusion_results[0] for r in fusion_results)
        print(f"融合一致性: {'✅ 通过' if fusion_consistent else '❌ 失败'}")
        
        if not fusion_consistent:
            for i, r in enumerate(fusion_results):
                print(f"  第{i+1}次: {r[:5]}...")
        
        return fusion_consistent
        
    except Exception as e:
        print(f"❌ 融合管理器检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 完美预测系统全面检查")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查完美预测系统
    perfect_ok = check_perfect_prediction_system()
    
    # 检查各个模块
    modules_ok = check_individual_modules()
    
    # 检查融合管理器
    fusion_ok = check_fusion_manager()
    
    # 总结
    print()
    print("=" * 60)
    print("📊 检查结果总结")
    print("=" * 60)
    
    results = [
        ("完美预测系统", perfect_ok),
        ("各模块一致性", modules_ok),
        ("融合管理器", fusion_ok)
    ]
    
    passed = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:15} : {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{len(results)} 项检查通过")
    print(f"成功率: {passed/len(results):.1%}")
    
    if passed == len(results):
        print("🎊 完美预测系统运行正常！")
    else:
        print("⚠️ 完美预测系统存在问题，需要修复")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
