"""
高级六合彩数据抓取器
尝试多种方法从指定API抓取数据
"""

import requests
import json
import time
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional
import urllib3
import ssl
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class AdvancedLotteryScraper:
    """高级六合彩数据抓取器"""
    
    def __init__(self):
        self.base_url = "https://yyswz.uhfasuf.com:14949/api/"
        self.session = None
        self.init_session()
        
    def init_session(self):
        """初始化会话"""
        self.session = requests.Session()
        
        # 设置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 设置通用请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })
        
        # 禁用SSL验证
        self.session.verify = False
    
    def test_basic_connection(self) -> bool:
        """测试基本连接"""
        print("🔗 方法1: 测试基本API连接...")
        
        try:
            response = self.session.get(self.base_url, timeout=10)
            print(f"  状态码: {response.status_code}")
            print(f"  响应头: {dict(list(response.headers.items())[:5])}")
            
            if response.ok:
                print(f"  响应内容长度: {len(response.text)}")
                print(f"  响应内容预览: {response.text[:200]}...")
                return True
            else:
                print(f"  错误响应: {response.text[:200]}...")
                return False
                
        except Exception as e:
            print(f"❌ 基本连接失败: {e}")
            return False
    
    def test_with_different_endpoints(self) -> Dict[str, Any]:
        """测试不同的端点"""
        print("\n🎯 方法2: 测试不同API端点...")
        
        endpoints = [
            "",  # 根路径
            "data",
            "lottery", 
            "results",
            "history",
            "api",
            "kjdata",
            "hk",
            "am",  # 澳门
            "v1",
            "v2"
        ]
        
        results = {}
        
        for endpoint in endpoints:
            url = f"{self.base_url.rstrip('/')}/{endpoint}" if endpoint else self.base_url
            print(f"  测试端点: {url}")
            
            try:
                response = self.session.get(url, timeout=8)
                results[endpoint or 'root'] = {
                    'status_code': response.status_code,
                    'content_length': len(response.text),
                    'content_preview': response.text[:100],
                    'headers': dict(response.headers)
                }
                
                if response.ok:
                    print(f"    ✅ 成功 ({response.status_code}) - {len(response.text)} 字节")
                else:
                    print(f"    ❌ 失败 ({response.status_code})")
                    
            except Exception as e:
                print(f"    ❌ 异常: {e}")
                results[endpoint or 'root'] = {'error': str(e)}
            
            time.sleep(0.5)  # 避免请求过快
        
        return results
    
    def test_with_parameters(self) -> Dict[str, Any]:
        """测试带参数的请求"""
        print("\n📊 方法3: 测试带参数的请求...")
        
        # 常见的参数组合
        param_combinations = [
            {"type": "hk"},
            {"type": "am"},
            {"game": "hk"},
            {"game": "am"},
            {"lottery": "hk"},
            {"lottery": "am"},
            {"g": "hk"},
            {"g": "am"},
            {"year": 2024},
            {"year": 2025},
            {"y": 2024},
            {"y": 2025},
            {"limit": 10},
            {"count": 10},
            {"page": 1},
            {"g": "am", "y": 2024},
            {"g": "hk", "y": 2024},
            {"type": "am", "year": 2024},
            {"lottery": "am", "limit": 10}
        ]
        
        results = {}
        
        for i, params in enumerate(param_combinations):
            print(f"  测试参数组合 {i+1}: {params}")
            
            try:
                response = self.session.get(self.base_url, params=params, timeout=8)
                
                result_key = f"params_{i+1}_{str(params)[:30]}"
                results[result_key] = {
                    'params': params,
                    'status_code': response.status_code,
                    'content_length': len(response.text),
                    'content_preview': response.text[:150]
                }
                
                if response.ok and len(response.text) > 10:
                    print(f"    ✅ 成功 - {len(response.text)} 字节")
                    
                    # 尝试解析JSON
                    try:
                        data = response.json()
                        if isinstance(data, list) and len(data) > 0:
                            print(f"    📊 JSON数据: {len(data)} 条记录")
                            print(f"    📝 示例: {data[0] if data else 'None'}")
                            results[result_key]['json_data'] = data[:3]  # 保存前3条
                        elif isinstance(data, dict):
                            print(f"    📊 JSON对象: {list(data.keys())}")
                            results[result_key]['json_data'] = data
                    except:
                        print(f"    ⚠️ 非JSON响应")
                else:
                    print(f"    ❌ 失败或空响应 ({response.status_code})")
                    
            except Exception as e:
                print(f"    ❌ 异常: {e}")
                results[result_key] = {'error': str(e)}
            
            time.sleep(0.3)
        
        return results
    
    def test_with_different_methods(self) -> Dict[str, Any]:
        """测试不同的HTTP方法"""
        print("\n🔧 方法4: 测试不同HTTP方法...")
        
        methods = ['GET', 'POST', 'PUT', 'OPTIONS']
        results = {}
        
        for method in methods:
            print(f"  测试 {method} 方法...")
            
            try:
                if method == 'GET':
                    response = self.session.get(self.base_url, timeout=8)
                elif method == 'POST':
                    response = self.session.post(self.base_url, 
                                               json={"type": "am", "year": 2024}, 
                                               timeout=8)
                elif method == 'PUT':
                    response = self.session.put(self.base_url, timeout=8)
                elif method == 'OPTIONS':
                    response = self.session.options(self.base_url, timeout=8)
                
                results[method] = {
                    'status_code': response.status_code,
                    'headers': dict(response.headers),
                    'content_length': len(response.text),
                    'content_preview': response.text[:100]
                }
                
                print(f"    状态码: {response.status_code}")
                if response.ok:
                    print(f"    ✅ 成功 - {len(response.text)} 字节")
                else:
                    print(f"    ❌ 失败")
                    
            except Exception as e:
                print(f"    ❌ 异常: {e}")
                results[method] = {'error': str(e)}
        
        return results
    
    def test_with_headers_variation(self) -> Dict[str, Any]:
        """测试不同请求头"""
        print("\n🎭 方法5: 测试不同请求头...")
        
        header_variations = [
            {
                'name': 'Chrome浏览器',
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                }
            },
            {
                'name': 'Firefox浏览器',
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
                }
            },
            {
                'name': '移动端',
                'headers': {
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
                }
            },
            {
                'name': 'API客户端',
                'headers': {
                    'User-Agent': 'LotteryClient/1.0',
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            },
            {
                'name': '带Referer',
                'headers': {
                    'Referer': 'https://yyswz.uhfasuf.com/',
                    'Origin': 'https://yyswz.uhfasuf.com'
                }
            }
        ]
        
        results = {}
        
        for variation in header_variations:
            print(f"  测试 {variation['name']}...")
            
            try:
                # 临时设置请求头
                temp_session = requests.Session()
                temp_session.headers.update(self.session.headers)
                temp_session.headers.update(variation['headers'])
                temp_session.verify = False
                
                response = temp_session.get(self.base_url, timeout=8)
                
                results[variation['name']] = {
                    'status_code': response.status_code,
                    'content_length': len(response.text),
                    'content_preview': response.text[:100]
                }
                
                if response.ok:
                    print(f"    ✅ 成功 ({response.status_code}) - {len(response.text)} 字节")
                else:
                    print(f"    ❌ 失败 ({response.status_code})")
                    
            except Exception as e:
                print(f"    ❌ 异常: {e}")
                results[variation['name']] = {'error': str(e)}
        
        return results
    
    def analyze_successful_responses(self, all_results: Dict[str, Any]):
        """分析成功的响应"""
        print("\n📈 分析成功的响应...")
        
        successful_responses = []
        
        # 收集所有成功的响应
        for method, results in all_results.items():
            if isinstance(results, dict):
                for key, result in results.items():
                    if isinstance(result, dict) and result.get('status_code') == 200:
                        if result.get('content_length', 0) > 10:  # 有实际内容
                            successful_responses.append({
                                'method': method,
                                'key': key,
                                'result': result
                            })
        
        if successful_responses:
            print(f"✅ 找到 {len(successful_responses)} 个成功响应:")
            
            for i, resp in enumerate(successful_responses):
                print(f"\n  响应 {i+1} ({resp['method']} - {resp['key']}):")
                print(f"    内容长度: {resp['result']['content_length']} 字节")
                print(f"    内容预览: {resp['result']['content_preview']}")
                
                # 尝试解析JSON
                if 'json_data' in resp['result']:
                    print(f"    JSON数据: {resp['result']['json_data']}")
        else:
            print("❌ 没有找到成功的响应")
    
    def save_results(self, all_results: Dict[str, Any]) -> str:
        """保存测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"api_test_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n💾 测试结果已保存: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
            return ""
    
    def comprehensive_test(self) -> Dict[str, Any]:
        """综合测试"""
        print("🚀 开始综合API测试")
        print("=" * 80)
        
        all_results = {}
        
        # 方法1: 基本连接测试
        basic_success = self.test_basic_connection()
        all_results['basic_connection'] = {'success': basic_success}
        
        # 方法2: 不同端点测试
        endpoint_results = self.test_with_different_endpoints()
        all_results['endpoints'] = endpoint_results
        
        # 方法3: 参数测试
        param_results = self.test_with_parameters()
        all_results['parameters'] = param_results
        
        # 方法4: HTTP方法测试
        method_results = self.test_with_different_methods()
        all_results['http_methods'] = method_results
        
        # 方法5: 请求头测试
        header_results = self.test_with_headers_variation()
        all_results['headers'] = header_results
        
        # 分析结果
        self.analyze_successful_responses(all_results)
        
        # 保存结果
        result_file = self.save_results(all_results)
        
        return all_results

def main():
    """主函数"""
    print("🎯 高级六合彩数据抓取测试")
    print("🌐 目标API: https://yyswz.uhfasuf.com:14949/api/")
    print("=" * 80)
    
    # 创建抓取器
    scraper = AdvancedLotteryScraper()
    
    # 执行综合测试
    results = scraper.comprehensive_test()
    
    print("\n🎉 测试完成!")
    print("\n📋 总结:")
    print("  1. 如果找到成功的响应，说明API可用")
    print("  2. 记录成功的参数组合和请求方式")
    print("  3. 可以基于成功的方法开发正式的数据抓取功能")
    print("  4. 建议查看生成的JSON文件了解详细结果")
    
    # 给出下一步建议
    successful_count = 0
    for method_results in results.values():
        if isinstance(method_results, dict):
            for result in method_results.values():
                if isinstance(result, dict) and result.get('status_code') == 200:
                    successful_count += 1
    
    if successful_count > 0:
        print(f"\n✅ 发现 {successful_count} 个可能的成功响应")
        print("💡 建议: 基于成功的方法开发数据抓取功能")
    else:
        print("\n❌ 未发现成功的API响应")
        print("💡 建议: ")
        print("  1. 检查API地址是否正确")
        print("  2. 确认是否需要认证或特殊权限")
        print("  3. 尝试其他数据源")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
