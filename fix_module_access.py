"""
修复完美预测系统中模组属性访问问题
"""

# 读取现有文件
with open("src/perfect_prediction_system.py", "r", encoding="utf-8") as f:
    content = f.read()

print("🔧 修复模组属性访问问题")
print("=" * 40)

# 查找initialize_modules方法并修复属性名
fixes = [
    # 修复传统分析模组属性名
    ("self.traditional_module = MockTraditionalModule()", "self.traditional_analysis = MockTraditionalModule()"),
    ("self.traditional_module = TraditionalStatisticalAnalysis(", "self.traditional_analysis = TraditionalStatisticalAnalysis("),
    
    # 修复机器学习模组属性名
    ("self.ml_module = MockMLModule()", "self.machine_learning = MockMLModule()"),
    ("self.ml_module = MachineLearningPredictor(", "self.machine_learning = MachineLearningPredictor("),
    
    # 修复生肖扩展模组属性名
    ("self.zodiac_extended_module = MockZodiacExtendedModule()", "self.zodiac_extended = MockZodiacExtendedModule()"),
]

# 应用修复
modified = False
for old_code, new_code in fixes:
    if old_code in content:
        content = content.replace(old_code, new_code)
        modified = True
        print(f"✅ 修复: {old_code[:30]}... → {new_code[:30]}...")

if modified:
    # 写回文件
    with open("src/perfect_prediction_system.py", "w", encoding="utf-8") as f:
        f.write(content)
    print("📁 文件已更新: src/perfect_prediction_system.py")
else:
    print("ℹ️ 未发现需要修复的属性名")

# 验证修复
print("\n🔍 验证修复结果...")

try:
    import sys
    sys.path.append('src')
    
    # 重新导入模块
    import importlib
    if 'perfect_prediction_system' in sys.modules:
        importlib.reload(sys.modules['perfect_prediction_system'])
    
    from perfect_prediction_system import PerfectPredictionSystem
    
    # 创建系统并初始化
    system = PerfectPredictionSystem()
    system.initialize_modules()
    
    print("✅ 系统初始化成功")
    
    # 检查模组属性
    modules_to_check = [
        ('traditional_analysis', '传统分析模组'),
        ('machine_learning', '机器学习模组'),
        ('zodiac_extended', '生肖扩展模组'),
        ('special_zodiac_module', '特码生肖模组')
    ]
    
    for attr_name, display_name in modules_to_check:
        if hasattr(system, attr_name):
            module = getattr(system, attr_name)
            if module is not None:
                print(f"✅ {display_name}: {type(module).__name__}")
                
                # 测试预测功能
                if hasattr(module, 'predict'):
                    try:
                        result = module.predict()
                        if isinstance(result, dict) and 'predicted_numbers' in result:
                            print(f"   �� 预测测试: {len(result['predicted_numbers'])} 个号码")
                        else:
                            print(f"   📊 预测测试: 成功")
                    except Exception as e:
                        print(f"   ❌ 预测测试失败: {e}")
                elif hasattr(module, 'predict_zodiacs'):
                    try:
                        result = module.predict_zodiacs([1, 2, 3, 4])
                        print(f"   📊 生肖预测测试: 成功")
                    except Exception as e:
                        print(f"   ❌ 生肖预测测试失败: {e}")
            else:
                print(f"❌ {display_name}: None")
        else:
            print(f"❌ {display_name}: 属性不存在")
    
    # 测试完整预测
    print(f"\n🎯 测试完整预测功能:")
    try:
        result = system.run_complete_prediction("2025-06-23")
        print(f"✅ 完整预测成功")
        
        final_results = result.get('final_results', {})
        if final_results:
            numbers = final_results.get('recommended_16_numbers', [])
            zodiacs = final_results.get('recommended_4_zodiacs', [])
            confidence = final_results.get('overall_confidence', 0)
            
            print(f"   📊 推荐号码: {len(numbers)} 个")
            print(f"   🐲 推荐生肖: {len(zodiacs)} 个")
            print(f"   📈 整体置信度: {confidence}")
        
    except Exception as e:
        print(f"❌ 完整预测失败: {e}")
        import traceback
        traceback.print_exc()
    
except Exception as e:
    print(f"❌ 验证失败: {e}")
    import traceback
    traceback.print_exc()
