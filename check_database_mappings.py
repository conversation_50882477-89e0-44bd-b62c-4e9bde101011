"""
检查数据库中的所有映射数据
"""

import sqlite3
import json
from pathlib import Path

def check_database_structure():
    """检查数据库结构和数据"""
    print("📊 数据库结构和映射数据检查")
    print("=" * 60)
    
    db_path = "data/lottery.db"
    if not Path(db_path).exists():
        print("❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📋 数据库包含 {len(tables)} 个表:")
        for table in tables:
            print(f"  - {table[0]}")
        print()
        
        # 检查每个表的详细信息
        for table in tables:
            table_name = table[0]
            print(f"🔍 表: {table_name}")
            print("-" * 40)
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print("  列信息:")
            for col in columns:
                print(f"    {col[1]} ({col[2]})")
            
            # 获取数据量
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"  数据量: {count} 条")
            
            # 显示数据示例
            if count > 0:
                if count <= 20:  # 如果数据少，显示全部
                    cursor.execute(f"SELECT * FROM {table_name}")
                    all_data = cursor.fetchall()
                    print("  所有数据:")
                    for i, row in enumerate(all_data, 1):
                        print(f"    {i}: {row}")
                else:  # 否则显示前几条
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 5")
                    sample_data = cursor.fetchall()
                    print("  数据示例:")
                    for i, row in enumerate(sample_data, 1):
                        print(f"    {i}: {row}")
            print()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")

def check_mapping_files():
    """检查映射文件"""
    print("📁 映射文件检查")
    print("=" * 60)
    
    # 可能的映射文件位置
    mapping_files = [
        "data/zodiac_mapping.json",
        "data/wuxing_mapping.json", 
        "data/lunar_year_mapping.json",
        "data/number_zodiac_mapping.json",
        "src/zodiac_mapping.py",
        "src/wuxing_mapping.py",
        "zodiac_mapping.json",
        "wuxing_mapping.json"
    ]
    
    for file_path in mapping_files:
        if Path(file_path).exists():
            print(f"✅ 找到映射文件: {file_path}")
            try:
                if file_path.endswith('.json'):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        print(f"  内容预览: {list(data.keys())[:5]}...")
                elif file_path.endswith('.py'):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()[:200]
                        print(f"  内容预览: {content}...")
            except Exception as e:
                print(f"  ❌ 读取失败: {e}")
        else:
            print(f"❌ 未找到: {file_path}")
    print()

def check_zodiac_data_in_code():
    """检查代码中的生肖数据"""
    print("🔍 代码中的生肖数据检查")
    print("=" * 60)
    
    # 检查主要模块文件
    module_files = [
        "src/independent_modules/zodiac_extended_module.py",
        "src/independent_modules/special_zodiac_module.py",
        "lottery_prediction_gui.py",
        "src/enhanced_feature_engineering_v2.py"
    ]
    
    for file_path in module_files:
        if Path(file_path).exists():
            print(f"📄 检查文件: {file_path}")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    # 查找五行相关数据
                    if "五行" in content or "金木水火土" in content:
                        print("  ✅ 包含五行数据")
                    
                    # 查找生肖相关数据
                    if "生肖" in content or "鼠牛虎兔" in content:
                        print("  ✅ 包含生肖数据")
                    
                    # 查找年份映射
                    if "2023" in content and "2024" in content and "2025" in content:
                        print("  ✅ 包含2023-2025年份数据")
                    
                    # 查找多维度属性
                    if "日肖" in content or "夜肖" in content:
                        print("  ✅ 包含日夜肖数据")
                    
                    if "阴肖" in content or "阳肖" in content:
                        print("  ✅ 包含阴阳肖数据")
                    
                    if "波色" in content or "红肖" in content:
                        print("  ✅ 包含波色数据")
                    
                    if "季节" in content or "春夏秋冬" in content:
                        print("  ✅ 包含季节数据")
                        
            except Exception as e:
                print(f"  ❌ 读取失败: {e}")
        else:
            print(f"❌ 文件不存在: {file_path}")
        print()

def check_specific_mappings():
    """检查特定的映射数据"""
    print("🎯 特定映射数据检查")
    print("=" * 60)
    
    # 检查是否有您提供的数据格式
    target_data = {
        "五行2025": {
            "金": [3, 4, 11, 12, 25, 26, 33, 34, 41, 42],
            "木": [7, 8, 15, 16, 23, 24, 37, 38, 45, 46],
            "水": [13, 14, 21, 22, 29, 30, 43, 44],
            "火": [1, 2, 9, 10, 17, 18, 31, 32, 39, 40, 47, 48],
            "土": [5, 6, 19, 20, 27, 28, 35, 36, 49]
        },
        "生肖2025": {
            "鼠": [6, 18, 30, 42], "牛": [5, 17, 29, 41], "虎": [4, 16, 28, 40],
            "兔": [3, 15, 27, 39], "龙": [2, 14, 26, 38], "蛇": [1, 13, 25, 37, 49],
            "马": [12, 24, 36, 48], "羊": [11, 23, 35, 47], "猴": [10, 22, 34, 46],
            "鸡": [9, 21, 33, 45], "狗": [8, 20, 32, 44], "猪": [7, 19, 31, 43]
        }
    }
    
    print("🔍 检查是否包含您提供的2025年数据格式...")
    
    # 在数据库中查找
    try:
        conn = sqlite3.connect("data/lottery.db")
        cursor = conn.cursor()
        
        # 检查是否有包含这些数字的记录
        for element, numbers in target_data["五行2025"].items():
            for num in numbers[:3]:  # 检查前3个数字
                cursor.execute("SELECT COUNT(*) FROM lottery_data WHERE special_number = ?", (num,))
                count = cursor.fetchone()[0]
                if count > 0:
                    print(f"  ✅ 数据库中有号码 {num} 的记录")
                    break
        
        conn.close()
        
    except Exception as e:
        print(f"  ❌ 数据库查询失败: {e}")
    
    print("\n📋 您提供的数据格式:")
    print("  - 2023-2025年五行映射")
    print("  - 2023-2025年生肖映射") 
    print("  - 多维度生肖属性 (日夜、阴阳、家野、天地等)")
    print("  - 波色分类 (红绿蓝)")
    print("  - 季节分类 (春夏秋冬)")
    print("  - 琴棋书画分类")
    print("  - 大小单双规则")
    print("  - 合数规则")
    print("  - 正反码规则")
    print("  - 7段分类")

def main():
    """主函数"""
    print("🎯 六合彩数据库和映射数据全面检查")
    print("=" * 80)
    print(f"检查时间: {Path.cwd()}")
    print()
    
    # 执行各项检查
    check_database_structure()
    check_mapping_files()
    check_zodiac_data_in_code()
    check_specific_mappings()
    
    print("\n📋 检查总结:")
    print("=" * 60)
    print("✅ 已完成数据库结构检查")
    print("✅ 已完成映射文件检查")
    print("✅ 已完成代码中数据检查")
    print("✅ 已完成特定数据格式检查")
    
    print("\n💡 建议:")
    print("  1. 如果现有数据不完整，需要添加您提供的2023-2025年数据")
    print("  2. 如果数据格式不匹配，需要更新映射结构")
    print("  3. 如果缺少多维度属性，需要扩展生肖分析模块")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
