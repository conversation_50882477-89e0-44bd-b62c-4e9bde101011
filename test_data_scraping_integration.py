"""
测试数据抓取功能集成
验证web_scraper模块和GUI界面的集成效果
"""

import sys
from pathlib import Path
import sqlite3
from datetime import datetime

# 添加路径
sys.path.insert(0, str(Path.cwd()))
sys.path.insert(0, str(Path.cwd() / "src"))

def test_web_scraper_module():
    """测试web_scraper模块"""
    print("🧪 测试web_scraper模块")
    print("=" * 60)
    
    try:
        from src.data_layer.data_import.web_scraper import WebScraper
        
        # 创建抓取器
        scraper = WebScraper()
        print("✅ WebScraper模块导入成功")
        
        # 测试kjdata连接
        print("\n🔗 测试kjdata API连接...")
        scrape_result = scraper.scrape_kjdata_api()
        
        if scrape_result.get('success'):
            print(f"✅ kjdata API连接成功")
            print(f"  总记录数: {scrape_result.get('total_records', 0):,}")
            print(f"  处理记录数: {scrape_result.get('processed_records', 0):,}")
            
            # 显示数据样例
            data = scrape_result.get('data', [])
            if data and len(data) > 0:
                sample = data[0]
                print(f"  数据样例: {sample.get('draw_date')} - {sample.get('raw_numbers')}")
            
            return scrape_result
        else:
            print(f"❌ kjdata API连接失败: {scrape_result.get('error')}")
            return None
            
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None

def test_database_integration():
    """测试数据库集成"""
    print("\n🗄️ 测试数据库集成")
    print("-" * 40)
    
    try:
        from src.data_layer.data_import.web_scraper import WebScraper
        
        scraper = WebScraper()
        
        # 先获取数据
        scrape_result = scraper.scrape_kjdata_api()
        
        if not scrape_result.get('success'):
            print("❌ 无法获取数据，跳过数据库测试")
            return False
        
        # 保存到数据库
        print("💾 保存数据到数据库...")
        processed_data = scrape_result['data']
        save_result = scraper.save_kjdata_to_database(processed_data)
        
        if save_result.get('success'):
            print(f"✅ 数据库保存成功")
            print(f"  保存记录数: {save_result.get('saved_count', 0):,}")
            
            # 验证数据库内容
            conn = sqlite3.connect("data/lottery_kjdata.db")
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM lottery_results_kjdata")
            total_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT MIN(draw_date), MAX(draw_date) FROM lottery_results_kjdata")
            date_range = cursor.fetchone()
            
            conn.close()
            
            print(f"  数据库验证:")
            print(f"    总记录数: {total_count:,}")
            print(f"    日期范围: {date_range[0]} 到 {date_range[1]}")
            
            return True
        else:
            print(f"❌ 数据库保存失败: {save_result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 数据库集成测试失败: {e}")
        return False

def test_comprehensive_scraping():
    """测试综合抓取功能"""
    print("\n🚀 测试综合抓取功能")
    print("-" * 40)
    
    try:
        from src.data_layer.data_import.web_scraper import WebScraper
        
        scraper = WebScraper()
        
        # 执行综合抓取
        print("🔄 执行综合抓取...")
        result = scraper.comprehensive_scrape_and_save(save_to_db=True)
        
        if result.get('success'):
            print(f"✅ 综合抓取成功")
            print(f"  总记录数: {result.get('total_records', 0):,}")
            print(f"  处理记录数: {result.get('processed_records', 0):,}")
            print(f"  保存记录数: {result.get('saved_records', 0):,}")
            
            # 显示统计信息
            stats = result.get('statistics', {})
            if stats:
                print(f"  统计信息:")
                print(f"    成功率: {stats.get('success_rate', 0):.1f}%")
                
                date_range = stats.get('date_range', {})
                if date_range:
                    print(f"    日期范围: {date_range.get('earliest')} 到 {date_range.get('latest')}")
                
                year_dist = stats.get('year_distribution', {})
                if year_dist:
                    print(f"    年份分布:")
                    for year, count in sorted(year_dist.items()):
                        print(f"      {year}: {count} 条")
            
            return True
        else:
            print(f"❌ 综合抓取失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 综合抓取测试失败: {e}")
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🖥️ 测试GUI集成")
    print("-" * 40)
    
    try:
        # 检查GUI文件中是否包含数据抓取相关代码
        gui_file = Path("lottery_prediction_gui.py")
        
        if not gui_file.exists():
            print("❌ GUI文件不存在")
            return False
        
        gui_content = gui_file.read_text(encoding='utf-8')
        
        # 检查关键功能
        checks = {
            "数据抓取组件": "数据抓取" in gui_content,
            "kjdata API": "kjdata" in gui_content,
            "抓取按钮": "start_data_scraping" in gui_content,
            "连接测试": "test_scrape_connection" in gui_content,
            "进度显示": "scrape_progress" in gui_content
        }
        
        print("📋 GUI集成检查:")
        all_passed = True
        for check_name, passed in checks.items():
            status = "✅" if passed else "❌"
            print(f"  {status} {check_name}")
            if not passed:
                all_passed = False
        
        if all_passed:
            print("✅ GUI集成检查通过")
            return True
        else:
            print("⚠️ 部分GUI功能可能缺失")
            return False
            
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        return False

def test_data_quality():
    """测试数据质量"""
    print("\n📊 测试数据质量")
    print("-" * 40)
    
    try:
        # 检查数据库是否存在
        db_path = "data/lottery_kjdata.db"
        if not Path(db_path).exists():
            print("❌ 数据库文件不存在，请先运行数据抓取")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 基本统计
        cursor.execute("SELECT COUNT(*) FROM lottery_results_kjdata")
        total_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM lottery_results_kjdata WHERE parse_status = 'success'")
        success_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT MIN(draw_date), MAX(draw_date) FROM lottery_results_kjdata")
        date_range = cursor.fetchone()
        
        # 年份分布
        cursor.execute("""
            SELECT year, COUNT(*) 
            FROM lottery_results_kjdata 
            WHERE parse_status = 'success'
            GROUP BY year 
            ORDER BY year
        """)
        year_stats = cursor.fetchall()
        
        # 数据完整性检查
        cursor.execute("""
            SELECT COUNT(*) FROM lottery_results_kjdata 
            WHERE parse_status = 'success' 
            AND special_number IS NOT NULL 
            AND regular_1 IS NOT NULL
        """)
        complete_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"📈 数据质量报告:")
        print(f"  总记录数: {total_count:,}")
        print(f"  成功解析: {success_count:,}")
        print(f"  解析成功率: {success_count/total_count*100:.1f}%")
        print(f"  数据完整性: {complete_count/success_count*100:.1f}%")
        print(f"  日期范围: {date_range[0]} 到 {date_range[1]}")
        
        print(f"  年份分布:")
        for year, count in year_stats:
            print(f"    {year}: {count:,} 条")
        
        # 质量评估
        if success_count / total_count >= 0.95 and complete_count / success_count >= 0.95:
            print("✅ 数据质量优秀")
            return True
        elif success_count / total_count >= 0.8:
            print("🔶 数据质量良好")
            return True
        else:
            print("❌ 数据质量需要改进")
            return False
            
    except Exception as e:
        print(f"❌ 数据质量测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 数据抓取功能集成测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项测试
    results = {}
    
    print("\n" + "="*80)
    results['web_scraper'] = test_web_scraper_module()
    
    print("\n" + "="*80)
    results['database'] = test_database_integration()
    
    print("\n" + "="*80)
    results['comprehensive'] = test_comprehensive_scraping()
    
    print("\n" + "="*80)
    results['gui'] = test_gui_integration()
    
    print("\n" + "="*80)
    results['data_quality'] = test_data_quality()
    
    # 总结
    print("\n" + "="*80)
    print("🎉 测试总结")
    print("-" * 40)
    
    passed_tests = 0
    total_tests = 0
    
    test_names = {
        'web_scraper': 'Web抓取模块',
        'database': '数据库集成',
        'comprehensive': '综合抓取功能',
        'gui': 'GUI界面集成',
        'data_quality': '数据质量'
    }
    
    for test_key, test_name in test_names.items():
        total_tests += 1
        if results.get(test_key):
            passed_tests += 1
            print(f"✅ {test_name}: 通过")
        else:
            print(f"❌ {test_name}: 失败")
    
    print(f"\n📊 测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎊 所有测试通过！数据抓取功能集成成功！")
        print("\n💡 下一步建议:")
        print("  1. 启动GUI程序测试数据抓取界面")
        print("  2. 使用抓取的数据重新训练预测模型")
        print("  3. 运行历史回测验证效果提升")
    elif passed_tests >= total_tests * 0.8:
        print("🔶 大部分测试通过，功能基本可用")
        print("💡 建议检查失败的测试项目")
    else:
        print("❌ 多项测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
