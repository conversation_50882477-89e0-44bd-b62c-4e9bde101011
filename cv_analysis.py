import numpy as np
from sklearn.model_selection import cross_val_score
from sklearn.ensemble import RandomForestClassifier
from collections import Counter

# 模拟当前问题场景
print('📊 当前问题分析:')
np.random.seed(42)
X = np.random.randn(40, 10)  # 40个样本，10个特征
y = np.random.randint(1, 50, 40)  # 40个目标值（1-49）

class_counts = Counter(y)
print(f'数据集大小: {X.shape[0]} 个样本')
print(f'类别数量: {len(class_counts)}')
print(f'最小类别样本数: {min(class_counts.values())}')
print(f'最大类别样本数: {max(class_counts.values())}')

# 测试不同CV分割数
model = RandomForestClassifier(n_estimators=10, random_state=42)
for cv in [2, 3, 5]:
    try:
        scores = cross_val_score(model, X, y, cv=cv)
        print(f'✅ CV={cv}: 成功, 得分={scores.mean():.3f}')
    except Exception as e:
        print(f'❌ CV={cv}: 失败 - {str(e)[:50]}...')

print()
print('🔍 问题根因:')
print('1. 类别数量(29个)远大于CV分割数(5个)')
print('2. 许多类别只有1个样本，无法分割')
print('3. 需要动态调整CV策略')
