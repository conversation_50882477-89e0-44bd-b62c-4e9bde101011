"""
六合彩预测工具 - 主程序入口
"""
import sys
import os
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from PyQt6.QtWidgets import QApplication
from src.application_layer.gui.main_window import MainWindow
from src.config.config import LOGGING_CONFIG
from loguru import logger

def setup_logging():
    """设置日志"""
    logger.remove()  # 移除默认处理器
    logger.add(
        sys.stderr,
        level=LOGGING_CONFIG["level"],
        format=LOGGING_CONFIG["format"]
    )
    logger.add(
        "logs/lottery_prediction.log",
        level=LOGGING_CONFIG["level"],
        format=LOGGING_CONFIG["format"],
        rotation=LOGGING_CONFIG["rotation"],
        retention=LOGGING_CONFIG["retention"],
        encoding="utf-8"
    )

def main():
    """主函数"""
    # 设置日志
    setup_logging()
    logger.info("六合彩预测工具启动")
    
    # 创建应用
    app = QApplication(sys.argv)
    app.setApplicationName("六合彩预测工具")
    app.setApplicationVersion("1.0.0")
    
    # 创建主窗口
    main_window = MainWindow()
    main_window.show()
    
    # 运行应用
    try:
        sys.exit(app.exec())
    except Exception as e:
        logger.error(f"应用运行错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
