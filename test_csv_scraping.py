"""
测试CSV格式数据抓取功能
"""

import sys
from pathlib import Path
import pandas as pd
from datetime import datetime

# 添加路径
sys.path.insert(0, str(Path.cwd()))
sys.path.insert(0, str(Path.cwd() / "src"))

def test_csv_scraping():
    """测试CSV格式抓取"""
    print("🧪 测试CSV格式数据抓取")
    print("=" * 60)
    
    try:
        from src.data_layer.data_import.web_scraper import WebScraper
        
        # 创建抓取器
        scraper = WebScraper()
        print("✅ WebScraper模块导入成功")
        
        # 测试CSV格式抓取
        print("\n🚀 开始CSV格式抓取...")
        result = scraper.comprehensive_scrape_and_save(save_format="csv", save_to_db=False)
        
        if result.get('success'):
            print("✅ CSV抓取成功")
            print(f"  总记录数: {result.get('total_records', 0):,}")
            print(f"  处理记录数: {result.get('processed_records', 0):,}")
            print(f"  保存记录数: {result.get('saved_count', 0):,}")
            
            # 检查文件
            filename = result.get('filename')
            if filename and Path(filename).exists():
                print(f"  CSV文件: {filename}")
                
                # 验证CSV内容
                df = pd.read_csv(filename, encoding='utf-8-sig')
                print(f"  文件大小: {Path(filename).stat().st_size:,} 字节")
                print(f"  CSV行数: {len(df):,}")
                print(f"  CSV列数: {len(df.columns)}")
                
                # 显示列名
                print(f"  列名: {list(df.columns)}")
                
                # 显示数据预览
                if len(df) > 0:
                    print(f"\n📊 数据预览:")
                    print(df.head().to_string())
                
                # 数据质量检查
                print(f"\n🔍 数据质量检查:")
                print(f"  完整记录: {len(df[df['解析状态'] == 'success'])}")
                print(f"  完整率: {len(df[df['解析状态'] == 'success'])/len(df)*100:.1f}%")
                
                # 年份分布
                if '年份' in df.columns:
                    year_counts = df['年份'].value_counts().sort_index()
                    print(f"  年份分布:")
                    for year, count in year_counts.items():
                        print(f"    {year}: {count} 条")
                
                return True
            else:
                print("❌ CSV文件未找到")
                return False
        else:
            print(f"❌ CSV抓取失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_multiple_formats():
    """测试多种保存格式"""
    print("\n🔄 测试多种保存格式")
    print("-" * 40)
    
    try:
        from src.data_layer.data_import.web_scraper import WebScraper
        
        scraper = WebScraper()
        
        # 测试CSV + 数据库格式
        print("📊 测试CSV + 数据库格式...")
        result = scraper.comprehensive_scrape_and_save(save_format="both", save_to_db=True)
        
        if result.get('success'):
            print("✅ 双格式保存成功")
            print(f"  CSV保存: {result.get('saved_count', 0):,} 条")
            print(f"  数据库保存: {result.get('db_saved_count', 0):,} 条")
            
            # 检查文件
            filename = result.get('filename')
            if filename and Path(filename).exists():
                print(f"  CSV文件: {filename}")
                print(f"  文件大小: {Path(filename).stat().st_size:,} 字节")
            
            # 检查数据库
            db_path = "data/lottery_kjdata.db"
            if Path(db_path).exists():
                print(f"  数据库文件: {db_path}")
                print(f"  数据库大小: {Path(db_path).stat().st_size:,} 字节")
            
            return True
        else:
            print(f"❌ 双格式保存失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 多格式测试失败: {e}")
        return False

def test_csv_data_quality():
    """测试CSV数据质量"""
    print("\n📈 测试CSV数据质量")
    print("-" * 40)
    
    try:
        # 查找最新的CSV文件
        data_dir = Path("data")
        csv_files = list(data_dir.glob("六合彩数据_kjdata_*.csv"))
        
        if not csv_files:
            print("❌ 未找到CSV文件")
            return False
        
        # 使用最新的文件
        latest_csv = max(csv_files, key=lambda x: x.stat().st_mtime)
        print(f"📁 分析文件: {latest_csv}")
        
        # 读取CSV
        df = pd.read_csv(latest_csv, encoding='utf-8-sig')
        
        print(f"📊 基本信息:")
        print(f"  总行数: {len(df):,}")
        print(f"  总列数: {len(df.columns)}")
        print(f"  文件大小: {latest_csv.stat().st_size:,} 字节")
        
        # 数据完整性
        complete_records = len(df[df['解析状态'] == 'success'])
        print(f"\n🔍 数据完整性:")
        print(f"  完整记录: {complete_records:,}")
        print(f"  完整率: {complete_records/len(df)*100:.1f}%")
        
        # 检查必要列
        required_cols = ['开奖日期', '期号', '正码1', '正码2', '正码3', '正码4', '正码5', '正码6', '特码']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"  ❌ 缺失列: {missing_cols}")
        else:
            print(f"  ✅ 所有必要列都存在")
        
        # 数据范围
        if '开奖日期' in df.columns:
            dates = df['开奖日期'].dropna()
            if len(dates) > 0:
                print(f"\n📅 数据范围:")
                print(f"  最早: {dates.min()}")
                print(f"  最新: {dates.max()}")
        
        # 号码范围检查
        number_cols = ['正码1', '正码2', '正码3', '正码4', '正码5', '正码6', '特码']
        valid_number_cols = [col for col in number_cols if col in df.columns]
        
        if valid_number_cols:
            print(f"\n🔢 号码范围检查:")
            for col in valid_number_cols:
                numbers = df[col].dropna()
                if len(numbers) > 0:
                    min_num = numbers.min()
                    max_num = numbers.max()
                    valid_range = (1 <= min_num <= 49) and (1 <= max_num <= 49)
                    status = "✅" if valid_range else "❌"
                    print(f"  {status} {col}: {min_num} - {max_num}")
        
        # 年份分布
        if '年份' in df.columns:
            year_counts = df['年份'].value_counts().sort_index()
            print(f"\n📊 年份分布:")
            for year, count in year_counts.items():
                print(f"  {year}: {count:,} 条")
        
        return True
        
    except Exception as e:
        print(f"❌ CSV数据质量测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 CSV格式数据抓取测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行测试
    results = {}
    
    print("\n" + "="*80)
    results['csv_scraping'] = test_csv_scraping()
    
    print("\n" + "="*80)
    results['multiple_formats'] = test_multiple_formats()
    
    print("\n" + "="*80)
    results['data_quality'] = test_csv_data_quality()
    
    # 总结
    print("\n" + "="*80)
    print("🎉 测试总结")
    print("-" * 40)
    
    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)
    
    test_names = {
        'csv_scraping': 'CSV格式抓取',
        'multiple_formats': '多格式保存',
        'data_quality': 'CSV数据质量'
    }
    
    for test_key, test_name in test_names.items():
        if results.get(test_key):
            print(f"✅ {test_name}: 通过")
        else:
            print(f"❌ {test_name}: 失败")
    
    print(f"\n📊 测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎊 所有测试通过！CSV格式抓取功能正常！")
        print("\n💡 使用建议:")
        print("  1. CSV格式便于Excel等工具打开和分析")
        print("  2. 文件保存在data/目录下，便于管理")
        print("  3. 可以选择CSV+数据库双格式保存")
        print("  4. 抓取完成后会自动打开文件位置")
    elif passed_tests >= total_tests * 0.8:
        print("🔶 大部分测试通过，功能基本可用")
    else:
        print("❌ 多项测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
