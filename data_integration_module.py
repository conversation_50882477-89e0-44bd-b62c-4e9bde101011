"""
数据集成模块
将抓取的kjdata数据集成到现有的六合彩预测系统中
"""

import sqlite3
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional
import shutil
import os

class DataIntegrationModule:
    """数据集成模块"""
    
    def __init__(self, 
                 source_db: str = "data/lottery_kjdata.db",
                 target_db: str = "data/lottery.db"):
        self.source_db = source_db
        self.target_db = target_db
    
    def analyze_existing_data(self) -> Dict[str, Any]:
        """分析现有数据"""
        print("📊 分析现有数据...")
        
        try:
            conn = sqlite3.connect(self.target_db)
            cursor = conn.cursor()
            
            # 检查现有数据
            cursor.execute("SELECT COUNT(*) FROM lottery_results")
            existing_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT MIN(draw_date), MAX(draw_date) FROM lottery_results")
            date_range = cursor.fetchone()
            
            conn.close()
            
            print(f"  现有记录: {existing_count} 条")
            print(f"  日期范围: {date_range[0]} 到 {date_range[1]}")
            
            return {
                'existing_count': existing_count,
                'date_range': date_range
            }
            
        except Exception as e:
            print(f"❌ 分析现有数据失败: {e}")
            return {}
    
    def analyze_new_data(self) -> Dict[str, Any]:
        """分析新数据"""
        print("📊 分析新抓取数据...")
        
        try:
            conn = sqlite3.connect(self.source_db)
            cursor = conn.cursor()
            
            # 检查新数据
            cursor.execute("SELECT COUNT(*) FROM lottery_results_kjdata")
            new_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT MIN(draw_date), MAX(draw_date) FROM lottery_results_kjdata")
            date_range = cursor.fetchone()
            
            # 按年份统计
            cursor.execute("""
                SELECT year, COUNT(*) 
                FROM lottery_results_kjdata 
                GROUP BY year 
                ORDER BY year
            """)
            year_stats = cursor.fetchall()
            
            conn.close()
            
            print(f"  新记录: {new_count} 条")
            print(f"  日期范围: {date_range[0]} 到 {date_range[1]}")
            print(f"  年份分布:")
            for year, count in year_stats:
                print(f"    {year}: {count} 条")
            
            return {
                'new_count': new_count,
                'date_range': date_range,
                'year_stats': year_stats
            }
            
        except Exception as e:
            print(f"❌ 分析新数据失败: {e}")
            return {}
    
    def backup_existing_data(self) -> bool:
        """备份现有数据"""
        print("💾 备份现有数据...")
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"data/lottery_backup_{timestamp}.db"
            
            shutil.copy2(self.target_db, backup_file)
            
            print(f"✅ 数据备份成功: {backup_file}")
            return True
            
        except Exception as e:
            print(f"❌ 数据备份失败: {e}")
            return False
    
    def merge_data(self, strategy: str = "replace_all") -> bool:
        """合并数据"""
        print(f"🔄 合并数据 (策略: {strategy})...")
        
        try:
            # 连接两个数据库
            source_conn = sqlite3.connect(self.source_db)
            target_conn = sqlite3.connect(self.target_db)
            
            source_cursor = source_conn.cursor()
            target_cursor = target_conn.cursor()
            
            if strategy == "replace_all":
                # 策略1: 完全替换
                print("  执行完全替换策略...")
                
                # 清空现有数据
                target_cursor.execute("DELETE FROM lottery_results")
                
                # 获取新数据
                source_cursor.execute("""
                    SELECT year, period, draw_date, 
                           regular_1, regular_2, regular_3, regular_4, regular_5, regular_6,
                           special_number
                    FROM lottery_results_kjdata
                    WHERE parse_status = 'success'
                    ORDER BY draw_date
                """)
                
                new_records = source_cursor.fetchall()
                
                # 插入新数据
                for record in new_records:
                    target_cursor.execute("""
                        INSERT INTO lottery_results 
                        (draw_date, period_number, regular_1, regular_2, regular_3, 
                         regular_4, regular_5, regular_6, special_number)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        record[2],  # draw_date
                        record[1],  # period
                        record[3],  # regular_1
                        record[4],  # regular_2
                        record[5],  # regular_3
                        record[6],  # regular_4
                        record[7],  # regular_5
                        record[8],  # regular_6
                        record[9]   # special_number
                    ))
                
                print(f"  插入记录: {len(new_records)} 条")
                
            elif strategy == "append_new":
                # 策略2: 仅添加新记录
                print("  执行增量添加策略...")
                
                # 获取现有最新日期
                target_cursor.execute("SELECT MAX(draw_date) FROM lottery_results")
                latest_date = target_cursor.fetchone()[0]
                
                if latest_date:
                    # 获取新于最新日期的记录
                    source_cursor.execute("""
                        SELECT year, period, draw_date, 
                               regular_1, regular_2, regular_3, regular_4, regular_5, regular_6,
                               special_number
                        FROM lottery_results_kjdata
                        WHERE parse_status = 'success' AND draw_date > ?
                        ORDER BY draw_date
                    """, (latest_date,))
                else:
                    # 如果没有现有数据，添加所有数据
                    source_cursor.execute("""
                        SELECT year, period, draw_date, 
                               regular_1, regular_2, regular_3, regular_4, regular_5, regular_6,
                               special_number
                        FROM lottery_results_kjdata
                        WHERE parse_status = 'success'
                        ORDER BY draw_date
                    """)
                
                new_records = source_cursor.fetchall()
                
                # 插入新记录
                for record in new_records:
                    target_cursor.execute("""
                        INSERT OR IGNORE INTO lottery_results 
                        (draw_date, period_number, regular_1, regular_2, regular_3, 
                         regular_4, regular_5, regular_6, special_number)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        record[2],  # draw_date
                        record[1],  # period
                        record[3],  # regular_1
                        record[4],  # regular_2
                        record[5],  # regular_3
                        record[6],  # regular_4
                        record[7],  # regular_5
                        record[8],  # regular_6
                        record[9]   # special_number
                    ))
                
                print(f"  新增记录: {len(new_records)} 条")
            
            # 提交更改
            target_conn.commit()
            
            # 关闭连接
            source_conn.close()
            target_conn.close()
            
            print("✅ 数据合并成功")
            return True
            
        except Exception as e:
            print(f"❌ 数据合并失败: {e}")
            return False
    
    def verify_integration(self) -> Dict[str, Any]:
        """验证集成结果"""
        print("🔍 验证集成结果...")
        
        try:
            conn = sqlite3.connect(self.target_db)
            cursor = conn.cursor()
            
            # 统计总记录数
            cursor.execute("SELECT COUNT(*) FROM lottery_results")
            total_count = cursor.fetchone()[0]
            
            # 统计日期范围
            cursor.execute("SELECT MIN(draw_date), MAX(draw_date) FROM lottery_results")
            date_range = cursor.fetchone()
            
            # 检查数据完整性
            cursor.execute("""
                SELECT COUNT(*) FROM lottery_results 
                WHERE special_number IS NULL OR special_number = 0
            """)
            incomplete_count = cursor.fetchone()[0]
            
            # 按年份统计
            cursor.execute("""
                SELECT strftime('%Y', draw_date) as year, COUNT(*) 
                FROM lottery_results 
                GROUP BY year 
                ORDER BY year
            """)
            year_stats = cursor.fetchall()
            
            conn.close()
            
            print(f"✅ 验证完成")
            print(f"  总记录数: {total_count}")
            print(f"  日期范围: {date_range[0]} 到 {date_range[1]}")
            print(f"  不完整记录: {incomplete_count}")
            print(f"  年份分布:")
            for year, count in year_stats:
                print(f"    {year}: {count} 条")
            
            return {
                'total_count': total_count,
                'date_range': date_range,
                'incomplete_count': incomplete_count,
                'year_stats': year_stats,
                'data_quality': 'excellent' if incomplete_count == 0 else 'good'
            }
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return {}
    
    def update_unified_mapping(self) -> bool:
        """更新统一年份映射"""
        print("🔄 更新统一年份映射...")
        
        try:
            conn = sqlite3.connect(self.target_db)
            cursor = conn.cursor()
            
            # 重新计算所有记录的生肖和五行
            cursor.execute("""
                SELECT id, draw_date, special_number 
                FROM lottery_results 
                WHERE special_number IS NOT NULL
            """)
            
            records = cursor.fetchall()
            
            # 这里可以调用统一年份映射模块重新计算
            # 暂时跳过，因为需要导入统一映射模块
            
            print(f"  需要更新的记录: {len(records)} 条")
            print("  (统一映射更新已预留，需要后续实现)")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ 更新统一映射失败: {e}")
            return False
    
    def comprehensive_integration(self, strategy: str = "replace_all") -> Dict[str, Any]:
        """综合数据集成"""
        print("🚀 开始综合数据集成")
        print("=" * 60)
        
        results = {}
        
        # 1. 分析现有数据
        existing_analysis = self.analyze_existing_data()
        results['existing_analysis'] = existing_analysis
        
        # 2. 分析新数据
        new_analysis = self.analyze_new_data()
        results['new_analysis'] = new_analysis
        
        if not new_analysis:
            print("❌ 新数据分析失败，终止集成")
            return results
        
        # 3. 备份现有数据
        backup_success = self.backup_existing_data()
        results['backup_success'] = backup_success
        
        if not backup_success:
            print("⚠️ 备份失败，但继续集成")
        
        # 4. 合并数据
        merge_success = self.merge_data(strategy)
        results['merge_success'] = merge_success
        
        if not merge_success:
            print("❌ 数据合并失败")
            return results
        
        # 5. 验证集成结果
        verification = self.verify_integration()
        results['verification'] = verification
        
        # 6. 更新统一映射
        mapping_success = self.update_unified_mapping()
        results['mapping_success'] = mapping_success
        
        return results

def main():
    """主函数"""
    print("🎯 数据集成模块")
    print("🔗 将kjdata数据集成到六合彩预测系统")
    print("=" * 80)
    
    # 检查源数据文件是否存在
    if not os.path.exists("data/lottery_kjdata.db"):
        print("❌ 源数据文件不存在，请先运行数据抓取")
        return
    
    # 创建集成模块
    integrator = DataIntegrationModule()
    
    # 询问集成策略
    print("\n📋 选择集成策略:")
    print("  1. replace_all - 完全替换现有数据")
    print("  2. append_new - 仅添加新数据")
    
    choice = input("请选择策略 (1/2, 默认1): ").strip()
    strategy = "append_new" if choice == "2" else "replace_all"
    
    print(f"\n🎯 使用策略: {strategy}")
    
    # 执行集成
    results = integrator.comprehensive_integration(strategy)
    
    print("\n🎉 数据集成完成!")
    
    if results.get('verification'):
        verification = results['verification']
        print(f"\n📊 集成结果:")
        print(f"  总记录数: {verification.get('total_count', 0):,}")
        print(f"  数据质量: {verification.get('data_quality', 'unknown')}")
        print(f"  日期范围: {verification.get('date_range', ['N/A', 'N/A'])[0]} 到 {verification.get('date_range', ['N/A', 'N/A'])[1]}")
        
        if verification.get('data_quality') == 'excellent':
            print("\n✅ 数据集成成功！现在可以使用更丰富的历史数据进行预测了！")
            print("💡 建议:")
            print("  1. 重新训练机器学习模型")
            print("  2. 运行历史回测验证效果")
            print("  3. 测试完美预测系统")
        else:
            print("\n⚠️ 数据质量需要进一步检查")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
