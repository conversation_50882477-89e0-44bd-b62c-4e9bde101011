{"test_results": {"data_management": {"total_records": 905, "latest_records_count": 5, "recent_records": 174, "hot_numbers": [[40, 28], [25, 28], [10, 28], [43, 26], [35, 25], [24, 25], [47, 23], [41, 22], [31, 22], [23, 22]]}, "prediction_modules": {"special_number": {"success": true, "numbers_count": 16, "confidence": 0.5, "duration": 0.1159980297088623}, "consistency": {"success": true, "numbers_count": 16, "confidence": 0.7050000000000001, "duration": 0.002999544143676758}, "perfect_prediction": {"success": false, "error": "'PerfectPredictionSystem' object has no attribute 'db_path'"}}, "analysis_modules": {"machine_learning": {"success": true, "numbers_count": 16, "confidence": 0.95, "version": "v2.0", "xgboost_enabled": true, "models_count": 5, "duration": 11.143097400665283}, "zodiac_extended": {"success": true, "numbers_count": 16, "confidence": 0.7146892655367232, "duration": 0.004000186920166016}, "special_zodiac": {"success": true, "numbers_count": 12, "confidence": 0.934438499807309, "duration": 0.002002716064453125}}, "fusion_optimization": {"dynamic_fusion": {"success": true, "numbers_count": 16, "confidence": 0.7666666666666666, "stability_score": 0.7875, "method": "stability_fusion", "duration": 0.0009987354278564453}, "stability_optimizer": {"success": true, "numbers_count": 13, "stability_score": 0.875, "final_stability_score": 0.7449928205128206, "method": "multi_strategy_fusion", "duration": 0.0}, "enhanced_features": {"success": true, "feature_count": 20, "version": "basic", "data_samples": 100, "duration": 0.004998445510864258}}, "cross_module": {"cross_module_interaction": {"success": true, "pipeline_steps": 3, "modules_involved": 2, "final_numbers": 12, "final_stability": 0.875, "total_duration": 10.810657262802124}}}, "interaction_log": [{"timestamp": "2025-06-24T08:49:39.056496", "module": "data_management", "action": "database_queries", "success": true, "duration": 0.01476740837097168, "result_summary": "{'total_records': 905, 'latest_records_count': 5, 'recent_records': 174, 'hot_numbers': [(40, 28), ("}, {"timestamp": "2025-06-24T08:49:39.172494", "module": "special_number_predictor", "action": "predict", "success": true, "duration": 0.1159980297088623, "result_summary": "{'predicted_numbers': [2, 6, 15, 22, 23, 25, 28, 33, 34, 36, 38, 40, 43, 44, 45, 47], 'confidence': "}, {"timestamp": "2025-06-24T08:49:39.175494", "module": "consistency_predictor", "action": "predict_with_consistency", "success": true, "duration": 0.002999544143676758, "result_summary": "{'recommended_numbers': [2, 2, 4, 5, 6, 6, 8, 9, 10, 12, 15, 23, 28, 39, 44, 48], 'recommended_zodia"}, {"timestamp": "2025-06-24T08:49:51.501048", "module": "ml_module", "action": "predict", "success": true, "duration": 11.143097400665283, "result_summary": "{'predicted_numbers': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], 'confidence': 0.95, '"}, {"timestamp": "2025-06-24T08:49:51.505048", "module": "zodiac_extended_module", "action": "predict", "success": true, "duration": 0.004000186920166016, "result_summary": "{'predicted_numbers': [1, 2, 5, 11, 13, 14, 17, 23, 25, 26, 29, 35, 37, 38, 41, 47], 'confidence': 0"}, {"timestamp": "2025-06-24T08:49:51.507051", "module": "special_zodiac_module", "action": "predict", "success": true, "duration": 0.002002716064453125, "result_summary": "{'predicted_numbers': [3, 4, 11, 15, 16, 23, 27, 28, 35, 39, 40, 47], 'confidence': 0.93443849980730"}, {"timestamp": "2025-06-24T08:49:51.508049", "module": "dynamic_fusion_manager", "action": "fuse_predictions", "success": true, "duration": 0.0009987354278564453, "result_summary": "{'numbers': [2, 3, 7, 14, 19, 1, 25, 5, 31, 12, 18, 36, 9, 23, 16, 41], 'confidence': 0.766666666666"}, {"timestamp": "2025-06-24T08:49:51.508049", "module": "stability_optimizer", "action": "optimize_stability", "success": true, "duration": 0.0, "result_summary": "{'numbers': [1, 3, 17, 23, 27, 29, 18, 5, 2, 11, 35, 41, 47], 'confidence': 0.75, 'stability_score':"}, {"timestamp": "2025-06-24T08:49:51.513048", "module": "enhanced_feature_engineering", "action": "generate_enhanced_features", "success": true, "duration": 0.004998445510864258, "result_summary": "{'features': {'mean': 21.75, 'std': 13.899190623917638, 'min': 1, 'max': 47, 'basic_feature_0': 0.95"}, {"timestamp": "2025-06-24T08:50:02.323705", "module": "cross_module", "action": "full_pipeline", "success": true, "duration": 10.810657262802124, "result_summary": "{'numbers': [1, 3, 17, 23, 27, 29, 2, 5, 11, 35, 41, 47], 'confidence': 0.8323446327683616, 'stabili"}], "test_time": "2025-06-24T08:50:02.328705"}