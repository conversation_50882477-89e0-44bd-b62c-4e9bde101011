"""
完整EXE打包构建器 - 按照最佳实践设计
遵循用户建议的完整打包流程：
1. 项目结构整理（最关键）
2. 所有资源必须以相对路径访问
3. 创建 .spec 文件
4. 调试技巧（功能不一致排查）
5. 打包后用 CMD 启动 .exe 文件观察报错
6. 使用 print() 写日志到文件调试
7. 打包成功验证 checklist
"""

import os
import sys
import json
import shutil
import subprocess
from pathlib import Path
from datetime import datetime
import logging

class CompleteEXEPackageBuilder:
    """完整EXE打包构建器"""
    
    def __init__(self):
        self.version = "2.0.0"
        self.build_date = datetime.now().strftime("%Y%m%d")
        self.project_name = "LotteryPredictionSystem"
        self.base_dir = Path.cwd()
        self.build_dir = self.base_dir / "build_exe"
        self.dist_dir = self.base_dir / "dist_exe"
        self.spec_file = self.base_dir / f"{self.project_name}.spec"
        
        # 设置日志
        self.setup_logging()
        
        # 项目结构映射
        self.project_structure = {
            "main_files": [
                "lottery_prediction_gui.py",
                "main.py"
            ],
            "source_dirs": [
                "src",
                "config"
            ],
            "data_dirs": [
                "data"
            ],
            "resource_files": [
                "requirements.txt",
                "optimal_config.json",
                "optimization_config.json",
                "icon.ico"
            ],
            "exclude_patterns": [
                "__pycache__",
                "*.pyc",
                "*.pyo", 
                ".git",
                ".vscode",
                ".idea",
                "*.log",
                "dist*",
                "build*",
                ".venv",
                "venv",
                "env",
                "releases",
                "tests",
                "docs",
                "*.md",
                "*.txt"
            ]
        }
        
    def setup_logging(self):
        """设置日志系统"""
        log_file = self.base_dir / "exe_build.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def print_and_log(self, message, level="INFO"):
        """同时打印和记录日志"""
        print(message)
        if level == "INFO":
            self.logger.info(message)
        elif level == "ERROR":
            self.logger.error(message)
        elif level == "WARNING":
            self.logger.warning(message)
            
    def step1_prepare_project_structure(self):
        """步骤1: 项目结构整理（最关键）"""
        self.print_and_log("=" * 60)
        self.print_and_log("Step 1: Preparing project structure (most critical)")
        self.print_and_log("=" * 60)
        
        # 1.1 检查主要文件
        self.print_and_log("1.1 Checking main files...")
        missing_files = []
        for file_name in self.project_structure["main_files"]:
            file_path = self.base_dir / file_name
            if file_path.exists():
                self.print_and_log(f"OK: {file_name}: Exists ({file_path.stat().st_size} bytes)")
            else:
                missing_files.append(file_name)
                self.print_and_log(f"ERROR: {file_name}: Does not exist", "ERROR")
        
        if missing_files:
            self.print_and_log(f"Missing critical files: {missing_files}", "ERROR")
            return False
            
        # 1.2 Checking source directories...
        self.print_and_log("\n1.2 Checking source directories...")
        for dir_name in self.project_structure["source_dirs"]:
            dir_path = self.base_dir / dir_name
            if dir_path.exists():
                file_count = len(list(dir_path.rglob("*.py")))
                self.print_and_log(f"OK: {dir_name}/: Exists ({file_count} Python files)")
            else:
                self.print_and_log(f"ERROR: {dir_name}/: Does not exist", "WARNING")
                
        # 1.3 Checking data directories...
        self.print_and_log("\n1.3 Checking data directories...")
        for dir_name in self.project_structure["data_dirs"]:
            dir_path = self.base_dir / dir_name
            if dir_path.exists():
                file_count = len(list(dir_path.rglob("*")))
                self.print_and_log(f"OK: {dir_name}/: Exists ({file_count} files)")
            else:
                self.print_and_log(f"ERROR: {dir_name}/: Does not exist", "WARNING")
                # Create empty data directory
                dir_path.mkdir(exist_ok=True)
                self.print_and_log(f"OK: Created {dir_name}/ directory")
                
        return True
        
    def step2_normalize_resource_paths(self):
        """Step 2: All resources must be accessed with relative paths"""
        self.print_and_log("\n" + "=" * 60)
        self.print_and_log("Step 2: Normalizing resource paths")
        self.print_and_log("=" * 60)
        
        # 2.1 Analyzing path references in code...
        self.print_and_log("2.1 Analyzing path references in code...")
        path_issues = self._analyze_path_references()
        
        if path_issues:
            self.print_and_log("Path issues found:")
            for issue in path_issues:
                self.print_and_log(f"  WARNING: {issue}", "WARNING")
        else:
            self.print_and_log("OK: Path reference check passed")
            
        # 2.2 Creating resource path handler...
        self._create_resource_path_handler()
        
        return True
        
    def _analyze_path_references(self):
        """Analyze path references in the code"""
        issues = []
        
        # Check for path references in main Python files
        python_files = list(self.base_dir.rglob("*.py"))
        
        problematic_patterns = [
            r'[""][A-Za-z]:[\\\/]',  # Absolute path C:\
            r'[""]\/[^\/]',          # Unix absolute path /
            r'\.\.\/\.\.\/',          # Excessive relative paths
        ]
        
        import re
        
        for py_file in python_files[:10]:  # Limit number of files to check
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for pattern in problematic_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        issues.append(f"{py_file.name}: Possible absolute path reference")
                        break
                        
            except Exception as e:
                continue
                
        return issues
        
    def _create_resource_path_handler(self):
        """Create the resource path handler function"""
        resource_handler_code = '''"""
Resource Path Handler - for handling paths after EXE packaging
"""
import os
import sys
from pathlib import Path

def get_resource_path(relative_path):
    """Get the absolute path to a resource file, compatible with dev and packaged environments"""
    try:
        # PyInstaller temporary folder
        base_path = sys._MEIPASS
    except AttributeError:
        # Development environment
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def get_data_path(relative_path):
    """Get the path to a data file, prioritizing the user directory"""
    # Create an application data directory in the user's home
    import os
    user_data_dir = os.path.expanduser("~/LotteryPredictionSystem")
    os.makedirs(user_data_dir, exist_ok=True)
    
    return os.path.join(user_data_dir, relative_path)

def ensure_data_directory():
    """Ensure the data directory exists"""
    data_dir = get_data_path("data")
    os.makedirs(data_dir, exist_ok=True)
    return data_dir
'''
        
        handler_file = self.base_dir / "src" / "resource_path_handler.py"
        with open(handler_file, 'w', encoding='utf-8') as f:
            f.write(resource_handler_code)
            
        self.print_and_log(f"OK: Created resource path handler: {handler_file}")

    def step3_create_spec_file(self):
        """Step 3: Create the complete .spec configuration file"""
        self.print_and_log("\n" + "=" * 60)
        self.print_and_log("Step 3: Creating the complete .spec file")
        self.print_and_log("=" * 60)

        # 3.1 Collecting all files to be included...
        self.print_and_log("3.1 Collecting files to include...")

        # Collect data files
        datas = []

        # Add source directories
        for src_dir in self.project_structure["source_dirs"]:
            if (self.base_dir / src_dir).exists():
                datas.append(f"('{src_dir}', '{src_dir}')")
                self.print_and_log(f"  Adding directory: {src_dir}")

        # Add data directories
        for data_dir in self.project_structure["data_dirs"]:
            if (self.base_dir / data_dir).exists():
                datas.append(f"('{data_dir}', '{data_dir}')")
                self.print_and_log(f"  Adding data directory: {data_dir}")

        # Add resource files
        for resource_file in self.project_structure["resource_files"]:
            if (self.base_dir / resource_file).exists():
                datas.append(f"('{resource_file}', '.')")
                self.print_and_log(f"  Adding resource file: {resource_file}")

        # 3.2 收集隐藏导入
        hiddenimports = [
            'PyQt5.QtCore',
            'PyQt5.QtGui',
            'PyQt5.QtWidgets',
            'numpy',
            'pandas',
            'sklearn',
            'sklearn.ensemble',
            'sklearn.svm',
            'sklearn.neighbors',
            'sklearn.naive_bayes',
            'sklearn.model_selection',
            'sklearn.preprocessing',
            'sklearn.metrics',
            'matplotlib',
            'seaborn',
            'sqlite3',
            'json',
            'datetime',
            'pathlib',
            'collections',
            'hashlib',
            'random',
            'warnings',
            'logging',
            'src.perfect_prediction_system',
            'src.dynamic_fusion_manager_v3',
            'src.algorithm_layer.ml_models.ml_predictor',
            'src.independent_modules.traditional_module',
            'src.independent_modules.ml_module',
            'src.independent_modules.zodiac_extended_module',
            'src.independent_modules.special_zodiac_module'
        ]

        # 3.3 生成.spec文件内容
        spec_content = self._generate_spec_content(datas, hiddenimports)

        # 3.4 写入.spec文件
        with open(self.spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)

        self.print_and_log(f"OK: Created .spec file: {self.spec_file}")
        return True

    def _generate_spec_content(self, datas, hiddenimports):
        """生成.spec文件内容"""
        datas_str = ",\n    ".join(datas)
        hiddenimports_str = ",\n    ".join([f"'{imp}'" for imp in hiddenimports])

        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
# {self.project_name} PyInstaller配置文件
# 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

import sys
import os

block_cipher = None

a = Analysis(
    ['lottery_prediction_gui.py'],
    pathex=[os.getcwd()],
    binaries=[],
    datas=[
        {datas_str}
    ],
    hiddenimports=[
        {hiddenimports_str}
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'unittest',
        'test',
        'distutils',
        'setuptools',
        'pip',
        'wheel'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤掉不需要的文件
a.datas = [x for x in a.datas if not any(pattern in x[0] for pattern in [
    '__pycache__',
    '.pyc',
    '.pyo',
    '.git',
    'test_',
    '.md',
    '.txt'
])]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.project_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 设置为True以便调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None
)
'''
        return spec_content

    def step4_build_exe(self):
        """Step 4: Build the EXE file"""
        self.print_and_log("\n" + "=" * 60)
        self.print_and_log("Step 4: Building the EXE file")
        self.print_and_log("=" * 60)

        # 4.1 Cleaning previous builds...
        self.print_and_log("4.1 Cleaning previous builds...")
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            self.print_and_log("OK: Cleaned build directory")

        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
            self.print_and_log("OK: Cleaned dist directory")

        # 4.2 Executing PyInstaller build...
        self.print_and_log("4.2 Executing PyInstaller build...")

        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            '--distpath', str(self.dist_dir),
            '--workpath', str(self.build_dir),
            str(self.spec_file)
        ]

        self.print_and_log(f"执行命令: {' '.join(cmd)}")

        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',  # 忽略编码错误
                cwd=str(self.base_dir)
            )

            # 记录构建输出
            build_log_file = self.base_dir / "pyinstaller_build.log"
            with open(build_log_file, 'w', encoding='utf-8') as f:
                f.write("=== PyInstaller 构建日志 ===\n")
                f.write(f"命令: {' '.join(cmd)}\n")
                f.write(f"返回码: {result.returncode}\n\n")
                f.write("=== 标准输出 ===\n")
                f.write(result.stdout or "无输出")
                f.write("\n=== 标准错误 ===\n")
                f.write(result.stderr or "无错误")

            if result.returncode == 0:
                self.print_and_log("OK: PyInstaller build successful")
                return True
            else:
                self.print_and_log(f"ERROR: PyInstaller build failed (return code: {result.returncode})", "ERROR")
                self.print_and_log(f"Error message: {result.stderr}", "ERROR")
                return False

        except Exception as e:
            self.print_and_log(f"ERROR: Build process exception: {e}", "ERROR")
            return False

    def step5_debug_and_test(self):
        """步骤5: 调试和测试"""
        self.print_and_log("\n" + "=" * 60)
        self.print_and_log("步骤5: 调试和测试")
        self.print_and_log("=" * 60)

        # 5.1 检查生成的EXE文件
        exe_file = self.dist_dir / f"{self.project_name}.exe"
        if not exe_file.exists():
            self.print_and_log("❌ EXE文件未生成", "ERROR")
            return False

        exe_size = exe_file.stat().st_size / (1024 * 1024)  # MB
        self.print_and_log(f"✅ EXE文件生成成功: {exe_file} ({exe_size:.1f} MB)")

        # 5.2 创建调试启动脚本
        self._create_debug_scripts(exe_file)

        # 5.3 执行基本测试
        self.print_and_log("5.3 执行基本测试...")
        test_result = self._run_basic_test(exe_file)

        if test_result:
            self.print_and_log("✅ 基本测试通过")
        else:
            self.print_and_log("⚠️ 基本测试未完全通过，请查看日志", "WARNING")

        return True

    def _create_debug_scripts(self, exe_file):
        """创建调试启动脚本"""
        # Windows批处理脚本
        bat_content = f'''@echo off
echo 启动 {self.project_name} 调试模式...
echo 如果出现错误，请查看下方输出信息
echo =====================================

cd /d "{exe_file.parent}"
"{exe_file.name}" > debug_output.log 2>&1

echo.
echo 程序已退出，输出已保存到 debug_output.log
echo 按任意键查看日志内容...
pause > nul

type debug_output.log
echo.
echo 按任意键退出...
pause > nul
'''

        bat_file = exe_file.parent / "debug_start.bat"
        with open(bat_file, 'w', encoding='gbk') as f:
            f.write(bat_content)

        self.print_and_log(f"✅ 创建调试脚本: {bat_file}")

        # PowerShell脚本
        ps1_content = f'''# {self.project_name} 调试启动脚本
Write-Host "启动 {self.project_name} 调试模式..." -ForegroundColor Green
Write-Host "如果出现错误，请查看输出信息" -ForegroundColor Yellow
Write-Host "=====================================" -ForegroundColor Cyan

Set-Location "{exe_file.parent}"

try {{
    & ".\\{exe_file.name}" *>&1 | Tee-Object -FilePath "debug_output.log"
}} catch {{
    Write-Host "程序启动异常: $_" -ForegroundColor Red
}}

Write-Host ""
Write-Host "程序已退出，输出已保存到 debug_output.log" -ForegroundColor Green
Read-Host "按回车键查看日志内容"

Get-Content "debug_output.log"
Read-Host "按回车键退出"
'''

        ps1_file = exe_file.parent / "debug_start.ps1"
        with open(ps1_file, 'w', encoding='utf-8') as f:
            f.write(ps1_content)

        self.print_and_log(f"✅ 创建PowerShell调试脚本: {ps1_file}")

    def _run_basic_test(self, exe_file):
        """运行基本测试"""
        try:
            # 测试EXE文件是否能正常启动（快速退出测试）
            test_cmd = [str(exe_file), '--help']

            result = subprocess.run(
                test_cmd,
                capture_output=True,
                text=True,
                timeout=30,  # 30秒超时
                cwd=str(exe_file.parent)
            )

            # 记录测试结果
            test_log_file = exe_file.parent / "basic_test.log"
            with open(test_log_file, 'w', encoding='utf-8') as f:
                f.write("=== 基本测试日志 ===\n")
                f.write(f"测试命令: {' '.join(test_cmd)}\n")
                f.write(f"返回码: {result.returncode}\n\n")
                f.write("=== 标准输出 ===\n")
                f.write(result.stdout)
                f.write("\n=== 标准错误 ===\n")
                f.write(result.stderr)

            # 简单的成功判断
            if result.returncode == 0 or "help" in result.stdout.lower():
                return True
            else:
                return False

        except subprocess.TimeoutExpired:
            self.print_and_log("⚠️ 测试超时，可能是GUI程序正常启动", "WARNING")
            return True
        except Exception as e:
            self.print_and_log(f"⚠️ 测试异常: {e}", "WARNING")
            return False

    def step6_verification_checklist(self):
        """步骤6: 打包成功验证 checklist"""
        self.print_and_log("\n" + "=" * 60)
        self.print_and_log("步骤6: 打包成功验证 checklist")
        self.print_and_log("=" * 60)

        checklist_items = [
            ("所有资源是否用 add-data 包含？", self._check_resources_included),
            ("所有路径是否基于 _MEIPASS 处理？", self._check_meipass_handling),
            ("是否使用 print+log.txt 记录流程？", self._check_logging_setup),
            ("是否排除了 .pyc、.git、大文件？", self._check_exclusions),
            ("是否在打包后目录运行测试验证？", self._check_post_build_test)
        ]

        self.print_and_log("📋 验证清单:")
        all_passed = True

        for i, (description, check_func) in enumerate(checklist_items, 1):
            self.print_and_log(f"\n{i}. {description}")
            try:
                result = check_func()
                if result:
                    self.print_and_log("   ✅ 通过")
                else:
                    self.print_and_log("   ❌ 未通过", "WARNING")
                    all_passed = False
            except Exception as e:
                self.print_and_log(f"   ⚠️ 检查异常: {e}", "WARNING")
                all_passed = False

        self.print_and_log("\n" + "=" * 60)
        if all_passed:
            self.print_and_log("🎉 所有验证项目通过！EXE打包成功！")
        else:
            self.print_and_log("⚠️ 部分验证项目未通过，请检查相关问题", "WARNING")

        return all_passed

    def _check_resources_included(self):
        """检查资源是否包含"""
        spec_file_content = ""
        if self.spec_file.exists():
            with open(self.spec_file, 'r', encoding='utf-8') as f:
                spec_file_content = f.read()

        # 检查关键资源是否在datas中
        required_resources = ['src', 'config', 'data']
        for resource in required_resources:
            if f"'{resource}'" in spec_file_content:
                continue
            else:
                return False
        return True

    def _check_meipass_handling(self):
        """检查_MEIPASS处理"""
        handler_file = self.base_dir / "src" / "resource_path_handler.py"
        if handler_file.exists():
            with open(handler_file, 'r', encoding='utf-8') as f:
                content = f.read()
                return "_MEIPASS" in content
        return False

    def _check_logging_setup(self):
        """检查日志设置"""
        log_files = [
            self.base_dir / "exe_build.log",
            self.base_dir / "pyinstaller_build.log"
        ]
        return any(log_file.exists() for log_file in log_files)

    def _check_exclusions(self):
        """检查排除项"""
        if self.spec_file.exists():
            with open(self.spec_file, 'r', encoding='utf-8') as f:
                content = f.read()
                exclusions = ['__pycache__', '.pyc', '.git']
                return all(excl in content for excl in exclusions)
        return False

    def _check_post_build_test(self):
        """检查构建后测试"""
        exe_file = self.dist_dir / f"{self.project_name}.exe"
        test_log = exe_file.parent / "basic_test.log" if exe_file.exists() else None
        return test_log and test_log.exists()

    def create_final_package(self):
        """创建最终发布包"""
        self.print_and_log("\n" + "=" * 60)
        self.print_and_log("创建最终发布包")
        self.print_and_log("=" * 60)

        exe_file = self.dist_dir / f"{self.project_name}.exe"
        if not exe_file.exists():
            self.print_and_log("❌ EXE文件不存在，无法创建发布包", "ERROR")
            return False

        # 创建发布目录
        release_name = f"{self.project_name}_Complete_EXE_v{self.version}_{self.build_date}"
        release_dir = self.base_dir / "releases" / release_name
        release_dir.mkdir(parents=True, exist_ok=True)

        # 复制EXE文件
        shutil.copy2(exe_file, release_dir)

        # 复制调试脚本
        debug_files = [
            exe_file.parent / "debug_start.bat",
            exe_file.parent / "debug_start.ps1"
        ]
        for debug_file in debug_files:
            if debug_file.exists():
                shutil.copy2(debug_file, release_dir)

        # 创建使用说明
        readme_content = f'''# {self.project_name} EXE版本使用说明

## 版本信息
- 版本: v{self.version}
- 构建日期: {self.build_date}
- 构建时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 文件说明
- `{self.project_name}.exe`: 主程序文件
- `debug_start.bat`: Windows调试启动脚本
- `debug_start.ps1`: PowerShell调试启动脚本

## 使用方法
1. 直接双击 `{self.project_name}.exe` 启动程序
2. 如果遇到问题，使用调试脚本启动以查看详细错误信息

## 调试方法
1. 双击 `debug_start.bat` 或右键运行 `debug_start.ps1`
2. 查看生成的 `debug_output.log` 文件
3. 如果程序无法启动，请检查系统要求

## 系统要求
- Windows 10/11 (64位)
- 4GB 可用内存
- 500MB 可用磁盘空间

## 故障排除
1. 如果提示缺少DLL文件，请安装 Visual C++ Redistributable
2. 如果程序启动缓慢，这是正常现象（首次启动需要解压）
3. 如果遇到权限问题，请以管理员身份运行

## 技术支持
如果遇到问题，请提供以下信息：
- 操作系统版本
- 错误信息截图
- debug_output.log 文件内容
'''

        readme_file = release_dir / "README.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)

        # 创建ZIP包
        zip_file = release_dir.with_suffix('.zip')
        shutil.make_archive(str(release_dir), 'zip', str(release_dir))

        self.print_and_log(f"✅ 创建发布包: {zip_file}")
        self.print_and_log(f"📁 发布目录: {release_dir}")

        return True

    def run_complete_build_process(self):
        """运行完整的构建流程"""
        self.print_and_log("🚀 开始完整EXE打包流程")
        self.print_and_log(f"项目: {self.project_name}")
        self.print_and_log(f"版本: v{self.version}")
        self.print_and_log(f"构建日期: {self.build_date}")

        try:
            # 步骤1: 项目结构整理
            if not self.step1_prepare_project_structure():
                self.print_and_log("❌ 项目结构整理失败", "ERROR")
                return False

            # 步骤2: 资源路径规范化
            if not self.step2_normalize_resource_paths():
                self.print_and_log("❌ 资源路径规范化失败", "ERROR")
                return False

            # 步骤3: 创建.spec文件
            if not self.step3_create_spec_file():
                self.print_and_log("❌ .spec文件创建失败", "ERROR")
                return False

            # 步骤4: 构建EXE
            if not self.step4_build_exe():
                self.print_and_log("❌ EXE构建失败", "ERROR")
                return False

            # 步骤5: 调试和测试
            if not self.step5_debug_and_test():
                self.print_and_log("❌ 调试和测试失败", "ERROR")
                return False

            # 步骤6: 验证checklist
            verification_passed = self.step6_verification_checklist()

            # 创建最终发布包
            if not self.create_final_package():
                self.print_and_log("❌ 最终发布包创建失败", "ERROR")
                return False

            # 构建成功总结
            self.print_and_log("\n" + "🎉" * 20)
            self.print_and_log("🎉 EXE打包流程完成！")
            self.print_and_log("🎉" * 20)

            self.print_and_log("\n📊 构建总结:")
            self.print_and_log(f"  项目名称: {self.project_name}")
            self.print_and_log(f"  版本: v{self.version}")
            self.print_and_log(f"  构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            exe_file = self.dist_dir / f"{self.project_name}.exe"
            if exe_file.exists():
                exe_size = exe_file.stat().st_size / (1024 * 1024)
                self.print_and_log(f"  EXE大小: {exe_size:.1f} MB")
                self.print_and_log(f"  EXE位置: {exe_file}")

            self.print_and_log(f"  验证状态: {'✅ 全部通过' if verification_passed else '⚠️ 部分通过'}")

            # 下一步建议
            self.print_and_log("\n📝 下一步建议:")
            self.print_and_log("  1. 使用 debug_start.bat 测试EXE文件")
            self.print_and_log("  2. 在不同Windows系统上测试兼容性")
            self.print_and_log("  3. 检查所有功能是否正常工作")
            self.print_and_log("  4. 如有问题，查看 exe_build.log 和 debug_output.log")

            return True

        except Exception as e:
            self.print_and_log(f"❌ 构建过程发生异常: {e}", "ERROR")
            import traceback
            self.print_and_log(f"详细错误: {traceback.format_exc()}", "ERROR")
            return False


def main():
    """主函数"""
    print("=" * 80)
    print("🔧 完整EXE打包构建器")
    print("按照最佳实践进行EXE打包")
    print("=" * 80)

    # 检查PyInstaller是否安装
    try:
        import PyInstaller
        print(f"✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller 未安装，请先安装: pip install pyinstaller")
        return False

    # 创建构建器并运行
    builder = CompleteEXEPackageBuilder()
    success = builder.run_complete_build_process()

    if success:
        print("\n🎉 EXE打包成功完成！")
        print("请查看 releases/ 目录中的最终发布包")
    else:
        print("\n❌ EXE打包失败，请查看日志文件排查问题")

    return success


if __name__ == "__main__":
    main()
