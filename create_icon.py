"""
创建系统图标文件
"""

def create_simple_icon():
    """创建简单的ICO图标文件"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建64x64的图像
        size = 64
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 绘制背景圆形
        margin = 4
        draw.ellipse([margin, margin, size-margin, size-margin], 
                    fill=(52, 152, 219, 255), outline=(41, 128, 185, 255), width=2)
        
        # 绘制文字
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", 16)
        except:
            # 使用默认字体
            font = ImageFont.load_default()
        
        # 绘制"六"字
        text = "六"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2 - 2
        
        draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
        
        # 保存为ICO文件
        img.save('icon.ico', format='ICO', sizes=[(64, 64), (32, 32), (16, 16)])
        print("✅ 图标文件已创建: icon.ico")
        return True
        
    except ImportError:
        print("⚠️ PIL库未安装，跳过图标创建")
        return False
    except Exception as e:
        print(f"⚠️ 图标创建失败: {e}")
        return False

if __name__ == "__main__":
    create_simple_icon()
