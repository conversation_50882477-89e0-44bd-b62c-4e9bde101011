"""
网络爬虫获取数据 - 升级版
集成kjdata API抓取功能
"""
import requests
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Any
from loguru import logger
import time
import json
import urllib3
from datetime import datetime
import sqlite3
import pandas as pd

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class WebScraper:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self.session.verify = False

        # kjdata API配置
        self.kjdata_url = "https://yyswz.uhfasuf.com:14949/api/kjdata"
    
    def scrape_kjdata_api(self) -> Dict[str, Any]:
        """抓取kjdata API数据"""
        try:
            logger.info("开始抓取kjdata API数据...")

            response = self.session.get(self.kjdata_url, timeout=30)

            if response.ok:
                logger.info(f"API响应成功，数据大小: {len(response.text):,} 字节")

                data = response.json()
                if isinstance(data, dict) and 'data' in data:
                    records = data['data']
                    logger.info(f"获取到 {len(records):,} 条记录")

                    # 处理数据
                    processed_data = self._process_kjdata_records(records)

                    return {
                        'success': True,
                        'total_records': len(records),
                        'processed_records': len(processed_data),
                        'data': processed_data,
                        'raw_data': data
                    }
                else:
                    logger.error(f"数据格式异常: {type(data)}")
                    return {'success': False, 'error': 'Invalid data format'}
            else:
                logger.error(f"API请求失败: {response.status_code}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}

        except Exception as e:
            logger.error(f"kjdata抓取失败: {e}")
            return {'success': False, 'error': str(e)}

    def scrape_lottery_data(self, url: str, max_pages: int = 1) -> List[Dict]:
        """爬取彩票数据 - 兼容原有接口"""
        try:
            logger.info(f"开始爬取数据: {url}")

            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')
            data = self._parse_lottery_page(soup)

            logger.info(f"成功爬取 {len(data)} 条记录")
            return data

        except Exception as e:
            logger.error(f"爬取失败: {e}")
            return []
    
    def _process_kjdata_records(self, records: List[Dict]) -> List[Dict]:
        """处理kjdata记录"""
        processed_records = []

        for record in records:
            try:
                # 解析号码字符串
                num_str = record.get('num', '')
                numbers = []

                if num_str:
                    parts = num_str.replace(' ', '').split(',')
                    numbers = [int(part) for part in parts if part.isdigit()]

                # 构建标准格式
                processed = {
                    'id': record.get('id'),
                    'year': record.get('year'),
                    'period': record.get('qishu', ''),
                    'draw_date': record.get('date', ''),
                    'raw_numbers': num_str,
                    'all_numbers': numbers,
                    'regular_numbers': numbers[:6] if len(numbers) >= 6 else numbers,
                    'special_number': numbers[6] if len(numbers) > 6 else None,
                    'number_count': len(numbers),
                    'parse_status': 'success' if len(numbers) >= 6 else 'incomplete',
                    'shengxiao': record.get('shengxiao', ''),
                    'wuxing': record.get('wuxing', '')
                }

                processed_records.append(processed)

            except Exception as e:
                logger.error(f"处理记录失败: {e}, 记录: {record}")
                processed_records.append({
                    'id': record.get('id'),
                    'parse_status': 'error',
                    'error': str(e),
                    'raw_record': record
                })

        return processed_records

    def save_kjdata_to_database(self, processed_data: List[Dict], db_path: str = "data/lottery.db") -> Dict[str, Any]:
        """保存kjdata数据到数据库"""
        try:
            logger.info(f"保存数据到数据库: {db_path}")

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 创建临时表用于kjdata
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS lottery_results_kjdata (
                    id INTEGER PRIMARY KEY,
                    year INTEGER,
                    period TEXT,
                    draw_date TEXT,
                    raw_numbers TEXT,
                    regular_1 INTEGER,
                    regular_2 INTEGER,
                    regular_3 INTEGER,
                    regular_4 INTEGER,
                    regular_5 INTEGER,
                    regular_6 INTEGER,
                    special_number INTEGER,
                    number_count INTEGER,
                    parse_status TEXT,
                    shengxiao TEXT,
                    wuxing TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 插入数据
            success_count = 0

            for record in processed_data:
                if record.get('parse_status') == 'success':
                    regular_nums = record.get('regular_numbers', [])

                    cursor.execute('''
                        INSERT OR REPLACE INTO lottery_results_kjdata
                        (id, year, period, draw_date, raw_numbers,
                         regular_1, regular_2, regular_3, regular_4, regular_5, regular_6,
                         special_number, number_count, parse_status, shengxiao, wuxing)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        record.get('id'),
                        record.get('year'),
                        record.get('period'),
                        record.get('draw_date'),
                        record.get('raw_numbers'),
                        regular_nums[0] if len(regular_nums) > 0 else None,
                        regular_nums[1] if len(regular_nums) > 1 else None,
                        regular_nums[2] if len(regular_nums) > 2 else None,
                        regular_nums[3] if len(regular_nums) > 3 else None,
                        regular_nums[4] if len(regular_nums) > 4 else None,
                        regular_nums[5] if len(regular_nums) > 5 else None,
                        record.get('special_number'),
                        record.get('number_count'),
                        record.get('parse_status'),
                        record.get('shengxiao'),
                        record.get('wuxing')
                    ))
                    success_count += 1

            conn.commit()
            conn.close()

            logger.info(f"数据库保存成功: {success_count} 条记录")

            return {
                'success': True,
                'saved_count': success_count,
                'total_count': len(processed_data)
            }

        except Exception as e:
            logger.error(f"数据库保存失败: {e}")
            return {'success': False, 'error': str(e)}

    def _parse_lottery_page(self, soup) -> List[Dict]:
        """解析彩票页面 - 兼容原有方法"""
        # 这里需要根据具体网站结构来实现
        # 示例解析逻辑
        data = []

        # 查找表格或数据容器
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows[1:]:  # 跳过表头
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 4:
                    try:
                        record = {
                            'issue': cells[0].get_text().strip(),
                            'date': cells[1].get_text().strip(),
                            'numbers': cells[2].get_text().strip(),
                            'special': cells[3].get_text().strip()
                        }
                        data.append(record)
                    except Exception as e:
                        logger.warning(f"解析行失败: {e}")
                        continue
        
        return data
    
    def comprehensive_scrape_and_save(self, save_to_db: bool = True) -> Dict[str, Any]:
        """综合抓取并保存数据"""
        try:
            logger.info("开始综合数据抓取...")

            # 1. 抓取kjdata数据
            scrape_result = self.scrape_kjdata_api()

            if not scrape_result.get('success'):
                return {
                    'success': False,
                    'error': scrape_result.get('error', 'Unknown error'),
                    'stage': 'scraping'
                }

            processed_data = scrape_result['data']

            # 2. 保存到数据库
            if save_to_db:
                save_result = self.save_kjdata_to_database(processed_data)

                if not save_result.get('success'):
                    return {
                        'success': False,
                        'error': save_result.get('error', 'Database save failed'),
                        'stage': 'saving',
                        'scraped_data': scrape_result
                    }
            else:
                save_result = {'success': True, 'saved_count': 0}

            # 3. 生成统计信息
            stats = self._generate_scrape_stats(processed_data)

            result = {
                'success': True,
                'total_records': scrape_result['total_records'],
                'processed_records': scrape_result['processed_records'],
                'saved_records': save_result.get('saved_count', 0),
                'statistics': stats,
                'timestamp': datetime.now().isoformat()
            }

            logger.info(f"综合抓取完成: {result}")
            return result

        except Exception as e:
            logger.error(f"综合抓取失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'stage': 'comprehensive'
            }

    def _generate_scrape_stats(self, processed_data: List[Dict]) -> Dict[str, Any]:
        """生成抓取统计信息"""
        try:
            success_records = [r for r in processed_data if r.get('parse_status') == 'success']

            if not success_records:
                return {'error': 'No successful records'}

            # 年份分布
            years = {}
            for record in success_records:
                year = record.get('year')
                if year:
                    years[year] = years.get(year, 0) + 1

            # 日期范围
            dates = [r.get('draw_date') for r in success_records if r.get('draw_date')]
            date_range = {
                'earliest': min(dates) if dates else None,
                'latest': max(dates) if dates else None
            }

            return {
                'total_records': len(processed_data),
                'success_records': len(success_records),
                'success_rate': len(success_records) / len(processed_data) * 100,
                'year_distribution': years,
                'date_range': date_range
            }

        except Exception as e:
            logger.error(f"生成统计信息失败: {e}")
            return {'error': str(e)}

    def set_delay(self, delay: float):
        """设置请求延迟"""
        self.delay = delay

    def close(self):
        """关闭会话"""
        self.session.close()
