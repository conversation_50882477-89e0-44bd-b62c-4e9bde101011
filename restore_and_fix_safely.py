"""
安全地恢复和修复编码问题
"""

import shutil
from pathlib import Path

def restore_and_fix_safely():
    """安全地恢复和修复"""
    print("🔄 安全恢复和修复编码问题")
    print("=" * 50)
    
    source_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    gui_file = source_dir / "lottery_prediction_gui.py"
    backup_file = source_dir / "lottery_prediction_gui.py.backup"
    
    # 1. 恢复备份文件
    if backup_file.exists():
        print("📄 恢复原始文件...")
        shutil.copy2(backup_file, gui_file)
        print("✅ 文件已恢复")
    else:
        print("❌ 备份文件不存在")
        return False
    
    # 2. 只修复关键的编码问题
    try:
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 文件大小: {len(content)} 字符")
        
        # 只替换导致启动失败的关键emoji
        critical_replacements = {
            'ℹ️': 'INFO:',
            '⚠️': 'WARNING:',
            '✅': '[OK]',
            '❌': '[ERROR]'
        }
        
        replacement_count = 0
        for emoji, replacement in critical_replacements.items():
            if emoji in content:
                count = content.count(emoji)
                content = content.replace(emoji, replacement)
                replacement_count += count
                print(f"  替换 {emoji} -> {replacement}: {count} 次")
        
        print(f"📊 总替换次数: {replacement_count}")
        
        # 保存修复后的文件
        with open(gui_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 关键编码问题已修复")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def test_startup():
    """测试启动"""
    print("\n🧪 测试程序启动")
    print("-" * 30)
    
    source_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    
    try:
        import sys
        import os
        
        # 添加路径
        sys.path.insert(0, str(source_dir))
        
        # 切换工作目录
        original_dir = Path.cwd()
        os.chdir(source_dir)
        
        try:
            # 尝试导入主模块
            import lottery_prediction_gui
            print("✅ 主模块导入成功")
            
            # 检查主函数
            if hasattr(lottery_prediction_gui, 'main'):
                print("✅ main函数存在")
            else:
                print("❌ main函数不存在")
                return False
            
            return True
            
        except SyntaxError as e:
            print(f"❌ 语法错误: {e}")
            return False
        except Exception as e:
            print(f"❌ 导入错误: {e}")
            return False
        finally:
            os.chdir(original_dir)
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_safe_launcher():
    """创建安全的启动器"""
    print("\n📝 创建安全启动器")
    print("-" * 30)
    
    source_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    
    # 创建安全的启动脚本
    launcher_content = '''# -*- coding: utf-8 -*-
"""
六合彩预测系统安全启动器
"""

import sys
import os
from pathlib import Path

def safe_start():
    """安全启动"""
    try:
        # 设置编码
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8', errors='ignore')
            sys.stderr.reconfigure(encoding='utf-8', errors='ignore')
        
        # 设置环境变量
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # 导入主程序
        print("正在启动六合彩预测系统...")
        
        from lottery_prediction_gui import main
        main()
        
    except UnicodeEncodeError as e:
        print(f"编码错误: {e}")
        print("请确保系统支持UTF-8编码")
        input("按回车键退出...")
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    safe_start()
'''
    
    launcher_path = source_dir / "safe_launcher.py"
    with open(launcher_path, 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print(f"✅ 安全启动器已创建: {launcher_path}")
    
    # 创建批处理文件
    bat_content = '''@echo off
echo Starting Lottery Prediction System (Safe Mode)...
python safe_launcher.py
pause
'''
    
    bat_path = source_dir / "safe_start.bat"
    with open(bat_path, 'w', encoding='gbk') as f:
        f.write(bat_content)
    
    print(f"✅ 批处理文件已创建: {bat_path}")

def main():
    """主函数"""
    print("🎯 安全编码修复工具")
    print("=" * 70)
    
    # 1. 恢复和安全修复
    success = restore_and_fix_safely()
    
    if success:
        # 2. 测试启动
        print("\n🧪 测试修复效果...")
        test_success = test_startup()
        
        # 3. 创建安全启动器
        create_safe_launcher()
        
        if test_success:
            print("\n🎉 安全修复完成！")
            print("✅ 关键编码问题已修复")
            print("✅ 程序语法正确")
            print("✅ 可以正常导入")
            print("✅ 创建了安全启动器")
            
            print("\n📋 下一步:")
            print("  1. 使用 safe_start.bat 启动程序")
            print("  2. 测试所有功能")
            print("  3. 如果正常，重新打包EXE")
        else:
            print("\n⚠️ 修复后仍有问题")
            print("请检查语法错误")
    else:
        print("\n❌ 安全修复失败")
    
    return success

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
