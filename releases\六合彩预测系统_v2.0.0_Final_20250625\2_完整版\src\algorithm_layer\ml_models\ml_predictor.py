"""
机器学习预测模块
实现多种ML算法进行特码预测，输出16-24个初选号码
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union
from datetime import date, datetime, timedelta
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.model_selection import cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, classification_report
from loguru import logger
import joblib
import json

class MachineLearningPredictor:
    """机器学习预测器"""
    
    def __init__(self, db_session, number_attribute_mapper, lunar_year_manager):
        self.db = db_session
        self.attr_mapper = number_attribute_mapper
        self.lunar_manager = lunar_year_manager
        
        # 模型配置
        self.models = {
            'random_forest': {
                'model': RandomForestClassifier(n_estimators=100, max_depth=10, random_state=42),
                'weight': 0.25
            },
            'gradient_boosting': {
                'model': GradientBoostingClassifier(n_estimators=100, max_depth=6, random_state=42),
                'weight': 0.25
            },
            'svm': {
                'model': SVC(kernel='rbf', probability=True, random_state=42),
                'weight': 0.20
            },
            'knn': {
                'model': KNeighborsClassifier(n_neighbors=5),
                'weight': 0.15
            },
            'naive_bayes': {
                'model': GaussianNB(),
                'weight': 0.15
            }
        }
        
        # 特征工程配置
        self.feature_config = {
            'sequence_length': 10,  # 序列特征长度
            'statistical_window': 20,  # 统计特征窗口
            'use_zodiac_features': True,  # 是否使用生肖特征
            'use_traditional_features': True,  # 是否使用传统特征
            'use_time_features': True  # 是否使用时间特征
        }
        
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.trained_models = {}
        self.feature_names = []
    
    def predict_numbers(self, recent_periods: int = 100, target_date: date = None, 
                       min_numbers: int = 16, max_numbers: int = 24) -> Dict[str, Any]:
        """预测特码号码"""
        logger.info(f"开始机器学习预测，分析最近 {recent_periods} 期数据")
        
        if target_date is None:
            target_date = date.today()
        
        # 获取历史数据
        historical_data = self._get_historical_data(recent_periods + 50, target_date)
        
        if len(historical_data) < 50:
            logger.warning(f"历史数据不足，仅有 {len(historical_data)} 期")
            return self._get_default_prediction(min_numbers, max_numbers)
        
        # 特征工程
        features, targets = self._feature_engineering(historical_data[:recent_periods])
        
        if len(features) < 30:
            logger.warning(f"特征数据不足，仅有 {len(features)} 条")
            return self._get_default_prediction(min_numbers, max_numbers)
        
        # 训练模型
        self._train_models(features, targets)
        
        # 生成预测特征
        prediction_features = self._generate_prediction_features(historical_data, target_date)
        
        # 执行预测
        predictions = self._ensemble_predict(prediction_features)
        
        # 选择推荐号码
        recommended_numbers = self._select_numbers_by_probability(
            predictions, min_numbers, max_numbers
        )
        
        # 计算置信度
        confidence = self._calculate_prediction_confidence(predictions, recommended_numbers)
        
        # 生成预测结果
        result = {
            "predicted_numbers": recommended_numbers,
            "number_probabilities": predictions,
            "confidence_level": confidence,
            "model_performance": self._get_model_performance(),
            "feature_importance": self._get_feature_importance(),
            "prediction_time": datetime.now(),
            "training_data_size": len(features),
            "models_used": list(self.models.keys())
        }
        
        logger.info(f"机器学习预测完成，推荐 {len(recommended_numbers)} 个号码")
        return result
    
    def _get_historical_data(self, recent_periods: int, target_date: date) -> List[Dict]:
        """获取历史数据"""
        from src.data_layer.database.models import LotteryData
        
        query = self.db.query(LotteryData).filter(
            LotteryData.draw_date < target_date
        ).order_by(LotteryData.draw_date.desc()).limit(recent_periods)
        
        records = query.all()
        
        historical_data = []
        for record in records:
            # 解析正码
            try:
                regular_numbers = json.loads(record.regular_numbers) if record.regular_numbers else []
            except:
                regular_numbers = []
            
            historical_data.append({
                'period_number': record.period_number,
                'draw_date': record.draw_date,
                'special_number': record.special_number,
                'regular_numbers': regular_numbers,
                'lunar_year': record.lunar_year,
                'special_zodiac': record.special_zodiac,
                'special_wuxing': record.special_wuxing,
                'special_size': record.special_size,
                'special_parity': record.special_parity,
                'special_prime': record.special_prime,
                'special_tail': record.special_tail,
                'special_wave_color': record.special_wave_color,
                'special_segment': record.special_segment,
                # 扩展生肖属性
                'zodiac_color_wave': record.zodiac_color_wave,
                'zodiac_season': record.zodiac_season,
                'zodiac_day_night': record.zodiac_day_night,
                'zodiac_left_right': record.zodiac_left_right,
                'zodiac_yin_yang': record.zodiac_yin_yang
            })
        
        return historical_data
    
    def _feature_engineering(self, historical_data: List[Dict]) -> Tuple[np.ndarray, np.ndarray]:
        """特征工程"""
        features = []
        targets = []
        
        for i in range(self.feature_config['sequence_length'], len(historical_data)):
            # 获取当前期和历史序列
            current_record = historical_data[i]
            history_sequence = historical_data[i-self.feature_config['sequence_length']:i]
            
            # 生成特征向量
            feature_vector = self._generate_feature_vector(current_record, history_sequence)
            
            if feature_vector is not None:
                features.append(feature_vector)
                targets.append(current_record['special_number'])
        
        if not features:
            return np.array([]), np.array([])
        
        features_array = np.array(features)
        targets_array = np.array(targets)
        
        # 标准化特征
        features_scaled = self.scaler.fit_transform(features_array)
        
        logger.info(f"特征工程完成，生成 {len(features)} 条训练样本，{features_array.shape[1]} 个特征")
        return features_scaled, targets_array
    
    def _generate_feature_vector(self, current_record: Dict, history_sequence: List[Dict]) -> Optional[np.ndarray]:
        """生成单个样本的特征向量"""
        features = []
        
        try:
            # 1. 序列特征
            sequence_features = self._extract_sequence_features(history_sequence)
            features.extend(sequence_features)
            
            # 2. 统计特征
            statistical_features = self._extract_statistical_features(history_sequence)
            features.extend(statistical_features)
            
            # 3. 生肖特征
            if self.feature_config['use_zodiac_features']:
                zodiac_features = self._extract_zodiac_features(history_sequence)
                features.extend(zodiac_features)
            
            # 4. 传统特征
            if self.feature_config['use_traditional_features']:
                traditional_features = self._extract_traditional_features(history_sequence)
                features.extend(traditional_features)
            
            # 5. 时间特征
            if self.feature_config['use_time_features']:
                time_features = self._extract_time_features(current_record)
                features.extend(time_features)
            
            return np.array(features, dtype=float)
            
        except Exception as e:
            logger.error(f"特征生成失败: {e}")
            return None
    
    def _extract_sequence_features(self, history_sequence: List[Dict]) -> List[float]:
        """提取序列特征"""
        features = []
        
        # 最近N期的特码
        recent_numbers = [record['special_number'] for record in history_sequence]
        
        # 序列统计
        features.append(np.mean(recent_numbers))  # 平均值
        features.append(np.std(recent_numbers))   # 标准差
        features.append(np.min(recent_numbers))   # 最小值
        features.append(np.max(recent_numbers))   # 最大值
        features.append(np.median(recent_numbers)) # 中位数
        
        # 趋势特征
        if len(recent_numbers) >= 3:
            # 线性趋势
            x = np.arange(len(recent_numbers))
            trend = np.polyfit(x, recent_numbers, 1)[0]
            features.append(trend)
        else:
            features.append(0)
        
        # 差分特征
        if len(recent_numbers) >= 2:
            diffs = np.diff(recent_numbers)
            features.append(np.mean(diffs))  # 平均差分
            features.append(np.std(diffs))   # 差分标准差
        else:
            features.extend([0, 0])
        
        # 周期性特征
        features.append(len(set(recent_numbers)))  # 唯一值数量
        
        return features
    
    def _extract_statistical_features(self, history_sequence: List[Dict]) -> List[float]:
        """提取统计特征"""
        features = []
        
        recent_numbers = [record['special_number'] for record in history_sequence]
        
        # 频率统计
        number_counts = {}
        for num in recent_numbers:
            number_counts[num] = number_counts.get(num, 0) + 1
        
        # 最高频率
        max_freq = max(number_counts.values()) if number_counts else 0
        features.append(max_freq / len(recent_numbers) if recent_numbers else 0)
        
        # 大小单双统计
        size_counts = {'大': 0, '小': 0}
        parity_counts = {'单': 0, '双': 0}
        
        for record in history_sequence:
            if record.get('special_size'):
                size_counts[record['special_size']] += 1
            if record.get('special_parity'):
                parity_counts[record['special_parity']] += 1
        
        total = len(history_sequence)
        features.append(size_counts['大'] / total if total > 0 else 0)  # 大号比例
        features.append(parity_counts['单'] / total if total > 0 else 0)  # 单号比例
        
        # 尾数分布
        tail_counts = {}
        for record in history_sequence:
            if record.get('special_tail') is not None:
                tail = record['special_tail']
                tail_counts[tail] = tail_counts.get(tail, 0) + 1
        
        # 尾数熵（多样性）
        if tail_counts:
            tail_probs = [count / sum(tail_counts.values()) for count in tail_counts.values()]
            tail_entropy = -sum(p * np.log2(p) for p in tail_probs if p > 0)
            features.append(tail_entropy)
        else:
            features.append(0)
        
        return features
    
    def _extract_zodiac_features(self, history_sequence: List[Dict]) -> List[float]:
        """提取生肖特征"""
        features = []
        
        # 生肖分布
        zodiac_counts = {}
        for record in history_sequence:
            if record.get('special_zodiac'):
                zodiac = record['special_zodiac']
                zodiac_counts[zodiac] = zodiac_counts.get(zodiac, 0) + 1
        
        # 12生肖的出现频率
        zodiac_names = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
        total = len(history_sequence)
        for zodiac in zodiac_names:
            freq = zodiac_counts.get(zodiac, 0) / total if total > 0 else 0
            features.append(freq)
        
        # 扩展生肖属性统计
        wave_counts = {'红肖': 0, '绿肖': 0, '蓝肖': 0}
        season_counts = {'春': 0, '夏': 0, '秋': 0, '冬': 0}
        
        for record in history_sequence:
            if record.get('zodiac_color_wave'):
                wave_counts[record['zodiac_color_wave']] += 1
            if record.get('zodiac_season'):
                season_counts[record['zodiac_season']] += 1
        
        # 波色比例
        for wave in ['红肖', '绿肖', '蓝肖']:
            features.append(wave_counts[wave] / total if total > 0 else 0)
        
        # 季节比例
        for season in ['春', '夏', '秋', '冬']:
            features.append(season_counts[season] / total if total > 0 else 0)
        
        return features
    
    def _extract_traditional_features(self, history_sequence: List[Dict]) -> List[float]:
        """提取传统特征"""
        features = []
        
        # 波色统计
        wave_counts = {'红': 0, '绿': 0, '蓝': 0}
        for record in history_sequence:
            if record.get('special_wave_color'):
                wave_counts[record['special_wave_color']] += 1
        
        total = len(history_sequence)
        for color in ['红', '绿', '蓝']:
            features.append(wave_counts[color] / total if total > 0 else 0)
        
        # 段位统计
        segment_counts = {i: 0 for i in range(1, 8)}
        for record in history_sequence:
            if record.get('special_segment'):
                segment_counts[record['special_segment']] += 1
        
        for segment in range(1, 8):
            features.append(segment_counts[segment] / total if total > 0 else 0)
        
        # 质合统计
        prime_counts = {'质': 0, '合': 0}
        for record in history_sequence:
            if record.get('special_prime'):
                prime_counts[record['special_prime']] += 1
        
        features.append(prime_counts['质'] / total if total > 0 else 0)
        
        return features
    
    def _extract_time_features(self, current_record: Dict) -> List[float]:
        """提取时间特征"""
        features = []
        
        draw_date = current_record['draw_date']
        
        # 基础时间特征
        features.append(draw_date.year % 100)  # 年份后两位
        features.append(draw_date.month)       # 月份
        features.append(draw_date.day)         # 日期
        features.append(draw_date.weekday())   # 星期几
        
        # 农历年特征
        if current_record.get('lunar_year'):
            features.append(current_record['lunar_year'] % 100)
        else:
            features.append(0)
        
        # 季度特征
        quarter = (draw_date.month - 1) // 3 + 1
        features.append(quarter)
        
        return features
    
    def _train_models(self, features: np.ndarray, targets: np.ndarray):
        """训练所有模型"""
        logger.info("开始训练机器学习模型")
        
        for model_name, model_config in self.models.items():
            try:
                model = model_config['model']
                
                # 训练模型
                model.fit(features, targets)
                
                # 交叉验证评估
                cv_scores = cross_val_score(model, features, targets, cv=5, scoring='accuracy')
                avg_score = np.mean(cv_scores)
                
                # 保存训练好的模型
                self.trained_models[model_name] = {
                    'model': model,
                    'cv_score': avg_score,
                    'weight': model_config['weight']
                }
                
                logger.info(f"{model_name} 训练完成，交叉验证准确率: {avg_score:.3f}")
                
            except Exception as e:
                logger.error(f"{model_name} 训练失败: {e}")
                # 移除失败的模型
                if model_name in self.trained_models:
                    del self.trained_models[model_name]
    
    def _generate_prediction_features(self, historical_data: List[Dict], target_date: date) -> np.ndarray:
        """生成预测特征"""
        # 使用最近的序列数据生成预测特征
        recent_sequence = historical_data[:self.feature_config['sequence_length']]
        
        # 创建虚拟的当前记录（用于时间特征）
        current_record = {
            'draw_date': target_date,
            'lunar_year': self.lunar_manager.get_lunar_year_by_date(target_date)['lunar_year']
        }
        
        feature_vector = self._generate_feature_vector(current_record, recent_sequence)
        
        if feature_vector is not None:
            # 标准化特征
            feature_scaled = self.scaler.transform(feature_vector.reshape(1, -1))
            return feature_scaled[0]
        else:
            # 返回零向量
            return np.zeros(len(self.feature_names) if self.feature_names else 50)
    
    def _ensemble_predict(self, features: np.ndarray) -> Dict[int, float]:
        """集成预测"""
        if not self.trained_models:
            logger.warning("没有训练好的模型，返回均匀概率")
            return {i: 1/49 for i in range(1, 50)}
        
        # 收集所有模型的预测概率
        all_predictions = {}
        total_weight = 0
        
        for model_name, model_info in self.trained_models.items():
            try:
                model = model_info['model']
                weight = model_info['weight']
                
                # 获取预测概率
                if hasattr(model, 'predict_proba'):
                    proba = model.predict_proba(features.reshape(1, -1))[0]
                    classes = model.classes_
                else:
                    # 对于不支持概率预测的模型，使用投票
                    prediction = model.predict(features.reshape(1, -1))[0]
                    proba = np.zeros(49)
                    if 1 <= prediction <= 49:
                        proba[prediction - 1] = 1.0
                    classes = list(range(1, 50))
                
                # 将预测结果加权累加
                for i, class_label in enumerate(classes):
                    if 1 <= class_label <= 49:
                        if class_label not in all_predictions:
                            all_predictions[class_label] = 0
                        all_predictions[class_label] += proba[i] * weight
                
                total_weight += weight
                
            except Exception as e:
                logger.error(f"模型 {model_name} 预测失败: {e}")
        
        # 归一化概率
        if total_weight > 0:
            for number in all_predictions:
                all_predictions[number] /= total_weight
        
        # 确保所有号码都有概率
        final_predictions = {}
        for number in range(1, 50):
            final_predictions[number] = all_predictions.get(number, 0.001)  # 最小概率
        
        # 归一化到概率和为1
        total_prob = sum(final_predictions.values())
        if total_prob > 0:
            for number in final_predictions:
                final_predictions[number] /= total_prob
        
        return final_predictions
    
    def _select_numbers_by_probability(self, predictions: Dict[int, float], 
                                     min_numbers: int, max_numbers: int) -> List[int]:
        """根据概率选择号码"""
        # 按概率排序
        sorted_numbers = sorted(predictions.items(), key=lambda x: x[1], reverse=True)
        
        # 动态选择数量
        threshold = np.percentile(list(predictions.values()), 80)  # 80分位数
        
        selected = []
        for number, prob in sorted_numbers:
            if prob >= threshold and len(selected) < max_numbers:
                selected.append(number)
            elif len(selected) >= min_numbers:
                break
        
        # 确保数量在范围内
        if len(selected) < min_numbers:
            selected = [num for num, prob in sorted_numbers[:min_numbers]]
        elif len(selected) > max_numbers:
            selected = selected[:max_numbers]
        
        return sorted(selected)
    
    def _calculate_prediction_confidence(self, predictions: Dict[int, float], 
                                       recommended_numbers: List[int]) -> float:
        """计算预测置信度"""
        if not recommended_numbers:
            return 0.0
        
        # 推荐号码的平均概率
        recommended_probs = [predictions[num] for num in recommended_numbers]
        avg_recommended_prob = np.mean(recommended_probs)
        
        # 所有号码的平均概率
        avg_all_prob = np.mean(list(predictions.values()))
        
        # 置信度基于推荐号码概率与平均概率的比值
        confidence = min(0.95, max(0.1, avg_recommended_prob / avg_all_prob))
        return confidence
    
    def _get_model_performance(self) -> Dict[str, float]:
        """获取模型性能"""
        performance = {}
        for model_name, model_info in self.trained_models.items():
            performance[model_name] = model_info.get('cv_score', 0.0)
        return performance
    
    def _get_feature_importance(self) -> Dict[str, float]:
        """获取特征重要性"""
        importance = {}
        
        for model_name, model_info in self.trained_models.items():
            model = model_info['model']
            if hasattr(model, 'feature_importances_'):
                importance[model_name] = model.feature_importances_.tolist()
        
        return importance
    
    def _get_default_prediction(self, min_numbers: int, max_numbers: int) -> Dict[str, Any]:
        """获取默认预测结果"""
        # 随机选择号码
        default_numbers = list(range(1, min_numbers + 1))
        
        return {
            "predicted_numbers": default_numbers,
            "number_probabilities": {i: 1/49 for i in range(1, 50)},
            "confidence_level": 0.2,
            "model_performance": {},
            "feature_importance": {},
            "prediction_time": datetime.now(),
            "training_data_size": 0,
            "models_used": [],
            "note": "数据不足，使用默认预测"
        }
    
    def save_models(self, model_dir: str = "data/models"):
        """保存训练好的模型"""
        import os
        os.makedirs(model_dir, exist_ok=True)
        
        for model_name, model_info in self.trained_models.items():
            model_path = os.path.join(model_dir, f"{model_name}.joblib")
            joblib.dump(model_info, model_path)
        
        # 保存标准化器
        scaler_path = os.path.join(model_dir, "scaler.joblib")
        joblib.dump(self.scaler, scaler_path)
        
        logger.info(f"模型保存完成，保存路径: {model_dir}")
    
    def load_models(self, model_dir: str = "data/models"):
        """加载训练好的模型"""
        import os
        
        try:
            # 加载标准化器
            scaler_path = os.path.join(model_dir, "scaler.joblib")
            if os.path.exists(scaler_path):
                self.scaler = joblib.load(scaler_path)
            
            # 加载模型
            for model_name in self.models.keys():
                model_path = os.path.join(model_dir, f"{model_name}.joblib")
                if os.path.exists(model_path):
                    self.trained_models[model_name] = joblib.load(model_path)
            
            logger.info(f"模型加载完成，加载了 {len(self.trained_models)} 个模型")
            return True
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return False
    
    def get_prediction_report(self, prediction_result: Dict[str, Any]) -> str:
        """生成预测报告"""
        report = []
        report.append("=== 机器学习预测报告 ===")
        report.append(f"预测时间: {prediction_result['prediction_time']}")
        report.append(f"训练数据量: {prediction_result['training_data_size']}")
        report.append(f"置信度: {prediction_result['confidence_level']:.2%}")
        report.append("")
        
        report.append(f"推荐号码 ({len(prediction_result['predicted_numbers'])}个):")
        for i, number in enumerate(prediction_result['predicted_numbers'], 1):
            prob = prediction_result['number_probabilities'][number]
            report.append(f"  {i:2d}. {number:2d} (概率: {prob:.3f})")
        
        report.append("")
        report.append("模型性能:")
        for model_name, score in prediction_result['model_performance'].items():
            report.append(f"  {model_name}: {score:.3f}")
        
        return "\n".join(report)
