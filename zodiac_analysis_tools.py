"""
多维生肖分析工具集
提供冷热、平衡、交叉分析的实用工具
"""

from enhanced_zodiac_analyzer import EnhancedZodiacAnalyzer
from datetime import datetime, date
import json

class ZodiacAnalysisTools:
    """生肖分析工具集"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.analyzer = EnhancedZodiacAnalyzer(db_path)
        
    def hot_cold_analysis_tool(self, target_date: str = None, days: int = 100) -> dict:
        """冷热分析工具"""
        if target_date is None:
            target_date = date.today().strftime('%Y-%m-%d')
            
        print(f"🔥❄️ 生肖冷热分析工具")
        print(f"分析日期: {target_date}")
        print(f"分析期数: {days}期")
        print("=" * 60)
        
        # 获取完整分析
        full_analysis = self.analyzer.comprehensive_analysis(target_date, days)
        hot_cold_data = full_analysis["analysis_details"]["冷热分析"]
        
        # 格式化输出
        result = {
            "分析类型": "生肖冷热分析",
            "分析日期": target_date,
            "分析期数": days,
            "热门生肖": {
                "生肖": hot_cold_data["hot_zodiacs"],
                "特征": "出现频率高于平均值40%以上",
                "建议": "谨慎选择，可能进入调整期",
                "策略": "回避为主"
            },
            "冷门生肖": {
                "生肖": hot_cold_data["cold_zodiacs"],
                "特征": "出现频率低于平均值40%以下",
                "建议": "重点关注，符合回补理论",
                "策略": "优先选择"
            },
            "正常生肖": {
                "生肖": hot_cold_data["normal_zodiacs"],
                "特征": "出现频率接近理论平均值",
                "建议": "可作为备选考虑",
                "策略": "中性选择"
            },
            "频率详情": hot_cold_data["frequencies"],
            "推荐指数": {zodiac: "⭐⭐⭐" if zodiac in hot_cold_data["cold_zodiacs"] 
                        else "⭐" if zodiac in hot_cold_data["hot_zodiacs"] 
                        else "⭐⭐" for zodiac in self.analyzer.zodiac_names}
        }
        
        return result
        
    def balance_analysis_tool(self, target_date: str = None, days: int = 100) -> dict:
        """平衡分析工具"""
        if target_date is None:
            target_date = date.today().strftime('%Y-%m-%d')
            
        print(f"⚖️ 生肖维度平衡分析工具")
        print(f"分析日期: {target_date}")
        print(f"分析期数: {days}期")
        print("=" * 60)
        
        # 获取完整分析
        full_analysis = self.analyzer.comprehensive_analysis(target_date, days)
        balance_data = full_analysis["analysis_details"]["平衡分析"]
        
        # 分析各维度的不平衡情况
        imbalance_analysis = {}
        recommendations = []
        
        for dimension, stats in balance_data["dimension_stats"].items():
            imbalance_analysis[dimension] = {}
            
            for category, data in stats.items():
                frequency = data["frequency"]
                expected = 1 / len(balance_data["dimension_stats"][dimension])
                
                if frequency < expected * 0.7:
                    status = "严重不足"
                    recommendations.extend(data["zodiacs"])
                elif frequency < expected * 0.85:
                    status = "轻微不足"
                elif frequency > expected * 1.3:
                    status = "严重过多"
                elif frequency > expected * 1.15:
                    status = "轻微过多"
                else:
                    status = "平衡"
                    
                imbalance_analysis[dimension][category] = {
                    "生肖": data["zodiacs"],
                    "出现次数": data["count"],
                    "频率": f"{frequency:.1%}",
                    "期望频率": f"{expected:.1%}",
                    "状态": status
                }
        
        # 去重推荐生肖
        unique_recommendations = list(set(recommendations))[:4]
        
        result = {
            "分析类型": "生肖维度平衡分析",
            "分析日期": target_date,
            "分析窗口": balance_data["analysis_window"],
            "维度分析": imbalance_analysis,
            "平衡推荐": {
                "推荐生肖": unique_recommendations,
                "推荐理由": "基于维度不平衡回补理论",
                "策略说明": "选择在各维度中出现不足的生肖"
            },
            "分析总结": {
                "最不平衡维度": self._find_most_imbalanced_dimension(imbalance_analysis),
                "建议关注": "出现'严重不足'状态的类别"
            }
        }
        
        return result
        
    def cross_analysis_tool(self, target_date: str = None, days: int = 100) -> dict:
        """交叉分析工具"""
        if target_date is None:
            target_date = date.today().strftime('%Y-%m-%d')
            
        print(f"🔀 生肖多维度交叉分析工具")
        print(f"分析日期: {target_date}")
        print(f"分析期数: {days}期")
        print("=" * 60)
        
        # 获取完整分析
        full_analysis = self.analyzer.comprehensive_analysis(target_date, days)
        cross_data = full_analysis["analysis_details"]["交叉分析"]
        
        # 分析维度组合模式
        combination_analysis = {}
        for combination, count in cross_data["patterns"]["dimension_combinations"].items():
            combination_dict = dict(combination)
            
            # 找出属于这个组合的生肖
            matching_zodiacs = []
            for zodiac in self.analyzer.zodiac_names:
                zodiac_combination = {}
                for dim_name, categories in self.analyzer.zodiac_dimensions.items():
                    for cat_name, zodiac_list in categories.items():
                        if zodiac in zodiac_list:
                            zodiac_combination[dim_name] = cat_name
                            break
                            
                if zodiac_combination == combination_dict:
                    matching_zodiacs.append(zodiac)
                    
            combination_key = " + ".join([f"{dim}:{cat}" for dim, cat in combination_dict.items()])
            combination_analysis[combination_key] = {
                "维度组合": combination_dict,
                "匹配生肖": matching_zodiacs,
                "近期出现": count,
                "状态": "过热" if count >= 3 else "冷门" if count == 0 else "正常"
            }
            
        # 找出冷门组合的生肖
        cold_combinations = [data for data in combination_analysis.values() if data["状态"] == "冷门"]
        recommended_from_cross = []
        for combo in cold_combinations:
            recommended_from_cross.extend(combo["匹配生肖"])
            
        result = {
            "分析类型": "生肖多维度交叉分析",
            "分析日期": target_date,
            "分析窗口": cross_data["patterns"]["recent_window"],
            "组合分析": combination_analysis,
            "交叉推荐": {
                "推荐生肖": list(set(recommended_from_cross))[:4],
                "推荐理由": "基于维度组合冷门回补",
                "策略说明": "选择维度组合近期未出现的生肖"
            },
            "分析洞察": {
                "冷门组合数": len(cold_combinations),
                "过热组合数": len([d for d in combination_analysis.values() if d["状态"] == "过热"]),
                "建议": "优先考虑冷门组合中的生肖"
            }
        }
        
        return result
        
    def seasonal_analysis_tool(self, target_date: str = None, days: int = 100) -> dict:
        """季节分析工具"""
        if target_date is None:
            target_date = date.today().strftime('%Y-%m-%d')
            
        print(f"🌸🌞🍂❄️ 生肖季节分析工具")
        print(f"分析日期: {target_date}")
        print(f"分析期数: {days}期")
        print("=" * 60)
        
        # 获取完整分析
        full_analysis = self.analyzer.comprehensive_analysis(target_date, days)
        seasonal_data = full_analysis["analysis_details"]["季节分析"]
        
        # 季节表情映射
        season_emoji = {"春": "🌸", "夏": "🌞", "秋": "🍂", "冬": "❄️"}
        
        result = {
            "分析类型": "生肖季节分析",
            "分析日期": target_date,
            "当前季节": f"{season_emoji[seasonal_data['current_season']]}{seasonal_data['current_season']}季",
            "目标月份": seasonal_data["target_month"],
            "季节统计": {},
            "季节推荐": {},
            "分析建议": {}
        }
        
        # 处理季节统计
        for season, stats in seasonal_data["seasonal_stats"].items():
            recent_freq = stats["recent_count"] / 20 if seasonal_data.get("analysis_window", 20) >= 20 else 0
            expected_freq = 0.25  # 每个季节期望25%
            
            result["季节统计"][f"{season_emoji.get(season, '')}{season}季"] = {
                "生肖": stats["zodiacs"],
                "近期出现": stats["recent_count"],
                "近期频率": f"{recent_freq:.1%}",
                "期望频率": f"{expected_freq:.1%}",
                "状态": "不足" if recent_freq < expected_freq * 0.8 else "过多" if recent_freq > expected_freq * 1.2 else "正常"
            }
            
        # 当前季节推荐
        current_season = seasonal_data["current_season"]
        current_season_zodiacs = self.analyzer.zodiac_dimensions["四季"][current_season]
        
        result["季节推荐"] = {
            "当前季节生肖": current_season_zodiacs,
            "推荐理由": f"当前为{current_season}季，季节生肖具有时令优势",
            "推荐指数": "⭐⭐⭐⭐"
        }
        
        result["分析建议"] = {
            "主要策略": f"优先选择{current_season}季生肖",
            "次要策略": "关注出现不足的季节生肖",
            "避免策略": "谨慎选择出现过多的季节生肖"
        }
        
        return result
        
    def comprehensive_recommendation(self, target_date: str = None, days: int = 100) -> dict:
        """综合推荐工具"""
        if target_date is None:
            target_date = date.today().strftime('%Y-%m-%d')
            
        print(f"🎯 生肖综合分析推荐工具")
        print(f"分析日期: {target_date}")
        print(f"分析期数: {days}期")
        print("=" * 60)
        
        # 获取完整分析
        full_analysis = self.analyzer.comprehensive_analysis(target_date, days)
        
        # 整合各种分析的推荐
        hot_cold = self.hot_cold_analysis_tool(target_date, days)
        balance = self.balance_analysis_tool(target_date, days)
        cross = self.cross_analysis_tool(target_date, days)
        seasonal = self.seasonal_analysis_tool(target_date, days)
        
        # 综合评分
        comprehensive_scores = {}
        for zodiac in self.analyzer.zodiac_names:
            score = 0
            
            # 冷热分析贡献
            if zodiac in hot_cold["冷门生肖"]["生肖"]:
                score += 3
            elif zodiac in hot_cold["正常生肖"]["生肖"]:
                score += 1
                
            # 平衡分析贡献
            if zodiac in balance["平衡推荐"]["推荐生肖"]:
                score += 2
                
            # 交叉分析贡献
            if zodiac in cross["交叉推荐"]["推荐生肖"]:
                score += 2
                
            # 季节分析贡献
            if zodiac in seasonal["季节推荐"]["当前季节生肖"]:
                score += 1
                
            comprehensive_scores[zodiac] = score
            
        # 排序推荐
        sorted_recommendations = sorted(comprehensive_scores.items(), key=lambda x: x[1], reverse=True)
        
        result = {
            "分析类型": "生肖综合分析推荐",
            "分析日期": target_date,
            "综合得分": comprehensive_scores,
            "推荐排序": sorted_recommendations,
            "强烈推荐": [zodiac for zodiac, score in sorted_recommendations[:2]],
            "一般推荐": [zodiac for zodiac, score in sorted_recommendations[2:4]],
            "备选推荐": [zodiac for zodiac, score in sorted_recommendations[4:6]],
            "分析汇总": {
                "冷热分析": f"冷门生肖{len(hot_cold['冷门生肖']['生肖'])}个",
                "平衡分析": f"推荐生肖{len(balance['平衡推荐']['推荐生肖'])}个",
                "交叉分析": f"推荐生肖{len(cross['交叉推荐']['推荐生肖'])}个",
                "季节分析": f"当前{seasonal['当前季节']}，推荐{len(seasonal['季节推荐']['当前季节生肖'])}个"
            },
            "使用建议": {
                "投注策略": "优先选择强烈推荐的2个生肖",
                "风险控制": "可适当考虑一般推荐的生肖作为补充",
                "注意事项": "建议结合个人经验和其他分析方法"
            }
        }
        
        return result
        
    def _find_most_imbalanced_dimension(self, imbalance_analysis: dict) -> str:
        """找出最不平衡的维度"""
        max_imbalance = 0
        most_imbalanced = ""
        
        for dimension, categories in imbalance_analysis.items():
            imbalance_count = sum(1 for cat_data in categories.values() 
                                if "不足" in cat_data["状态"] or "过多" in cat_data["状态"])
            
            if imbalance_count > max_imbalance:
                max_imbalance = imbalance_count
                most_imbalanced = dimension
                
        return most_imbalanced
