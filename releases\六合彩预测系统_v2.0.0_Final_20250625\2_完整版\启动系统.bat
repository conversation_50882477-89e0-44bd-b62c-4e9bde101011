@echo off
chcp 65001 >nul
title 六合彩预测系统

echo 启动六合彩预测系统...
echo ================================

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo Python未安装，正在启动EXE版本...
    if exist "六合彩预测系统_v2.0.0.exe" (
        start "六合彩预测系统" "六合彩预测系统_v2.0.0.exe"
    ) else (
        echo EXE文件不存在
        pause
        exit /b 1
    )
) else (
    echo 检测到Python环境，启动Python版本...
    if exist "lottery_prediction_gui.py" (
        python lottery_prediction_gui.py
    ) else (
        echo Python文件不存在，启动EXE版本...
        if exist "六合彩预测系统_v2.0.0.exe" (
            start "六合彩预测系统" "六合彩预测系统_v2.0.0.exe"
        ) else (
            echo 所有启动文件都不存在
            pause
            exit /b 1
        )
    )
)

echo 程序已启动
pause
