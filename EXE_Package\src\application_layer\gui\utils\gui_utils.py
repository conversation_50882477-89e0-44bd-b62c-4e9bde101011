"""
GUI工具模块
"""
import tkinter as tk
from tkinter import ttk
import threading

class ProgressDialog:
    def __init__(self, parent, title="处理中...", message="请稍候..."):
        self.parent = parent
        
        # 创建进度对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("300x120")
        self.dialog.configure(bg='#f0f0f0')
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 禁用关闭按钮
        self.dialog.protocol("WM_DELETE_WINDOW", lambda: None)
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets(message)
    
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (300 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (120 // 2)
        self.dialog.geometry(f"300x120+{x}+{y}")
    
    def create_widgets(self, message):
        """创建界面组件"""
        # 消息标签
        self.message_label = ttk.Label(self.dialog, text=message, 
                                      font=('Arial', 10), background='#f0f0f0')
        self.message_label.pack(pady=20)
        
        # 进度条
        self.progress = ttk.Progressbar(self.dialog, mode='indeterminate')
        self.progress.pack(fill='x', padx=20, pady=10)
        self.progress.start()
    
    def update_message(self, message):
        """更新消息"""
        self.message_label.config(text=message)
    
    def close(self):
        """关闭对话框"""
        self.progress.stop()
        self.dialog.destroy()

def run_with_progress(parent, func, title="处理中...", message="请稍候..."):
    """在进度对话框中运行函数"""
    progress_dialog = ProgressDialog(parent, title, message)
    result = {'value': None, 'error': None}
    
    def worker():
        try:
            result['value'] = func()
        except Exception as e:
            result['error'] = e
        finally:
            parent.after(0, progress_dialog.close)
    
    # 启动工作线程
    thread = threading.Thread(target=worker, daemon=True)
    thread.start()
    
    # 等待完成
    parent.wait_window(progress_dialog.dialog)
    
    if result['error']:
        raise result['error']
    
    return result['value']
