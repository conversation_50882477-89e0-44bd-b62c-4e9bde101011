"""
自动特征工程 - 自动生成、选择和变换特征
"""
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.decomposition import PCA
from sklearn.feature_selection import RFE
from sklearn.ensemble import RandomForestClassifier
import itertools

class AutoFeatureEngineering:
    def __init__(self):
        """初始化自动特征工程"""
        self.feature_generators = []
        self.feature_selectors = []
        self.feature_transformers = []
        self.generated_features = []
        
    def generate_polynomial_features(self, X: np.ndarray, degree: int = 2) -> np.ndarray:
        """生成多项式特征"""
        from sklearn.preprocessing import PolynomialFeatures
        
        poly = PolynomialFeatures(degree=degree, include_bias=False)
        X_poly = poly.fit_transform(X)
        
        return X_poly
    
    def generate_interaction_features(self, X: np.ndarray, max_interactions: int = 10) -> np.ndarray:
        """生成交互特征"""
        n_features = X.shape[1]
        interaction_features = []
        
        # 生成两两交互特征
        feature_pairs = list(itertools.combinations(range(n_features), 2))
        
        # 限制交互特征数量
        if len(feature_pairs) > max_interactions:
            # 随机选择交互特征
            np.random.shuffle(feature_pairs)
            feature_pairs = feature_pairs[:max_interactions]
        
        for i, j in feature_pairs:
            # 乘积交互
            interaction = X[:, i] * X[:, j]
            interaction_features.append(interaction)
            
            # 差值交互
            diff = X[:, i] - X[:, j]
            interaction_features.append(diff)
            
            # 比值交互（避免除零）
            ratio = np.divide(X[:, i], X[:, j] + 1e-8)
            interaction_features.append(ratio)
        
        if interaction_features:
            return np.column_stack(interaction_features)
        else:
            return np.array([]).reshape(X.shape[0], 0)
    
    def generate_statistical_features(self, X: np.ndarray, window_size: int = 5) -> np.ndarray:
        """生成统计特征"""
        statistical_features = []
        
        for i in range(X.shape[1]):
            feature_col = X[:, i]
            
            # 滑动窗口统计特征
            rolling_mean = self._rolling_window_stat(feature_col, window_size, np.mean)
            rolling_std = self._rolling_window_stat(feature_col, window_size, np.std)
            rolling_min = self._rolling_window_stat(feature_col, window_size, np.min)
            rolling_max = self._rolling_window_stat(feature_col, window_size, np.max)
            
            statistical_features.extend([rolling_mean, rolling_std, rolling_min, rolling_max])
        
        return np.column_stack(statistical_features)
    
    def _rolling_window_stat(self, data: np.ndarray, window_size: int, stat_func) -> np.ndarray:
        """计算滑动窗口统计量"""
        result = np.zeros_like(data)
        
        for i in range(len(data)):
            start_idx = max(0, i - window_size + 1)
            window_data = data[start_idx:i+1]
            result[i] = stat_func(window_data)
        
        return result
    
    def generate_lag_features(self, X: np.ndarray, lags: List[int] = [1, 2, 3, 5, 10]) -> np.ndarray:
        """生成滞后特征"""
        lag_features = []
        
        for lag in lags:
            for i in range(X.shape[1]):
                feature_col = X[:, i]
                
                # 创建滞后特征
                lagged = np.zeros_like(feature_col)
                lagged[lag:] = feature_col[:-lag]
                
                lag_features.append(lagged)
        
        return np.column_stack(lag_features)
    
    def auto_generate_features(self, X: np.ndarray, feature_types: List[str] = None) -> np.ndarray:
        """自动生成特征"""
        if feature_types is None:
            feature_types = ['polynomial', 'interaction', 'statistical', 'lag']
        
        generated_features = [X]  # 包含原始特征
        
        if 'polynomial' in feature_types:
            poly_features = self.generate_polynomial_features(X, degree=2)
            generated_features.append(poly_features)
        
        if 'interaction' in feature_types:
            interaction_features = self.generate_interaction_features(X, max_interactions=20)
            if interaction_features.shape[1] > 0:
                generated_features.append(interaction_features)
        
        if 'statistical' in feature_types:
            stat_features = self.generate_statistical_features(X, window_size=5)
            generated_features.append(stat_features)
        
        if 'lag' in feature_types:
            lag_features = self.generate_lag_features(X, lags=[1, 2, 3])
            generated_features.append(lag_features)
        
        # 合并所有特征
        all_features = np.column_stack(generated_features)
        
        return all_features
    
    def select_features_univariate(self, X: np.ndarray, y: np.ndarray, k: int = 50) -> Tuple[np.ndarray, np.ndarray]:
        """单变量特征选择"""
        selector = SelectKBest(score_func=f_classif, k=min(k, X.shape[1]))
        X_selected = selector.fit_transform(X, y)
        
        return X_selected, selector.get_support()
    
    def select_features_mutual_info(self, X: np.ndarray, y: np.ndarray, k: int = 50) -> Tuple[np.ndarray, np.ndarray]:
        """互信息特征选择"""
        selector = SelectKBest(score_func=mutual_info_classif, k=min(k, X.shape[1]))
        X_selected = selector.fit_transform(X, y)
        
        return X_selected, selector.get_support()
    
    def select_features_rfe(self, X: np.ndarray, y: np.ndarray, n_features: int = 30) -> Tuple[np.ndarray, np.ndarray]:
        """递归特征消除"""
        estimator = RandomForestClassifier(n_estimators=50, random_state=42)
        selector = RFE(estimator, n_features_to_select=min(n_features, X.shape[1]))
        X_selected = selector.fit_transform(X, y)
        
        return X_selected, selector.support_
    
    def auto_select_features(self, X: np.ndarray, y: np.ndarray, method: str = 'mutual_info', k: int = 50) -> Tuple[np.ndarray, np.ndarray]:
        """自动特征选择"""
        if method == 'univariate':
            return self.select_features_univariate(X, y, k)
        elif method == 'mutual_info':
            return self.select_features_mutual_info(X, y, k)
        elif method == 'rfe':
            return self.select_features_rfe(X, y, k)
        else:
            raise ValueError(f"不支持的特征选择方法: {method}")
    
    def transform_features(self, X: np.ndarray, method: str = 'standard') -> np.ndarray:
        """特征变换"""
        if method == 'standard':
            scaler = StandardScaler()
        elif method == 'minmax':
            scaler = MinMaxScaler()
        elif method == 'robust':
            scaler = RobustScaler()
        else:
            raise ValueError(f"不支持的特征变换方法: {method}")
        
        X_transformed = scaler.fit_transform(X)
        return X_transformed
    
    def reduce_dimensions(self, X: np.ndarray, n_components: int = 20) -> np.ndarray:
        """降维"""
        pca = PCA(n_components=min(n_components, X.shape[1]))
        X_reduced = pca.fit_transform(X)
        
        return X_reduced
    
    def auto_feature_engineering_pipeline(self, 
                                        X: np.ndarray, 
                                        y: np.ndarray,
                                        generate_types: List[str] = None,
                                        selection_method: str = 'mutual_info',
                                        n_features: int = 50,
                                        transform_method: str = 'standard') -> Dict:
        """自动特征工程流水线"""
        
        results = {}
        
        # 1. 特征生成
        print("正在生成特征...")
        X_generated = self.auto_generate_features(X, generate_types)
        results['original_features'] = X.shape[1]
        results['generated_features'] = X_generated.shape[1]
        
        # 2. 特征选择
        print("正在选择特征...")
        X_selected, feature_mask = self.auto_select_features(
            X_generated, y, selection_method, n_features
        )
        results['selected_features'] = X_selected.shape[1]
        results['feature_mask'] = feature_mask
        
        # 3. 特征变换
        print("正在变换特征...")
        X_transformed = self.transform_features(X_selected, transform_method)
        results['final_features'] = X_transformed.shape[1]
        
        # 4. 特征评估
        print("正在评估特征...")
        feature_importance = self._evaluate_features(X_transformed, y)
        results['feature_importance'] = feature_importance
        
        results['X_final'] = X_transformed
        results['pipeline_summary'] = {
            'generation': generate_types or ['polynomial', 'interaction', 'statistical', 'lag'],
            'selection': selection_method,
            'transformation': transform_method,
            'feature_reduction_ratio': X_transformed.shape[1] / X.shape[1]
        }
        
        return results
    
    def _evaluate_features(self, X: np.ndarray, y: np.ndarray) -> List[float]:
        """评估特征重要性"""
        rf = RandomForestClassifier(n_estimators=50, random_state=42)
        rf.fit(X, y)
        
        return rf.feature_importances_.tolist()
