澳门六合彩预测系统 - 核心构思与模块关联图 (大纲版)
🎯 核心预测目标： (同前)
🔄 核心预测流程概览： (同前)
** layered_architecture_start **
第一层：数据层 (Data Layer) - 数据基石
1.1 数据存储子系统
1.1.1 核心数据库 (LotteryDB)
├── lottery_data (开奖历史：期号, 日期, 正特码, 农历, 年份)
├── zodiac_extended_attributes (生肖扩展属性定义表：存储19个维度分类定义，如：
│ │ 琴棋书画: [琴:兔蛇鸡, ...], 四季: [春:虎兔龙, ...], 红绿蓝肖: [红肖:鼠兔马鸡, ...],
│ │ 日夜肖, 左右肖, 阴阳肖, 合独肖, 家野肖, 天地肖, 男女肖, 吉凶肖,
│ │ 前后肖, 单双笔画, 胆量大小)
├── fixed_number_attributes_definition (固定号码属性定义表：
│ │ 波色: [红波:01,02..., 蓝波:03,04..., 绿波:05,06...],
│ │ 正反码: [正码:05,06..., 反码:01,02...],
│ │ 大小数: [小数:01-24, 大数:25-49],
│ │ 合数大小: [合大:07,08..., 合小:01,02...],
│ │ 合数单双: [合单:01,03..., 合双:02,04...],
│ │ 七段: [1段:01-07, ..., 7段:43-49])
├── yearly_wuxing_number_mapping (年度五行号码映射表：[年份, 五行属性, 号码列表]，如：
│ │ [2023, 金, (01,02,09,10,23,24,31,32,39,40)], ...
│ │ [2024, 金, (02,03,10,11,24,25,32,33,40,41)], ... )
├── prediction_results (历史预测记录与反馈)
└── model_performance (模型性能跟踪)
1.2 数据管理子系统
├── 1.2.1 数据导入与校验模块 (确保导入的数据符合规范，如号码范围1-49)
└── 1.2.2 数据清洗与标准化模块
1.3 特征工程子系统 (Feature Engineering)
├── 1.3.1 基础与统计特征提取 (号码, 日期, 序列, 统计量)
├── 1.3.2 传统文化与固定属性特征映射
│ ├── dynamic_zodiac_mapper.py (核心：根据年份和号码，动态映射到当年所属生肖 - 关联工具层4.1.2.1)
│ ├── extended_zodiac_attribute_mapper.py (根据动态映射出的生肖，再映射到19个扩展生肖维度 - 关联1.1.1 zodiac_extended_attributes)
│ ├── dynamic_wuxing_mapper.py (根据年份和号码，动态映射到当年五行属性 - 关联1.1.1 yearly_wuxing_number_mapping 或工具层4.1.2.2)
│ ├── fixed_number_attribute_mapper.py (将号码映射到固定属性：波色, 正反, 大小, 合数, 七段 - 关联1.1.1 fixed_number_attributes_definition)
│ └── zodiac_feature_generator.py (🌟基于生肖模块分析结果生成ML特征 - 关联生肖模块)
├── 1.3.3 组合特征生成 (如：生肖+波色，五行+大小)
└── 1.3.4 高级特征工程 (模式识别, 周期分析, 特征选择)
第二层：算法分析层 (Algorithm Layer) - 预测核心
2.1 传统统计分析模块组
├── 2.1.1 基础统计分析 (号码频率, 冷热, 遗漏, 奇偶, 大小等)
├── 2.1.2 传统文化分析
│ ├── dynamic_wuxing_analysis.py (基于动态五行号码的分析)
│ ├── fixed_attribute_analysis.py (波色, 正反, 大小, 合数, 七段等固定属性的分析)
│ └── 🌟 extended_zodiac_analysis.py (生肖维度扩展分析 - 核心模块)
│ ├── ZodiacExtendedAnalyzer (基于19个扩展生肖维度的统计分析：频率, 组合, 周期等)
│ ├── ZodiacPredictionEngine (预测引擎 -> 输出4个高概率生肖)
│ │ └── 方法：频率, 周期, 关联, 模式, 综合评分 (基于扩展维度)
│ ├── ZodiacRangeNarrower (范围缩小策略 - 辅助)
│ └── ZodiacCrossValidator (交叉验证接口 - 供融合模块调用)
2.2 机器学习与深度学习模型组 (同前，输入特征来自1.3)
├── 2.2.1 基础机器学习模型
├── 2.2.2 集成学习模型
├── 2.2.3 深度学习模型
├── 2.2.4 时间序列专用模型
└── 输出：各模型独立的特码预测号码集 (构成初步16-24个号码池的基础)
2.3 🎯 模型融合与筛选模块 (Prediction Fusion & Refinement) (同前)
├── 2.3.1 融合策略
├── 2.3.2 输入整合
├── 2.3.3 交叉验证与号码精炼
└── 2.3.4 置信度评估
2.4 智能优化与验证子系统 (同前)
第三层：应用展示层 (Application Layer) - 人机交互
3.1 图形用户界面 (GUI)
├── 3.1.1 主界面框架
├── 3.1.2 核心结果展示标签页 (Result Tabs)
│ ├── 📊 PredictionResultsTab (展示最终推荐的12-16个特码号码 + 置信度)
│ ├── 🐉 ZodiacAnalysisTab (展示预测的4个生肖 + 19个维度详细分析 + 预测过程)
│ ├── 🔗 FusionAnalysisTab (展示模型融合过程、各模型贡献度、生肖如何影响选择)
│ ├── 🧮 ModelScoringTab (各独立模型性能对比)
│ ├── 🏛️ TraditionalAnalysisTab (动态五行、固定属性如波色/正反/大小/七段的分析结果)
│ ├── 📁 DataOverviewTab (历史数据查阅，可显示当期动态生肖、五行等)
│ └── 🔄 SimulationRecordsTab (回测记录)
├── 3.1.3 对话框与弹窗系统
└── 3.1.4 用户交互与可视化组件
第四层：工具层 (Utility Layer) - 系统支撑
4.1 年度映射与属性管理子系统 (CRITICAL FOR ACCURACY)
├── 4.1.1 核心日历与周期计算
│ ├── lunar_calendar_util.py (公历转农历, 计算节气, 确定农历新年起始)
│ ├── head_zodiac_calculator.py (根据农历年份确定当值头肖)
│ └── zodiac_sequence_manager.py (管理12生肖顺序及逆时针查询)
├── 4.1.2 年度动态属性映射器
│ ├── year_specific_zodiac_number_mapper.py (核心：实现生肖与号码的年度动态分配规则)
│ │ └── 输入：年份 -> 输出：当年每个号码对应的生肖
│ ├── year_specific_wuxing_number_mapper.py (核心：实现五行与号码的年度动态分配规则)
│ │ └── 输入：年份 -> 输出：当年每个号码对应的五行属性 (可从1.1.1 yearly_wuxing_number_mapping读取或动态计算)
│ └── attribute_cache_manager.py (缓存动态映射结果以提高性能)
4.2 模型管理与调参系统 (同前)
4.3 结果保存、日志与报告系统 (同前)
4.4 AI策略优化系统 (同前)
4.5 系统监控、维护与故障恢复 (同前)

AIzaSyDUPr2hUb55s8d9DoY3F972VvssMyg_L_I
python lottery_prediction_gui.py
✅ special_zodiac_analyzer.py - 特码生肖专项分析器
✅ lottery_prediction_gui.py - 更新的GUI主文件
✅ test_special_zodiac_analyzer.py - 完整功能测试脚本
✅ quick_test_zodiac.py - 快速测试脚本

机器学习原理: 这就是典型的"训练-验证-应用"流程
统计学支持: 通过多次采样找到最优参数组合
实际应用: 很多量化交易系统都使用类似方法