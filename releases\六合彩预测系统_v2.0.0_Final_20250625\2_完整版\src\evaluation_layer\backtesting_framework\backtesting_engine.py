"""
历史回测引擎 - 完整的回测框架
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime, timedelta
import json
from abc import ABC, abstractmethod
import warnings

class BacktestingStrategy(ABC):
    """回测策略基类"""
    
    @abstractmethod
    def predict(self, historical_data: pd.DataFrame, current_date: datetime) -> Dict[str, Any]:
        """预测方法"""
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        pass

class BacktestingEngine:
    """历史回测引擎"""
    
    def __init__(self, 
                 start_date: str = None,
                 end_date: str = None,
                 min_training_periods: int = 100,
                 prediction_horizon: int = 1):
        """
        初始化回测引擎
        
        Args:
            start_date: 回测开始日期
            end_date: 回测结束日期
            min_training_periods: 最小训练期数
            prediction_horizon: 预测时间跨度
        """
        self.start_date = pd.to_datetime(start_date) if start_date else None
        self.end_date = pd.to_datetime(end_date) if end_date else None
        self.min_training_periods = min_training_periods
        self.prediction_horizon = prediction_horizon
        
        # 回测结果
        self.backtest_results = {}
        self.performance_metrics = {}
        
    def run_backtest(self, 
                    strategy: BacktestingStrategy,
                    historical_data: pd.DataFrame,
                    rebalance_frequency: str = 'weekly') -> Dict[str, Any]:
        """
        运行回测
        
        Args:
            strategy: 回测策略
            historical_data: 历史数据
            rebalance_frequency: 重新平衡频率
        """
        
        print(f"🔄 开始回测策略: {strategy.get_strategy_name()}")
        
        # 数据预处理
        data = self._prepare_data(historical_data)
        
        # 确定回测时间范围
        start_idx, end_idx = self._determine_backtest_range(data)
        
        # 初始化回测结果
        predictions = []
        actual_results = []
        prediction_dates = []
        
        # 执行回测
        for i in range(start_idx, end_idx):
            current_date = data.iloc[i]['draw_date']
            
            # 获取训练数据
            training_data = data.iloc[:i]
            
            if len(training_data) < self.min_training_periods:
                continue
            
            try:
                # 生成预测
                prediction = strategy.predict(training_data, current_date)
                
                # 获取实际结果
                actual = self._get_actual_result(data.iloc[i])
                
                # 记录结果
                predictions.append(prediction)
                actual_results.append(actual)
                prediction_dates.append(current_date)
                
            except Exception as e:
                warnings.warn(f"预测失败 {current_date}: {e}")
                continue
        
        # 计算性能指标
        performance = self._calculate_performance(predictions, actual_results)
        
        # 保存结果
        strategy_name = strategy.get_strategy_name()
        self.backtest_results[strategy_name] = {
            'predictions': predictions,
            'actual_results': actual_results,
            'prediction_dates': prediction_dates,
            'performance': performance,
            'backtest_config': {
                'start_date': self.start_date.isoformat() if self.start_date else None,
                'end_date': self.end_date.isoformat() if self.end_date else None,
                'min_training_periods': self.min_training_periods,
                'rebalance_frequency': rebalance_frequency
            }
        }
        
        print(f"✅ 回测完成，共 {len(predictions)} 个预测")
        
        return self.backtest_results[strategy_name]
    
    def _prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """数据预处理"""
        # 确保日期列存在并正确格式化
        if 'draw_date' not in data.columns:
            raise ValueError("数据必须包含 'draw_date' 列")
        
        data = data.copy()
        data['draw_date'] = pd.to_datetime(data['draw_date'])
        data = data.sort_values('draw_date').reset_index(drop=True)
        
        return data
    
    def _determine_backtest_range(self, data: pd.DataFrame) -> Tuple[int, int]:
        """确定回测范围"""
        # 开始索引
        if self.start_date:
            start_mask = data['draw_date'] >= self.start_date
            start_idx = data[start_mask].index[0] if start_mask.any() else self.min_training_periods
        else:
            start_idx = self.min_training_periods
        
        # 结束索引
        if self.end_date:
            end_mask = data['draw_date'] <= self.end_date
            end_idx = data[end_mask].index[-1] + 1 if end_mask.any() else len(data)
        else:
            end_idx = len(data)
        
        return start_idx, end_idx
    
    def _get_actual_result(self, row: pd.Series) -> Dict[str, Any]:
        """获取实际结果"""
        try:
            regular_numbers = json.loads(row['regular_numbers']) if isinstance(row['regular_numbers'], str) else row['regular_numbers']
            special_number = row['special_number']
            
            return {
                'regular_numbers': regular_numbers,
                'special_number': special_number,
                'all_numbers': regular_numbers + [special_number],
                'draw_date': row['draw_date']
            }
        except Exception as e:
            return {
                'regular_numbers': [],
                'special_number': 0,
                'all_numbers': [],
                'draw_date': row['draw_date'],
                'error': str(e)
            }
    
    def _calculate_performance(self, predictions: List[Dict], actual_results: List[Dict]) -> Dict[str, float]:
        """计算性能指标"""
        if not predictions or not actual_results:
            return {}
        
        metrics = {}
        
        # 准确率指标
        exact_matches = 0
        partial_matches = []
        hit_rates = []
        
        for pred, actual in zip(predictions, actual_results):
            if 'error' in actual:
                continue
                
            pred_numbers = pred.get('predicted_numbers', [])
            actual_numbers = actual.get('all_numbers', [])
            
            if not pred_numbers or not actual_numbers:
                continue
            
            # 完全匹配
            if set(pred_numbers) == set(actual_numbers):
                exact_matches += 1
            
            # 部分匹配
            matches = len(set(pred_numbers) & set(actual_numbers))
            partial_matches.append(matches)
            
            # 命中率
            hit_rate = matches / len(actual_numbers) if actual_numbers else 0
            hit_rates.append(hit_rate)
        
        # 计算指标
        total_predictions = len([p for p, a in zip(predictions, actual_results) if 'error' not in a])
        
        if total_predictions > 0:
            metrics['exact_match_rate'] = exact_matches / total_predictions
            metrics['average_partial_matches'] = np.mean(partial_matches) if partial_matches else 0
            metrics['average_hit_rate'] = np.mean(hit_rates) if hit_rates else 0
            metrics['std_hit_rate'] = np.std(hit_rates) if hit_rates else 0
            metrics['max_hit_rate'] = np.max(hit_rates) if hit_rates else 0
            metrics['min_hit_rate'] = np.min(hit_rates) if hit_rates else 0
            metrics['total_predictions'] = total_predictions
        
        return metrics
    
    def compare_strategies(self, strategies: List[BacktestingStrategy], 
                          historical_data: pd.DataFrame) -> Dict[str, Any]:
        """比较多个策略"""
        
        print(f"�� 开始比较 {len(strategies)} 个策略...")
        
        comparison_results = {}
        
        # 运行每个策略的回测
        for strategy in strategies:
            try:
                result = self.run_backtest(strategy, historical_data)
                comparison_results[strategy.get_strategy_name()] = result
            except Exception as e:
                print(f"❌ 策略 {strategy.get_strategy_name()} 回测失败: {e}")
                continue
        
        # 生成比较报告
        comparison_report = self._generate_comparison_report(comparison_results)
        
        return {
            'individual_results': comparison_results,
            'comparison_report': comparison_report
        }
    
    def _generate_comparison_report(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成比较报告"""
        
        if not results:
            return {}
        
        report = {
            'strategy_count': len(results),
            'metrics_comparison': {},
            'rankings': {}
        }
        
        # 提取所有策略的性能指标
        all_metrics = {}
        for strategy_name, result in results.items():
            performance = result.get('performance', {})
            all_metrics[strategy_name] = performance
        
        # 比较各项指标
        if all_metrics:
            metric_names = set()
            for metrics in all_metrics.values():
                metric_names.update(metrics.keys())
            
            for metric in metric_names:
                metric_values = {}
                for strategy, metrics in all_metrics.items():
                    if metric in metrics:
                        metric_values[strategy] = metrics[metric]
                
                if metric_values:
                    report['metrics_comparison'][metric] = {
                        'values': metric_values,
                        'best_strategy': max(metric_values.items(), key=lambda x: x[1])[0],
                        'worst_strategy': min(metric_values.items(), key=lambda x: x[1])[0],
                        'average': np.mean(list(metric_values.values())),
                        'std': np.std(list(metric_values.values()))
                    }
        
        # 综合排名
        if 'average_hit_rate' in report['metrics_comparison']:
            hit_rate_values = report['metrics_comparison']['average_hit_rate']['values']
            sorted_strategies = sorted(hit_rate_values.items(), key=lambda x: x[1], reverse=True)
            report['rankings']['by_hit_rate'] = [strategy for strategy, _ in sorted_strategies]
        
        return report
    
    def get_backtest_summary(self) -> Dict[str, Any]:
        """获取回测总结"""
        
        if not self.backtest_results:
            return {'message': '暂无回测结果'}
        
        summary = {
            'total_strategies': len(self.backtest_results),
            'strategies': {}
        }
        
        for strategy_name, result in self.backtest_results.items():
            performance = result.get('performance', {})
            summary['strategies'][strategy_name] = {
                'total_predictions': performance.get('total_predictions', 0),
                'average_hit_rate': performance.get('average_hit_rate', 0),
                'exact_match_rate': performance.get('exact_match_rate', 0),
                'backtest_period': {
                    'start': result['prediction_dates'][0].isoformat() if result['prediction_dates'] else None,
                    'end': result['prediction_dates'][-1].isoformat() if result['prediction_dates'] else None
                }
            }
        
        return summary
    
    def export_results(self, filepath: str, format: str = 'json'):
        """导出回测结果"""
        
        if format == 'json':
            # 转换日期为字符串
            export_data = {}
            for strategy_name, result in self.backtest_results.items():
                export_result = result.copy()
                export_result['prediction_dates'] = [
                    date.isoformat() for date in result['prediction_dates']
                ]
                export_data[strategy_name] = export_result
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        elif format == 'csv':
            # 导出为CSV格式
            all_data = []
            for strategy_name, result in self.backtest_results.items():
                for i, (pred, actual, date) in enumerate(zip(
                    result['predictions'], 
                    result['actual_results'], 
                    result['prediction_dates']
                )):
                    row = {
                        'strategy': strategy_name,
                        'prediction_date': date.isoformat(),
                        'predicted_numbers': json.dumps(pred.get('predicted_numbers', [])),
                        'actual_numbers': json.dumps(actual.get('all_numbers', [])),
                        'hit_count': len(set(pred.get('predicted_numbers', [])) & 
                                        set(actual.get('all_numbers', [])))
                    }
                    all_data.append(row)
            
            df = pd.DataFrame(all_data)
            df.to_csv(filepath, index=False, encoding='utf-8')
        
        print(f"✅ 回测结果已导出到: {filepath}")


class SimpleStrategy(BacktestingStrategy):
    """简单策略示例"""
    
    def __init__(self, strategy_name: str = "simple_random"):
        self.strategy_name = strategy_name
    
    def predict(self, historical_data: pd.DataFrame, current_date: datetime) -> Dict[str, Any]:
        """简单随机预测"""
        # 随机选择7个号码
        predicted_numbers = sorted(np.random.choice(range(1, 50), 7, replace=False).tolist())
        
        return {
            'predicted_numbers': predicted_numbers,
            'confidence': 0.5,
            'prediction_date': current_date.isoformat(),
            'strategy': self.strategy_name
        }
    
    def get_strategy_name(self) -> str:
        return self.strategy_name


class FrequencyBasedStrategy(BacktestingStrategy):
    """基于频率的策略"""
    
    def __init__(self, lookback_periods: int = 50):
        self.lookback_periods = lookback_periods
    
    def predict(self, historical_data: pd.DataFrame, current_date: datetime) -> Dict[str, Any]:
        """基于历史频率预测"""
        
        # 获取最近的数据
        recent_data = historical_data.tail(self.lookback_periods)
        
        # 统计号码频率
        number_freq = {}
        for _, row in recent_data.iterrows():
            try:
                regular_numbers = json.loads(row['regular_numbers']) if isinstance(row['regular_numbers'], str) else row['regular_numbers']
                special_number = row['special_number']
                all_numbers = regular_numbers + [special_number]
                
                for num in all_numbers:
                    number_freq[num] = number_freq.get(num, 0) + 1
            except:
                continue
        
        # 选择频率最高的7个号码
        if number_freq:
            sorted_numbers = sorted(number_freq.items(), key=lambda x: x[1], reverse=True)
            predicted_numbers = [num for num, freq in sorted_numbers[:7]]
            
            # 如果不足7个，随机补充
            if len(predicted_numbers) < 7:
                remaining = [i for i in range(1, 50) if i not in predicted_numbers]
                additional = np.random.choice(remaining, 7 - len(predicted_numbers), replace=False)
                predicted_numbers.extend(additional.tolist())
            
            predicted_numbers = sorted(predicted_numbers[:7])
        else:
            # 如果没有历史数据，随机选择
            predicted_numbers = sorted(np.random.choice(range(1, 50), 7, replace=False).tolist())
        
        return {
            'predicted_numbers': predicted_numbers,
            'confidence': 0.7,
            'prediction_date': current_date.isoformat(),
            'strategy': 'frequency_based',
            'lookback_periods': self.lookback_periods
        }
    
    def get_strategy_name(self) -> str:
        return f"frequency_based_{self.lookback_periods}"
