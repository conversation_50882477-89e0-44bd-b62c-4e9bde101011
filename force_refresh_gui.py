"""
强制刷新GUI，解决完美预测系统显示问题
"""

import os
import sys
import shutil
from pathlib import Path

def force_refresh_gui():
    """强制刷新GUI"""
    print("🔄 强制刷新GUI...")
    
    # 目标目录
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    
    if not windows_dir.exists():
        print("❌ Windows版本目录不存在")
        return False
    
    print(f"📁 目标目录: {windows_dir}")
    
    # 1. 清理Python缓存
    print("\n1. 清理Python缓存...")
    
    cache_dirs = [
        windows_dir / "__pycache__",
        windows_dir / "src" / "__pycache__"
    ]
    
    for cache_dir in cache_dirs:
        if cache_dir.exists():
            shutil.rmtree(cache_dir)
            print(f"✅ 清理缓存: {cache_dir}")
    
    # 清理.pyc文件
    for pyc_file in windows_dir.rglob("*.pyc"):
        pyc_file.unlink()
        print(f"✅ 删除缓存文件: {pyc_file}")
    
    # 2. 更新GUI文件中的PERFECT_SYSTEM_AVAILABLE检查
    print("\n2. 更新GUI文件...")
    
    gui_file = windows_dir / "lottery_prediction_gui.py"
    if gui_file.exists():
        # 读取GUI文件
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否需要更新
        if "PERFECT_SYSTEM_AVAILABLE = False" in content:
            # 强制设置为True
            content = content.replace(
                "PERFECT_SYSTEM_AVAILABLE = False",
                "PERFECT_SYSTEM_AVAILABLE = True"
            )
            
            # 写回文件
            with open(gui_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 强制设置 PERFECT_SYSTEM_AVAILABLE = True")
        else:
            print("✅ GUI文件状态正常")
    
    # 3. 创建强制启动脚本
    print("\n3. 创建强制启动脚本...")
    
    force_start_script = '''@echo off
chcp 65001 >nul
title 强制启动六合彩预测系统

echo 🔄 强制启动六合彩预测系统...
echo ================================

REM 清理Python缓存
echo 清理Python缓存...
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "src\\__pycache__" rmdir /s /q "src\\__pycache__"
del /s /q *.pyc >nul 2>&1

REM 设置环境变量强制刷新
set PYTHONDONTWRITEBYTECODE=1
set PYTHONPATH=%CD%;%CD%\\src

echo 启动GUI程序...
if exist "lottery_prediction_gui.py" (
    python lottery_prediction_gui.py
) else (
    echo 启动EXE版本...
    if exist "六合彩预测系统_v2.0.0.exe" (
        start "六合彩预测系统" "六合彩预测系统_v2.0.0.exe"
    ) else (
        echo 未找到可执行文件
        pause
        exit /b 1
    )
)

echo 程序已启动
pause
'''
    
    force_start_file = windows_dir / "强制启动.bat"
    with open(force_start_file, 'w', encoding='utf-8') as f:
        f.write(force_start_script)
    
    print(f"✅ 创建强制启动脚本: {force_start_file}")
    
    # 4. 创建PowerShell强制启动脚本
    print("\n4. 创建PowerShell强制启动脚本...")
    
    ps_force_script = '''# 强制启动六合彩预测系统 PowerShell脚本

Write-Host "🔄 强制启动六合彩预测系统..." -ForegroundColor Green
Write-Host "================================" -ForegroundColor Cyan

# 清理Python缓存
Write-Host "清理Python缓存..." -ForegroundColor Yellow
if (Test-Path "__pycache__") {
    Remove-Item "__pycache__" -Recurse -Force
    Write-Host "✅ 清理 __pycache__" -ForegroundColor Green
}
if (Test-Path "src\\__pycache__") {
    Remove-Item "src\\__pycache__" -Recurse -Force
    Write-Host "✅ 清理 src\\__pycache__" -ForegroundColor Green
}

# 删除.pyc文件
Get-ChildItem -Recurse -Filter "*.pyc" | Remove-Item -Force
Write-Host "✅ 清理.pyc文件" -ForegroundColor Green

# 设置环境变量
$env:PYTHONDONTWRITEBYTECODE = "1"
$env:PYTHONPATH = "$PWD;$PWD\\src"

Write-Host "启动GUI程序..." -ForegroundColor Yellow

try {
    if (Test-Path "lottery_prediction_gui.py") {
        python lottery_prediction_gui.py
    } elseif (Test-Path "六合彩预测系统_v2.0.0.exe") {
        Write-Host "启动EXE版本..." -ForegroundColor Yellow
        Start-Process "六合彩预测系统_v2.0.0.exe"
    } else {
        Write-Host "未找到可执行文件" -ForegroundColor Red
    }
} catch {
    Write-Host "启动失败: $_" -ForegroundColor Red
}

Write-Host "程序已启动" -ForegroundColor Green
Read-Host "按回车键退出"
'''
    
    ps_force_file = windows_dir / "强制启动.ps1"
    with open(ps_force_file, 'w', encoding='utf-8') as f:
        f.write(ps_force_script)
    
    print(f"✅ 创建PowerShell强制启动脚本: {ps_force_file}")
    
    # 5. 创建测试脚本
    print("\n5. 创建测试脚本...")
    
    test_script = '''"""
测试完美预测系统状态
"""

import sys
import os
from pathlib import Path

# 添加路径
sys.path.insert(0, str(Path.cwd()))
sys.path.insert(0, str(Path.cwd() / "src"))

def test_system():
    print("🧪 测试完美预测系统状态...")
    
    try:
        # 测试导入
        from special_number_predictor import SpecialNumberPredictor
        from consistent_predictor import ConsistentSpecialNumberPredictor
        from historical_backtest import HistoricalBacktestSystem
        from consistency_test import test_prediction_consistency
        from src.perfect_prediction_system import PerfectPredictionSystem
        from src.fusion_manager import FusionManager
        
        print("✅ 所有模块导入成功")
        
        # 测试初始化
        system = PerfectPredictionSystem()
        system.initialize_modules()
        
        print("✅ 完美预测系统初始化成功")
        print("✅ 融合管理器可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_system()
    if success:
        print("\\n🎉 系统状态正常！")
    else:
        print("\\n❌ 系统有问题！")
    
    input("\\n按回车键退出...")
'''
    
    test_file = windows_dir / "测试系统.py"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print(f"✅ 创建测试脚本: {test_file}")
    
    print("\n🎉 强制刷新完成！")
    return True

def create_usage_instructions():
    """创建使用说明"""
    print("\n" + "="*50)
    print("📝 使用说明")
    print("="*50)
    
    instructions = """
🚀 解决完美预测系统显示问题的方法

方法1: 使用强制启动脚本（推荐）
  双击: 强制启动.bat
  或右键运行: 强制启动.ps1

方法2: 手动清理缓存后启动
  1. 删除 __pycache__ 目录
  2. 删除所有 .pyc 文件
  3. 重新启动程序

方法3: 测试系统状态
  双击: 测试系统.py
  查看系统是否正常

🔧 如果问题仍然存在：
  1. 以管理员身份运行
  2. 重新安装Python依赖
  3. 检查防火墙设置
"""
    
    print(instructions)

def main():
    """主函数"""
    print("🔄 强制刷新GUI解决方案")
    print("=" * 40)
    
    # 执行强制刷新
    refresh_success = force_refresh_gui()
    
    if refresh_success:
        print("\n🎉 强制刷新成功！")
        
        # 显示使用说明
        create_usage_instructions()
        
        return True
    else:
        print("\n❌ 强制刷新失败")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*40}")
    if success:
        print("✅ 强制刷新完成！")
        print("现在使用 '强制启动.bat' 启动程序")
    else:
        print("❌ 强制刷新失败")
    
    input("\n按回车键退出...")
