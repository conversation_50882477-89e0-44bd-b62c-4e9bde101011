"""
快速测试数据抓取集成
"""

import sys
from pathlib import Path

# 添加路径
sys.path.insert(0, str(Path.cwd()))
sys.path.insert(0, str(Path.cwd() / "src"))

def test_import():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        from src.data_layer.data_import.web_scraper import WebScraper
        print("✅ WebScraper模块导入成功")
        
        scraper = WebScraper()
        print("✅ WebScraper实例创建成功")
        
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🖥️ 测试GUI集成...")
    
    try:
        gui_file = Path("lottery_prediction_gui.py")
        
        if not gui_file.exists():
            print("❌ GUI文件不存在")
            return False
        
        gui_content = gui_file.read_text(encoding='utf-8')
        
        # 检查关键功能
        checks = [
            ("数据抓取组件", "数据抓取" in gui_content),
            ("kjdata API", "kjdata" in gui_content),
            ("抓取按钮", "start_data_scraping" in gui_content),
            ("连接测试", "test_scrape_connection" in gui_content)
        ]
        
        print("📋 GUI集成检查:")
        all_passed = True
        for check_name, passed in checks:
            status = "✅" if passed else "❌"
            print(f"  {status} {check_name}")
            if not passed:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 数据抓取功能快速测试")
    print("=" * 50)
    
    # 测试模块导入
    import_ok = test_import()
    
    # 测试GUI集成
    gui_ok = test_gui_integration()
    
    print("\n📊 测试结果:")
    print(f"  模块导入: {'✅' if import_ok else '❌'}")
    print(f"  GUI集成: {'✅' if gui_ok else '❌'}")
    
    if import_ok and gui_ok:
        print("\n🎉 数据抓取功能集成成功！")
        print("💡 现在可以启动GUI程序测试数据抓取功能")
    else:
        print("\n⚠️ 部分功能可能存在问题")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
