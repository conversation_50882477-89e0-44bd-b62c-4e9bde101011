"""
测试性能图表修复
"""

import sys
import os
from pathlib import Path

def test_performance_chart_fix():
    """测试性能图表修复"""
    print("🧪 测试性能图表修复")
    print("=" * 50)
    
    # 切换到Windows版本目录
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    original_dir = Path.cwd()
    
    if not windows_dir.exists():
        print("❌ Windows版本目录不存在")
        return False
    
    try:
        os.chdir(windows_dir)
        
        # 添加路径
        sys.path.insert(0, str(Path.cwd()))
        sys.path.insert(0, str(Path.cwd() / "src"))
        
        print(f"📁 当前目录: {Path.cwd()}")
        
        # 1. 测试融合管理器
        print("\n1️⃣ 测试融合管理器...")
        try:
            from src.dynamic_fusion_manager_v3 import DynamicFusionManager
            fusion_manager = DynamicFusionManager()
            print("✅ 融合管理器创建成功")
        except Exception as e:
            print(f"❌ 融合管理器创建失败: {e}")
            return False
        
        # 2. 测试 get_module_statistics 方法
        print("\n2️⃣ 测试 get_module_statistics 方法...")
        try:
            stats = fusion_manager.get_module_statistics()
            
            print(f"📊 返回数据类型: {type(stats)}")
            
            if isinstance(stats, dict):
                print("✅ 返回字典类型")
                print(f"📋 模块数量: {len(stats)}")
                
                for module_name, module_stats in stats.items():
                    print(f"  📦 {module_name}: {type(module_stats)}")
                    
                    if isinstance(module_stats, dict):
                        required_fields = ['total_predictions', 'recent_hit_rate', 'recent_precision', 'stability', 'last_update']
                        for field in required_fields:
                            if field in module_stats:
                                print(f"    ✅ {field}: {module_stats[field]}")
                            else:
                                print(f"    ❌ 缺少字段: {field}")
                    else:
                        print(f"    ❌ 模块统计不是字典: {type(module_stats)}")
                        
            else:
                print(f"❌ 返回类型错误: {type(stats)}")
                print(f"📄 内容: {str(stats)[:200]}...")
                return False
                
        except Exception as e:
            print(f"❌ get_module_statistics 测试失败: {e}")
            return False
        
        # 3. 模拟性能图表刷新逻辑
        print("\n3️⃣ 模拟性能图表刷新逻辑...")
        try:
            # 模拟GUI中的刷新逻辑
            chart_type = "命中率趋势"
            period = "最近30天"
            
            module_stats = fusion_manager.get_module_statistics()
            
            chart_text = f"📊 {chart_type} - {period}\n"
            chart_text += "=" * 50 + "\n\n"
            
            if not module_stats:
                chart_text += "暂无性能数据\n"
            elif not isinstance(module_stats, dict):
                chart_text += f"⚠️ 数据格式错误: {type(module_stats)}\n"
            else:
                for module_name, stats in module_stats.items():
                    if stats and isinstance(stats, dict):
                        chart_text += f"🔧 {module_name}:\n"
                        chart_text += f"  总预测次数: {stats.get('total_predictions', 0)}\n"
                        chart_text += f"  最近命中率: {stats.get('recent_hit_rate', 0):.1%}\n"
                        chart_text += f"  最近精确度: {stats.get('recent_precision', 0):.3f}\n"
                        chart_text += f"  稳定性: {stats.get('stability', 0):.1%}\n"
                        chart_text += f"  最后更新: {stats.get('last_update', 'N/A')}\n\n"
            
            print("✅ 图表文本生成成功")
            print("📄 图表内容预览:")
            print(chart_text[:300] + "..." if len(chart_text) > 300 else chart_text)
            
        except Exception as e:
            print(f"❌ 图表刷新逻辑测试失败: {e}")
            return False
        
        # 4. 测试边界情况
        print("\n4️⃣ 测试边界情况...")
        try:
            # 测试空的性能跟踪器
            fusion_manager.performance_tracker = {}
            stats = fusion_manager.get_module_statistics()
            
            if isinstance(stats, dict):
                print("✅ 空性能跟踪器处理正常")
            else:
                print(f"❌ 空性能跟踪器处理异常: {type(stats)}")
                return False
            
            # 测试有数据的性能跟踪器
            fusion_manager.performance_tracker = {
                "predictions": [1, 2, 3, 4, 5],
                "confidences": [0.75, 0.82, 0.78, 0.85, 0.80],
                "stabilities": [0.70, 0.75, 0.72, 0.78, 0.74]
            }
            
            stats = fusion_manager.get_module_statistics()
            
            if isinstance(stats, dict):
                print("✅ 有数据的性能跟踪器处理正常")
                
                # 检查预测次数是否正确
                for module_name, module_stats in stats.items():
                    if module_stats.get('total_predictions') == 5:
                        print(f"  ✅ {module_name}: 预测次数正确")
                    else:
                        print(f"  ⚠️ {module_name}: 预测次数异常 ({module_stats.get('total_predictions')})")
            else:
                print(f"❌ 有数据的性能跟踪器处理异常: {type(stats)}")
                return False
                
        except Exception as e:
            print(f"❌ 边界情况测试失败: {e}")
            return False
        
        return True
        
    finally:
        os.chdir(original_dir)

def print_fix_summary():
    """打印修复总结"""
    print("\n" + "="*60)
    print("🔧 性能图表修复总结")
    print("="*60)
    
    print("\n✅ 修复内容:")
    print("  1. 增强了数据类型验证")
    print("  2. 添加了安全的字典访问")
    print("  3. 改进了错误处理和调试信息")
    print("  4. 增加了边界情况处理")
    
    print("\n🎯 修复特点:")
    print("  - 验证返回数据是否为字典类型")
    print("  - 安全地处理每个模块的统计信息")
    print("  - 提供详细的错误调试信息")
    print("  - 处理空数据和异常数据情况")
    
    print("\n🚀 使用方法:")
    print("  1. 启动GUI程序")
    print("  2. 切换到 '📈 性能监控' 标签页")
    print("  3. 点击 '🔄 刷新图表' 按钮")
    print("  4. 查看图表显示区域")

def main():
    """主函数"""
    print("🔧 性能图表修复测试")
    print("=" * 70)
    
    # 执行测试
    test_success = test_performance_chart_fix()
    
    if test_success:
        print("\n🎉 所有测试通过！")
        print("✅ 融合管理器正常")
        print("✅ get_module_statistics 方法正常")
        print("✅ 图表刷新逻辑正常")
        print("✅ 边界情况处理正常")
        
        # 显示修复总结
        print_fix_summary()
        
        return True
    else:
        print("\n❌ 部分测试失败")
        print("请检查错误信息并重新修复")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*70}")
    if success:
        print("🎉 修复测试完成：性能图表功能已修复！")
        print("现在可以正常使用刷新图表功能了！")
    else:
        print("❌ 修复测试失败：仍需进一步修复")
    
    input("\n按回车键退出...")
