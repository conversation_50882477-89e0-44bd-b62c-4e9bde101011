"""
快速修复验证测试
"""

import sys
import os
from pathlib import Path

def quick_fix_test():
    """快速修复验证测试"""
    print("🔧 快速修复验证测试")
    print("=" * 50)
    
    # 切换到Windows版本目录
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    original_dir = Path.cwd()
    
    if not windows_dir.exists():
        print("❌ Windows版本目录不存在")
        return False
    
    try:
        os.chdir(windows_dir)
        
        # 添加路径
        sys.path.insert(0, str(Path.cwd()))
        sys.path.insert(0, str(Path.cwd() / "src"))
        
        print(f"📁 测试目录: {Path.cwd()}")
        
        # 测试修复的导入问题
        test_results = {}
        
        # 1. 测试GUI类导入
        print("\n1️⃣ 测试GUI类导入...")
        try:
            from lottery_prediction_gui import LotteryPredictionGUI
            print("✅ LotteryPredictionGUI 导入成功")
            test_results['gui'] = True
        except Exception as e:
            print(f"❌ LotteryPredictionGUI 导入失败: {e}")
            test_results['gui'] = False
        
        # 2. 测试一致性预测器导入
        print("\n2️⃣ 测试一致性预测器导入...")
        try:
            from consistent_predictor import ConsistentPredictor
            predictor = ConsistentPredictor()
            print("✅ ConsistentPredictor 导入和创建成功")
            test_results['consistent'] = True
        except Exception as e:
            print(f"❌ ConsistentPredictor 测试失败: {e}")
            test_results['consistent'] = False
        
        # 3. 测试历史回测导入
        print("\n3️⃣ 测试历史回测导入...")
        try:
            from historical_backtest import HistoricalBacktest
            backtest = HistoricalBacktest()
            print("✅ HistoricalBacktest 导入和创建成功")
            test_results['backtest'] = True
        except Exception as e:
            print(f"❌ HistoricalBacktest 测试失败: {e}")
            test_results['backtest'] = False
        
        # 4. 测试融合管理器方法
        print("\n4️⃣ 测试融合管理器方法...")
        try:
            from src.dynamic_fusion_manager_v3 import DynamicFusionManager
            fusion_manager = DynamicFusionManager()
            
            # 测试简化的融合方法
            test_predictions = {
                "traditional_analysis": {
                    "numbers": [1, 5, 8, 12, 15, 18, 22, 25, 28, 31],
                    "confidence": 0.75
                },
                "machine_learning": {
                    "numbers": [3, 8, 12, 15, 22, 28, 35, 38, 42, 45],
                    "confidence": 0.84
                }
            }
            
            # 测试原方法（需要target_date）
            result1 = fusion_manager.fuse_predictions(test_predictions, "2025-06-25")
            print("✅ fuse_predictions (带target_date) 正常")
            
            # 测试简化方法（不需要target_date）
            result2 = fusion_manager.fuse_predictions_simple(test_predictions)
            print("✅ fuse_predictions_simple (无target_date) 正常")
            
            test_results['fusion'] = True
        except Exception as e:
            print(f"❌ 融合管理器方法测试失败: {e}")
            test_results['fusion'] = False
        
        # 5. 测试完整的预测模块创建
        print("\n5️⃣ 测试完整的预测模块创建...")
        try:
            from special_number_predictor import SpecialNumberPredictor
            from consistent_predictor import ConsistentPredictor
            from src.perfect_prediction_system import PerfectPredictionSystem
            from historical_backtest import HistoricalBacktest
            
            # 创建所有预测器
            special_predictor = SpecialNumberPredictor()
            consistent_predictor = ConsistentPredictor()
            perfect_system = PerfectPredictionSystem()
            backtest_system = HistoricalBacktest()
            
            print("✅ 所有预测模块创建成功")
            test_results['all_modules'] = True
        except Exception as e:
            print(f"❌ 预测模块创建失败: {e}")
            test_results['all_modules'] = False
        
        # 生成测试报告
        print("\n" + "="*50)
        print("📋 快速修复测试报告")
        print("="*50)
        
        total_tests = len(test_results)
        passed_tests = sum(test_results.values())
        success_rate = passed_tests / total_tests
        
        print(f"\n📊 测试结果:")
        print(f"  总测试项: {total_tests}")
        print(f"  通过测试: {passed_tests}")
        print(f"  失败测试: {total_tests - passed_tests}")
        print(f"  成功率: {success_rate:.1%}")
        
        print(f"\n📋 详细结果:")
        test_names = {
            'gui': 'GUI类导入',
            'consistent': '一致性预测器',
            'backtest': '历史回测',
            'fusion': '融合管理器方法',
            'all_modules': '完整模块创建'
        }
        
        for test_key, result in test_results.items():
            test_name = test_names.get(test_key, test_key)
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        if success_rate >= 0.8:
            print(f"\n🎉 修复效果良好！")
            print(f"✅ 主要问题已解决")
            print(f"✅ 系统可以正常使用")
        else:
            print(f"\n⚠️ 仍有问题需要解决")
            failed_tests = [test_names.get(k, k) for k, v in test_results.items() if not v]
            for failed_test in failed_tests:
                print(f"❌ {failed_test}")
        
        return success_rate >= 0.8
        
    finally:
        os.chdir(original_dir)

def main():
    """主函数"""
    print("🎯 快速修复验证测试")
    print("=" * 70)
    
    success = quick_fix_test()
    
    print("\n" + "="*70)
    if success:
        print("🎉 快速修复验证完成：修复效果良好！")
        print("✅ 主要导入问题已解决")
        print("✅ 方法参数问题已解决")
        print("✅ 系统可以正常使用")
        print("\n📋 建议:")
        print("  1. 重新运行全面系统测试")
        print("  2. 重新打包EXE文件")
        print("  3. 测试GUI功能")
    else:
        print("⚠️ 快速修复验证：仍有问题需要解决")
        print("📋 建议继续修复失败的测试项")
    
    return success

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
