"""
PowerShell创建的数据库修复脚本
"""
import sys
sys.path.insert(0, 'src')

import pandas as pd
from datetime import datetime, date
import os
import json

def powershell_database_fix():
    print("🔧 PowerShell数据库修复脚本")
    print("=" * 60)
    
    try:
        # 1. 初始化数据库
        print("📊 初始化数据库结构...")
        from src.data_layer.database.init_db import initialize_database
        
        if not initialize_database():
            print("❌ 数据库初始化失败")
            return False
        print("✅ 数据库结构创建成功")
        
        # 2. 创建组件
        print("📊 创建系统组件...")
        from src.data_layer.database.models import create_database_engine, get_session
        from src.data_layer.year_mapping.lunar_year_manager import LunarYearManager
        from src.data_layer.year_mapping.number_attribute_mapper import NumberAttributeMapper
        from src.data_layer.data_import.lottery_data_processor import LotteryDataProcessor
        
        engine = create_database_engine()
        session = get_session(engine)
        lunar_manager = LunarYearManager(session)
        attr_mapper = NumberAttributeMapper()
        data_processor = LotteryDataProcessor(lunar_manager, attr_mapper, session)
        print("✅ 组件创建成功")
        
        # 3. 读取数据
        print("📊 读取历史数据...")
        df = pd.read_csv("历史数据.csv", encoding='utf-8')
        print(f"✅ 读取 {len(df)} 条记录")
        
        # 4. 导入数据（小批量）
        print("📊 开始导入数据...")
        success_count = 0
        error_count = 0
        
        # 只导入前100条进行测试
        test_data = df.head(100)
        
        for index, row in test_data.iterrows():
            try:
                period_number = str(row['issue'])
                draw_date_str = str(row['date'])
                numbers_str = str(row['numbers']).replace('"', '').strip()
                special_number = int(row['special'])
                
                draw_date = datetime.strptime(draw_date_str, '%Y-%m-%d').date()
                regular_numbers = [int(x.strip()) for x in numbers_str.split(',')]
                
                # 验证数据
                if len(regular_numbers) != 6:
                    error_count += 1
                    continue
                
                if not all(1 <= n <= 49 for n in regular_numbers):
                    error_count += 1
                    continue
                
                if not (1 <= special_number <= 49):
                    error_count += 1
                    continue
                
                # 导入数据
                success = data_processor.manual_input_record(
                    period_number=period_number,
                    draw_date=draw_date,
                    regular_numbers=regular_numbers,
                    special_number=special_number
                )
                
                if success:
                    success_count += 1
                    if success_count % 20 == 0:
                        print(f"   已导入 {success_count} 条...")
                else:
                    error_count += 1
                    
            except Exception as e:
                error_count += 1
                if error_count <= 5:  # 只显示前5个错误
                    print(f"   错误 {index}: {e}")
        
        # 5. 验证结果
        from src.data_layer.database.models import LotteryData
        total_records = session.query(LotteryData).count()
        
        print(f"\n📊 导入结果:")
        print(f"   ✅ 成功: {success_count} 条")
        print(f"   ❌ 失败: {error_count} 条")
        print(f"   📋 数据库总记录: {total_records} 条")
        
        # 6. 显示样本数据
        if total_records > 0:
            latest_records = session.query(LotteryData).order_by(LotteryData.draw_date.desc()).limit(3).all()
            print(f"\n📋 最新3条记录:")
            for record in latest_records:
                regular_nums = json.loads(record.regular_numbers) if record.regular_numbers else []
                print(f"   {record.period_number} | {record.draw_date} | 正码: {regular_nums} | 特码: {record.special_number}")
                print(f"      生肖: {record.special_zodiac} | 五行: {record.special_wuxing}")
        
        session.close()
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = powershell_database_fix()
    if result:
        print("\n�� 数据库修复成功！")
    else:
        print("\n❌ 数据库修复失败！")
