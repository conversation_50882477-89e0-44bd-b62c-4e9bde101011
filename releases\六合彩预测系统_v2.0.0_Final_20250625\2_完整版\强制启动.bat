@echo off
chcp 65001 >nul
title 强制启动六合彩预测系统

echo 🔄 强制启动六合彩预测系统...
echo ================================

REM 清理Python缓存
echo 清理Python缓存...
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "src\__pycache__" rmdir /s /q "src\__pycache__"
del /s /q *.pyc >nul 2>&1

REM 设置环境变量强制刷新
set PYTHONDONTWRITEBYTECODE=1
set PYTHONPATH=%CD%;%CD%\src

echo 启动GUI程序...
if exist "lottery_prediction_gui.py" (
    python lottery_prediction_gui.py
) else (
    echo 启动EXE版本...
    if exist "六合彩预测系统_v2.0.0.exe" (
        start "六合彩预测系统" "六合彩预测系统_v2.0.0.exe"
    ) else (
        echo 未找到可执行文件
        pause
        exit /b 1
    )
)

echo 程序已启动
pause
