"""
特码预测器模块
提供特码预测的核心功能
"""
import random
import numpy as np
from typing import List, Dict, Any, Optional
import logging

class SpecialNumberPredictor:
    """特码预测器"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        """初始化特码预测器"""
        self.db_path = db_path  # 添加数据库路径属性
        self.logger = logging.getLogger(__name__)
        self.prediction_methods = [
            'traditional_analysis',
            'frequency_analysis',
            'pattern_analysis',
            'trend_analysis'
        ]

        # 预测参数
        self.hot_threshold = 5  # 热号阈值
        self.cold_threshold = 10  # 冷号阈值
        self.prediction_count = 16  # 预测号码数量

        self.logger.info("特码预测器初始化完成")
    
    def predict(self, target_date: str, historical_data: List[Dict] = None) -> Dict[str, Any]:
        """
        执行特码预测

        Args:
            target_date: 目标日期
            historical_data: 历史数据（可选，如果不提供则自动获取）

        Returns:
            预测结果字典
        """
        try:
            # 如果没有提供历史数据，则自动获取
            if historical_data is None:
                historical_data = self._get_historical_data(target_date)

            self.logger.info(f"开始特码预测，历史数据: {len(historical_data)} 条")

            # 分析历史数据
            analysis_result = self._analyze_historical_data(historical_data)

            # 生成预测号码
            predicted_numbers = self._generate_predictions(analysis_result)

            # 计算置信度
            confidence = self._calculate_confidence(analysis_result, predicted_numbers)

            result = {
                'predicted_numbers': predicted_numbers,
                'confidence': confidence,
                'analysis': analysis_result,
                'method': 'special_number_predictor',
                'target_date': target_date
            }

            self.logger.info(f"特码预测完成，预测号码: {predicted_numbers}")
            return result

        except Exception as e:
            self.logger.error(f"特码预测失败: {e}")
            return self._get_fallback_prediction(target_date)

    def _get_historical_data(self, target_date: str, days: int = 100) -> List[Dict]:
        """获取历史数据"""
        try:
            import sqlite3

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = """
            SELECT draw_date, special_number, period_number
            FROM lottery_results
            WHERE draw_date < ?
            ORDER BY draw_date DESC
            LIMIT ?
            """

            cursor.execute(query, (target_date, days))
            rows = cursor.fetchall()

            historical_data = []
            for row in rows:
                historical_data.append({
                    'draw_date': row[0],
                    'special_number': row[1],
                    'period_number': row[2]
                })

            conn.close()
            return historical_data

        except Exception as e:
            self.logger.error(f"获取历史数据失败: {e}")
            return []

    def _analyze_historical_data(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """分析历史数据"""
        if not historical_data:
            return self._get_default_analysis()
        
        # 统计号码频率
        number_frequency = {}
        recent_numbers = []
        
        for record in historical_data[-30:]:  # 最近30期
            special_number = record.get('special_number', 0)
            if special_number:
                number_frequency[special_number] = number_frequency.get(special_number, 0) + 1
                recent_numbers.append(special_number)
        
        # 分析热号和冷号
        hot_numbers = [num for num, freq in number_frequency.items() if freq >= self.hot_threshold]
        cold_numbers = [num for num in range(1, 50) if num not in number_frequency or number_frequency[num] <= 1]
        
        # 分析号码模式
        patterns = self._analyze_patterns(recent_numbers)
        
        return {
            'number_frequency': number_frequency,
            'hot_numbers': hot_numbers[:10],  # 前10个热号
            'cold_numbers': cold_numbers[:10],  # 前10个冷号
            'recent_numbers': recent_numbers[-10:],  # 最近10个号码
            'patterns': patterns,
            'total_records': len(historical_data)
        }
    
    def _analyze_patterns(self, numbers: List[int]) -> Dict[str, Any]:
        """分析号码模式"""
        if len(numbers) < 3:
            return {'trend': 'insufficient_data'}
        
        # 分析奇偶模式
        odd_count = sum(1 for num in numbers[-10:] if num % 2 == 1)
        even_count = len(numbers[-10:]) - odd_count
        
        # 分析大小模式
        big_count = sum(1 for num in numbers[-10:] if num > 24)
        small_count = len(numbers[-10:]) - big_count
        
        # 分析连续性
        consecutive_count = 0
        for i in range(1, len(numbers)):
            if abs(numbers[i] - numbers[i-1]) == 1:
                consecutive_count += 1
        
        return {
            'odd_ratio': odd_count / len(numbers[-10:]) if numbers else 0.5,
            'big_ratio': big_count / len(numbers[-10:]) if numbers else 0.5,
            'consecutive_ratio': consecutive_count / max(len(numbers) - 1, 1),
            'trend': 'ascending' if len(numbers) >= 3 and numbers[-1] > numbers[-3] else 'descending'
        }
    
    def _generate_predictions(self, analysis: Dict[str, Any]) -> List[int]:
        """生成预测号码"""
        predicted_numbers = []
        
        # 从热号中选择一些
        hot_numbers = analysis.get('hot_numbers', [])
        if hot_numbers:
            predicted_numbers.extend(random.sample(hot_numbers, min(6, len(hot_numbers))))
        
        # 从冷号中选择一些
        cold_numbers = analysis.get('cold_numbers', [])
        if cold_numbers:
            predicted_numbers.extend(random.sample(cold_numbers, min(4, len(cold_numbers))))
        
        # 根据模式选择一些号码
        patterns = analysis.get('patterns', {})
        pattern_numbers = self._generate_pattern_numbers(patterns)
        predicted_numbers.extend(pattern_numbers)
        
        # 随机补充到16个
        all_numbers = list(range(1, 50))
        remaining_numbers = [num for num in all_numbers if num not in predicted_numbers]
        
        while len(predicted_numbers) < self.prediction_count and remaining_numbers:
            num = random.choice(remaining_numbers)
            predicted_numbers.append(num)
            remaining_numbers.remove(num)
        
        # 确保不超过16个
        predicted_numbers = predicted_numbers[:self.prediction_count]
        
        # 排序
        predicted_numbers.sort()
        
        return predicted_numbers
    
    def _generate_pattern_numbers(self, patterns: Dict[str, Any]) -> List[int]:
        """根据模式生成号码"""
        pattern_numbers = []
        
        # 根据奇偶比例选择
        odd_ratio = patterns.get('odd_ratio', 0.5)
        if odd_ratio > 0.6:  # 偏向奇数
            pattern_numbers.extend([1, 3, 5, 7, 9])
        elif odd_ratio < 0.4:  # 偏向偶数
            pattern_numbers.extend([2, 4, 6, 8, 10])
        
        # 根据大小比例选择
        big_ratio = patterns.get('big_ratio', 0.5)
        if big_ratio > 0.6:  # 偏向大数
            pattern_numbers.extend([25, 30, 35, 40, 45])
        elif big_ratio < 0.4:  # 偏向小数
            pattern_numbers.extend([11, 15, 18, 20, 23])
        
        # 去重并限制数量
        pattern_numbers = list(set(pattern_numbers))
        return pattern_numbers[:6]
    
    def _calculate_confidence(self, analysis: Dict[str, Any], predicted_numbers: List[int]) -> float:
        """计算预测置信度"""
        base_confidence = 0.6
        
        # 根据历史数据量调整
        total_records = analysis.get('total_records', 0)
        if total_records > 100:
            base_confidence += 0.1
        elif total_records > 50:
            base_confidence += 0.05
        
        # 根据热号比例调整
        hot_numbers = analysis.get('hot_numbers', [])
        hot_in_prediction = len([num for num in predicted_numbers if num in hot_numbers])
        if hot_in_prediction > 0:
            base_confidence += 0.05 * hot_in_prediction
        
        # 限制在0.5-0.9之间
        confidence = max(0.5, min(0.9, base_confidence))
        
        return round(confidence, 3)
    
    def _get_default_analysis(self) -> Dict[str, Any]:
        """获取默认分析结果"""
        return {
            'number_frequency': {},
            'hot_numbers': [],
            'cold_numbers': list(range(1, 20)),
            'recent_numbers': [],
            'patterns': {'trend': 'neutral'},
            'total_records': 0
        }
    
    def _get_fallback_prediction(self, target_date: str = None) -> Dict[str, Any]:
        """获取备用预测结果"""
        # 生成随机预测
        predicted_numbers = sorted(random.sample(range(1, 50), self.prediction_count))
        
        return {
            'predicted_numbers': predicted_numbers,
            'confidence': 0.5,
            'analysis': self._get_default_analysis(),
            'method': 'fallback_prediction',
            'target_date': target_date,
            'note': '使用备用预测方法'
        }
    
    def get_prediction_info(self) -> Dict[str, Any]:
        """获取预测器信息"""
        return {
            'name': '特码预测器',
            'version': '1.0.0',
            'methods': self.prediction_methods,
            'prediction_count': self.prediction_count,
            'parameters': {
                'hot_threshold': self.hot_threshold,
                'cold_threshold': self.cold_threshold
            }
        }

# 兼容性别名
class SpecialNumberAnalyzer(SpecialNumberPredictor):
    """特码分析器 - SpecialNumberPredictor的别名"""
    pass

# 模块级别的便捷函数
def create_predictor() -> SpecialNumberPredictor:
    """创建特码预测器实例"""
    return SpecialNumberPredictor()

def predict_special_numbers(target_date: str, historical_data: List[Dict] = None) -> Dict[str, Any]:
    """预测特码号码的便捷函数"""
    predictor = create_predictor()
    return predictor.predict(target_date, historical_data)
