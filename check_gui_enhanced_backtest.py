"""
检查GUI中增强回测界面的功能按钮
"""
import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def check_enhanced_backtest_gui():
    """检查增强回测GUI功能"""
    print("🖥️ 检查GUI中的增强回测界面功能")
    print("=" * 50)
    
    try:
        # 导入GUI类
        from lottery_prediction_gui import LotteryPredictionGUI
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建GUI实例
        gui = LotteryPredictionGUI()
        
        # 检查增强回测标签页是否存在
        tab_count = gui.tab_widget.count()
        print(f"📋 总标签页数量: {tab_count}")
        
        # 查找增强回测标签页
        enhanced_backtest_tab_index = -1
        for i in range(tab_count):
            tab_text = gui.tab_widget.tabText(i)
            print(f"   {i+1}. {tab_text}")
            if "增强回测" in tab_text:
                enhanced_backtest_tab_index = i
        
        if enhanced_backtest_tab_index >= 0:
            print(f"\n✅ 找到增强回测标签页: 索引 {enhanced_backtest_tab_index}")
            
            # 切换到增强回测标签页
            gui.tab_widget.setCurrentIndex(enhanced_backtest_tab_index)
            
            # 检查增强回测界面的按钮
            print(f"\n🔧 检查增强回测界面的功能按钮:")
            
            # 检查各种按钮是否存在
            buttons_to_check = [
                ("enhanced_backtest_button", "🚀 开始增强回测"),
                ("stop_enhanced_backtest_button", "⏹️ 停止回测"),
                ("export_enhanced_results_button", "📊 导出结果"),
                ("run_optimal_selection_button", "🎯 运行最优模式选择"),
                ("apply_optimal_pattern_button", "✨ 应用最优模式到完美预测"),
                ("run_adaptive_optimization_button", "🧠 运行自适应参数优化"),
                ("apply_adaptive_config_button", "⚡ 应用自适应配置"),
                ("comprehensive_evaluation_button", "🎯 综合评估流程")
            ]
            
            found_buttons = []
            missing_buttons = []
            
            for button_attr, button_desc in buttons_to_check:
                if hasattr(gui, button_attr):
                    button = getattr(gui, button_attr)
                    button_text = button.text() if hasattr(button, 'text') else "无文本"
                    found_buttons.append((button_attr, button_text))
                    print(f"   ✅ {button_attr}: {button_text}")
                else:
                    missing_buttons.append((button_attr, button_desc))
                    print(f"   ❌ {button_attr}: 未找到")
            
            # 检查回测模式选择
            if hasattr(gui, 'enhanced_backtest_mode'):
                mode_combo = gui.enhanced_backtest_mode
                print(f"\n📋 回测模式选项:")
                for i in range(mode_combo.count()):
                    mode_text = mode_combo.itemText(i)
                    print(f"   {i+1}. {mode_text}")
            else:
                print(f"\n❌ 未找到回测模式选择控件")
            
            # 检查训练-验证-应用相关功能
            print(f"\n🔄 训练-验证-应用流程功能检查:")
            
            workflow_features = [
                ("最优模式选择", "run_optimal_selection_button"),
                ("自适应参数优化", "run_adaptive_optimization_button"), 
                ("综合评估流程", "comprehensive_evaluation_button"),
                ("应用最优模式", "apply_optimal_pattern_button"),
                ("应用自适应配置", "apply_adaptive_config_button")
            ]
            
            for feature_name, button_attr in workflow_features:
                if hasattr(gui, button_attr):
                    print(f"   ✅ {feature_name}: 功能按钮存在")
                else:
                    print(f"   ❌ {feature_name}: 功能按钮缺失")
            
            # 检查统计学支持功能
            print(f"\n📊 统计学支持功能检查:")
            
            statistical_features = [
                ("多次采样", "optimal_iterations"),
                ("参数优化", "run_adaptive_optimization_button"),
                ("性能评估", "enhanced_backtest_mode"),
                ("结果导出", "export_enhanced_results_button")
            ]
            
            for feature_name, attr_name in statistical_features:
                if hasattr(gui, attr_name):
                    print(f"   ✅ {feature_name}: 支持")
                else:
                    print(f"   ❌ {feature_name}: 不支持")
            
            # 总结
            print(f"\n📊 功能完整性总结:")
            print(f"   找到的按钮: {len(found_buttons)}/{len(buttons_to_check)}")
            print(f"   缺失的按钮: {len(missing_buttons)}")
            
            if len(found_buttons) >= len(buttons_to_check) * 0.8:
                print(f"   ✅ 增强回测界面功能基本完整")
            else:
                print(f"   ⚠️ 增强回测界面功能不完整")
            
        else:
            print(f"\n❌ 未找到增强回测标签页")
        
        # 不显示GUI，直接退出
        app.quit()
        
    except Exception as e:
        print(f"❌ 检查GUI时发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_enhanced_backtest_gui()
