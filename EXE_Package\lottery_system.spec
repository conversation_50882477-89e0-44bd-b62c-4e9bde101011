# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 获取当前目录
current_dir = Path.cwd()

a = Analysis(
    ['main.py'],
    pathex=[str(current_dir)],
    binaries=[],
    datas=[
        ('data', 'data'),
        ('config', 'config'),
        ('src', 'src'),
        ('*.json', '.'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'numpy',
        'pandas',
        'sklearn',
        'sklearn.ensemble',
        'sklearn.svm',
        'sklearn.neural_network',
        'sklearn.preprocessing',
        'sklearn.model_selection',
        'sklearn.feature_selection',
        'sklearn.decomposition',
        'xgboost',
        'sqlite3',
        'json',
        'datetime',
        'collections',
        'typing',
        'logging',
        'warnings',
        'src.perfect_prediction_system',
        'src.dynamic_fusion_manager_v3',
        'src.stability_optimizer_v3',
        'src.enhanced_feature_engineering_v2',
        'src.independent_modules.ml_module',
        'src.independent_modules.traditional_module',
        'src.independent_modules.zodiac_extended_module',
        'src.independent_modules.special_zodiac_module',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'tkinter',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='六合彩预测系统_v2.0.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 显示控制台用于调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if Path('icon.ico').exists() else None,
)
