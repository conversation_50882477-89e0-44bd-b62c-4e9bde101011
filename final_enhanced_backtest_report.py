#!/usr/bin/env python3
"""
最终增强回测功能检查报告
全面验证增强回测系统的各项功能
"""

import sys
import json
from datetime import datetime
from typing import Dict, Any

def test_simple_enhanced_backtest():
    """测试简化增强回测系统"""
    print("🎯 测试简化增强回测系统")
    print("-" * 50)
    
    try:
        from simple_enhanced_backtest import SimpleEnhancedBacktest
        
        # 初始化系统
        backtest_system = SimpleEnhancedBacktest()
        print("✅ 简化增强回测系统初始化成功")
        
        # 测试短期回测
        start_date = "2025-01-01"
        end_date = "2025-01-10"
        iterations = 3
        
        print(f"🔧 开始短期回测: {start_date} 到 {end_date}, {iterations}次迭代")
        
        result = backtest_system.run_enhanced_backtest(start_date, end_date, iterations)
        
        if result and "best_config" in result and not result.get("error"):
            best_hit_rate = result.get("best_hit_rate", 0)
            iteration_results = result.get("iteration_results", [])
            data_points = result.get("data_points", 0)
            
            print(f"✅ 短期回测完成")
            print(f"📊 最佳命中率: {best_hit_rate:.1%}")
            print(f"📊 迭代次数: {len(iteration_results)}")
            print(f"📊 数据点数: {data_points}")
            
            return True, {
                "success": True,
                "best_hit_rate": best_hit_rate,
                "iterations": len(iteration_results),
                "data_points": data_points,
                "test_type": "short_term"
            }
        else:
            error_msg = result.get("error", "未知错误") if result else "无结果"
            print(f"❌ 短期回测失败: {error_msg}")
            return False, {"error": error_msg}
            
    except Exception as e:
        print(f"❌ 简化增强回测系统测试失败: {e}")
        return False, {"error": str(e)}

def test_historical_simulation_predictor():
    """测试历史模拟预测器"""
    print("\n📊 测试历史模拟预测器")
    print("-" * 50)
    
    try:
        from src.historical_simulation_predictor import HistoricalSimulationPredictor
        
        # 初始化预测器
        predictor = HistoricalSimulationPredictor()
        print("✅ 历史模拟预测器初始化成功")
        
        # 测试历史模拟
        start_date = "2025-01-01"
        end_date = "2025-01-15"  # 扩大日期范围
        
        print(f"🔧 开始历史模拟: {start_date} 到 {end_date}")
        
        result = predictor.run_historical_simulation(start_date, end_date)
        
        if result and "simulation_results" in result and not result.get("error"):
            simulation_results = result.get("simulation_results", [])
            overall_hit_rate = result.get("overall_hit_rate", 0)
            total_predictions = result.get("total_predictions", 0)
            
            print(f"✅ 历史模拟完成")
            print(f"📊 模拟结果数: {len(simulation_results)}")
            print(f"📊 总预测次数: {total_predictions}")
            print(f"📊 整体命中率: {overall_hit_rate:.1%}")
            
            return True, {
                "success": True,
                "simulation_count": len(simulation_results),
                "total_predictions": total_predictions,
                "overall_hit_rate": overall_hit_rate
            }
        else:
            error_msg = result.get("error", "无有效模拟结果") if result else "无结果"
            print(f"❌ 历史模拟失败: {error_msg}")
            return False, {"error": error_msg}
            
    except Exception as e:
        print(f"❌ 历史模拟预测器测试失败: {e}")
        return False, {"error": str(e)}

def test_backtest_data_and_config():
    """测试回测数据和配置"""
    print("\n📊 测试回测数据和配置")
    print("-" * 50)
    
    try:
        import sqlite3
        import os
        
        # 检查数据库
        conn = sqlite3.connect("data/lottery.db")
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM lottery_results")
        total_records = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM lottery_results WHERE draw_date >= '2025-01-01'")
        recent_records = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ 数据库访问成功")
        print(f"📊 总记录数: {total_records}")
        print(f"📊 2025年数据: {recent_records}条")
        
        # 检查配置文件
        config_files = ["optimization_config.json", "optimal_config.json"]
        config_count = 0
        
        for config_file in config_files:
            if os.path.exists(config_file):
                config_count += 1
                print(f"✅ {config_file}: 存在")
            else:
                print(f"⚠️ {config_file}: 不存在")
        
        return True, {
            "success": True,
            "total_records": total_records,
            "recent_records": recent_records,
            "config_files": config_count
        }
        
    except Exception as e:
        print(f"❌ 回测数据和配置检查失败: {e}")
        return False, {"error": str(e)}

def test_backtest_performance():
    """测试回测性能"""
    print("\n⚡ 测试回测性能")
    print("-" * 50)
    
    try:
        from simple_enhanced_backtest import SimpleEnhancedBacktest
        import time
        
        backtest_system = SimpleEnhancedBacktest()
        
        # 性能测试1: 快速回测
        print("🔧 性能测试1: 快速回测")
        start_time = time.time()
        
        result1 = backtest_system.run_enhanced_backtest("2025-01-01", "2025-01-05", 2)
        
        duration1 = time.time() - start_time
        print(f"  快速回测耗时: {duration1:.2f}秒")
        
        # 性能测试2: 标准回测
        print("🔧 性能测试2: 标准回测")
        start_time = time.time()
        
        result2 = backtest_system.run_enhanced_backtest("2025-01-01", "2025-01-10", 3)
        
        duration2 = time.time() - start_time
        print(f"  标准回测耗时: {duration2:.2f}秒")
        
        # 性能评估
        performance_metrics = {
            "fast_test": {
                "duration": duration1,
                "success": result1 is not None and not result1.get("error"),
                "hit_rate": result1.get("best_hit_rate", 0) if result1 else 0
            },
            "standard_test": {
                "duration": duration2,
                "success": result2 is not None and not result2.get("error"),
                "hit_rate": result2.get("best_hit_rate", 0) if result2 else 0
            }
        }
        
        print(f"✅ 回测性能检查完成")
        print(f"📊 快速回测: {duration1:.2f}秒")
        print(f"📊 标准回测: {duration2:.2f}秒")
        
        # 性能评级
        if duration2 < 10:
            print(f"🎊 回测性能: 优秀")
        elif duration2 < 30:
            print(f"👍 回测性能: 良好")
        else:
            print(f"⚠️ 回测性能: 需要优化")
        
        return True, performance_metrics
        
    except Exception as e:
        print(f"❌ 回测性能检查失败: {e}")
        return False, {"error": str(e)}

def generate_final_backtest_report(test_results: Dict[str, Any]):
    """生成最终回测报告"""
    print("\n" + "=" * 60)
    print("📊 最终增强回测功能检查报告")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 统计测试结果
    total_tests = len(test_results)
    successful_tests = sum(1 for result in test_results.values() if result.get("success", False))
    
    print(f"📊 检查统计:")
    print(f"  总检查项: {total_tests}")
    print(f"  成功项目: {successful_tests}")
    print(f"  成功率: {successful_tests/total_tests:.1%}" if total_tests > 0 else "  成功率: 0%")
    print()
    
    # 详细结果
    print("📋 详细检查结果:")
    for test_name, result in test_results.items():
        status = "✅" if result.get("success", False) else "❌"
        print(f"  {status} {test_name}")
        
        if result.get("success", False):
            if "best_hit_rate" in result:
                print(f"    最佳命中率: {result['best_hit_rate']:.1%}")
            if "simulation_count" in result:
                print(f"    模拟次数: {result['simulation_count']}")
            if "total_records" in result:
                print(f"    数据记录: {result['total_records']}条")
            if "duration" in result:
                print(f"    执行时间: {result['duration']:.2f}秒")
    
    print()
    
    # 功能状态评估
    if successful_tests == total_tests:
        status = "🎊 优秀"
        description = "所有增强回测功能正常运行"
    elif successful_tests >= total_tests * 0.75:
        status = "👍 良好"
        description = "大部分增强回测功能正常"
    elif successful_tests >= total_tests * 0.5:
        status = "⚠️ 一般"
        description = "部分增强回测功能存在问题"
    else:
        status = "❌ 较差"
        description = "多个增强回测功能需要修复"
    
    print(f"🏥 增强回测系统状态: {status}")
    print(f"   {description}")
    print()
    
    # 核心功能可用性
    simple_backtest = test_results.get("simple_enhanced_backtest", {}).get("success", False)
    historical_simulation = test_results.get("historical_simulation_predictor", {}).get("success", False)
    data_config = test_results.get("backtest_data_and_config", {}).get("success", False)
    performance = test_results.get("backtest_performance", {}).get("success", False)
    
    print("🔍 核心功能可用性:")
    print(f"  简化增强回测: {'✅ 可用' if simple_backtest else '❌ 不可用'}")
    print(f"  历史模拟预测: {'✅ 可用' if historical_simulation else '❌ 不可用'}")
    print(f"  数据和配置: {'✅ 可用' if data_config else '❌ 不可用'}")
    print(f"  性能测试: {'✅ 通过' if performance else '❌ 未通过'}")
    print()
    
    # 性能分析
    if performance and "standard_test" in test_results.get("backtest_performance", {}):
        perf_data = test_results["backtest_performance"]["standard_test"]
        duration = perf_data.get("duration", 0)
        hit_rate = perf_data.get("hit_rate", 0)
        
        print("⚡ 性能分析:")
        print(f"  标准回测时间: {duration:.2f}秒")
        print(f"  回测命中率: {hit_rate:.1%}")
        
        if duration < 10:
            print(f"  性能评级: 🎊 优秀")
        elif duration < 30:
            print(f"  性能评级: 👍 良好")
        else:
            print(f"  性能评级: ⚠️ 需要优化")
    
    print()
    
    # 数据统计
    if data_config:
        data_info = test_results.get("backtest_data_and_config", {})
        total_records = data_info.get("total_records", 0)
        recent_records = data_info.get("recent_records", 0)
        
        print("📊 数据统计:")
        print(f"  历史数据总量: {total_records}条")
        print(f"  2025年数据: {recent_records}条")
        print(f"  数据充足度: {'✅ 充足' if recent_records >= 50 else '⚠️ 有限'}")
    
    print()
    
    # 建议和总结
    print("💡 建议:")
    if successful_tests == total_tests:
        print("  • 所有增强回测功能正常，可以进行全面回测分析")
        print("  • 建议定期使用回测验证和优化预测策略")
        print("  • 可以尝试更长期的回测来发现最优参数")
        print("  • 建议将回测结果应用到实际预测中")
    elif successful_tests >= total_tests * 0.75:
        print("  • 核心回测功能可用，可以进行基础回测分析")
        print("  • 建议修复剩余问题以获得完整功能")
        print("  • 可以使用简化回测系统进行参数优化")
    else:
        failed_tests = [name for name, result in test_results.items() if not result.get("success", False)]
        if failed_tests:
            print(f"  • 需要修复: {', '.join(failed_tests)}")
        print("  • 建议优先修复核心回测功能")
        print("  • 检查数据库连接和配置文件")
    
    print()
    print("🎯 总结:")
    print("  增强回测功能检查完成")
    print(f"  系统状态: {status.split()[1]}")
    
    if successful_tests >= total_tests * 0.75:
        print("  ✅ 增强回测系统基本可用")
        print("  ✅ 可以进行回测分析和参数优化")
        print("  ✅ 支持多种预测方法的回测验证")
    else:
        print("  ⚠️ 增强回测系统需要进一步修复")
        print("  ⚠️ 建议优先解决核心功能问题")
    
    print()
    print("🚀 增强回测系统功能验证完成！")

def main():
    """主检查函数"""
    print("🎊 最终增强回测功能检查开始")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_results = {}
    
    try:
        # 测试简化增强回测系统
        success1, result1 = test_simple_enhanced_backtest()
        test_results["simple_enhanced_backtest"] = result1
        
        # 测试历史模拟预测器
        success2, result2 = test_historical_simulation_predictor()
        test_results["historical_simulation_predictor"] = result2
        
        # 测试回测数据和配置
        success3, result3 = test_backtest_data_and_config()
        test_results["backtest_data_and_config"] = result3
        
        # 测试回测性能
        success4, result4 = test_backtest_performance()
        test_results["backtest_performance"] = result4
        
        # 生成最终报告
        generate_final_backtest_report(test_results)
        
        # 保存检查结果
        with open("final_enhanced_backtest_check_results.json", "w", encoding="utf-8") as f:
            json.dump({
                "test_results": test_results,
                "check_time": datetime.now().isoformat(),
                "summary": {
                    "total_tests": len(test_results),
                    "successful_tests": sum(1 for r in test_results.values() if r.get("success", False)),
                    "success_rate": sum(1 for r in test_results.values() if r.get("success", False)) / len(test_results)
                }
            }, f, ensure_ascii=False, indent=2)
        
        print("📄 检查结果已保存到: final_enhanced_backtest_check_results.json")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
