"""
数据库初始化脚本
"""
import os
from pathlib import Path
from sqlalchemy import create_engine
from loguru import logger

def initialize_database():
    """初始化数据库"""
    try:
        # 确保数据目录存在
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        
        # 创建数据库引擎
        database_url = "sqlite:///data/lottery.db"
        engine = create_engine(database_url, echo=False)
        
        # 导入模型
        from src.data_layer.database.models import Base, create_tables, get_session
        from src.data_layer.year_mapping.lunar_year_manager import LunarYearManager
        
        # 创建所有表
        create_tables(engine)
        logger.info("数据库表创建完成")
        
        # 获取数据库会话
        session = get_session(engine)
        
        # 初始化农历年份数据
        lunar_manager = LunarYearManager(session)
        lunar_manager.initialize_lunar_years(2000, 2050)
        
        session.close()
        logger.info("数据库初始化完成")
        
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False

if __name__ == "__main__":
    initialize_database()
