import sys
sys.path.insert(0, 'src')
from datetime import date
from src.data_layer.database.models import create_database_engine, get_session, LotteryData
from src.data_layer.year_mapping.lunar_year_manager import <PERSON>r<PERSON>earManager
from src.data_layer.year_mapping.number_attribute_mapper import Number<PERSON>ttributeMapper
from src.algorithm_layer.model_fusion.cross_validation_fusion import CrossValidationFusion

print("🎯 900期数据预测测试")
engine = create_database_engine()
session = get_session(engine)
total_records = session.query(LotteryData).count()
print(f"✅ 数据库: {total_records}期")

lunar_manager = LunarYearManager(session)
attr_mapper = NumberAttributeMapper()
fusion_system = CrossValidationFusion(session, lunar_manager, attr_mapper)

prediction_result = fusion_system.comprehensive_prediction(recent_periods=50, target_date=date(2025, 6, 21))
final_numbers = prediction_result['final_recommended_numbers']
confidence = prediction_result['confidence_level']

print(f"🎯 推荐号码: {final_numbers[:10]}")
print(f"📊 置信度: {confidence:.1%}")
session.close()
