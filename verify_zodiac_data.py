import sys
sys.path.insert(0, 'src')

from src.algorithm_layer.zodiac_extended.zodiac_extended_analyzer import ZodiacExtendedAnalyzer
from src.data_layer.year_mapping.number_attribute_mapper import NumberAttributeMapper

# 测试生肖扩展分析器
analyzer = ZodiacExtendedAnalyzer()
mapper = NumberAttributeMapper()

print("🔍 验证生肖维度数据:")
print("=" * 50)

# 验证2025年生肖映射
print("\n📊 2025年生肖映射验证:")
test_numbers = [1, 13, 25, 37, 49]  # 蛇年的蛇生肖号码
for num in test_numbers:
    zodiac = analyzer.get_zodiac_by_number(num, 2025)
    print(f"   号码{num:2d}: {zodiac}")

# 验证琴棋书画分类
print("\n📊 琴棋书画分类验证:")
for category, zodiacs in analyzer.qin_qi_shu_hua.items():
    print(f"   {category}: {', '.join(zodiacs)}")

# 验证四季分类
print("\n📊 四季分类验证:")
for season, zodiacs in analyzer.four_seasons.items():
    print(f"   {season}: {', '.join(zodiacs)}")

# 验证波色分类
print("\n📊 波色分类验证:")
for color, zodiacs in analyzer.wave_colors.items():
    print(f"   {color}: {', '.join(zodiacs)}")

# 验证号码属性
print("\n📊 号码属性验证:")
test_num = 1
attrs = mapper.get_all_attributes(test_num, 2025)
print(f"   号码{test_num}: 生肖={attrs['zodiac']}, 五行={attrs['element']}, 颜色={attrs['color']}")

print("\n✅ 生肖维度数据验证完成！")
