"""
号码属性映射器 - 更新版本
基于用户提供的准确数据
"""
from typing import Dict, List, Optional
from datetime import date

class NumberAttributeMapper:
    def __init__(self):
        """初始化号码属性映射器"""
        self.init_zodiac_mappings()
        self.init_element_mappings()
        self.init_color_mappings()
    
    def init_zodiac_mappings(self):
        """初始化生肖映射 - 按农历年份"""
        self.zodiac_mappings = {
            # 2025年 - 蛇年 (蛇为头肖)
            2025: {
                '鼠': [6, 18, 30, 42],
                '牛': [5, 17, 29, 41],
                '虎': [4, 16, 28, 40],
                '兔': [3, 15, 27, 39],
                '龙': [2, 14, 26, 38],
                '蛇': [1, 13, 25, 37, 49],  # 头肖，5个号码
                '马': [12, 24, 36, 48],
                '羊': [11, 23, 35, 47],
                '猴': [10, 22, 34, 46],
                '鸡': [9, 21, 33, 45],
                '狗': [8, 20, 32, 44],
                '猪': [7, 19, 31, 43]
            },
            # 2024年 - 龙年 (龙为头肖)
            2024: {
                '鼠': [5, 17, 29, 41],
                '牛': [4, 16, 28, 40],
                '虎': [3, 15, 27, 39],
                '兔': [2, 14, 26, 38],
                '龙': [1, 13, 25, 37, 49],  # 头肖，5个号码
                '蛇': [12, 24, 36, 48],
                '马': [11, 23, 35, 47],
                '羊': [10, 22, 34, 46],
                '猴': [9, 21, 33, 45],
                '鸡': [8, 20, 32, 44],
                '狗': [7, 19, 31, 43],
                '猪': [6, 18, 30, 42]
            },
            # 2023年 - 兔年 (兔为头肖)
            2023: {
                '鼠': [4, 16, 28, 40],
                '牛': [3, 15, 27, 39],
                '虎': [2, 14, 26, 38],
                '兔': [1, 13, 25, 37, 49],  # 头肖，5个号码
                '龙': [12, 24, 36, 48],
                '蛇': [11, 23, 35, 47],
                '马': [10, 22, 34, 46],
                '羊': [9, 21, 33, 45],
                '猴': [8, 20, 32, 44],
                '鸡': [7, 19, 31, 43],
                '狗': [6, 18, 30, 42],
                '猪': [5, 17, 29, 41]
            },
            # 2022年 - 虎年 (虎为头肖)
            2022: {
                '鼠': [3, 15, 27, 39],
                '牛': [2, 14, 26, 38],
                '虎': [1, 13, 25, 37, 49],  # 头肖，5个号码
                '兔': [12, 24, 36, 48],
                '龙': [11, 23, 35, 47],
                '蛇': [10, 22, 34, 46],
                '马': [9, 21, 33, 45],
                '羊': [8, 20, 32, 44],
                '猴': [7, 19, 31, 43],
                '鸡': [6, 18, 30, 42],
                '狗': [5, 17, 29, 41],
                '猪': [4, 16, 28, 40]
            }
        }
    
    def init_element_mappings(self):
        """初始化五行映射 - 按农历年份"""
        self.element_mappings = {
            2025: {
                '金': [3, 4, 11, 12, 25, 26, 33, 34, 41, 42],
                '木': [7, 8, 15, 16, 23, 24, 37, 38, 45, 46],
                '水': [13, 14, 20, 21, 28, 29, 42, 43],
                '火': [1, 2, 9, 10, 17, 18, 31, 32, 39, 40, 47, 48],
                '土': [5, 6, 19, 20, 27, 28, 35, 36, 49]
            },
            2024: {
                '金': [2, 3, 10, 11, 24, 25, 32, 33, 40, 41],
                '木': [6, 7, 14, 15, 22, 23, 36, 37, 44, 45],
                '水': [12, 13, 20, 21, 28, 29, 42, 43],
                '火': [1, 8, 9, 16, 17, 30, 31, 38, 39, 46, 47],
                '土': [4, 5, 18, 19, 26, 27, 34, 35, 48, 49]
            },
            2023: {
                '金': [1, 2, 9, 10, 23, 24, 31, 32, 39, 40],
                '木': [5, 6, 13, 14, 21, 22, 35, 36, 43, 44],
                '水': [11, 12, 19, 20, 27, 28, 41, 42, 49],
                '火': [7, 8, 15, 16, 29, 30, 37, 38, 45, 46],
                '土': [3, 4, 17, 18, 25, 26, 33, 34, 47, 48]
            }
        }
    
    def init_color_mappings(self):
        """初始化颜色映射"""
        self.color_mappings = {
            '红': [1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46],
            '蓝': [3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48],
            '绿': [5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49]
        }
    
    def get_zodiac(self, number: int, lunar_year: int) -> str:
        """获取号码对应的生肖"""
        if lunar_year not in self.zodiac_mappings:
            # 如果年份不在映射中，使用最近的年份
            available_years = sorted(self.zodiac_mappings.keys())
            lunar_year = min(available_years, key=lambda x: abs(x - lunar_year))
        
        year_mapping = self.zodiac_mappings[lunar_year]
        for zodiac, numbers in year_mapping.items():
            if number in numbers:
                return zodiac
        return '未知'
    
    def get_element(self, number: int, lunar_year: int = 2025) -> str:
        """获取号码对应的五行"""
        if lunar_year not in self.element_mappings:
            lunar_year = 2025  # 默认使用2025年
        
        year_mapping = self.element_mappings[lunar_year]
        for element, numbers in year_mapping.items():
            if number in numbers:
                return element
        return '未知'
    
    def get_color(self, number: int) -> str:
        """获取号码对应的颜色"""
        for color, numbers in self.color_mappings.items():
            if number in numbers:
                return color
        return '未知'
    
    def get_all_attributes(self, number: int, lunar_year: int) -> Dict[str, str]:
        """获取号码的所有属性"""
        return {
            'zodiac': self.get_zodiac(number, lunar_year),
            'element': self.get_element(number, lunar_year),
            'color': self.get_color(number)
        }
