# 验证生肖映射数据 - PowerShell版本
# 对比数据库数据与标准数据的一致性

param(
    [string]$Year = "2025",
    [int]$TestNumber = 0
)

Clear-Host
Write-Host "🎯 生肖映射数据验证工具" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Yellow
Write-Host "验证年份: $Year" -ForegroundColor Green
Write-Host "验证时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Green
Write-Host ""

# 标准生肖映射数据 (与您提供的数据完全一致)
$StandardZodiacMappings = @{
    "2023" = @{
        "鼠" = @(4, 16, 28, 40)
        "牛" = @(3, 15, 27, 39)
        "虎" = @(2, 14, 26, 38)
        "兔" = @(1, 13, 25, 37, 49)
        "龙" = @(12, 24, 36, 48)
        "蛇" = @(11, 23, 35, 47)
        "马" = @(10, 22, 34, 46)
        "羊" = @(9, 21, 33, 45)
        "猴" = @(8, 20, 32, 44)
        "鸡" = @(7, 19, 31, 43)
        "狗" = @(6, 18, 30, 42)
        "猪" = @(5, 17, 29, 41)
    }
    "2024" = @{
        "鼠" = @(5, 17, 29, 41)
        "牛" = @(4, 16, 28, 40)
        "虎" = @(3, 15, 27, 39)
        "兔" = @(2, 14, 26, 38)
        "龙" = @(1, 13, 25, 37, 49)
        "蛇" = @(12, 24, 36, 48)
        "马" = @(11, 23, 35, 47)
        "羊" = @(10, 22, 34, 46)
        "猴" = @(9, 21, 33, 45)
        "鸡" = @(8, 20, 32, 44)
        "狗" = @(7, 19, 31, 43)
        "猪" = @(6, 18, 30, 42)
    }
    "2025" = @{
        "鼠" = @(6, 18, 30, 42)
        "牛" = @(5, 17, 29, 41)
        "虎" = @(4, 16, 28, 40)
        "兔" = @(3, 15, 27, 39)
        "龙" = @(2, 14, 26, 38)
        "蛇" = @(1, 13, 25, 37, 49)
        "马" = @(12, 24, 36, 48)
        "羊" = @(11, 23, 35, 47)
        "猴" = @(10, 22, 34, 46)
        "鸡" = @(9, 21, 33, 45)
        "狗" = @(8, 20, 32, 44)
        "猪" = @(7, 19, 31, 43)
    }
}

# 标准五行映射数据
$StandardWuXingMappings = @{
    "2023" = @{
        "金" = @(1, 2, 9, 10, 23, 24, 31, 32, 39, 40)
        "木" = @(5, 6, 13, 14, 21, 22, 35, 36, 43, 44)
        "水" = @(11, 12, 19, 20, 27, 28, 41, 42, 49)
        "火" = @(7, 8, 15, 16, 29, 30, 37, 38, 45, 46)
        "土" = @(3, 4, 17, 18, 25, 26, 33, 34, 47, 48)
    }
    "2024" = @{
        "金" = @(2, 3, 10, 11, 24, 25, 32, 33, 40, 41)
        "木" = @(6, 7, 14, 15, 22, 23, 36, 37, 44, 45)
        "水" = @(12, 13, 20, 21, 28, 29, 42, 43)
        "火" = @(1, 8, 9, 16, 17, 30, 31, 38, 39, 46, 47)
        "土" = @(4, 5, 18, 19, 26, 27, 34, 35, 48, 49)
    }
    "2025" = @{
        "金" = @(3, 4, 11, 12, 25, 26, 33, 34, 41, 42)
        "木" = @(7, 8, 15, 16, 23, 24, 37, 38, 45, 46)
        "水" = @(13, 14, 21, 22, 29, 30, 43, 44)
        "火" = @(1, 2, 9, 10, 17, 18, 31, 32, 39, 40, 47, 48)
        "土" = @(5, 6, 19, 20, 27, 28, 35, 36, 49)
    }
}

# 多维度生肖属性
$ZodiacAttributes = @{
    "日夜" = @{
        "日肖" = @("兔", "龙", "蛇", "马", "羊", "猴")
        "夜肖" = @("鼠", "牛", "虎", "鸡", "狗", "猪")
    }
    "阴阳" = @{
        "阴肖" = @("鼠", "龙", "蛇", "马", "狗", "猪")
        "阳肖" = @("牛", "虎", "兔", "羊", "猴", "鸡")
    }
    "家野" = @{
        "家肖" = @("牛", "马", "羊", "狗", "鸡", "猪")
        "野肖" = @("鼠", "虎", "兔", "龙", "蛇", "猴")
    }
    "波色" = @{
        "红肖" = @("鼠", "兔", "马", "鸡")
        "绿肖" = @("牛", "龙", "羊", "狗")
        "蓝肖" = @("虎", "蛇", "猴", "猪")
    }
    "季节" = @{
        "春" = @("虎", "兔", "龙")
        "夏" = @("蛇", "马", "羊")
        "秋" = @("猴", "狗", "鸡")
        "冬" = @("鼠", "牛", "猪")
    }
}

function Get-ZodiacFromNumber {
    param([int]$Number, [string]$Year)
    
    $mapping = $StandardZodiacMappings[$Year]
    foreach ($zodiac in $mapping.Keys) {
        if ($mapping[$zodiac] -contains $Number) {
            return $zodiac
        }
    }
    return "未知"
}

function Get-WuXingFromNumber {
    param([int]$Number, [string]$Year)
    
    $mapping = $StandardWuXingMappings[$Year]
    foreach ($element in $mapping.Keys) {
        if ($mapping[$element] -contains $Number) {
            return $element
        }
    }
    return "未知"
}

function Test-NumberAnalysis {
    param([int]$Number, [string]$Year)
    
    Write-Host "🔍 号码 $Number 的完整分析 ($Year 年)" -ForegroundColor Yellow
    Write-Host "-" * 50 -ForegroundColor Gray
    
    # 基础映射
    $zodiac = Get-ZodiacFromNumber -Number $Number -Year $Year
    $wuxing = Get-WuXingFromNumber -Number $Number -Year $Year
    
    Write-Host "🐲 生肖: $zodiac" -ForegroundColor Green
    Write-Host "⚡ 五行: $wuxing" -ForegroundColor Cyan
    
    # 多维度属性分析
    Write-Host ""
    Write-Host "📊 多维度属性分析:" -ForegroundColor Magenta
    
    foreach ($category in $ZodiacAttributes.Keys) {
        foreach ($subcategory in $ZodiacAttributes[$category].Keys) {
            if ($ZodiacAttributes[$category][$subcategory] -contains $zodiac) {
                Write-Host "  $category : $subcategory" -ForegroundColor White
                break
            }
        }
    }
    
    # 号码属性
    Write-Host ""
    Write-Host "🔢 号码属性:" -ForegroundColor Magenta
    Write-Host "  大小: $(if ($Number -ge 25) { '大数' } else { '小数' })" -ForegroundColor White
    Write-Host "  单双: $(if ($Number % 2 -eq 1) { '单数' } else { '双数' })" -ForegroundColor White
    
    # 合数
    $digitSum = [string]$Number -split '' | Where-Object { $_ -ne '' } | ForEach-Object { [int]$_ } | Measure-Object -Sum | Select-Object -ExpandProperty Sum
    Write-Host "  合数: $digitSum" -ForegroundColor White
    
    # 段位
    $segment = [Math]::Ceiling($Number / 7)
    Write-Host "  段位: 第 $segment 段" -ForegroundColor White
}

function Show-YearMapping {
    param([string]$Year)
    
    Write-Host "📊 $Year 年生肖映射验证" -ForegroundColor Yellow
    Write-Host "-" * 50 -ForegroundColor Gray
    
    $mapping = $StandardZodiacMappings[$Year]
    $yearName = switch ($Year) {
        "2023" { "兔年" }
        "2024" { "龙年" }
        "2025" { "蛇年" }
        default { "未知年" }
    }
    
    Write-Host "$Year 年 $yearName" -ForegroundColor Green
    Write-Host ""
    
    foreach ($zodiac in @("鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪")) {
        if ($mapping.ContainsKey($zodiac)) {
            $numbers = $mapping[$zodiac] -join "-"
            Write-Host "$zodiac $numbers" -ForegroundColor White
        }
    }
}

function Show-WuXingMapping {
    param([string]$Year)
    
    Write-Host ""
    Write-Host "⚡ $Year 年五行映射" -ForegroundColor Yellow
    Write-Host "-" * 50 -ForegroundColor Gray
    
    $mapping = $StandardWuXingMappings[$Year]
    
    foreach ($element in @("金", "木", "水", "火", "土")) {
        if ($mapping.ContainsKey($element)) {
            $numbers = $mapping[$element] -join ", "
            $count = $mapping[$element].Count
            Write-Host "$element ($count 个): $numbers" -ForegroundColor White
        }
    }
}

function Test-CrossAnalysis {
    param([string]$Year)
    
    Write-Host ""
    Write-Host "🔀 $Year 年交叉分析" -ForegroundColor Yellow
    Write-Host "-" * 50 -ForegroundColor Gray
    
    # 生肖与五行交叉
    $zodiacMapping = $StandardZodiacMappings[$Year]
    $wuxingMapping = $StandardWuXingMappings[$Year]
    
    Write-Host "🌟 生肖与五行交叉分析:" -ForegroundColor Magenta
    
    foreach ($zodiac in @("鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪")) {
        if ($zodiacMapping.ContainsKey($zodiac)) {
            $zodiacNumbers = $zodiacMapping[$zodiac]
            $wuxingCount = @{}
            
            foreach ($num in $zodiacNumbers) {
                foreach ($element in $wuxingMapping.Keys) {
                    if ($wuxingMapping[$element] -contains $num) {
                        if ($wuxingCount.ContainsKey($element)) {
                            $wuxingCount[$element]++
                        } else {
                            $wuxingCount[$element] = 1
                        }
                        break
                    }
                }
            }
            
            $dominantElement = ($wuxingCount.GetEnumerator() | Sort-Object Value -Descending | Select-Object -First 1).Name
            $elementCount = $wuxingCount[$dominantElement]
            Write-Host "  $zodiac : 主要五行 $dominantElement ($elementCount/$($zodiacNumbers.Count))" -ForegroundColor White
        }
    }
}

# 主程序执行
if ($TestNumber -gt 0) {
    Test-NumberAnalysis -Number $TestNumber -Year $Year
} else {
    Show-YearMapping -Year $Year
    Show-WuXingMapping -Year $Year
    Test-CrossAnalysis -Year $Year
}

Write-Host ""
Write-Host "✅ 验证完成!" -ForegroundColor Green
Write-Host ""
Write-Host "💡 使用示例:" -ForegroundColor Cyan
Write-Host "  .\verify_zodiac_mappings.ps1 -Year 2025" -ForegroundColor Gray
Write-Host "  .\verify_zodiac_mappings.ps1 -Year 2024 -TestNumber 25" -ForegroundColor Gray
Write-Host "  .\verify_zodiac_mappings.ps1 -Year 2023 -TestNumber 37" -ForegroundColor Gray
