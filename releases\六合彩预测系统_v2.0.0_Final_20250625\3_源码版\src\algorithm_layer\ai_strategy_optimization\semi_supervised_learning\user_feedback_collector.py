"""
用户反馈收集器 - 收集和处理用户反馈数据
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import json
import sqlite3
from pathlib import Path

class UserFeedbackCollector:
    """用户反馈收集器"""
    
    def __init__(self, db_path: str = "data/user_feedback.db"):
        """初始化用户反馈收集器"""
        self.db_path = db_path
        self.init_database()
        
        # 反馈类型定义
        self.feedback_types = {
            'explicit': ['rating', 'comment', 'correction', 'preference'],
            'implicit': ['click', 'view_time', 'selection', 'usage_pattern']
        }
        
        # 反馈质量权重
        self.quality_weights = {
            'rating': 1.0,
            'comment': 0.8,
            'correction': 1.2,
            'preference': 0.9,
            'click': 0.3,
            'view_time': 0.4,
            'selection': 0.7,
            'usage_pattern': 0.5
        }
    
    def init_database(self):
        """初始化数据库"""
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建显式反馈表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS explicit_feedback (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                prediction_id TEXT NOT NULL,
                feedback_type TEXT NOT NULL,
                feedback_value TEXT NOT NULL,
                confidence REAL DEFAULT 1.0,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                context TEXT,
                processed BOOLEAN DEFAULT FALSE
            )
        """)
        
        # 创建隐式反馈表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS implicit_feedback (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                session_id TEXT NOT NULL,
                action_type TEXT NOT NULL,
                action_data TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                duration REAL,
                context TEXT,
                processed BOOLEAN DEFAULT FALSE
            )
        """)
        
        # 创建反馈质量评估表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS feedback_quality (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                feedback_id INTEGER NOT NULL,
                feedback_table TEXT NOT NULL,
                quality_score REAL NOT NULL,
                reliability_score REAL NOT NULL,
                consistency_score REAL NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.commit()
        conn.close()
    
    def collect_explicit_feedback(self,
                                 user_id: str,
                                 prediction_id: str,
                                 feedback_type: str,
                                 feedback_value: Any,
                                 confidence: float = 1.0,
                                 context: Dict = None) -> int:
        """收集显式反馈"""
        
        if feedback_type not in self.feedback_types['explicit']:
            raise ValueError(f"不支持的显式反馈类型: {feedback_type}")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO explicit_feedback 
            (user_id, prediction_id, feedback_type, feedback_value, confidence, context)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            user_id, prediction_id, feedback_type, 
            json.dumps(feedback_value), confidence,
            json.dumps(context or {})
        ))
        
        feedback_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        # 评估反馈质量
        self._evaluate_feedback_quality(feedback_id, 'explicit_feedback')
        
        return feedback_id
    
    def collect_implicit_feedback(self,
                                 user_id: str,
                                 session_id: str,
                                 action_type: str,
                                 action_data: Any,
                                 duration: float = None,
                                 context: Dict = None) -> int:
        """收集隐式反馈"""
        
        if action_type not in self.feedback_types['implicit']:
            raise ValueError(f"不支持的隐式反馈类型: {action_type}")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO implicit_feedback 
            (user_id, session_id, action_type, action_data, duration, context)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            user_id, session_id, action_type,
            json.dumps(action_data), duration,
            json.dumps(context or {})
        ))
        
        feedback_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        # 评估反馈质量
        self._evaluate_feedback_quality(feedback_id, 'implicit_feedback')
        
        return feedback_id
    
    def _evaluate_feedback_quality(self, feedback_id: int, feedback_table: str):
        """评估反馈质量"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取反馈数据
        cursor.execute(f"""
            SELECT * FROM {feedback_table} WHERE id = ?
        """, (feedback_id,))
        
        feedback = cursor.fetchone()
        if not feedback:
            conn.close()
            return
        
        # 计算质量分数
        quality_score = self._calculate_quality_score(feedback, feedback_table)
        reliability_score = self._calculate_reliability_score(feedback, feedback_table)
        consistency_score = self._calculate_consistency_score(feedback, feedback_table)
        
        # 保存质量评估
        cursor.execute("""
            INSERT INTO feedback_quality 
            (feedback_id, feedback_table, quality_score, reliability_score, consistency_score)
            VALUES (?, ?, ?, ?, ?)
        """, (feedback_id, feedback_table, quality_score, reliability_score, consistency_score))
        
        conn.commit()
        conn.close()
    
    def _calculate_quality_score(self, feedback: Tuple, feedback_table: str) -> float:
        """计算质量分数"""
        
        if feedback_table == 'explicit_feedback':
            # 显式反馈质量评估
            feedback_type = feedback[3]  # feedback_type字段
            confidence = feedback[5]     # confidence字段
            
            base_weight = self.quality_weights.get(feedback_type, 0.5)
            quality_score = base_weight * confidence
            
            # 根据反馈内容调整
            feedback_value = json.loads(feedback[4])  # feedback_value字段
            
            if feedback_type == 'rating':
                # 评分反馈：1-5分，归一化
                if isinstance(feedback_value, (int, float)):
                    normalized_rating = feedback_value / 5.0
                    quality_score *= normalized_rating
            
            elif feedback_type == 'comment':
                # 评论反馈：根据长度和内容质量
                if isinstance(feedback_value, str):
                    comment_length = len(feedback_value)
                    length_factor = min(1.0, comment_length / 100)  # 100字符为满分
                    quality_score *= length_factor
            
        else:
            # 隐式反馈质量评估
            action_type = feedback[3]    # action_type字段
            duration = feedback[5]       # duration字段
            
            base_weight = self.quality_weights.get(action_type, 0.3)
            quality_score = base_weight
            
            # 根据持续时间调整
            if duration is not None and action_type in ['view_time', 'usage_pattern']:
                # 时间越长，质量越高（但有上限）
                time_factor = min(1.0, duration / 300)  # 5分钟为满分
                quality_score *= time_factor
        
        return min(1.0, quality_score)
    
    def _calculate_reliability_score(self, feedback: Tuple, feedback_table: str) -> float:
        """计算可靠性分数"""
        
        user_id = feedback[1]  # user_id字段
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取用户历史反馈
        cursor.execute(f"""
            SELECT COUNT(*) FROM {feedback_table} WHERE user_id = ?
        """, (user_id,))
        
        feedback_count = cursor.fetchone()[0]
        
        # 获取用户反馈的一致性
        cursor.execute(f"""
            SELECT AVG(fq.consistency_score) 
            FROM feedback_quality fq
            JOIN {feedback_table} f ON fq.feedback_id = f.id
            WHERE f.user_id = ? AND fq.feedback_table = ?
        """, (user_id, feedback_table))
        
        avg_consistency = cursor.fetchone()[0] or 0.5
        
        conn.close()
        
        # 计算可靠性：经验 + 一致性
        experience_factor = min(1.0, feedback_count / 50)  # 50次反馈为满分
        reliability_score = 0.6 * experience_factor + 0.4 * avg_consistency
        
        return reliability_score
    
    def _calculate_consistency_score(self, feedback: Tuple, feedback_table: str) -> float:
        """计算一致性分数"""
        
        user_id = feedback[1]
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取用户最近的反馈
        cursor.execute(f"""
            SELECT feedback_value FROM {feedback_table} 
            WHERE user_id = ? 
            ORDER BY timestamp DESC 
            LIMIT 10
        """, (user_id,))
        
        recent_feedbacks = cursor.fetchall()
        conn.close()
        
        if len(recent_feedbacks) < 2:
            return 0.5  # 默认一致性
        
        # 简化的一致性计算
        # 这里可以根据具体业务逻辑实现更复杂的一致性算法
        consistency_score = 0.7  # 默认较高一致性
        
        return consistency_score
    
    def get_high_quality_feedback(self, 
                                 min_quality: float = 0.7,
                                 limit: int = 1000) -> List[Dict]:
        """获取高质量反馈"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 获取高质量显式反馈
        cursor.execute("""
            SELECT ef.*, fq.quality_score, fq.reliability_score, fq.consistency_score
            FROM explicit_feedback ef
            JOIN feedback_quality fq ON ef.id = fq.feedback_id
            WHERE fq.feedback_table = 'explicit_feedback' 
            AND fq.quality_score >= ?
            ORDER BY fq.quality_score DESC
            LIMIT ?
        """, (min_quality, limit // 2))
        
        explicit_feedbacks = cursor.fetchall()
        
        # 获取高质量隐式反馈
        cursor.execute("""
            SELECT if.*, fq.quality_score, fq.reliability_score, fq.consistency_score
            FROM implicit_feedback if
            JOIN feedback_quality fq ON if.id = fq.feedback_id
            WHERE fq.feedback_table = 'implicit_feedback' 
            AND fq.quality_score >= ?
            ORDER BY fq.quality_score DESC
            LIMIT ?
        """, (min_quality, limit // 2))
        
        implicit_feedbacks = cursor.fetchall()
        
        conn.close()
        
        # 转换为字典格式
        high_quality_feedback = []
        
        # 处理显式反馈
        for feedback in explicit_feedbacks:
            high_quality_feedback.append({
                'id': feedback[0],
                'type': 'explicit',
                'user_id': feedback[1],
                'prediction_id': feedback[2],
                'feedback_type': feedback[3],
                'feedback_value': json.loads(feedback[4]),
                'confidence': feedback[5],
                'timestamp': feedback[6],
                'context': json.loads(feedback[7]) if feedback[7] else {},
                'quality_score': feedback[9],
                'reliability_score': feedback[10],
                'consistency_score': feedback[11]
            })
        
        # 处理隐式反馈
        for feedback in implicit_feedbacks:
            high_quality_feedback.append({
                'id': feedback[0],
                'type': 'implicit',
                'user_id': feedback[1],
                'session_id': feedback[2],
                'action_type': feedback[3],
                'action_data': json.loads(feedback[4]),
                'timestamp': feedback[5],
                'duration': feedback[6],
                'context': json.loads(feedback[7]) if feedback[7] else {},
                'quality_score': feedback[9],
                'reliability_score': feedback[10],
                'consistency_score': feedback[11]
            })
        
        return high_quality_feedback
    
    def clean_feedback_data(self, 
                           min_quality: float = 0.3,
                           max_age_days: int = 365) -> Dict[str, int]:
        """清理反馈数据"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 删除低质量反馈
        cursor.execute("""
            DELETE FROM explicit_feedback 
            WHERE id IN (
                SELECT ef.id FROM explicit_feedback ef
                JOIN feedback_quality fq ON ef.id = fq.feedback_id
                WHERE fq.feedback_table = 'explicit_feedback' 
                AND fq.quality_score < ?
            )
        """, (min_quality,))
        
        deleted_explicit = cursor.rowcount
        
        cursor.execute("""
            DELETE FROM implicit_feedback 
            WHERE id IN (
                SELECT if.id FROM implicit_feedback if
                JOIN feedback_quality fq ON if.id = fq.feedback_id
                WHERE fq.feedback_table = 'implicit_feedback' 
                AND fq.quality_score < ?
            )
        """, (min_quality,))
        
        deleted_implicit = cursor.rowcount
        
        # 删除过期反馈
        cutoff_date = datetime.now() - timedelta(days=max_age_days)
        
        cursor.execute("""
            DELETE FROM explicit_feedback 
            WHERE timestamp < ?
        """, (cutoff_date,))
        
        deleted_old_explicit = cursor.rowcount
        
        cursor.execute("""
            DELETE FROM implicit_feedback 
            WHERE timestamp < ?
        """, (cutoff_date,))
        
        deleted_old_implicit = cursor.rowcount
        
        # 清理孤立的质量评估记录
        cursor.execute("""
            DELETE FROM feedback_quality 
            WHERE (feedback_table = 'explicit_feedback' AND feedback_id NOT IN (SELECT id FROM explicit_feedback))
            OR (feedback_table = 'implicit_feedback' AND feedback_id NOT IN (SELECT id FROM implicit_feedback))
        """)
        
        deleted_orphaned = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        return {
            'deleted_low_quality_explicit': deleted_explicit,
            'deleted_low_quality_implicit': deleted_implicit,
            'deleted_old_explicit': deleted_old_explicit,
            'deleted_old_implicit': deleted_old_implicit,
            'deleted_orphaned_quality': deleted_orphaned
        }
    
    def get_feedback_statistics(self) -> Dict[str, Any]:
        """获取反馈统计信息"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        stats = {}
        
        # 显式反馈统计
        cursor.execute("SELECT COUNT(*) FROM explicit_feedback")
        stats['total_explicit_feedback'] = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT feedback_type, COUNT(*) 
            FROM explicit_feedback 
            GROUP BY feedback_type
        """)
        stats['explicit_feedback_by_type'] = dict(cursor.fetchall())
        
        # 隐式反馈统计
        cursor.execute("SELECT COUNT(*) FROM implicit_feedback")
        stats['total_implicit_feedback'] = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT action_type, COUNT(*) 
            FROM implicit_feedback 
            GROUP BY action_type
        """)
        stats['implicit_feedback_by_type'] = dict(cursor.fetchall())
        
        # 质量统计
        cursor.execute("""
            SELECT AVG(quality_score), AVG(reliability_score), AVG(consistency_score)
            FROM feedback_quality
        """)
        quality_stats = cursor.fetchone()
        stats['average_quality_score'] = quality_stats[0] or 0
        stats['average_reliability_score'] = quality_stats[1] or 0
        stats['average_consistency_score'] = quality_stats[2] or 0
        
        # 用户活跃度统计
        cursor.execute("""
            SELECT COUNT(DISTINCT user_id) FROM (
                SELECT user_id FROM explicit_feedback
                UNION
                SELECT user_id FROM implicit_feedback
            )
        """)
        stats['active_users'] = cursor.fetchone()[0]
        
        conn.close()
        
        return stats
