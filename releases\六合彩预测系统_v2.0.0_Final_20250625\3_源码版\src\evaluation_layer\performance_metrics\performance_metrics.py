"""
模型性能评估指标 - 全面的性能评估体系
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from scipy import stats
import json
import warnings

class PerformanceMetrics:
    """性能评估指标计算器"""
    
    def __init__(self):
        """初始化性能指标计算器"""
        self.metric_functions = {
            # 基础准确性指标
            'hit_rate': self._calculate_hit_rate,
            'exact_match_rate': self._calculate_exact_match_rate,
            'partial_match_rate': self._calculate_partial_match_rate,
            
            # 统计指标
            'precision': self._calculate_precision,
            'recall': self._calculate_recall,
            'f1_score': self._calculate_f1_score,
            
            # 排序指标
            'top_k_accuracy': self._calculate_top_k_accuracy,
            'mean_reciprocal_rank': self._calculate_mrr,
            
            # 概率指标
            'log_likelihood': self._calculate_log_likelihood,
            'brier_score': self._calculate_brier_score,
            
            # 稳定性指标
            'consistency_score': self._calculate_consistency,
            'volatility': self._calculate_volatility,
            'sharpe_ratio': self._calculate_sharpe_ratio
        }
    
    def evaluate_predictions(self, 
                           predictions: List[Dict], 
                           actual_results: List[Dict],
                           metrics: List[str] = None) -> Dict[str, float]:
        """评估预测结果"""
        
        if not predictions or not actual_results:
            return {}
        
        if len(predictions) != len(actual_results):
            warnings.warn("预测结果和实际结果数量不匹配")
            min_len = min(len(predictions), len(actual_results))
            predictions = predictions[:min_len]
            actual_results = actual_results[:min_len]
        
        # 如果未指定指标，计算所有指标
        if metrics is None:
            metrics = list(self.metric_functions.keys())
        
        results = {}
        
        for metric in metrics:
            if metric in self.metric_functions:
                try:
                    value = self.metric_functions[metric](predictions, actual_results)
                    results[metric] = value
                except Exception as e:
                    warnings.warn(f"计算指标 {metric} 时出错: {e}")
                    results[metric] = None
            else:
                warnings.warn(f"未知指标: {metric}")
        
        return results
    
    def _extract_numbers(self, predictions: List[Dict], actual_results: List[Dict]) -> Tuple[List, List]:
        """提取预测和实际号码"""
        pred_numbers = []
        actual_numbers = []
        
        for pred, actual in zip(predictions, actual_results):
            if 'error' in actual:
                continue
                
            pred_nums = pred.get('predicted_numbers', [])
            actual_nums = actual.get('all_numbers', [])
            
            if pred_nums and actual_nums:
                pred_numbers.append(pred_nums)
                actual_numbers.append(actual_nums)
        
        return pred_numbers, actual_numbers
    
    def _calculate_hit_rate(self, predictions: List[Dict], actual_results: List[Dict]) -> float:
        """计算命中率"""
        pred_numbers, actual_numbers = self._extract_numbers(predictions, actual_results)
        
        if not pred_numbers:
            return 0.0
        
        hit_rates = []
        for pred, actual in zip(pred_numbers, actual_numbers):
            matches = len(set(pred) & set(actual))
            hit_rate = matches / len(actual) if actual else 0
            hit_rates.append(hit_rate)
        
        return np.mean(hit_rates)
    
    def _calculate_exact_match_rate(self, predictions: List[Dict], actual_results: List[Dict]) -> float:
        """计算完全匹配率"""
        pred_numbers, actual_numbers = self._extract_numbers(predictions, actual_results)
        
        if not pred_numbers:
            return 0.0
        
        exact_matches = 0
        for pred, actual in zip(pred_numbers, actual_numbers):
            if set(pred) == set(actual):
                exact_matches += 1
        
        return exact_matches / len(pred_numbers)
    
    def _calculate_partial_match_rate(self, predictions: List[Dict], actual_results: List[Dict]) -> float:
        """计算部分匹配率"""
        pred_numbers, actual_numbers = self._extract_numbers(predictions, actual_results)
        
        if not pred_numbers:
            return 0.0
        
        partial_matches = []
        for pred, actual in zip(pred_numbers, actual_numbers):
            matches = len(set(pred) & set(actual))
            partial_matches.append(matches)
        
        return np.mean(partial_matches)
    
    def _calculate_precision(self, predictions: List[Dict], actual_results: List[Dict]) -> float:
        """计算精确率"""
        pred_numbers, actual_numbers = self._extract_numbers(predictions, actual_results)
        
        if not pred_numbers:
            return 0.0
        
        precisions = []
        for pred, actual in zip(pred_numbers, actual_numbers):
            if not pred:
                precisions.append(0.0)
                continue
            
            matches = len(set(pred) & set(actual))
            precision = matches / len(pred)
            precisions.append(precision)
        
        return np.mean(precisions)
    
    def _calculate_recall(self, predictions: List[Dict], actual_results: List[Dict]) -> float:
        """计算召回率"""
        pred_numbers, actual_numbers = self._extract_numbers(predictions, actual_results)
        
        if not pred_numbers:
            return 0.0
        
        recalls = []
        for pred, actual in zip(pred_numbers, actual_numbers):
            if not actual:
                recalls.append(0.0)
                continue
            
            matches = len(set(pred) & set(actual))
            recall = matches / len(actual)
            recalls.append(recall)
        
        return np.mean(recalls)
    
    def _calculate_f1_score(self, predictions: List[Dict], actual_results: List[Dict]) -> float:
        """计算F1分数"""
        precision = self._calculate_precision(predictions, actual_results)
        recall = self._calculate_recall(predictions, actual_results)
        
        if precision + recall == 0:
            return 0.0
        
        return 2 * (precision * recall) / (precision + recall)
    
    def _calculate_top_k_accuracy(self, predictions: List[Dict], actual_results: List[Dict], k: int = 3) -> float:
        """计算Top-K准确率"""
        pred_numbers, actual_numbers = self._extract_numbers(predictions, actual_results)
        
        if not pred_numbers:
            return 0.0
        
        top_k_hits = 0
        for pred, actual in zip(pred_numbers, actual_numbers):
            # 取预测的前k个号码
            top_k_pred = pred[:k] if len(pred) >= k else pred
            
            # 检查是否有命中
            if set(top_k_pred) & set(actual):
                top_k_hits += 1
        
        return top_k_hits / len(pred_numbers)
    
    def _calculate_mrr(self, predictions: List[Dict], actual_results: List[Dict]) -> float:
        """计算平均倒数排名"""
        pred_numbers, actual_numbers = self._extract_numbers(predictions, actual_results)
        
        if not pred_numbers:
            return 0.0
        
        reciprocal_ranks = []
        for pred, actual in zip(pred_numbers, actual_numbers):
            # 找到第一个命中的位置
            for i, num in enumerate(pred):
                if num in actual:
                    reciprocal_ranks.append(1.0 / (i + 1))
                    break
            else:
                reciprocal_ranks.append(0.0)
        
        return np.mean(reciprocal_ranks)
    
    def _calculate_log_likelihood(self, predictions: List[Dict], actual_results: List[Dict]) -> float:
        """计算对数似然"""
        log_likelihoods = []
        
        for pred, actual in zip(predictions, actual_results):
            if 'error' in actual:
                continue
            
            confidence = pred.get('confidence', 0.5)
            pred_nums = pred.get('predicted_numbers', [])
            actual_nums = actual.get('all_numbers', [])
            
            if not pred_nums or not actual_nums:
                continue
            
            # 简化的对数似然计算
            matches = len(set(pred_nums) & set(actual_nums))
            total_pred = len(pred_nums)
            
            if matches > 0:
                likelihood = confidence * (matches / total_pred)
            else:
                likelihood = (1 - confidence) * 0.1
            
            log_likelihoods.append(np.log(max(likelihood, 1e-10)))
        
        return np.mean(log_likelihoods) if log_likelihoods else 0.0
    
    def _calculate_brier_score(self, predictions: List[Dict], actual_results: List[Dict]) -> float:
        """计算Brier分数"""
        brier_scores = []
        
        for pred, actual in zip(predictions, actual_results):
            if 'error' in actual:
                continue
            
            confidence = pred.get('confidence', 0.5)
            pred_nums = pred.get('predicted_numbers', [])
            actual_nums = actual.get('all_numbers', [])
            
            if not pred_nums or not actual_nums:
                continue
            
            # 计算预测概率和实际结果
            matches = len(set(pred_nums) & set(actual_nums))
            hit_rate = matches / len(actual_nums)
            
            # Brier分数
            brier = (confidence - hit_rate) ** 2
            brier_scores.append(brier)
        
        return np.mean(brier_scores) if brier_scores else 0.0
    
    def _calculate_consistency(self, predictions: List[Dict], actual_results: List[Dict]) -> float:
        """计算一致性分数"""
        pred_numbers, actual_numbers = self._extract_numbers(predictions, actual_results)
        
        if len(pred_numbers) < 2:
            return 0.0
        
        # 计算相邻预测的相似性
        similarities = []
        for i in range(1, len(pred_numbers)):
            prev_pred = set(pred_numbers[i-1])
            curr_pred = set(pred_numbers[i])
            
            # Jaccard相似性
            intersection = len(prev_pred & curr_pred)
            union = len(prev_pred | curr_pred)
            similarity = intersection / union if union > 0 else 0
            similarities.append(similarity)
        
        return np.mean(similarities)
    
    def _calculate_volatility(self, predictions: List[Dict], actual_results: List[Dict]) -> float:
        """计算波动性"""
        hit_rates = []
        
        for pred, actual in zip(predictions, actual_results):
            if 'error' in actual:
                continue
            
            pred_nums = pred.get('predicted_numbers', [])
            actual_nums = actual.get('all_numbers', [])
            
            if not pred_nums or not actual_nums:
                continue
            
            matches = len(set(pred_nums) & set(actual_nums))
            hit_rate = matches / len(actual_nums)
            hit_rates.append(hit_rate)
        
        return np.std(hit_rates) if len(hit_rates) > 1 else 0.0
    
    def _calculate_sharpe_ratio(self, predictions: List[Dict], actual_results: List[Dict]) -> float:
        """计算夏普比率"""
        hit_rates = []
        
        for pred, actual in zip(predictions, actual_results):
            if 'error' in actual:
                continue
            
            pred_nums = pred.get('predicted_numbers', [])
            actual_nums = actual.get('all_numbers', [])
            
            if not pred_nums or not actual_nums:
                continue
            
            matches = len(set(pred_nums) & set(actual_nums))
            hit_rate = matches / len(actual_nums)
            hit_rates.append(hit_rate)
        
        if len(hit_rates) < 2:
            return 0.0
        
        mean_return = np.mean(hit_rates)
        std_return = np.std(hit_rates)
        
        return mean_return / std_return if std_return > 0 else 0.0
    
    def generate_performance_report(self, 
                                  predictions: List[Dict], 
                                  actual_results: List[Dict],
                                  strategy_name: str = "Unknown") -> Dict[str, Any]:
        """生成完整的性能报告"""
        
        # 计算所有指标
        metrics = self.evaluate_predictions(predictions, actual_results)
        
        # 基础统计
        pred_numbers, actual_numbers = self._extract_numbers(predictions, actual_results)
        
        basic_stats = {
            'total_predictions': len(pred_numbers),
            'prediction_period': {
                'start': predictions[0].get('prediction_date') if predictions else None,
                'end': predictions[-1].get('prediction_date') if predictions else None
            },
            'average_predicted_numbers': np.mean([len(pred) for pred in pred_numbers]) if pred_numbers else 0,
            'average_actual_numbers': np.mean([len(actual) for actual in actual_numbers]) if actual_numbers else 0
        }
        
        # 分类指标
        categorized_metrics = {
            'accuracy_metrics': {
                'hit_rate': metrics.get('hit_rate'),
                'exact_match_rate': metrics.get('exact_match_rate'),
                'partial_match_rate': metrics.get('partial_match_rate'),
                'precision': metrics.get('precision'),
                'recall': metrics.get('recall'),
                'f1_score': metrics.get('f1_score')
            },
            'ranking_metrics': {
                'top_k_accuracy': metrics.get('top_k_accuracy'),
                'mean_reciprocal_rank': metrics.get('mean_reciprocal_rank')
            },
            'probability_metrics': {
                'log_likelihood': metrics.get('log_likelihood'),
                'brier_score': metrics.get('brier_score')
            },
            'stability_metrics': {
                'consistency_score': metrics.get('consistency_score'),
                'volatility': metrics.get('volatility'),
                'sharpe_ratio': metrics.get('sharpe_ratio')
            }
        }
        
        return {
            'strategy_name': strategy_name,
            'basic_statistics': basic_stats,
            'all_metrics': metrics,
            'categorized_metrics': categorized_metrics,
            'report_generated_at': pd.Timestamp.now().isoformat()
        }
