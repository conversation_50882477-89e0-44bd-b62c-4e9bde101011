# 模型管理与调参系统使用指南

## 系统概述

澳门六合彩预测系统的模型管理与调参系统是一个完整的机器学习模型生命周期管理平台，包含以下核心功能：

### 1. 模型生命周期管理 (model_lifecycle/)

#### 1.1 模型注册中心 (model_registry.py)
- **功能**: 模型版本管理、元数据存储、依赖关系跟踪
- **使用示例**:
```python
from src.algorithm_layer.model_management.model_lifecycle.model_registry import ModelRegistry

registry = ModelRegistry()
registry.register_model(
    model_name="lottery_predictor",
    model_version="1.0.0",
    model_type="RandomForest",
    model_object=trained_model,
    metadata={"accuracy": 0.85},
    performance_metrics={"f1_score": 0.82}
)
```

#### 1.2 模型训练器 (model_trainer.py)
- **功能**: 自动化训练流程、进度监控、早停机制
- **特点**: 支持增量训练和批量训练
- **使用示例**:
```python
from src.algorithm_layer.model_management.model_lifecycle.model_trainer import ModelTrainer

trainer = ModelTrainer(registry)
result = trainer.train_model(
    model_class=RandomForestClassifier,
    model_params={"n_estimators": 100},
    X_train=X_train,
    y_train=y_train,
    model_name="lottery_rf",
    early_stopping=True
)
```

#### 1.3 模型验证器 (model_validator.py)
- **功能**: 性能验证、数据漂移检测、稳定性测试、A/B测试
- **使用示例**:
```python
from src.algorithm_layer.model_management.model_lifecycle.model_validator import ModelValidator

validator = ModelValidator()
validation_result = validator.comprehensive_validation(
    model=trained_model,
    X_train=X_train,
    y_train=y_train,
    X_test=X_test,
    y_test=y_test
)
```

### 2. 超参数优化系统 (hyperparameter_optimization/)

#### 2.1 贝叶斯优化 (bayesian_optimization.py)
- **功能**: 使用Optuna进行智能超参数搜索
- **使用示例**:
```python
from src.algorithm_layer.model_management.hyperparameter_optimization.bayesian_optimization import BayesianOptimizer

param_space = {
    'n_estimators': {'type': 'int', 'low': 50, 'high': 200},
    'max_depth': {'type': 'int', 'low': 3, 'high': 20},
    'learning_rate': {'type': 'float', 'low': 0.01, 'high': 0.3, 'log': True}
}

optimizer = BayesianOptimizer(
    model_class=RandomForestClassifier,
    param_space=param_space,
    n_trials=100
)

result = optimizer.optimize(X_train, y_train)
best_model = optimizer.get_best_model()
```

### 3. 模型集成管理 (model_ensemble/)

#### 3.1 集成构建器 (ensemble_builder.py)
- **功能**: 构建Voting、Bagging、Boosting、Stacking集成
- **使用示例**:
```python
from src.algorithm_layer.model_management.model_ensemble.ensemble_builder import EnsembleBuilder

builder = EnsembleBuilder()

# 构建投票集成
base_models = [
    ('rf', RandomForestClassifier()),
    ('svm', SVC(probability=True)),
    ('nb', GaussianNB())
]

voting_ensemble = builder.build_voting_ensemble(base_models, voting='soft')
voting_ensemble.fit(X_train, y_train)

# 构建Stacking集成
stacking_ensemble = builder.build_stacking_ensemble(base_models)
stacking_ensemble.fit(X_train, y_train)
```

### 4. AutoML自动化 (automl_system/)

#### 4.1 自动特征工程 (auto_feature_engineering.py)
- **功能**: 自动特征生成、选择、变换
- **使用示例**:
```python
from src.algorithm_layer.model_management.automl_system.auto_feature_engineering import AutoFeatureEngineering

auto_fe = AutoFeatureEngineering()

# 自动特征工程流水线
result = auto_fe.auto_feature_engineering_pipeline(
    X=X_train,
    y=y_train,
    generate_types=['polynomial', 'interaction', 'statistical'],
    selection_method='mutual_info',
    n_features=50
)

X_processed = result['X_final']
```

## 完整使用流程

### 方式1: 使用模型管理系统主入口

```python
from src.algorithm_layer.model_management.model_manager import ModelManagementSystem
from sklearn.ensemble import RandomForestClassifier

# 初始化系统
model_manager = ModelManagementSystem()

# 定义超参数空间
param_space = {
    'n_estimators': {'type': 'int', 'low': 50, 'high': 200},
    'max_depth': {'type': 'int', 'low': 3, 'high': 20}
}

# 自动训练和优化
result = model_manager.auto_train_and_optimize(
    model_class=RandomForestClassifier,
    param_space=param_space,
    X_train=X_train,
    y_train=y_train,
    X_val=X_val,
    y_val=y_val,
    model_name="lottery_auto_model",
    optimize_hyperparams=True,
    auto_features=True
)

# 获取最终模型
final_model = result['final_model']
```

### 方式2: 分步骤使用各个模块

```python
# 1. 自动特征工程
auto_fe = AutoFeatureEngineering()
feature_result = auto_fe.auto_feature_engineering_pipeline(X_train, y_train)
X_processed = feature_result['X_final']

# 2. 超参数优化
optimizer = BayesianOptimizer(RandomForestClassifier, param_space)
opt_result = optimizer.optimize(X_processed, y_train)
best_params = opt_result['best_params']

# 3. 模型训练
trainer = ModelTrainer()
training_result = trainer.train_model(
    RandomForestClassifier, best_params, X_processed, y_train
)

# 4. 模型验证
validator = ModelValidator()
validation_result = validator.comprehensive_validation(
    training_result['model'], X_processed, y_train, X_test, y_test
)

# 5. 模型注册
registry = ModelRegistry()
registry.register_model(
    "optimized_model", "1.0", "RandomForest", 
    training_result['model'], performance_metrics=validation_result['performance']
)
```

## 系统特点

1. **完整的生命周期管理**: 从训练到部署的全流程管理
2. **智能超参数优化**: 使用贝叶斯优化提高效率
3. **自动特征工程**: 自动生成和选择最优特征
4. **多种集成策略**: 支持Voting、Stacking等集成方法
5. **模型版本控制**: 完整的模型版本和元数据管理
6. **性能监控**: 实时监控模型性能和数据漂移
7. **A/B测试支持**: 科学比较不同模型性能

## 注意事项

1. 确保安装所需依赖: `optuna`, `scikit-learn`, `numpy`, `pandas`
2. 大数据集建议使用分布式训练
3. 定期清理旧模型版本以节省存储空间
4. 监控数据漂移，及时重训练模型
5. 使用交叉验证确保模型泛化能力
