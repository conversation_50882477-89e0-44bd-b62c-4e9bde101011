"""
训练数据增强实施代码
实现时间窗口滑动、特征重组合等数据增强技术
"""

import numpy as np
import pandas as pd
import sqlite3
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Any
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
import itertools

class DataEnhancer:
    """数据增强器"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.scaler = StandardScaler()
        
    def get_historical_data(self, limit: int = None) -> List[Dict]:
        """获取历史数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query = """
                SELECT draw_date, period_number, special_number, 
                       regular_1, regular_2, regular_3, regular_4, regular_5, regular_6
                FROM lottery_results 
                ORDER BY draw_date DESC
            """
            
            if limit:
                query += f" LIMIT {limit}"
            
            cursor.execute(query)
            rows = cursor.fetchall()
            conn.close()
            
            data = []
            for row in rows:
                data.append({
                    'draw_date': row[0],
                    'period_number': row[1],
                    'special_number': row[2],
                    'regular_numbers': [row[3], row[4], row[5], row[6], row[7], row[8]]
                })
            
            return list(reversed(data))  # 按时间正序
            
        except Exception as e:
            print(f"获取历史数据失败: {e}")
            return []
    
    def sliding_window_augmentation(self, data: List[Dict], 
                                  window_sizes: List[int] = [5, 10, 15, 20, 30]) -> List[Tuple]:
        """时间窗口滑动增强"""
        print(f"🔄 执行时间窗口滑动增强...")
        
        augmented_samples = []
        
        for window_size in window_sizes:
            print(f"  处理窗口大小: {window_size}")
            
            for i in range(window_size, len(data)):
                # 获取窗口数据
                window_data = data[i-window_size:i]
                target = data[i]['special_number']
                
                # 提取特征
                features = self._extract_window_features(window_data)
                
                if features is not None:
                    augmented_samples.append({
                        'features': features,
                        'target': target,
                        'window_size': window_size,
                        'end_date': data[i]['draw_date']
                    })
        
        print(f"✅ 时间窗口滑动增强完成: 生成 {len(augmented_samples)} 个样本")
        return augmented_samples
    
    def _extract_window_features(self, window_data: List[Dict]) -> np.ndarray:
        """从窗口数据提取特征"""
        if not window_data:
            return None
        
        features = []
        special_numbers = [record['special_number'] for record in window_data]
        
        # 基础统计特征 (5维)
        features.extend([
            np.mean(special_numbers),
            np.std(special_numbers),
            np.min(special_numbers),
            np.max(special_numbers),
            np.median(special_numbers)
        ])
        
        # 趋势特征 (2维)
        if len(special_numbers) >= 3:
            x = np.arange(len(special_numbers))
            trend_coef = np.polyfit(x, special_numbers, 1)[0]
            features.append(trend_coef)
            
            # 趋势强度
            correlation = np.corrcoef(x, special_numbers)[0, 1]
            features.append(correlation if not np.isnan(correlation) else 0)
        else:
            features.extend([0, 0])
        
        # 差分特征 (2维)
        if len(special_numbers) >= 2:
            diffs = np.diff(special_numbers)
            features.extend([np.mean(diffs), np.std(diffs)])
        else:
            features.extend([0, 0])
        
        # 频率特征 (3维)
        from collections import Counter
        number_counts = Counter(special_numbers)
        max_freq = max(number_counts.values()) if number_counts else 0
        unique_count = len(number_counts)
        
        features.extend([
            max_freq / len(special_numbers) if special_numbers else 0,
            unique_count / len(special_numbers) if special_numbers else 0,
            len(special_numbers) - unique_count  # 重复数量
        ])
        
        # 大小单双特征 (4维)
        big_count = sum(1 for num in special_numbers if num > 24)
        small_count = len(special_numbers) - big_count
        odd_count = sum(1 for num in special_numbers if num % 2 == 1)
        even_count = len(special_numbers) - odd_count
        
        features.extend([
            big_count / len(special_numbers) if special_numbers else 0,
            small_count / len(special_numbers) if special_numbers else 0,
            odd_count / len(special_numbers) if special_numbers else 0,
            even_count / len(special_numbers) if special_numbers else 0
        ])
        
        # 范围特征 (4维)
        if special_numbers:
            number_range = max(special_numbers) - min(special_numbers)
            features.extend([
                number_range,
                number_range / len(special_numbers),
                sum(special_numbers) / len(special_numbers),  # 平均值
                len([n for n in special_numbers if 1 <= n <= 12]) / len(special_numbers)  # 小数占比
            ])
        else:
            features.extend([0, 0, 0, 0])
        
        return np.array(features)
    
    def feature_combination_augmentation(self, samples: List[Dict]) -> List[Dict]:
        """特征重组合增强"""
        print(f"🔧 执行特征重组合增强...")
        
        augmented_samples = []
        
        # 定义特征组合策略
        feature_combinations = [
            {'name': 'statistical_only', 'indices': list(range(5))},
            {'name': 'trend_only', 'indices': list(range(5, 7))},
            {'name': 'frequency_only', 'indices': list(range(7, 10))},
            {'name': 'size_parity_only', 'indices': list(range(10, 14))},
            {'name': 'range_only', 'indices': list(range(14, 18))},
            {'name': 'stat_trend', 'indices': list(range(7))},
            {'name': 'stat_freq', 'indices': list(range(5)) + list(range(7, 10))},
            {'name': 'all_features', 'indices': list(range(20))}
        ]
        
        for sample in samples:
            original_features = sample['features']
            
            for combo in feature_combinations:
                # 选择特征子集
                selected_features = original_features[combo['indices']]
                
                # 创建新样本
                new_sample = sample.copy()
                new_sample['features'] = selected_features
                new_sample['feature_combination'] = combo['name']
                
                augmented_samples.append(new_sample)
        
        print(f"✅ 特征重组合增强完成: 生成 {len(augmented_samples)} 个样本")
        return augmented_samples
    
    def polynomial_feature_augmentation(self, samples: List[Dict], degree: int = 2) -> List[Dict]:
        """多项式特征增强"""
        print(f"🔬 执行多项式特征增强 (degree={degree})...")
        
        # 收集所有特征
        all_features = np.array([sample['features'] for sample in samples])
        
        # 生成多项式特征
        poly = PolynomialFeatures(degree=degree, include_bias=False)
        poly_features = poly.fit_transform(all_features)
        
        # 创建增强样本
        augmented_samples = []
        for i, sample in enumerate(samples):
            new_sample = sample.copy()
            new_sample['features'] = poly_features[i]
            new_sample['feature_type'] = f'polynomial_degree_{degree}'
            augmented_samples.append(new_sample)
        
        print(f"✅ 多项式特征增强完成: 特征维度 {all_features.shape[1]} → {poly_features.shape[1]}")
        return augmented_samples
    
    def noise_injection_augmentation(self, samples: List[Dict], 
                                   noise_levels: List[float] = [0.01, 0.02, 0.05]) -> List[Dict]:
        """噪声注入增强"""
        print(f"🎲 执行噪声注入增强...")
        
        augmented_samples = []
        
        for noise_level in noise_levels:
            print(f"  处理噪声水平: {noise_level}")
            
            for sample in samples:
                original_features = sample['features']
                
                # 添加高斯噪声
                noise = np.random.normal(0, noise_level, original_features.shape)
                noisy_features = original_features + noise
                
                # 创建新样本
                new_sample = sample.copy()
                new_sample['features'] = noisy_features
                new_sample['noise_level'] = noise_level
                
                augmented_samples.append(new_sample)
        
        print(f"✅ 噪声注入增强完成: 生成 {len(augmented_samples)} 个样本")
        return augmented_samples
    
    def time_series_cross_validation_split(self, samples: List[Dict], 
                                         n_splits: int = 5) -> List[Tuple]:
        """时间序列交叉验证分割"""
        print(f"📊 执行时间序列交叉验证分割 (n_splits={n_splits})...")
        
        # 按时间排序
        sorted_samples = sorted(samples, key=lambda x: x.get('end_date', ''))
        
        tscv = TimeSeriesSplit(n_splits=n_splits)
        X = np.array([sample['features'] for sample in sorted_samples])
        y = np.array([sample['target'] for sample in sorted_samples])
        
        cv_splits = []
        for fold, (train_idx, test_idx) in enumerate(tscv.split(X)):
            train_samples = [sorted_samples[i] for i in train_idx]
            test_samples = [sorted_samples[i] for i in test_idx]
            
            cv_splits.append({
                'fold': fold,
                'train_samples': train_samples,
                'test_samples': test_samples,
                'train_size': len(train_samples),
                'test_size': len(test_samples)
            })
        
        print(f"✅ 时间序列CV分割完成: {n_splits} 折")
        return cv_splits
    
    def multi_scale_feature_extraction(self, data: List[Dict]) -> List[Dict]:
        """多尺度特征提取"""
        print(f"🔍 执行多尺度特征提取...")
        
        scales = [5, 10, 20, 50, 100]  # 不同时间尺度
        multi_scale_samples = []
        
        for i in range(max(scales), len(data)):
            combined_features = []
            
            for scale in scales:
                if i >= scale:
                    # 提取该尺度的特征
                    window_data = data[i-scale:i]
                    scale_features = self._extract_window_features(window_data)
                    
                    if scale_features is not None:
                        combined_features.extend(scale_features)
                    else:
                        combined_features.extend([0] * 20)  # 填充零值
                else:
                    combined_features.extend([0] * 20)  # 填充零值
            
            if combined_features:
                multi_scale_samples.append({
                    'features': np.array(combined_features),
                    'target': data[i]['special_number'],
                    'end_date': data[i]['draw_date'],
                    'feature_type': 'multi_scale'
                })
        
        print(f"✅ 多尺度特征提取完成: 生成 {len(multi_scale_samples)} 个样本")
        print(f"   特征维度: {len(combined_features)} 维 ({len(scales)} 尺度 × 20 特征)")
        return multi_scale_samples
    
    def comprehensive_data_enhancement(self) -> Dict[str, Any]:
        """综合数据增强"""
        print("🚀 开始综合数据增强...")
        
        # 1. 获取历史数据
        historical_data = self.get_historical_data()
        print(f"📊 原始数据: {len(historical_data)} 条记录")
        
        if len(historical_data) < 50:
            print("❌ 数据量不足，无法进行有效增强")
            return {}
        
        # 2. 时间窗口滑动增强
        sliding_samples = self.sliding_window_augmentation(historical_data)
        
        # 3. 特征重组合增强
        combination_samples = self.feature_combination_augmentation(sliding_samples[:1000])  # 限制数量
        
        # 4. 多尺度特征提取
        multi_scale_samples = self.multi_scale_feature_extraction(historical_data)
        
        # 5. 噪声注入增强 (少量)
        noise_samples = self.noise_injection_augmentation(sliding_samples[:500], [0.01])
        
        # 6. 时间序列交叉验证分割
        cv_splits = self.time_series_cross_validation_split(sliding_samples)
        
        # 统计结果
        total_samples = len(sliding_samples) + len(combination_samples) + len(multi_scale_samples) + len(noise_samples)
        
        results = {
            'original_data_count': len(historical_data),
            'sliding_window_samples': len(sliding_samples),
            'combination_samples': len(combination_samples),
            'multi_scale_samples': len(multi_scale_samples),
            'noise_samples': len(noise_samples),
            'total_enhanced_samples': total_samples,
            'enhancement_ratio': total_samples / len(historical_data) if historical_data else 0,
            'cv_splits': len(cv_splits),
            'all_samples': {
                'sliding_window': sliding_samples,
                'combination': combination_samples,
                'multi_scale': multi_scale_samples,
                'noise': noise_samples
            },
            'cv_splits_data': cv_splits
        }
        
        print(f"\n🎉 综合数据增强完成!")
        print(f"📊 增强效果统计:")
        print(f"  原始数据: {results['original_data_count']} 条")
        print(f"  滑动窗口样本: {results['sliding_window_samples']} 个")
        print(f"  特征组合样本: {results['combination_samples']} 个")
        print(f"  多尺度样本: {results['multi_scale_samples']} 个")
        print(f"  噪声注入样本: {results['noise_samples']} 个")
        print(f"  总增强样本: {results['total_enhanced_samples']} 个")
        print(f"  增强倍数: {results['enhancement_ratio']:.1f}x")
        print(f"  交叉验证折数: {results['cv_splits']} 折")
        
        return results

def main():
    """主函数"""
    print("🎯 训练数据增强实施")
    print("=" * 60)
    
    # 创建数据增强器
    enhancer = DataEnhancer()
    
    # 执行综合数据增强
    results = enhancer.comprehensive_data_enhancement()
    
    if results:
        print(f"\n💡 使用建议:")
        print(f"  1. 优先使用滑动窗口样本进行基础训练")
        print(f"  2. 使用多尺度样本提升模型复杂度")
        print(f"  3. 使用特征组合样本进行特征选择")
        print(f"  4. 使用交叉验证评估模型性能")
        print(f"  5. 适量使用噪声样本增强鲁棒性")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
