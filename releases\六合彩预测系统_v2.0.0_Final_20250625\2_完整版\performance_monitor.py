"""
性能监控组件
"""

import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any
import statistics

class PerformanceMonitor:
    """性能监控器"""

    def __init__(self, gui_instance=None):
        self.gui = gui_instance
        self.performance_data = []
        self.module_metrics = {}

    def start_monitoring(self):
        """开始性能监控"""
        print("📈 开始性能监控...")

        # 初始化监控数据
        self.performance_data = []
        self.module_metrics = {
            'traditional_analysis': {'hit_rates': [], 'response_times': []},
            'machine_learning': {'hit_rates': [], 'response_times': []},
            'zodiac_extended': {'hit_rates': [], 'response_times': []},
            'special_zodiac': {'hit_rates': [], 'response_times': []},
            'fusion_manager': {'hit_rates': [], 'response_times': []}
        }

        return True

    def record_prediction_performance(self, module_name: str, hit_rate: float,
                                    response_time: float, confidence: float = 0.0):
        """记录预测性能"""
        timestamp = datetime.now().isoformat()

        performance_record = {
            'timestamp': timestamp,
            'module': module_name,
            'hit_rate': hit_rate,
            'response_time': response_time,
            'confidence': confidence
        }

        self.performance_data.append(performance_record)

        # 更新模块指标
        if module_name in self.module_metrics:
            self.module_metrics[module_name]['hit_rates'].append(hit_rate)
            self.module_metrics[module_name]['response_times'].append(response_time)

        print(f"📊 记录性能: {module_name} - 命中率: {hit_rate:.1%}, 响应时间: {response_time:.2f}s")

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.performance_data:
            return {'error': '没有性能数据'}

        summary = {
            'total_records': len(self.performance_data),
            'monitoring_period': {
                'start': self.performance_data[0]['timestamp'],
                'end': self.performance_data[-1]['timestamp']
            },
            'modules': {}
        }

        for module_name, metrics in self.module_metrics.items():
            if metrics['hit_rates']:
                summary['modules'][module_name] = {
                    'avg_hit_rate': statistics.mean(metrics['hit_rates']),
                    'max_hit_rate': max(metrics['hit_rates']),
                    'min_hit_rate': min(metrics['hit_rates']),
                    'avg_response_time': statistics.mean(metrics['response_times']),
                    'total_predictions': len(metrics['hit_rates'])
                }

        return summary

    def generate_performance_report(self) -> str:
        """生成性能报告"""
        summary = self.get_performance_summary()

        if 'error' in summary:
            return "❌ 没有可用的性能数据"

        report = []
        report.append("📈 性能监控报告")
        report.append("=" * 40)
        report.append(f"总记录数: {summary['total_records']}")
        report.append(f"监控开始: {summary['monitoring_period']['start'][:19]}")
        report.append(f"监控结束: {summary['monitoring_period']['end'][:19]}")
        report.append("")

        for module_name, metrics in summary['modules'].items():
            report.append(f"🔧 {module_name}")
            report.append(f"  平均命中率: {metrics['avg_hit_rate']:.1%}")
            report.append(f"  最高命中率: {metrics['max_hit_rate']:.1%}")
            report.append(f"  最低命中率: {metrics['min_hit_rate']:.1%}")
            report.append(f"  平均响应时间: {metrics['avg_response_time']:.2f}s")
            report.append(f"  预测次数: {metrics['total_predictions']}")
            report.append("")

        return "\n".join(report)

    def reset_monitoring(self):
        """重置监控数据"""
        print("🔄 重置性能监控数据...")
        self.performance_data = []
        for module_name in self.module_metrics:
            self.module_metrics[module_name] = {'hit_rates': [], 'response_times': []}

        return True

    def export_performance_data(self, filename: str = None) -> bool:
        """导出性能数据"""
        if not filename:
            filename = f"performance_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        try:
            export_data = {
                'export_time': datetime.now().isoformat(),
                'summary': self.get_performance_summary(),
                'raw_data': self.performance_data
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            print(f"✅ 性能数据已导出到: {filename}")
            return True

        except Exception as e:
            print(f"❌ 导出性能数据失败: {e}")
            return False
