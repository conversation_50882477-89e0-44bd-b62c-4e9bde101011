"""
生肖维度扩展分析模块
核心功能：预测下期特码可能的4个最高得分生肖
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from datetime import date, datetime, timedelta
from collections import Counter, defaultdict
from loguru import logger
import json

class ZodiacExtendedAnalysis:
    """生肖维度扩展分析器"""
    
    def __init__(self, db_session, number_attribute_mapper):
        self.db = db_session
        self.attr_mapper = number_attribute_mapper
        
        # 12生肖
        self.zodiac_names = ["鼠", "牛", "虎", "兔", "龙", "蛇", 
                            "马", "羊", "猴", "鸡", "狗", "猪"]
        
        # 16个扩展维度
        self.zodiac_dimensions = {
            "琴棋书画": {
                "琴": ["兔", "蛇", "鸡"],
                "棋": ["鼠", "牛", "狗"], 
                "书": ["马", "龙", "虎"],
                "画": ["羊", "猴", "猪"]
            },
            "四季分类": {
                "春": ["虎", "兔", "龙"],
                "夏": ["蛇", "马", "羊"],
                "秋": ["猴", "鸡", "狗"], 
                "冬": ["猪", "鼠", "牛"]
            },
            "波色分类": {
                "红肖": ["鼠", "兔", "马", "鸡"],
                "绿肖": ["牛", "龙", "羊", "狗"],
                "蓝肖": ["虎", "蛇", "猴", "猪"]
            },
            "昼夜分类": {
                "日肖": ["兔", "龙", "蛇", "马", "羊", "猴"],
                "夜肖": ["鼠", "牛", "虎", "鸡", "狗", "猪"]
            },
            "左右分类": {
                "左肖": ["鼠", "牛", "龙", "蛇", "猴", "鸡"],
                "右肖": ["虎", "兔", "马", "羊", "狗", "猪"]
            },
            "阴阳分类": {
                "阴肖": ["鼠", "龙", "蛇", "马", "狗", "猪"],
                "阳肖": ["牛", "虎", "兔", "羊", "猴", "鸡"]
            },
            "合独分类": {
                "合肖": ["龙", "蛇", "猴", "鸡", "狗", "猪"],
                "独肖": ["鼠", "牛", "虎", "兔", "马", "羊"]
            },
            "家野分类": {
                "家肖": ["牛", "马", "羊", "狗", "鸡", "猪"],
                "野肖": ["鼠", "虎", "兔", "龙", "蛇", "猴"]
            },
            "天地分类": {
                "天肖": ["牛", "兔", "龙", "马", "猴", "猪"],
                "地肖": ["鼠", "虎", "蛇", "羊", "鸡", "狗"]
            },
            "男女分类": {
                "男肖": ["鼠", "牛", "虎", "龙", "马", "猴", "狗"],
                "女肖": ["兔", "蛇", "羊", "鸡", "猪"]
            },
            "吉凶分类": {
                "吉肖": ["兔", "龙", "蛇", "马", "羊", "鸡"],
                "凶肖": ["鼠", "牛", "虎", "猴", "狗", "猪"]
            },
            "前后分类": {
                "前肖": ["鼠", "牛", "虎", "兔", "龙", "蛇"],
                "后肖": ["马", "羊", "猴", "鸡", "狗", "猪"]
            },
            "笔画分类": {
                "单笔": ["鼠", "龙", "马", "蛇", "鸡", "猪"],
                "双笔": ["虎", "猴", "狗", "兔", "羊", "牛"]
            },
            "胆量分类": {
                "胆大": ["牛", "虎", "马", "猴", "狗", "猪"],
                "胆小": ["鼠", "兔", "龙", "蛇", "羊", "鸡"]
            }
        }
        
        # 预测方法权重
        self.method_weights = {
            "frequency_analysis": 0.25,    # 频率分析
            "cycle_analysis": 0.20,        # 周期分析
            "correlation_analysis": 0.20,  # 关联分析
            "pattern_analysis": 0.15,      # 模式分析
            "dimension_balance": 0.20       # 维度平衡分析
        }
    
    def predict_next_zodiac_range(self, recent_periods: int = 50, target_date: date = None) -> Dict[str, Any]:
        """预测下期可能的4个生肖"""
        logger.info(f"开始生肖维度扩展分析，分析最近 {recent_periods} 期数据")
        
        if target_date is None:
            target_date = date.today()
        
        # 获取历史数据
        historical_data = self._get_historical_data(recent_periods, target_date)
        
        if len(historical_data) < 10:
            logger.warning(f"历史数据不足，仅有 {len(historical_data)} 期")
            return self._get_default_prediction()
        
        # 执行多种分析方法
        analysis_results = {}
        
        # 1. 频率分析
        analysis_results["frequency"] = self._frequency_analysis(historical_data)
        
        # 2. 周期分析
        analysis_results["cycle"] = self._cycle_analysis(historical_data)
        
        # 3. 关联分析
        analysis_results["correlation"] = self._correlation_analysis(historical_data)
        
        # 4. 模式分析
        analysis_results["pattern"] = self._pattern_analysis(historical_data)
        
        # 5. 维度平衡分析
        analysis_results["dimension_balance"] = self._dimension_balance_analysis(historical_data)
        
        # 综合评分
        final_scores = self._calculate_final_scores(analysis_results)
        
        # 选择得分最高的4个生肖
        top_4_zodiac = self._select_top_zodiac(final_scores, count=4)
        
        # 生成预测结果
        prediction_result = {
            "predicted_zodiac": top_4_zodiac,
            "zodiac_scores": final_scores,
            "analysis_details": analysis_results,
            "confidence_level": self._calculate_confidence_level(final_scores, top_4_zodiac),
            "prediction_time": datetime.now(),
            "data_periods": len(historical_data),
            "method_weights": self.method_weights
        }
        
        logger.info(f"生肖预测完成，推荐生肖: {top_4_zodiac}")
        return prediction_result
    
    def _get_historical_data(self, recent_periods: int, target_date: date) -> List[Dict]:
        """获取历史数据"""
        from src.data_layer.database.models import LotteryData
        
        # 获取目标日期之前的数据
        query = self.db.query(LotteryData).filter(
            LotteryData.draw_date < target_date
        ).order_by(LotteryData.draw_date.desc()).limit(recent_periods)
        
        records = query.all()
        
        historical_data = []
        for record in records:
            historical_data.append({
                'period_number': record.period_number,
                'draw_date': record.draw_date,
                'special_number': record.special_number,
                'special_zodiac': record.special_zodiac,
                'lunar_year': record.lunar_year,
                'zodiac_color_wave': record.zodiac_color_wave,
                'zodiac_season': record.zodiac_season,
                'zodiac_day_night': record.zodiac_day_night,
                'zodiac_left_right': record.zodiac_left_right,
                'zodiac_yin_yang': record.zodiac_yin_yang,
                'zodiac_courage': record.zodiac_courage,
                'zodiac_fortune': record.zodiac_fortune,
                'zodiac_group': record.zodiac_group,
                'zodiac_origin': record.zodiac_origin,
                'zodiac_heaven_earth': record.zodiac_heaven_earth,
                'zodiac_gender': record.zodiac_gender,
                'zodiac_position': record.zodiac_position,
                'zodiac_strokes': record.zodiac_strokes,
                'zodiac_combine': record.zodiac_combine
            })
        
        return historical_data
    
    def _frequency_analysis(self, historical_data: List[Dict]) -> Dict[str, float]:
        """频率分析：基于历史出现频率"""
        zodiac_counts = Counter([record['special_zodiac'] for record in historical_data])
        total_count = len(historical_data)
        
        # 计算频率得分
        frequency_scores = {}
        for zodiac in self.zodiac_names:
            count = zodiac_counts.get(zodiac, 0)
            frequency = count / total_count if total_count > 0 else 0
            
            # 冷热号分析：冷号给予更高分数（回补理论）
            if frequency < 0.05:  # 冷号
                score = 0.8
            elif frequency > 0.15:  # 热号
                score = 0.3
            else:  # 正常
                score = 0.5
            
            frequency_scores[zodiac] = score
        
        return frequency_scores
    
    def _cycle_analysis(self, historical_data: List[Dict]) -> Dict[str, float]:
        """周期分析：基于生肖出现的周期性"""
        cycle_scores = {}
        
        for zodiac in self.zodiac_names:
            # 找到该生肖最近出现的位置
            last_positions = []
            for i, record in enumerate(historical_data):
                if record['special_zodiac'] == zodiac:
                    last_positions.append(i)
            
            if not last_positions:
                cycle_scores[zodiac] = 0.5  # 没有出现过，给中等分数
                continue
            
            # 计算间隔
            intervals = []
            for i in range(1, len(last_positions)):
                intervals.append(last_positions[i-1] - last_positions[i])
            
            if not intervals:
                # 只出现过一次
                last_pos = last_positions[0]
                if last_pos > 10:  # 很久没出现
                    cycle_scores[zodiac] = 0.8
                else:
                    cycle_scores[zodiac] = 0.3
            else:
                # 基于平均间隔预测
                avg_interval = np.mean(intervals)
                last_pos = last_positions[0]
                
                if last_pos >= avg_interval * 0.8:  # 接近预期出现时间
                    cycle_scores[zodiac] = 0.8
                elif last_pos < avg_interval * 0.3:  # 刚刚出现过
                    cycle_scores[zodiac] = 0.2
                else:
                    cycle_scores[zodiac] = 0.5
        
        return cycle_scores
    
    def _correlation_analysis(self, historical_data: List[Dict]) -> Dict[str, float]:
        """关联分析：基于生肖间的关联关系"""
        correlation_scores = {}
        
        if len(historical_data) < 2:
            return {zodiac: 0.5 for zodiac in self.zodiac_names}
        
        # 分析最近几期的生肖模式
        recent_zodiac = [record['special_zodiac'] for record in historical_data[:5]]
        
        for zodiac in self.zodiac_names:
            score = 0.5  # 基础分数
            
            # 检查是否与最近生肖有关联
            for dimension, categories in self.zodiac_dimensions.items():
                for category, zodiac_list in categories.items():
                    if zodiac in zodiac_list:
                        # 检查同类生肖最近的出现情况
                        same_category_recent = sum(1 for z in recent_zodiac if z in zodiac_list)
                        category_size = len(zodiac_list)
                        
                        if same_category_recent < category_size * 0.3:  # 该类生肖出现较少
                            score += 0.1
                        elif same_category_recent > category_size * 0.7:  # 该类生肖出现较多
                            score -= 0.1
            
            correlation_scores[zodiac] = max(0.1, min(0.9, score))
        
        return correlation_scores
    
    def _pattern_analysis(self, historical_data: List[Dict]) -> Dict[str, float]:
        """模式分析：识别生肖出现的模式"""
        pattern_scores = {}
        
        # 分析连续出现模式
        consecutive_patterns = self._analyze_consecutive_patterns(historical_data)
        
        # 分析跳跃模式
        jump_patterns = self._analyze_jump_patterns(historical_data)
        
        for zodiac in self.zodiac_names:
            score = 0.5
            
            # 连续模式评分
            if zodiac in consecutive_patterns:
                if consecutive_patterns[zodiac]['should_appear']:
                    score += 0.2
                else:
                    score -= 0.1
            
            # 跳跃模式评分
            if zodiac in jump_patterns:
                if jump_patterns[zodiac]['should_appear']:
                    score += 0.2
                else:
                    score -= 0.1
            
            pattern_scores[zodiac] = max(0.1, min(0.9, score))
        
        return pattern_scores
    
    def _dimension_balance_analysis(self, historical_data: List[Dict]) -> Dict[str, float]:
        """维度平衡分析：基于各维度的平衡性"""
        balance_scores = {}
        
        # 分析各维度的分布情况
        dimension_stats = {}
        for dimension, categories in self.zodiac_dimensions.items():
            dimension_stats[dimension] = {}
            for category, zodiac_list in categories.items():
                count = sum(1 for record in historical_data[:10] 
                           if record['special_zodiac'] in zodiac_list)
                dimension_stats[dimension][category] = count
        
        # 为每个生肖计算平衡得分
        for zodiac in self.zodiac_names:
            score = 0.5
            
            for dimension, categories in self.zodiac_dimensions.items():
                for category, zodiac_list in categories.items():
                    if zodiac in zodiac_list:
                        category_count = dimension_stats[dimension][category]
                        expected_count = 10 / len(categories)  # 期望出现次数
                        
                        if category_count < expected_count * 0.7:  # 该类别出现不足
                            score += 0.05
                        elif category_count > expected_count * 1.3:  # 该类别出现过多
                            score -= 0.05
            
            balance_scores[zodiac] = max(0.1, min(0.9, score))
        
        return balance_scores
    
    def _analyze_consecutive_patterns(self, historical_data: List[Dict]) -> Dict[str, Dict]:
        """分析连续出现模式"""
        patterns = {}
        
        for zodiac in self.zodiac_names:
            # 查找连续出现的模式
            consecutive_count = 0
            for record in historical_data:
                if record['special_zodiac'] == zodiac:
                    consecutive_count += 1
                    break
                consecutive_count += 1
            
            patterns[zodiac] = {
                'last_gap': consecutive_count,
                'should_appear': consecutive_count > 8  # 超过8期未出现
            }
        
        return patterns
    
    def _analyze_jump_patterns(self, historical_data: List[Dict]) -> Dict[str, Dict]:
        """分析跳跃模式"""
        patterns = {}
        
        for zodiac in self.zodiac_names:
            positions = []
            for i, record in enumerate(historical_data):
                if record['special_zodiac'] == zodiac:
                    positions.append(i)
            
            should_appear = False
            if len(positions) >= 2:
                # 计算间隔模式
                intervals = [positions[i] - positions[i+1] for i in range(len(positions)-1)]
                if intervals:
                    avg_interval = np.mean(intervals)
                    if len(positions) > 0 and positions[0] >= avg_interval * 0.8:
                        should_appear = True
            
            patterns[zodiac] = {
                'positions': positions,
                'should_appear': should_appear
            }
        
        return patterns
    
    def _calculate_final_scores(self, analysis_results: Dict[str, Dict[str, float]]) -> Dict[str, float]:
        """计算最终综合得分"""
        final_scores = {}
        
        for zodiac in self.zodiac_names:
            total_score = 0
            
            for method, weight in self.method_weights.items():
                if method in analysis_results:
                    method_score = analysis_results[method].get(zodiac, 0.5)
                    total_score += method_score * weight
            
            final_scores[zodiac] = total_score
        
        return final_scores
    
    def _select_top_zodiac(self, scores: Dict[str, float], count: int = 4) -> List[str]:
        """选择得分最高的生肖"""
        sorted_zodiac = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        return [zodiac for zodiac, score in sorted_zodiac[:count]]
    
    def _calculate_confidence_level(self, scores: Dict[str, float], top_zodiac: List[str]) -> float:
        """计算预测置信度"""
        if not top_zodiac:
            return 0.0
        
        top_scores = [scores[zodiac] for zodiac in top_zodiac]
        avg_top_score = np.mean(top_scores)
        
        all_scores = list(scores.values())
        avg_all_score = np.mean(all_scores)
        
        # 置信度基于top生肖与平均分的差距
        confidence = min(0.95, max(0.1, (avg_top_score - avg_all_score) * 2 + 0.5))
        return confidence
    
    def _get_default_prediction(self) -> Dict[str, Any]:
        """获取默认预测结果（数据不足时）"""
        # 基于传统经验的默认预测
        default_zodiac = ["龙", "虎", "马", "鸡"]  # 常见的吉祥生肖
        
        return {
            "predicted_zodiac": default_zodiac,
            "zodiac_scores": {zodiac: 0.5 for zodiac in self.zodiac_names},
            "analysis_details": {},
            "confidence_level": 0.3,
            "prediction_time": datetime.now(),
            "data_periods": 0,
            "method_weights": self.method_weights,
            "note": "数据不足，使用默认预测"
        }
    
    def get_zodiac_analysis_report(self, prediction_result: Dict[str, Any]) -> str:
        """生成生肖分析报告"""
        report = []
        report.append("=== 生肖维度扩展分析报告 ===")
        report.append(f"预测时间: {prediction_result['prediction_time']}")
        report.append(f"分析期数: {prediction_result['data_periods']}")
        report.append(f"置信度: {prediction_result['confidence_level']:.2%}")
        report.append("")
        
        report.append("推荐生肖:")
        for i, zodiac in enumerate(prediction_result['predicted_zodiac'], 1):
            score = prediction_result['zodiac_scores'][zodiac]
            report.append(f"  {i}. {zodiac} (得分: {score:.3f})")
        
        report.append("")
        report.append("所有生肖得分:")
        sorted_scores = sorted(prediction_result['zodiac_scores'].items(), 
                              key=lambda x: x[1], reverse=True)
        for zodiac, score in sorted_scores:
            report.append(f"  {zodiac}: {score:.3f}")
        
        return "\n".join(report)
