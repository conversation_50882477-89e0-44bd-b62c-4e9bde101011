import re

# 读取完美预测系统代码
with open("src/perfect_prediction_system.py", "r", encoding="utf-8") as f:
    content = f.read()

print("🔍 完美预测系统代码分析")
print("=" * 50)

# 查找模组初始化代码
print("📋 模组初始化代码分析:")

# 查找initialize_modules方法
init_method_start = content.find("def initialize_modules")
if init_method_start != -1:
    print("✅ 找到initialize_modules方法")
    
    # 提取方法内容
    lines = content[init_method_start:].split('\n')
    method_lines = []
    indent_level = None
    
    for i, line in enumerate(lines):
        if i == 0:  # 方法定义行
            method_lines.append(line)
            continue
        
        # 确定缩进级别
        if indent_level is None and line.strip():
            indent_level = len(line) - len(line.lstrip())
        
        # 如果遇到同级别或更低级别的定义，停止
        if line.strip() and not line.startswith(' ' * indent_level) and not line.startswith('\t'):
            break
        
        method_lines.append(line)
        
        # 限制输出长度
        if len(method_lines) > 50:
            break
    
    print("📊 initialize_modules方法内容:")
    for i, line in enumerate(method_lines[:30]):
        print(f"   {i+1:2d}: {line}")
    
    # 分析模组初始化
    print(f"\n🔧 模组初始化分析:")
    
    # 查找各模组的初始化
    modules = [
        ('traditional_analysis', '传统分析'),
        ('machine_learning', '机器学习'),
        ('zodiac_extended', '生肖扩展'),
        ('special_zodiac', '特码生肖')
    ]
    
    method_content = '\n'.join(method_lines)
    
    for module_name, display_name in modules:
        if module_name in method_content:
            print(f"   ✅ {display_name}: 在初始化方法中找到")
            
            # 查找具体的初始化代码
            pattern = rf"self\.{module_name}.*=.*"
            matches = re.findall(pattern, method_content)
            if matches:
                for match in matches:
                    print(f"      代码: {match.strip()}")
        else:
            print(f"   ❌ {display_name}: 未在初始化方法中找到")
    
else:
    print("❌ 未找到initialize_modules方法")

# 查找Mock类定义
print(f"\n🎭 Mock类定义分析:")
mock_classes = [
    'MockTraditionalAnalysis',
    'MockMachineLearning', 
    'MockZodiacExtended',
    'MockSpecialZodiacModule'
]

for mock_class in mock_classes:
    if mock_class in content:
        print(f"   ✅ {mock_class}: 已定义")
        
        # 查找类的方法
        class_start = content.find(f"class {mock_class}")
        if class_start != -1:
            class_section = content[class_start:class_start+1000]
            methods = re.findall(r"def (\w+)\(", class_section)
            if methods:
                print(f"      方法: {', '.join(methods[:5])}")
    else:
        print(f"   ❌ {mock_class}: 未定义")

# 查找预测方法
print(f"\n🎯 预测方法分析:")
prediction_methods = [
    'run_complete_prediction',
    '_predict_with_modules',
    '_fuse_predictions'
]

for method in prediction_methods:
    if f"def {method}" in content:
        print(f"   ✅ {method}: 已定义")
    else:
        print(f"   ❌ {method}: 未定义")
