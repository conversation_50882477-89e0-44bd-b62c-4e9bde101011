"""
统一年份映射模块
处理农历年份、生肖、五行的动态映射
确保所有预测和回测模块使用一致的映射逻辑
"""

import sqlite3
from datetime import datetime, date
from typing import Dict, List, Tuple, Optional
import logging

class UnifiedYearMapping:
    """统一年份映射管理器"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # 缓存映射数据
        self._lunar_year_cache = {}
        self._zodiac_mapping_cache = {}
        self._wuxing_mapping_cache = {}
        
        # 初始化映射数据
        self._load_mapping_data()
    
    def _load_mapping_data(self):
        """加载映射数据到缓存"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 加载农历年份映射
            cursor.execute("""
                SELECT lunar_year, zodiac_name, spring_festival_date, 
                       year_start_date, year_end_date 
                FROM lunar_year_mapping 
                ORDER BY lunar_year
            """)
            
            for row in cursor.fetchall():
                lunar_year, zodiac_name, spring_date, start_date, end_date = row
                self._lunar_year_cache[lunar_year] = {
                    'zodiac': zodiac_name,
                    'spring_festival': spring_date,
                    'start_date': start_date,
                    'end_date': end_date
                }
            
            conn.close()
            self.logger.info(f"已加载 {len(self._lunar_year_cache)} 个农历年份映射")
            
        except Exception as e:
            self.logger.error(f"加载映射数据失败: {e}")
    
    def get_lunar_year_from_date(self, target_date: str) -> int:
        """根据日期获取对应的农历年份"""
        try:
            if isinstance(target_date, str):
                target_date = datetime.strptime(target_date, '%Y-%m-%d').date()
            elif isinstance(target_date, datetime):
                target_date = target_date.date()
            
            target_str = target_date.strftime('%Y-%m-%d')
            
            # 查找对应的农历年份
            for lunar_year, info in self._lunar_year_cache.items():
                if info['start_date'] <= target_str <= info['end_date']:
                    return lunar_year
            
            # 如果没找到，使用自然年份作为fallback
            self.logger.warning(f"未找到日期 {target_str} 对应的农历年份，使用自然年份")
            return target_date.year
            
        except Exception as e:
            self.logger.error(f"获取农历年份失败: {e}")
            return datetime.now().year
    
    def get_zodiac_mapping(self, year: int) -> Dict[str, List[int]]:
        """获取指定年份的生肖映射"""
        if year in self._zodiac_mapping_cache:
            return self._zodiac_mapping_cache[year]
        
        # 标准生肖映射数据
        standard_mappings = {
            2023: {
                "鼠": [4, 16, 28, 40],
                "牛": [3, 15, 27, 39],
                "虎": [2, 14, 26, 38],
                "兔": [1, 13, 25, 37, 49],
                "龙": [12, 24, 36, 48],
                "蛇": [11, 23, 35, 47],
                "马": [10, 22, 34, 46],
                "羊": [9, 21, 33, 45],
                "猴": [8, 20, 32, 44],
                "鸡": [7, 19, 31, 43],
                "狗": [6, 18, 30, 42],
                "猪": [5, 17, 29, 41]
            },
            2024: {
                "鼠": [5, 17, 29, 41],
                "牛": [4, 16, 28, 40],
                "虎": [3, 15, 27, 39],
                "兔": [2, 14, 26, 38],
                "龙": [1, 13, 25, 37, 49],
                "蛇": [12, 24, 36, 48],
                "马": [11, 23, 35, 47],
                "羊": [10, 22, 34, 46],
                "猴": [9, 21, 33, 45],
                "鸡": [8, 20, 32, 44],
                "狗": [7, 19, 31, 43],
                "猪": [6, 18, 30, 42]
            },
            2025: {
                "鼠": [6, 18, 30, 42],
                "牛": [5, 17, 29, 41],
                "虎": [4, 16, 28, 40],
                "兔": [3, 15, 27, 39],
                "龙": [2, 14, 26, 38],
                "蛇": [1, 13, 25, 37, 49],
                "马": [12, 24, 36, 48],
                "羊": [11, 23, 35, 47],
                "猴": [10, 22, 34, 46],
                "鸡": [9, 21, 33, 45],
                "狗": [8, 20, 32, 44],
                "猪": [7, 19, 31, 43]
            }
        }
        
        if year in standard_mappings:
            self._zodiac_mapping_cache[year] = standard_mappings[year]
            return standard_mappings[year]
        
        # 如果年份不在标准映射中，使用最近年份的映射
        closest_year = min(standard_mappings.keys(), key=lambda x: abs(x - year))
        self.logger.warning(f"年份 {year} 不在标准映射中，使用 {closest_year} 年映射")
        return standard_mappings[closest_year]
    
    def get_wuxing_mapping(self, year: int) -> Dict[str, List[int]]:
        """获取指定年份的五行映射"""
        if year in self._wuxing_mapping_cache:
            return self._wuxing_mapping_cache[year]
        
        # 标准五行映射数据
        standard_wuxing = {
            2023: {
                "金": [1, 2, 9, 10, 23, 24, 31, 32, 39, 40],
                "木": [5, 6, 13, 14, 21, 22, 35, 36, 43, 44],
                "水": [11, 12, 19, 20, 27, 28, 41, 42, 49],
                "火": [7, 8, 15, 16, 29, 30, 37, 38, 45, 46],
                "土": [3, 4, 17, 18, 25, 26, 33, 34, 47, 48]
            },
            2024: {
                "金": [2, 3, 10, 11, 24, 25, 32, 33, 40, 41],
                "木": [6, 7, 14, 15, 22, 23, 36, 37, 44, 45],
                "水": [12, 13, 20, 21, 28, 29, 42, 43],
                "火": [1, 8, 9, 16, 17, 30, 31, 38, 39, 46, 47],
                "土": [4, 5, 18, 19, 26, 27, 34, 35, 48, 49]
            },
            2025: {
                "金": [3, 4, 11, 12, 25, 26, 33, 34, 41, 42],
                "木": [7, 8, 15, 16, 23, 24, 37, 38, 45, 46],
                "水": [13, 14, 21, 22, 29, 30, 43, 44],
                "火": [1, 2, 9, 10, 17, 18, 31, 32, 39, 40, 47, 48],
                "土": [5, 6, 19, 20, 27, 28, 35, 36, 49]
            }
        }
        
        if year in standard_wuxing:
            self._wuxing_mapping_cache[year] = standard_wuxing[year]
            return standard_wuxing[year]
        
        # 如果年份不在标准映射中，使用最近年份的映射
        closest_year = min(standard_wuxing.keys(), key=lambda x: abs(x - year))
        self.logger.warning(f"年份 {year} 不在五行映射中，使用 {closest_year} 年映射")
        return standard_wuxing[closest_year]
    
    def get_number_zodiac(self, number: int, target_date: str) -> str:
        """根据号码和日期获取对应的生肖"""
        lunar_year = self.get_lunar_year_from_date(target_date)
        zodiac_mapping = self.get_zodiac_mapping(lunar_year)
        
        for zodiac, numbers in zodiac_mapping.items():
            if number in numbers:
                return zodiac
        
        return "未知"
    
    def get_number_wuxing(self, number: int, target_date: str) -> str:
        """根据号码和日期获取对应的五行"""
        lunar_year = self.get_lunar_year_from_date(target_date)
        wuxing_mapping = self.get_wuxing_mapping(lunar_year)
        
        for element, numbers in wuxing_mapping.items():
            if number in numbers:
                return element
        
        return "未知"
    
    def get_zodiac_numbers(self, zodiac: str, target_date: str) -> List[int]:
        """根据生肖和日期获取对应的号码列表"""
        lunar_year = self.get_lunar_year_from_date(target_date)
        zodiac_mapping = self.get_zodiac_mapping(lunar_year)
        
        return zodiac_mapping.get(zodiac, [])
    
    def get_wuxing_numbers(self, element: str, target_date: str) -> List[int]:
        """根据五行和日期获取对应的号码列表"""
        lunar_year = self.get_lunar_year_from_date(target_date)
        wuxing_mapping = self.get_wuxing_mapping(lunar_year)
        
        return wuxing_mapping.get(element, [])
    
    def validate_mapping_consistency(self, target_date: str) -> Dict[str, bool]:
        """验证映射一致性"""
        lunar_year = self.get_lunar_year_from_date(target_date)
        zodiac_mapping = self.get_zodiac_mapping(lunar_year)
        wuxing_mapping = self.get_wuxing_mapping(lunar_year)
        
        results = {
            "zodiac_complete": True,
            "wuxing_complete": True,
            "no_overlap": True,
            "all_numbers_covered": True
        }
        
        # 检查生肖映射完整性
        zodiac_numbers = set()
        for numbers in zodiac_mapping.values():
            zodiac_numbers.update(numbers)
        
        if len(zodiac_numbers) != 49:
            results["zodiac_complete"] = False
        
        # 检查五行映射完整性
        wuxing_numbers = set()
        for numbers in wuxing_mapping.values():
            wuxing_numbers.update(numbers)
        
        if len(wuxing_numbers) != 49:
            results["wuxing_complete"] = False
        
        # 检查是否覆盖所有号码
        expected_numbers = set(range(1, 50))
        if zodiac_numbers != expected_numbers or wuxing_numbers != expected_numbers:
            results["all_numbers_covered"] = False
        
        return results
    
    def get_spring_festival_info(self, year: int) -> Dict[str, str]:
        """获取春节信息"""
        if year in self._lunar_year_cache:
            info = self._lunar_year_cache[year]
            return {
                "zodiac": info['zodiac'],
                "spring_festival": info['spring_festival'],
                "start_date": info['start_date'],
                "end_date": info['end_date']
            }
        
        return {}

# 创建全局实例
year_mapper = UnifiedYearMapping()

# 便捷函数
def get_lunar_year(target_date: str) -> int:
    """获取农历年份"""
    return year_mapper.get_lunar_year_from_date(target_date)

def get_zodiac_for_number(number: int, target_date: str) -> str:
    """获取号码对应的生肖"""
    return year_mapper.get_number_zodiac(number, target_date)

def get_wuxing_for_number(number: int, target_date: str) -> str:
    """获取号码对应的五行"""
    return year_mapper.get_number_wuxing(number, target_date)

def get_numbers_for_zodiac(zodiac: str, target_date: str) -> List[int]:
    """获取生肖对应的号码"""
    return year_mapper.get_zodiac_numbers(zodiac, target_date)

def get_numbers_for_wuxing(element: str, target_date: str) -> List[int]:
    """获取五行对应的号码"""
    return year_mapper.get_wuxing_numbers(element, target_date)
