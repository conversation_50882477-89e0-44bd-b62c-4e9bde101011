"""
修复Windows版本的完美预测系统和融合管理器问题
"""

import os
import shutil
import json
from pathlib import Path

def fix_windows_version():
    """修复Windows版本缺少的文件和配置"""
    
    print("🔧 开始修复Windows版本...")
    
    # 目标目录
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    
    if not windows_dir.exists():
        print("❌ Windows版本目录不存在")
        return False
    
    print(f"📁 目标目录: {windows_dir}")
    
    # 1. 复制缺少的源码目录
    print("\n1. 复制源码目录...")
    src_source = Path("src")
    src_target = windows_dir / "src"
    
    if src_source.exists():
        if src_target.exists():
            shutil.rmtree(src_target)
        shutil.copytree(src_source, src_target)
        print(f"✅ 复制源码目录: {src_target}")
    else:
        print("❌ 源码目录不存在")
        return False
    
    # 2. 复制配置目录
    print("\n2. 复制配置目录...")
    config_source = Path("config")
    config_target = windows_dir / "config"
    
    if config_source.exists():
        if config_target.exists():
            shutil.rmtree(config_target)
        shutil.copytree(config_source, config_target)
        print(f"✅ 复制配置目录: {config_target}")
    else:
        print("⚠️ 配置目录不存在，创建默认配置")
        config_target.mkdir(exist_ok=True)
    
    # 3. 复制关键配置文件
    print("\n3. 复制关键配置文件...")
    config_files = [
        "optimal_config.json",
        "optimization_config.json", 
        "enhanced_backtest_config.json",
        "optimal_prediction_config.json"
    ]
    
    for config_file in config_files:
        source_file = Path(config_file)
        if source_file.exists():
            target_file = windows_dir / config_file
            shutil.copy2(source_file, target_file)
            print(f"✅ 复制配置文件: {config_file}")
        else:
            print(f"⚠️ 配置文件不存在: {config_file}")
    
    # 4. 创建缺少的配置文件
    print("\n4. 创建缺少的配置文件...")
    
    # 创建完美预测系统配置
    perfect_config = {
        "modules": {
            "traditional_analysis": {"enabled": True, "weight": 0.35},
            "machine_learning": {"enabled": True, "weight": 0.4},
            "zodiac_extended": {"enabled": True, "weight": 0.15},
            "special_zodiac": {"enabled": True, "weight": 0.1}
        },
        "fusion_strategy": "enhanced_fusion",
        "confidence_threshold": 0.82,
        "stability_target": 0.75,
        "optimization_enabled": True
    }
    
    perfect_config_file = config_target / "perfect_prediction_config.json"
    with open(perfect_config_file, 'w', encoding='utf-8') as f:
        json.dump(perfect_config, f, ensure_ascii=False, indent=2)
    print(f"✅ 创建完美预测配置: {perfect_config_file}")
    
    # 创建融合管理器配置
    fusion_config = {
        "fusion_weights": {
            "traditional_analysis": 0.35,
            "machine_learning": 0.4,
            "zodiac_extended": 0.15,
            "special_zodiac": 0.1
        },
        "filter_thresholds": {
            "voting_threshold": 0.65,
            "confidence_threshold": 0.7,
            "diversity_factor": 0.25,
            "hit_rate_threshold": 0.6
        },
        "prediction_strategy": "enhanced_fusion"
    }
    
    fusion_config_file = windows_dir / "fusion_config.json"
    with open(fusion_config_file, 'w', encoding='utf-8') as f:
        json.dump(fusion_config, f, ensure_ascii=False, indent=2)
    print(f"✅ 创建融合管理器配置: {fusion_config_file}")
    
    # 5. 复制主要Python文件
    print("\n5. 复制主要Python文件...")
    main_files = [
        "lottery_prediction_gui.py",
        "main.py",
        "special_number_predictor.py",
        "consistent_predictor.py"
    ]
    
    for main_file in main_files:
        source_file = Path(main_file)
        if source_file.exists():
            target_file = windows_dir / main_file
            shutil.copy2(source_file, target_file)
            print(f"✅ 复制主文件: {main_file}")
        else:
            print(f"⚠️ 主文件不存在: {main_file}")
    
    # 6. 创建启动脚本
    print("\n6. 创建启动脚本...")
    
    # 更新启动脚本
    start_script = '''@echo off
chcp 65001 >nul
title 六合彩预测系统

echo 启动六合彩预测系统...
echo ================================

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo Python未安装，正在启动EXE版本...
    if exist "六合彩预测系统_v2.0.0.exe" (
        start "六合彩预测系统" "六合彩预测系统_v2.0.0.exe"
    ) else (
        echo EXE文件不存在
        pause
        exit /b 1
    )
) else (
    echo 检测到Python环境，启动Python版本...
    if exist "lottery_prediction_gui.py" (
        python lottery_prediction_gui.py
    ) else (
        echo Python文件不存在，启动EXE版本...
        if exist "六合彩预测系统_v2.0.0.exe" (
            start "六合彩预测系统" "六合彩预测系统_v2.0.0.exe"
        ) else (
            echo 所有启动文件都不存在
            pause
            exit /b 1
        )
    )
)

echo 程序已启动
pause
'''
    
    start_script_file = windows_dir / "启动系统.bat"
    with open(start_script_file, 'w', encoding='utf-8') as f:
        f.write(start_script)
    print(f"✅ 更新启动脚本: {start_script_file}")
    
    # 7. 创建README文件
    print("\n7. 创建README文件...")
    
    readme_content = '''# 六合彩预测系统 Windows版

## 版本信息
- 版本: v2.0.0
- 构建日期: 2025-06-24
- 类型: Windows完整版

## 文件说明
- `六合彩预测系统_v2.0.0.exe`: 主程序EXE文件
- `lottery_prediction_gui.py`: Python源码版本
- `src/`: 完整源码目录
- `config/`: 配置文件目录
- `data/`: 数据目录

## 使用方法

### 方法1: 直接运行EXE（推荐）
1. 双击 `六合彩预测系统_v2.0.0.exe`
2. 等待程序启动（首次启动较慢）

### 方法2: 使用启动脚本
1. 双击 `启动系统.bat`
2. 脚本会自动选择最佳启动方式

### 方法3: Python环境运行
1. 确保已安装Python 3.7+
2. 安装依赖: `pip install -r requirements.txt`
3. 运行: `python lottery_prediction_gui.py`

## 功能说明
- ✅ 特码预测
- ✅ 完美预测系统
- ✅ 历史回测
- ✅ 增强回测
- ✅ 数据管理
- ✅ 融合管理器

## 故障排除
1. 如果完美预测系统不可用，请检查配置文件
2. 如果融合管理器不可用，请重新启动程序
3. 如果遇到权限问题，请以管理员身份运行

## 技术支持
如有问题，请提供错误信息和系统环境信息。
'''
    
    readme_file = windows_dir / "README.md"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"✅ 创建README文件: {readme_file}")
    
    print("\n🎉 Windows版本修复完成！")
    print("\n📋 修复内容:")
    print("  ✅ 复制完整源码目录")
    print("  ✅ 复制配置目录")
    print("  ✅ 创建必要的配置文件")
    print("  ✅ 更新启动脚本")
    print("  ✅ 创建使用说明")
    
    print(f"\n📁 修复后的目录结构:")
    print(f"  {windows_dir}/")
    print(f"  ├── 六合彩预测系统_v2.0.0.exe")
    print(f"  ├── lottery_prediction_gui.py")
    print(f"  ├── src/ (完整源码)")
    print(f"  ├── config/ (配置文件)")
    print(f"  ├── data/ (数据目录)")
    print(f"  ├── 启动系统.bat")
    print(f"  └── README.md")
    
    return True

if __name__ == "__main__":
    success = fix_windows_version()
    if success:
        print("\n✅ 修复成功！现在可以正常使用完美预测系统和融合管理器了。")
    else:
        print("\n❌ 修复失败，请检查错误信息。")
    
    input("\n按回车键退出...")
