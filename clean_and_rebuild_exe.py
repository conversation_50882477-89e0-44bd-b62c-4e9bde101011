"""
清理旧EXE并重新打包
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_and_rebuild():
    """清理旧文件并重新打包"""
    print("🧹 清理旧EXE并重新打包")
    print("=" * 60)
    
    # 设置路径
    source_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    if not source_dir.exists():
        print("❌ 源目录不存在")
        return False
    
    print(f"📁 源目录: {source_dir}")
    
    # 切换到源目录
    original_dir = Path.cwd()
    os.chdir(source_dir)
    
    try:
        # 1. 清理旧文件
        print("\n1️⃣ 清理旧文件...")
        cleanup_old_files()
        print("✅ 旧文件清理完成")
        
        # 2. 检查PyInstaller
        print("\n2️⃣ 检查PyInstaller...")
        if not check_pyinstaller():
            return False
        print("✅ PyInstaller 已准备就绪")
        
        # 3. 使用最简单的方式打包
        print("\n3️⃣ 执行简单打包...")
        if not simple_package():
            return False
        print("✅ 打包完成")
        
        # 4. 验证结果
        print("\n4️⃣ 验证结果...")
        if verify_exe():
            print("✅ EXE验证成功")
            create_final_package()
            return True
        else:
            print("❌ EXE验证失败")
            return False
            
    finally:
        os.chdir(original_dir)

def cleanup_old_files():
    """清理旧文件"""
    files_to_remove = [
        "六合彩预测系统_v2.0.0.exe",  # 旧的EXE
        "dist",  # 旧的打包目录
        "build",  # 旧的构建目录
        "__pycache__",  # Python缓存
        "*.spec",  # 旧的spec文件
    ]
    
    for item in files_to_remove:
        if "*" in item:
            # 处理通配符
            import glob
            for file_path in glob.glob(item):
                try:
                    if Path(file_path).is_file():
                        Path(file_path).unlink()
                        print(f"  🗑️ 删除文件: {file_path}")
                    elif Path(file_path).is_dir():
                        shutil.rmtree(file_path)
                        print(f"  🗑️ 删除目录: {file_path}")
                except Exception as e:
                    print(f"  ⚠️ 删除失败 {file_path}: {e}")
        else:
            try:
                item_path = Path(item)
                if item_path.exists():
                    if item_path.is_file():
                        item_path.unlink()
                        print(f"  🗑️ 删除文件: {item}")
                    elif item_path.is_dir():
                        shutil.rmtree(item_path)
                        print(f"  🗑️ 删除目录: {item}")
            except Exception as e:
                print(f"  ⚠️ 删除失败 {item}: {e}")

def check_pyinstaller():
    """检查PyInstaller"""
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "show", "pyinstaller"], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("📦 安装PyInstaller...")
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        return True
    except Exception as e:
        print(f"❌ PyInstaller 处理失败: {e}")
        return False

def simple_package():
    """执行简单打包"""
    try:
        # 使用最简单的PyInstaller命令
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",  # 单文件模式
            "--windowed",  # 无控制台
            "--name=六合彩预测系统_v2.0.0_Fixed",
            "--add-data=src;src",
            "--add-data=data;data",
            "--add-data=config;config",
            "--hidden-import=PyQt5.QtCore",
            "--hidden-import=PyQt5.QtGui",
            "--hidden-import=PyQt5.QtWidgets",
            "--hidden-import=sqlite3",
            "--hidden-import=pandas",
            "--hidden-import=numpy",
            "--hidden-import=scikit-learn",
            "--hidden-import=joblib",
            "lottery_prediction_gui.py"
        ]
        
        print(f"🔧 执行打包命令...")
        print("⏳ 这可能需要几分钟时间，请耐心等待...")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 打包成功")
            return True
        else:
            print("❌ 打包失败")
            print("📄 错误信息:")
            print(result.stderr[-1000:])
            
            # 尝试onedir模式
            print("\n🔄 尝试onedir模式...")
            return try_onedir_package()
            
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return False

def try_onedir_package():
    """尝试onedir模式打包"""
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onedir",  # 目录模式
            "--windowed",
            "--name=六合彩预测系统_v2.0.0_Fixed",
            "--add-data=src;src",
            "--add-data=data;data", 
            "--add-data=config;config",
            "--hidden-import=PyQt5.QtCore",
            "--hidden-import=PyQt5.QtGui",
            "--hidden-import=PyQt5.QtWidgets",
            "--hidden-import=sqlite3",
            "--hidden-import=pandas",
            "--hidden-import=numpy",
            "lottery_prediction_gui.py"
        ]
        
        print("🔧 执行onedir模式打包...")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ onedir模式打包成功")
            return True
        else:
            print("❌ onedir模式打包也失败")
            print("📄 错误信息:")
            print(result.stderr[-500:])
            return False
            
    except Exception as e:
        print(f"❌ onedir打包出错: {e}")
        return False

def verify_exe():
    """验证EXE文件"""
    # 检查onefile模式的EXE
    exe_path = Path("dist/六合彩预测系统_v2.0.0_Fixed.exe")
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"✅ 找到单文件EXE: {exe_path}")
        print(f"📊 文件大小: {size_mb:.1f} MB")
        return True
    
    # 检查onedir模式的EXE
    exe_path = Path("dist/六合彩预测系统_v2.0.0_Fixed/六合彩预测系统_v2.0.0_Fixed.exe")
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"✅ 找到目录模式EXE: {exe_path}")
        print(f"📊 文件大小: {size_mb:.1f} MB")
        return True
    
    print("❌ 未找到EXE文件")
    return False

def create_final_package():
    """创建最终发布包"""
    print("\n📦 创建最终发布包...")
    
    # 检查哪种模式成功了
    onefile_exe = Path("dist/六合彩预测系统_v2.0.0_Fixed.exe")
    onedir_exe = Path("dist/六合彩预测系统_v2.0.0_Fixed/六合彩预测系统_v2.0.0_Fixed.exe")
    
    if onefile_exe.exists():
        print("📁 使用单文件模式")
        # 创建发布目录
        release_dir = Path("六合彩预测系统_v2.0.0_Final")
        release_dir.mkdir(exist_ok=True)
        
        # 复制EXE文件
        shutil.copy2(onefile_exe, release_dir / "六合彩预测系统_v2.0.0.exe")
        
        # 复制必要的数据文件
        if Path("data").exists():
            shutil.copytree("data", release_dir / "data", dirs_exist_ok=True)
        if Path("config").exists():
            shutil.copytree("config", release_dir / "config", dirs_exist_ok=True)
        
        create_release_files(release_dir)
        
    elif onedir_exe.exists():
        print("📁 使用目录模式")
        # 重命名目录
        old_dir = Path("dist/六合彩预测系统_v2.0.0_Fixed")
        new_dir = Path("六合彩预测系统_v2.0.0_Final")
        
        if new_dir.exists():
            shutil.rmtree(new_dir)
        shutil.move(str(old_dir), str(new_dir))
        
        # 重命名EXE文件
        old_exe = new_dir / "六合彩预测系统_v2.0.0_Fixed.exe"
        new_exe = new_dir / "六合彩预测系统_v2.0.0.exe"
        if old_exe.exists():
            old_exe.rename(new_exe)
        
        create_release_files(new_dir)
    
    print(f"✅ 最终发布包创建完成: 六合彩预测系统_v2.0.0_Final")

def create_release_files(release_dir):
    """创建发布文件"""
    # 创建启动脚本
    bat_content = '''@echo off
echo Starting Lottery Prediction System...
start "" "六合彩预测系统_v2.0.0.exe"
'''
    
    with open(release_dir / "启动系统.bat", "w", encoding="utf-8") as f:
        f.write(bat_content)
    
    # 创建说明文件
    readme_content = '''六合彩预测系统 v2.0.0 (修复版)

🎉 新版本特点:
✅ 修复了所有已知问题
✅ 性能监控功能正常
✅ 应用最优模式功能正常
✅ 增强回测功能完整
✅ 所有模块交互正常

🚀 使用方法:
1. 双击 "六合彩预测系统_v2.0.0.exe" 启动
2. 或双击 "启动系统.bat" 启动

📋 主要功能:
- 🎯 特码预测
- ⭐ 完美预测系统
- 📈 性能监控
- 🔄 增强回测
- 📊 数据管理
- ⚖️ 融合配置

💡 使用提示:
- 首次使用请先导入历史数据
- 建议使用增强回测优化参数
- 性能监控可查看各模块状态

🔧 系统要求:
- Windows 10/11 (64位)
- 4GB+ 内存
- 1GB+ 磁盘空间

版本: v2.0.0 (修复版)
构建日期: 2025-06-24
'''
    
    with open(release_dir / "使用说明.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)

def main():
    """主函数"""
    print("🎯 清理并重新打包EXE")
    print("=" * 70)
    
    print("⚠️ 注意: 这将删除旧的EXE文件并重新打包")
    confirm = input("是否继续? (y/N): ").strip().lower()
    
    if confirm != 'y':
        print("❌ 操作已取消")
        return False
    
    success = clean_and_rebuild()
    
    if success:
        print("\n🎉 重新打包完成！")
        print("📁 新的EXE位于: 六合彩预测系统_v2.0.0_Final/")
        print("🚀 请测试新的EXE文件")
        print("\n📋 测试清单:")
        print("  ✅ 程序能正常启动")
        print("  ✅ 性能监控功能正常")
        print("  ✅ 应用最优模式功能正常")
        print("  ✅ 所有预测功能正常")
    else:
        print("\n❌ 重新打包失败")
        print("建议手动使用PyInstaller打包")
    
    return success

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
