
# 分析模组显示功能使用指南

## 功能概述
特码预测界面现在包含两个标签页：
1. 🎯 预测结果 - 显示最终预测结果
2. 📊 分析模组 - 显示三大分析模组的详细信息

## 三大分析模组

### 1️⃣ 传统统计分析
**子模块：**
- 📊 频率分析：热号、冷号、平号统计
- 📈 趋势分析：上升趋势、下降趋势号码
- ⏰ 遗漏分析：遗漏期数、回补概率
- 📋 传统分析推荐：基于统计的推荐号码

**显示内容：**
- 各类号码的前5个
- 趋势强度和概率数据
- 综合推荐结果

### 2️⃣ 机器学习分析
**子模块：**
- 🔧 特征工程：特征提取、特征选择
- 🤖 模型训练：随机森林、神经网络等
- 📊 预测评估：准确率、置信度评估
- 🎯 机器学习推荐：基于ML的推荐号码

**显示内容：**
- 特征数量和重要特征
- 模型类型和训练准确率
- 预测置信度和稳定性
- ML推荐结果

### 3️⃣ 多维生肖扩展分析
**子模块：**
- 🐉 生肖频率：12生肖出现频率
- ⚡ 五行分析：金木水火土分析
- 🌸 季节分析：春夏秋冬季节分析
- 🎭 生肖扩展推荐：基于19维分析的推荐

**显示内容：**
- 热门、冷门、平衡生肖
- 强势、弱势五行元素
- 当前季节和季节生肖
- 生肖推荐结果

## 状态指示器
界面底部显示三个模组的运行状态：
- 🔴 未运行：模组尚未执行
- 🟢 已完成：模组成功执行并显示结果
- ⚠️ 显示错误：模组执行出错

## 使用方法
1. 在特码预测界面设置预测参数
2. 点击"开始预测"按钮
3. 切换到"📊 分析模组"标签页查看详细分析
4. 各子模块显示对应的分析结果
5. 查看状态指示器了解模组运行情况

## 数据格式要求
预测结果需要包含以下结构：
- traditional_analysis：传统分析数据
- ml_analysis：机器学习分析数据  
- zodiac_extended_analysis：生肖扩展分析数据

每个模组包含相应的子模块数据和推荐结果。

## 故障排除
- 如果某个模组显示为"未运行"，说明预测结果中缺少对应数据
- 如果显示"显示错误"，检查数据格式是否正确
- 确保预测器返回完整的分析结果结构

生成时间：2025-06-22 15:06:04