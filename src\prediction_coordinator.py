"""
预测系统协调器 - 防止多预测功能交互冲突
管理特码预测、完美预测、历史回测、增强回测之间的协调
"""

import threading
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging
from enum import Enum

from .unified_config_manager import config_manager

class PredictionMode(Enum):
    """预测模式枚举"""
    NONE = "none"
    SPECIAL = "special"
    PERFECT = "perfect"
    HISTORICAL_BACKTEST = "historical_backtest"
    ENHANCED_BACKTEST = "enhanced_backtest"

class PredictionStatus(Enum):
    """预测状态枚举"""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    ERROR = "error"
    CANCELLED = "cancelled"

class PredictionCoordinator:
    """预测系统协调器 - 防止预测功能冲突"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式确保全局唯一协调器"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self.logger = logging.getLogger(__name__)
        self._initialized = True
        self._coordination_lock = threading.RLock()
        
        # 预测任务状态跟踪
        self._active_tasks = {}
        self._task_history = []
        self._resource_locks = {
            'database': threading.Lock(),
            'ml_models': threading.Lock(),
            'config_files': threading.Lock(),
            'cache': threading.Lock()
        }
        
        # 系统状态
        self._current_mode = PredictionMode.NONE
        self._system_busy = False
        self._last_activity = None
        
        # 冲突检测配置
        self._conflict_detection_enabled = True
        self._max_concurrent_tasks = 2
        self._task_timeout = 300  # 5分钟超时
        
        self.logger.info("预测系统协调器初始化完成")
    
    def request_prediction(self, mode: PredictionMode, task_id: str, 
                          params: Dict[str, Any] = None) -> Dict[str, Any]:
        """请求预测任务"""
        with self._coordination_lock:
            try:
                # 检查系统状态
                if not self._can_start_task(mode, task_id):
                    return {
                        'success': False,
                        'error': '系统忙碌或存在冲突',
                        'current_tasks': list(self._active_tasks.keys())
                    }
                
                # 获取资源锁
                required_resources = self._get_required_resources(mode)
                acquired_locks = []
                
                try:
                    for resource in required_resources:
                        if resource in self._resource_locks:
                            lock_acquired = self._resource_locks[resource].acquire(timeout=10)
                            if not lock_acquired:
                                # 释放已获取的锁
                                for acquired_lock in acquired_locks:
                                    self._resource_locks[acquired_lock].release()
                                return {
                                    'success': False,
                                    'error': f'无法获取资源锁: {resource}',
                                    'resource': resource
                                }
                            acquired_locks.append(resource)
                    
                    # 注册任务
                    task_info = {
                        'task_id': task_id,
                        'mode': mode,
                        'status': PredictionStatus.RUNNING,
                        'start_time': datetime.now(),
                        'params': params or {},
                        'acquired_locks': acquired_locks,
                        'thread_id': threading.current_thread().ident
                    }
                    
                    self._active_tasks[task_id] = task_info
                    self._current_mode = mode
                    self._system_busy = True
                    self._last_activity = datetime.now()
                    
                    # 更新配置管理器
                    config_manager.set_active_mode(mode.value)
                    
                    self.logger.info(f"预测任务已启动: {task_id} ({mode.value})")
                    
                    return {
                        'success': True,
                        'task_id': task_id,
                        'mode': mode.value,
                        'acquired_resources': acquired_locks
                    }
                    
                except Exception as e:
                    # 释放已获取的锁
                    for acquired_lock in acquired_locks:
                        if acquired_lock in self._resource_locks:
                            self._resource_locks[acquired_lock].release()
                    raise e
                    
            except Exception as e:
                self.logger.error(f"预测任务请求失败 {task_id}: {e}")
                return {
                    'success': False,
                    'error': str(e)
                }
    
    def complete_prediction(self, task_id: str, result: Dict[str, Any] = None) -> bool:
        """完成预测任务"""
        with self._coordination_lock:
            try:
                if task_id not in self._active_tasks:
                    self.logger.warning(f"任务不存在: {task_id}")
                    return False
                
                task_info = self._active_tasks[task_id]
                
                # 释放资源锁
                for resource in task_info.get('acquired_locks', []):
                    if resource in self._resource_locks:
                        self._resource_locks[resource].release()
                
                # 更新任务状态
                task_info['status'] = PredictionStatus.COMPLETED
                task_info['end_time'] = datetime.now()
                task_info['duration'] = (task_info['end_time'] - task_info['start_time']).total_seconds()
                task_info['result'] = result
                
                # 移动到历史记录
                self._task_history.append(task_info)
                del self._active_tasks[task_id]
                
                # 更新系统状态
                if not self._active_tasks:
                    self._system_busy = False
                    self._current_mode = PredictionMode.NONE
                    config_manager.set_active_mode('none')
                
                self.logger.info(f"预测任务已完成: {task_id}")
                
                # 清理历史记录（保留最近100条）
                if len(self._task_history) > 100:
                    self._task_history = self._task_history[-100:]
                
                return True
                
            except Exception as e:
                self.logger.error(f"预测任务完成失败 {task_id}: {e}")
                return False
    
    def cancel_prediction(self, task_id: str, reason: str = "用户取消") -> bool:
        """取消预测任务"""
        with self._coordination_lock:
            try:
                if task_id not in self._active_tasks:
                    return False
                
                task_info = self._active_tasks[task_id]
                
                # 释放资源锁
                for resource in task_info.get('acquired_locks', []):
                    if resource in self._resource_locks:
                        self._resource_locks[resource].release()
                
                # 更新任务状态
                task_info['status'] = PredictionStatus.CANCELLED
                task_info['end_time'] = datetime.now()
                task_info['cancel_reason'] = reason
                
                # 移动到历史记录
                self._task_history.append(task_info)
                del self._active_tasks[task_id]
                
                # 更新系统状态
                if not self._active_tasks:
                    self._system_busy = False
                    self._current_mode = PredictionMode.NONE
                    config_manager.set_active_mode('none')
                
                self.logger.info(f"预测任务已取消: {task_id} - {reason}")
                return True
                
            except Exception as e:
                self.logger.error(f"预测任务取消失败 {task_id}: {e}")
                return False
    
    def _can_start_task(self, mode: PredictionMode, task_id: str) -> bool:
        """检查是否可以启动任务"""
        # 检查任务ID是否已存在
        if task_id in self._active_tasks:
            return False
        
        # 检查并发任务数量
        if len(self._active_tasks) >= self._max_concurrent_tasks:
            return False
        
        # 检查模式冲突
        if self._has_mode_conflict(mode):
            return False
        
        # 检查资源冲突
        required_resources = self._get_required_resources(mode)
        for resource in required_resources:
            if not self._is_resource_available(resource):
                return False
        
        return True
    
    def _has_mode_conflict(self, new_mode: PredictionMode) -> bool:
        """检查模式冲突"""
        if not self._active_tasks:
            return False
        
        # 定义冲突规则
        conflict_rules = {
            PredictionMode.ENHANCED_BACKTEST: [
                PredictionMode.PERFECT,
                PredictionMode.HISTORICAL_BACKTEST
            ],
            PredictionMode.HISTORICAL_BACKTEST: [
                PredictionMode.ENHANCED_BACKTEST
            ],
            PredictionMode.PERFECT: [
                PredictionMode.ENHANCED_BACKTEST
            ]
        }
        
        current_modes = {task['mode'] for task in self._active_tasks.values()}
        
        # 检查新模式是否与当前模式冲突
        if new_mode in conflict_rules:
            for conflicting_mode in conflict_rules[new_mode]:
                if conflicting_mode in current_modes:
                    return True
        
        # 检查当前模式是否与新模式冲突
        for current_mode in current_modes:
            if current_mode in conflict_rules:
                if new_mode in conflict_rules[current_mode]:
                    return True
        
        return False
    
    def _get_required_resources(self, mode: PredictionMode) -> List[str]:
        """获取模式所需资源"""
        resource_map = {
            PredictionMode.SPECIAL: ['database'],
            PredictionMode.PERFECT: ['database', 'ml_models', 'config_files'],
            PredictionMode.HISTORICAL_BACKTEST: ['database', 'cache'],
            PredictionMode.ENHANCED_BACKTEST: ['database', 'ml_models', 'config_files', 'cache']
        }
        
        return resource_map.get(mode, [])
    
    def _is_resource_available(self, resource: str) -> bool:
        """检查资源是否可用"""
        if resource not in self._resource_locks:
            return True
        
        # 尝试获取锁（非阻塞）
        acquired = self._resource_locks[resource].acquire(blocking=False)
        if acquired:
            self._resource_locks[resource].release()
            return True
        return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        with self._coordination_lock:
            active_tasks = []
            for task_id, task_info in self._active_tasks.items():
                active_tasks.append({
                    'task_id': task_id,
                    'mode': task_info['mode'].value,
                    'status': task_info['status'].value,
                    'start_time': task_info['start_time'].isoformat(),
                    'duration': (datetime.now() - task_info['start_time']).total_seconds(),
                    'acquired_locks': task_info.get('acquired_locks', [])
                })
            
            return {
                'current_mode': self._current_mode.value,
                'system_busy': self._system_busy,
                'active_tasks': active_tasks,
                'active_task_count': len(self._active_tasks),
                'total_completed_tasks': len(self._task_history),
                'last_activity': self._last_activity.isoformat() if self._last_activity else None,
                'conflict_detection_enabled': self._conflict_detection_enabled,
                'max_concurrent_tasks': self._max_concurrent_tasks,
                'resource_status': {
                    resource: self._is_resource_available(resource)
                    for resource in self._resource_locks.keys()
                }
            }
    
    def cleanup_stale_tasks(self):
        """清理过期任务"""
        with self._coordination_lock:
            current_time = datetime.now()
            stale_tasks = []
            
            for task_id, task_info in self._active_tasks.items():
                duration = (current_time - task_info['start_time']).total_seconds()
                if duration > self._task_timeout:
                    stale_tasks.append(task_id)
            
            for task_id in stale_tasks:
                self.cancel_prediction(task_id, "任务超时")
                self.logger.warning(f"清理过期任务: {task_id}")

# 全局协调器实例
prediction_coordinator = PredictionCoordinator()

class SafePredictionWrapper:
    """安全预测包装器 - 为现有预测功能提供协调保护"""

    def __init__(self, coordinator: PredictionCoordinator = None):
        self.coordinator = coordinator or prediction_coordinator
        self.logger = logging.getLogger(__name__)

    def safe_special_prediction(self, target_date: str, **kwargs) -> Dict[str, Any]:
        """安全的特码预测"""
        task_id = f"special_{target_date}_{int(time.time())}"

        # 请求任务
        request_result = self.coordinator.request_prediction(
            PredictionMode.SPECIAL, task_id, {'target_date': target_date, **kwargs}
        )

        if not request_result['success']:
            return {
                'success': False,
                'error': request_result['error'],
                'conflict_info': request_result
            }

        try:
            # 执行特码预测
            from .special_number_predictor import SpecialNumberPredictor
            predictor = SpecialNumberPredictor()
            result = predictor.predict(target_date)

            # 完成任务
            self.coordinator.complete_prediction(task_id, result)

            return {
                'success': True,
                'result': result,
                'task_id': task_id
            }

        except Exception as e:
            self.coordinator.cancel_prediction(task_id, f"执行错误: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'task_id': task_id
            }

    def safe_perfect_prediction(self, target_date: str, **kwargs) -> Dict[str, Any]:
        """安全的完美预测"""
        task_id = f"perfect_{target_date}_{int(time.time())}"

        # 请求任务
        request_result = self.coordinator.request_prediction(
            PredictionMode.PERFECT, task_id, {'target_date': target_date, **kwargs}
        )

        if not request_result['success']:
            return {
                'success': False,
                'error': request_result['error'],
                'conflict_info': request_result
            }

        try:
            # 执行完美预测
            from .perfect_prediction_system import PerfectPredictionSystem
            system = PerfectPredictionSystem()
            system.initialize_modules()
            result = system.run_complete_prediction(target_date)

            # 完成任务
            self.coordinator.complete_prediction(task_id, result)

            return {
                'success': True,
                'result': result,
                'task_id': task_id
            }

        except Exception as e:
            self.coordinator.cancel_prediction(task_id, f"执行错误: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'task_id': task_id
            }

    def safe_historical_backtest(self, start_date: str, end_date: str, **kwargs) -> Dict[str, Any]:
        """安全的历史回测"""
        task_id = f"backtest_{start_date}_{end_date}_{int(time.time())}"

        # 请求任务
        request_result = self.coordinator.request_prediction(
            PredictionMode.HISTORICAL_BACKTEST, task_id,
            {'start_date': start_date, 'end_date': end_date, **kwargs}
        )

        if not request_result['success']:
            return {
                'success': False,
                'error': request_result['error'],
                'conflict_info': request_result
            }

        try:
            # 执行历史回测
            from historical_backtest import HistoricalBacktestSystem
            backtest_system = HistoricalBacktestSystem()
            result = backtest_system.run_batch_backtest(start_date, end_date, **kwargs)

            # 完成任务
            self.coordinator.complete_prediction(task_id, result)

            return {
                'success': True,
                'result': result,
                'task_id': task_id
            }

        except Exception as e:
            self.coordinator.cancel_prediction(task_id, f"执行错误: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'task_id': task_id
            }

    def safe_enhanced_backtest(self, **kwargs) -> Dict[str, Any]:
        """安全的增强回测"""
        task_id = f"enhanced_backtest_{int(time.time())}"

        # 请求任务
        request_result = self.coordinator.request_prediction(
            PredictionMode.ENHANCED_BACKTEST, task_id, kwargs
        )

        if not request_result['success']:
            return {
                'success': False,
                'error': request_result['error'],
                'conflict_info': request_result
            }

        try:
            # 执行增强回测
            from optimal_pattern_selector import OptimalPatternSelector
            selector = OptimalPatternSelector()
            result = selector.run_multiple_backtests(**kwargs)

            # 完成任务
            self.coordinator.complete_prediction(task_id, result)

            return {
                'success': True,
                'result': result,
                'task_id': task_id
            }

        except Exception as e:
            self.coordinator.cancel_prediction(task_id, f"执行错误: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'task_id': task_id
            }

# 全局安全包装器实例
safe_prediction = SafePredictionWrapper()
