import sqlite3

conn = sqlite3.connect("data/lottery.db")
cursor = conn.cursor()

# 检查农历年份数据
cursor.execute("SELECT DISTINCT lunar_year FROM lottery_data ORDER BY lunar_year")
lunar_years = cursor.fetchall()

print("📅 数据库中的农历年份:")
for year in lunar_years:
    if year[0]:
        print(f"   • {year[0]}")

# 检查年份跨度的数据
cursor.execute("""
    SELECT period_number, draw_date, natural_year, lunar_year 
    FROM lottery_data 
    WHERE draw_date BETWEEN '2023-12-31' AND '2024-02-15'
    ORDER BY draw_date
    LIMIT 10
""")
year_transition = cursor.fetchall()

print(f"\n🎊 2023-2024年份过渡期数据:")
for record in year_transition:
    print(f"   {record[0]} | {record[1]} | 自然年:{record[2]} | 农历年:{record[3]}")

conn.close()
