import re

# 读取GUI文件
with open("lottery_prediction_gui.py", "r", encoding="utf-8") as f:
    content = f.read()

print("🔧 增强回测界面按钮功能分析")
print("=" * 50)

# 查找增强回测相关的按钮定义
button_patterns = [
    r"self\.(\w*enhanced\w*button\w*)\s*=\s*QPushButton\([^)]*\)",
    r"self\.(\w*backtest\w*button\w*)\s*=\s*QPushButton\([^)]*\)",
    r"self\.(\w*optimal\w*button\w*)\s*=\s*QPushButton\([^)]*\)",
    r"self\.(\w*adaptive\w*button\w*)\s*=\s*QPushButton\([^)]*\)",
    r"self\.(\w*comprehensive\w*button\w*)\s*=\s*QPushButton\([^)]*\)"
]

found_buttons = []

for pattern in button_patterns:
    matches = re.finditer(pattern, content, re.IGNORECASE)
    for match in matches:
        button_name = match.group(1)
        # 找到按钮定义的完整行
        start_pos = match.start()
        line_start = content.rfind('\n', 0, start_pos) + 1
        line_end = content.find('\n', start_pos)
        if line_end == -1:
            line_end = len(content)
        
        button_line = content[line_start:line_end].strip()
        found_buttons.append((button_name, button_line))

print(f"✅ 找到 {len(found_buttons)} 个增强回测相关按钮:")
for i, (name, line) in enumerate(found_buttons, 1):
    print(f"   {i}. {name}")
    print(f"      定义: {line[:80]}...")

# 查找按钮的点击事件连接
print(f"\n🔗 按钮点击事件连接:")
click_patterns = [
    r"(\w*enhanced\w*button\w*)\.clicked\.connect\(([^)]+)\)",
    r"(\w*backtest\w*button\w*)\.clicked\.connect\(([^)]+)\)",
    r"(\w*optimal\w*button\w*)\.clicked\.connect\(([^)]+)\)",
    r"(\w*adaptive\w*button\w*)\.clicked\.connect\(([^)]+)\)",
    r"(\w*comprehensive\w*button\w*)\.clicked\.connect\(([^)]+)\)"
]

found_connections = []

for pattern in click_patterns:
    matches = re.finditer(pattern, content, re.IGNORECASE)
    for match in matches:
        button_name = match.group(1)
        function_name = match.group(2)
        found_connections.append((button_name, function_name))

for i, (button, function) in enumerate(found_connections, 1):
    print(f"   {i}. {button} → {function}")

# 查找具体的功能方法
print(f"\n🎯 增强回测功能方法:")
function_patterns = [
    r"def\s+(run_enhanced_backtest)\s*\(",
    r"def\s+(run_optimal_selection)\s*\(",
    r"def\s+(run_adaptive_optimization)\s*\(",
    r"def\s+(run_comprehensive_evaluation)\s*\(",
    r"def\s+(simulate_enhanced_backtest)\s*\(",
    r"def\s+(stop_enhanced_backtest)\s*\(",
    r"def\s+(export_enhanced_backtest_results)\s*\("
]

found_functions = []

for pattern in function_patterns:
    matches = re.finditer(pattern, content, re.IGNORECASE)
    for match in matches:
        function_name = match.group(1)
        # 找到函数的文档字符串
        func_start = match.end()
        next_lines = content[func_start:func_start+200]
        doc_match = re.search(r'"""([^"]+)"""', next_lines)
        doc_string = doc_match.group(1).strip() if doc_match else "无文档说明"
        
        found_functions.append((function_name, doc_string))

for i, (func, doc) in enumerate(found_functions, 1):
    print(f"   {i}. {func}()")
    print(f"      说明: {doc}")

# 查找训练-验证-应用相关的功能
print(f"\n🔄 训练-验证-应用流程功能:")
workflow_keywords = [
    "训练", "验证", "应用", "train", "valid", "apply",
    "多次采样", "sampling", "参数优化", "optimization",
    "最优选择", "optimal", "自适应", "adaptive"
]

workflow_features = []

for keyword in workflow_keywords:
    if keyword in content.lower():
        # 找到包含关键词的方法或功能
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if keyword in line.lower() and ('def ' in line or 'button' in line.lower()):
                workflow_features.append((keyword, line.strip()))

# 去重并显示
unique_features = list(set(workflow_features))
for keyword, line in unique_features[:10]:  # 显示前10个
    print(f"   🔍 {keyword}: {line[:60]}...")
