2025-06-24 17:03:30,322 - INFO - 🚀 开始完整EXE打包流程
2025-06-24 17:03:30,322 - INFO - 项目: LotteryPredictionSystem
2025-06-24 17:03:30,322 - INFO - 版本: v2.0.0
2025-06-24 17:03:30,322 - INFO - 构建日期: 20250624
2025-06-24 17:03:30,322 - INFO - ============================================================
2025-06-24 17:03:30,322 - INFO - 步骤1: 项目结构整理（最关键）
2025-06-24 17:03:30,322 - INFO - ============================================================
2025-06-24 17:03:30,322 - INFO - 1.1 检查主要文件...
2025-06-24 17:03:30,323 - INFO - ✅ lottery_prediction_gui.py: 存在 (339251 bytes)
2025-06-24 17:03:30,323 - INFO - ✅ main.py: 存在 (1403 bytes)
2025-06-24 17:03:30,323 - INFO - 
1.2 检查源码目录...
2025-06-24 17:03:30,330 - INFO - ✅ src/: 存在 (86 Python文件)
2025-06-24 17:03:30,330 - INFO - ✅ config/: 存在 (0 Python文件)
2025-06-24 17:03:30,331 - INFO - 
1.3 检查数据目录...
2025-06-24 17:03:30,331 - INFO - ✅ data/: 存在 (6 文件)
2025-06-24 17:03:30,331 - INFO - 
============================================================
2025-06-24 17:03:30,331 - INFO - 步骤2: 资源路径规范化
2025-06-24 17:03:30,331 - INFO - ============================================================
2025-06-24 17:03:30,332 - INFO - 2.1 分析代码中的路径引用...
2025-06-24 17:03:30,581 - INFO - 发现路径问题:
2025-06-24 17:03:30,581 - WARNING -   ⚠️ analyze_modules.py: 可能的绝对路径引用
2025-06-24 17:03:30,582 - INFO - ✅ 创建资源路径处理器: I:\编程\lottery\src\resource_path_handler.py
2025-06-24 17:03:30,582 - INFO - 
============================================================
2025-06-24 17:03:30,582 - INFO - 步骤3: 创建完整的.spec配置文件
2025-06-24 17:03:30,582 - INFO - ============================================================
2025-06-24 17:03:30,582 - INFO - 3.1 收集需要包含的文件...
2025-06-24 17:03:30,582 - INFO -   📁 添加目录: src
2025-06-24 17:03:30,583 - INFO -   📁 添加目录: config
2025-06-24 17:03:30,583 - INFO -   📁 添加数据目录: data
2025-06-24 17:03:30,583 - INFO -   📄 添加资源文件: requirements.txt
2025-06-24 17:03:30,583 - INFO -   📄 添加资源文件: optimal_config.json
2025-06-24 17:03:30,583 - INFO -   📄 添加资源文件: optimization_config.json
2025-06-24 17:03:30,583 - INFO -   📄 添加资源文件: icon.ico
2025-06-24 17:03:30,584 - INFO - ✅ 创建.spec文件: I:\编程\lottery\LotteryPredictionSystem.spec
2025-06-24 17:03:30,584 - INFO - 
============================================================
2025-06-24 17:03:30,584 - INFO - 步骤4: 构建EXE文件
2025-06-24 17:03:30,584 - INFO - ============================================================
2025-06-24 17:03:30,584 - INFO - 4.1 清理之前的构建...
2025-06-24 17:03:30,585 - INFO - 4.2 执行PyInstaller构建...
2025-06-24 17:03:30,585 - INFO - 执行命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe -m PyInstaller --clean --noconfirm --distpath I:\编程\lottery\dist_exe --workpath I:\编程\lottery\build_exe I:\编程\lottery\LotteryPredictionSystem.spec
2025-06-24 17:03:31,536 - ERROR - ❌ 构建过程异常: write() argument must be str, not None
2025-06-24 17:03:31,536 - ERROR - ❌ EXE构建失败
2025-06-24 17:09:18,315 - INFO - 🚀 开始完整EXE打包流程
2025-06-24 17:09:18,315 - INFO - 项目: LotteryPredictionSystem
2025-06-24 17:09:18,315 - INFO - 版本: v2.0.0
2025-06-24 17:09:18,315 - INFO - 构建日期: 20250624
2025-06-24 17:09:18,315 - INFO - ============================================================
2025-06-24 17:09:18,315 - INFO - 步骤1: 项目结构整理（最关键）
2025-06-24 17:09:18,315 - INFO - ============================================================
2025-06-24 17:09:18,316 - INFO - 1.1 检查主要文件...
2025-06-24 17:09:18,316 - INFO - ✅ lottery_prediction_gui.py: 存在 (339251 bytes)
2025-06-24 17:09:18,316 - INFO - ✅ main.py: 存在 (1403 bytes)
2025-06-24 17:09:18,316 - INFO - 
1.2 检查源码目录...
2025-06-24 17:09:18,322 - INFO - ✅ src/: 存在 (87 Python文件)
2025-06-24 17:09:18,322 - INFO - ✅ config/: 存在 (0 Python文件)
2025-06-24 17:09:18,322 - INFO - 
1.3 检查数据目录...
2025-06-24 17:09:18,323 - INFO - ✅ data/: 存在 (6 文件)
2025-06-24 17:09:18,323 - INFO - 
============================================================
2025-06-24 17:09:18,323 - INFO - 步骤2: 资源路径规范化
2025-06-24 17:09:18,323 - INFO - ============================================================
2025-06-24 17:09:18,323 - INFO - 2.1 分析代码中的路径引用...
2025-06-24 17:09:18,544 - INFO - 发现路径问题:
2025-06-24 17:09:18,544 - WARNING -   ⚠️ analyze_modules.py: 可能的绝对路径引用
2025-06-24 17:09:18,544 - INFO - ✅ 创建资源路径处理器: I:\编程\lottery\src\resource_path_handler.py
2025-06-24 17:09:18,545 - INFO - 
============================================================
2025-06-24 17:09:18,545 - INFO - 步骤3: 创建完整的.spec配置文件
2025-06-24 17:09:18,545 - INFO - ============================================================
2025-06-24 17:09:18,545 - INFO - 3.1 收集需要包含的文件...
2025-06-24 17:09:18,545 - INFO -   📁 添加目录: src
2025-06-24 17:09:18,545 - INFO -   📁 添加目录: config
2025-06-24 17:09:18,545 - INFO -   📁 添加数据目录: data
2025-06-24 17:09:18,545 - INFO -   📄 添加资源文件: requirements.txt
2025-06-24 17:09:18,546 - INFO -   📄 添加资源文件: optimal_config.json
2025-06-24 17:09:18,546 - INFO -   📄 添加资源文件: optimization_config.json
2025-06-24 17:09:18,546 - INFO -   📄 添加资源文件: icon.ico
2025-06-24 17:09:18,546 - INFO - ✅ 创建.spec文件: I:\编程\lottery\LotteryPredictionSystem.spec
2025-06-24 17:09:18,547 - INFO - 
============================================================
2025-06-24 17:09:18,547 - INFO - 步骤4: 构建EXE文件
2025-06-24 17:09:18,547 - INFO - ============================================================
2025-06-24 17:09:18,547 - INFO - 4.1 清理之前的构建...
2025-06-24 17:09:18,548 - INFO - ✅ 清理build目录
2025-06-24 17:09:18,548 - INFO - ✅ 清理dist目录
2025-06-24 17:09:18,548 - INFO - 4.2 执行PyInstaller构建...
2025-06-24 17:09:18,548 - INFO - 执行命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe -m PyInstaller --clean --noconfirm --distpath I:\编程\lottery\dist_exe --workpath I:\编程\lottery\build_exe I:\编程\lottery\LotteryPredictionSystem.spec
2025-06-24 17:09:19,175 - ERROR - ❌ PyInstaller构建失败 (返回码: 1)
2025-06-24 17:09:19,176 - ERROR - 错误信息: 455 INFO: PyInstaller: 6.3.0
455 INFO: Python: 3.11.9
465 INFO: Platform: Windows-10-10.0.26100-SP0
471 INFO: Removing temporary files and cleaning cache in C:\Users\<USER>\AppData\Local\pyinstaller
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\__main__.py", line 218, in <module>
    run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\__main__.py", line 198, in run
    run_build(pyi_config, spec_file, **vars(args))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\__main__.py", line 69, in run_build
    PyInstaller.building.build_main.main(pyi_config, spec_file, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\building\build_main.py", line 1071, in main
    build(specfile, distpath, workpath, clean_build)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\building\build_main.py", line 1011, in build
    exec(code, spec_namespace)
  File "I:\\lottery\LotteryPredictionSystem.spec", line 10, in <module>
    project_root = Path(__file__).parent
                        ^^^^^^^^
NameError: name '__file__' is not defined. Did you mean: '__name__'?

2025-06-24 17:09:19,176 - ERROR - ❌ EXE构建失败
2025-06-24 17:11:35,500 - INFO - 🚀 开始完整EXE打包流程
2025-06-24 17:11:35,500 - INFO - 项目: LotteryPredictionSystem
2025-06-24 17:11:35,500 - INFO - 版本: v2.0.0
2025-06-24 17:11:35,501 - INFO - 构建日期: 20250624
2025-06-24 17:11:35,501 - INFO - ============================================================
2025-06-24 17:11:35,501 - INFO - 步骤1: 项目结构整理（最关键）
2025-06-24 17:11:35,501 - INFO - ============================================================
2025-06-24 17:11:35,501 - INFO - 1.1 检查主要文件...
2025-06-24 17:11:35,501 - INFO - ✅ lottery_prediction_gui.py: 存在 (339251 bytes)
2025-06-24 17:11:35,501 - INFO - ✅ main.py: 存在 (1403 bytes)
2025-06-24 17:11:35,502 - INFO - 
1.2 检查源码目录...
2025-06-24 17:11:35,506 - INFO - ✅ src/: 存在 (87 Python文件)
2025-06-24 17:11:35,507 - INFO - ✅ config/: 存在 (0 Python文件)
2025-06-24 17:11:35,507 - INFO - 
1.3 检查数据目录...
2025-06-24 17:11:35,507 - INFO - ✅ data/: 存在 (6 文件)
2025-06-24 17:11:35,508 - INFO - 
============================================================
2025-06-24 17:11:35,508 - INFO - 步骤2: 资源路径规范化
2025-06-24 17:11:35,508 - INFO - ============================================================
2025-06-24 17:11:35,508 - INFO - 2.1 分析代码中的路径引用...
2025-06-24 17:11:35,730 - INFO - 发现路径问题:
2025-06-24 17:11:35,731 - WARNING -   ⚠️ analyze_modules.py: 可能的绝对路径引用
2025-06-24 17:11:35,731 - INFO - ✅ 创建资源路径处理器: I:\编程\lottery\src\resource_path_handler.py
2025-06-24 17:11:35,731 - INFO - 
============================================================
2025-06-24 17:11:35,731 - INFO - 步骤3: 创建完整的.spec配置文件
2025-06-24 17:11:35,731 - INFO - ============================================================
2025-06-24 17:11:35,731 - INFO - 3.1 收集需要包含的文件...
2025-06-24 17:11:35,732 - INFO -   📁 添加目录: src
2025-06-24 17:11:35,732 - INFO -   📁 添加目录: config
2025-06-24 17:11:35,732 - INFO -   📁 添加数据目录: data
2025-06-24 17:11:35,732 - INFO -   📄 添加资源文件: requirements.txt
2025-06-24 17:11:35,732 - INFO -   📄 添加资源文件: optimal_config.json
2025-06-24 17:11:35,732 - INFO -   📄 添加资源文件: optimization_config.json
2025-06-24 17:11:35,732 - INFO -   📄 添加资源文件: icon.ico
2025-06-24 17:11:35,734 - INFO - ✅ 创建.spec文件: I:\编程\lottery\LotteryPredictionSystem.spec
2025-06-24 17:11:35,734 - INFO - 
============================================================
2025-06-24 17:11:35,734 - INFO - 步骤4: 构建EXE文件
2025-06-24 17:11:35,734 - INFO - ============================================================
2025-06-24 17:11:35,734 - INFO - 4.1 清理之前的构建...
2025-06-24 17:11:35,735 - INFO - ✅ 清理build目录
2025-06-24 17:11:35,735 - INFO - ✅ 清理dist目录
2025-06-24 17:11:35,735 - INFO - 4.2 执行PyInstaller构建...
2025-06-24 17:11:35,735 - INFO - 执行命令: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe -m PyInstaller --clean --noconfirm --distpath I:\编程\lottery\dist_exe --workpath I:\编程\lottery\build_exe I:\编程\lottery\LotteryPredictionSystem.spec
2025-06-24 17:14:07,829 - INFO - ✅ PyInstaller构建成功
2025-06-24 17:14:07,829 - INFO - 
============================================================
2025-06-24 17:14:07,829 - INFO - 步骤5: 调试和测试
2025-06-24 17:14:07,829 - INFO - ============================================================
2025-06-24 17:14:07,830 - INFO - ✅ EXE文件生成成功: I:\编程\lottery\dist_exe\LotteryPredictionSystem.exe (189.4 MB)
2025-06-24 17:14:07,830 - INFO - ✅ 创建调试脚本: I:\编程\lottery\dist_exe\debug_start.bat
2025-06-24 17:14:07,854 - INFO - ✅ 创建PowerShell调试脚本: I:\编程\lottery\dist_exe\debug_start.ps1
2025-06-24 17:14:07,854 - INFO - 5.3 执行基本测试...
2025-06-24 17:14:12,630 - WARNING - ⚠️ 基本测试未完全通过，请查看日志
2025-06-24 17:14:12,630 - INFO - 
============================================================
2025-06-24 17:14:12,630 - INFO - 步骤6: 打包成功验证 checklist
2025-06-24 17:14:12,631 - INFO - ============================================================
2025-06-24 17:14:12,631 - INFO - 📋 验证清单:
2025-06-24 17:14:12,631 - INFO - 
1. 所有资源是否用 add-data 包含？
2025-06-24 17:14:12,631 - INFO -    ✅ 通过
2025-06-24 17:14:12,631 - INFO - 
2. 所有路径是否基于 _MEIPASS 处理？
2025-06-24 17:14:12,632 - INFO -    ✅ 通过
2025-06-24 17:14:12,632 - INFO - 
3. 是否使用 print+log.txt 记录流程？
2025-06-24 17:14:12,632 - INFO -    ✅ 通过
2025-06-24 17:14:12,633 - INFO - 
4. 是否排除了 .pyc、.git、大文件？
2025-06-24 17:14:12,633 - INFO -    ✅ 通过
2025-06-24 17:14:12,633 - INFO - 
5. 是否在打包后目录运行测试验证？
2025-06-24 17:14:12,633 - INFO -    ✅ 通过
2025-06-24 17:14:12,634 - INFO - 
============================================================
2025-06-24 17:14:12,634 - INFO - 🎉 所有验证项目通过！EXE打包成功！
2025-06-24 17:14:12,634 - INFO - 
============================================================
2025-06-24 17:14:12,634 - INFO - 创建最终发布包
2025-06-24 17:14:12,634 - INFO - ============================================================
2025-06-24 17:14:17,096 - INFO - ✅ 创建发布包: I:\编程\lottery\releases\LotteryPredictionSystem_Complete_EXE_v2.0.zip
2025-06-24 17:14:17,097 - INFO - 📁 发布目录: I:\编程\lottery\releases\LotteryPredictionSystem_Complete_EXE_v2.0.0_20250624
2025-06-24 17:14:17,097 - INFO - 
🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉
2025-06-24 17:14:17,097 - INFO - 🎉 EXE打包流程完成！
2025-06-24 17:14:17,097 - INFO - 🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉
2025-06-24 17:14:17,098 - INFO - 
📊 构建总结:
2025-06-24 17:14:17,098 - INFO -   项目名称: LotteryPredictionSystem
2025-06-24 17:14:17,098 - INFO -   版本: v2.0.0
2025-06-24 17:14:17,098 - INFO -   构建时间: 2025-06-24 17:14:17
2025-06-24 17:14:17,098 - INFO -   EXE大小: 189.4 MB
2025-06-24 17:14:17,098 - INFO -   EXE位置: I:\编程\lottery\dist_exe\LotteryPredictionSystem.exe
2025-06-24 17:14:17,099 - INFO -   验证状态: ✅ 全部通过
2025-06-24 17:14:17,099 - INFO - 
📝 下一步建议:
2025-06-24 17:14:17,099 - INFO -   1. 使用 debug_start.bat 测试EXE文件
2025-06-24 17:14:17,099 - INFO -   2. 在不同Windows系统上测试兼容性
2025-06-24 17:14:17,099 - INFO -   3. 检查所有功能是否正常工作
2025-06-24 17:14:17,099 - INFO -   4. 如有问题，查看 exe_build.log 和 debug_output.log
