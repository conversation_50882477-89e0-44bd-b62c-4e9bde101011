#!/usr/bin/env python3
"""
增强机器学习模组 - 集成到现有系统
基于三层架构：主力/辅助/候补模型体系
"""

import sqlite3
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from enhanced_ml_architecture import EnhancedMLArchitecture
from enhanced_feature_engineering import EnhancedFeatureEngineering
import warnings
warnings.filterwarnings('ignore')

class EnhancedMachineLearningModule:
    """增强机器学习模组"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.module_name = "增强机器学习模组"
        
        # 初始化核心组件
        self.ml_architecture = EnhancedMLArchitecture()
        self.feature_engineer = EnhancedFeatureEngineering()
        
        # 配置参数
        self.config = {
            'sequence_length': 20,
            'min_training_samples': 50,
            'feature_selection_ratio': 0.8,
            'confidence_threshold': 0.3
        }
        
        self.is_trained = False
        self.last_training_time = None
        
    def predict(self, target_date: str, analysis_days: int = 100) -> Dict[str, Any]:
        """增强预测方法"""
        print(f"🚀 {self.module_name}开始预测 - 目标日期: {target_date}")
        
        try:
            # 获取历史数据
            historical_data = self._get_historical_data(target_date, analysis_days + 30)
            
            if len(historical_data) < self.config['min_training_samples']:
                print(f"⚠️ 历史数据不足 ({len(historical_data)}期)，使用默认预测")
                return self._get_default_result()
            
            # 增强特征工程
            feature_matrix = self._extract_enhanced_features(historical_data)
            
            if feature_matrix.size == 0:
                print("⚠️ 特征提取失败，使用默认预测")
                return self._get_default_result()
            
            # 准备训练数据
            X, y = self._prepare_training_data(feature_matrix, historical_data)
            
            if len(X) < self.config['min_training_samples']:
                print(f"⚠️ 训练样本不足 ({len(X)}个)，使用默认预测")
                return self._get_default_result()
            
            # 训练分层模型
            training_results = self._train_models_if_needed(X, y)
            
            # 生成预测特征
            prediction_features = self._generate_prediction_features(historical_data, target_date)
            
            # 执行分层预测
            prediction_result = self.ml_architecture.hierarchical_predict(prediction_features)
            
            # 构建完整结果
            result = self._build_complete_result(prediction_result, training_results, len(X))
            
            print(f"✅ {self.module_name}预测完成 - 推荐{len(result['predicted_numbers'])}个号码")
            return result
            
        except Exception as e:
            print(f"❌ {self.module_name}预测失败: {e}")
            return self._get_default_result()
    
    def _get_historical_data(self, before_date: str, days: int) -> List[Dict[str, Any]]:
        """获取历史数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT draw_date, special_number, period_number,
                   regular_1, regular_2, regular_3, regular_4, regular_5, regular_6
            FROM lottery_results 
            WHERE draw_date < ? 
            ORDER BY draw_date DESC 
            LIMIT ?
        ''', (before_date, days))
        
        data = []
        for row in cursor.fetchall():
            data.append({
                "draw_date": row[0],
                "special_number": row[1],
                "period_number": row[2],
                "regular_numbers": [row[3], row[4], row[5], row[6], row[7], row[8]]
            })
        
        conn.close()
        return data
    
    def _extract_enhanced_features(self, historical_data: List[Dict]) -> np.ndarray:
        """提取增强特征"""
        try:
            print("🔧 开始增强特征工程...")
            
            # 使用增强特征工程提取综合特征
            feature_matrix = self.feature_engineer.extract_comprehensive_features(
                historical_data, 
                window_size=self.config['sequence_length']
            )
            
            if feature_matrix.size > 0:
                print(f"✅ 特征提取完成，特征维度: {feature_matrix.shape}")
                return feature_matrix
            else:
                print("⚠️ 增强特征提取失败，使用基础特征")
                return self._extract_basic_features(historical_data)
                
        except Exception as e:
            print(f"⚠️ 增强特征工程失败: {e}，使用基础特征")
            return self._extract_basic_features(historical_data)
    
    def _extract_basic_features(self, historical_data: List[Dict]) -> np.ndarray:
        """提取基础特征（备用方案）"""
        features = []
        seq_len = self.config['sequence_length']
        
        for i in range(seq_len, len(historical_data)):
            current_record = historical_data[i]
            history_sequence = historical_data[i-seq_len:i]
            
            feature_vector = self._generate_basic_feature_vector(current_record, history_sequence)
            if feature_vector is not None:
                features.append(feature_vector)
        
        return np.array(features) if features else np.array([])
    
    def _generate_basic_feature_vector(self, current_record: Dict, history_sequence: List[Dict]) -> Optional[np.ndarray]:
        """生成基础特征向量"""
        try:
            features = []
            recent_numbers = [record['special_number'] for record in history_sequence]
            
            # 基础统计特征
            features.extend([
                np.mean(recent_numbers),
                np.std(recent_numbers),
                np.min(recent_numbers),
                np.max(recent_numbers),
                np.median(recent_numbers)
            ])
            
            # 趋势特征
            if len(recent_numbers) >= 3:
                x = np.arange(len(recent_numbers))
                trend = np.polyfit(x, recent_numbers, 1)[0]
                features.append(trend)
            else:
                features.append(0)
            
            # 大小单双特征
            big_count = sum(1 for num in recent_numbers if num > 24)
            odd_count = sum(1 for num in recent_numbers if num % 2 == 1)
            features.extend([
                big_count / len(recent_numbers),
                odd_count / len(recent_numbers)
            ])
            
            # 频率特征
            from collections import Counter
            number_counts = Counter(recent_numbers)
            max_freq = max(number_counts.values()) if number_counts else 0
            features.append(max_freq / len(recent_numbers))
            
            # 时间特征
            try:
                draw_date = datetime.strptime(current_record['draw_date'], '%Y-%m-%d')
                features.extend([
                    draw_date.month,
                    draw_date.day,
                    draw_date.weekday()
                ])
            except:
                features.extend([1, 1, 0])
            
            return np.array(features, dtype=float)
            
        except Exception as e:
            print(f"基础特征生成失败: {e}")
            return None
    
    def _prepare_training_data(self, feature_matrix: np.ndarray, historical_data: List[Dict]) -> tuple:
        """准备训练数据"""
        if feature_matrix.size == 0:
            return np.array([]), np.array([])
        
        # 获取对应的目标变量
        seq_len = self.config['sequence_length']
        targets = []
        
        for i in range(seq_len, len(historical_data)):
            if i - seq_len < len(feature_matrix):
                targets.append(historical_data[i]['special_number'])
        
        # 确保特征和目标数量匹配
        min_length = min(len(feature_matrix), len(targets))
        X = feature_matrix[:min_length]
        y = np.array(targets[:min_length])
        
        return X, y
    
    def _train_models_if_needed(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """根据需要训练模型"""
        # 检查是否需要重新训练
        need_training = (
            not self.is_trained or 
            self.last_training_time is None or
            (datetime.now() - self.last_training_time).days > 7  # 7天重新训练一次
        )
        
        if need_training:
            print("🏋️ 开始训练分层模型...")
            training_results = self.ml_architecture.train_hierarchical_models(X, y)
            self.is_trained = True
            self.last_training_time = datetime.now()
            print("✅ 模型训练完成")
            return training_results
        else:
            print("ℹ️ 使用已训练的模型")
            return {"note": "使用缓存模型"}
    
    def _generate_prediction_features(self, historical_data: List[Dict], target_date: str) -> np.ndarray:
        """生成预测特征"""
        try:
            # 使用最近的数据生成预测特征
            recent_data = historical_data[:self.config['sequence_length']]
            
            # 创建虚拟当前记录
            current_record = {
                'draw_date': target_date,
                'special_number': 0,
                'period_number': '',
                'regular_numbers': [0, 0, 0, 0, 0, 0]
            }
            
            # 尝试使用增强特征工程
            try:
                # 构造用于特征工程的数据
                fe_data = recent_data + [current_record]
                feature_matrix = self.feature_engineer.extract_comprehensive_features(
                    fe_data, window_size=self.config['sequence_length']
                )
                
                if feature_matrix.size > 0:
                    return feature_matrix[-1]  # 返回最后一行（预测特征）
                    
            except Exception as e:
                print(f"增强特征生成失败: {e}")
            
            # 使用基础特征作为备用
            feature_vector = self._generate_basic_feature_vector(current_record, recent_data)
            return feature_vector if feature_vector is not None else np.zeros(50)
            
        except Exception as e:
            print(f"预测特征生成失败: {e}")
            return np.zeros(50)
    
    def _build_complete_result(self, prediction_result: Dict, training_results: Dict, training_samples: int) -> Dict[str, Any]:
        """构建完整结果"""
        return {
            "predicted_numbers": prediction_result["recommended_numbers"],
            "confidence": prediction_result["confidence"],
            "hierarchical_analysis": {
                "primary_models": self._extract_model_info("primary_models"),
                "auxiliary_models": self._extract_model_info("auxiliary_models"),
                "backup_models": self._extract_model_info("backup_models")
            },
            "model_contributions": prediction_result["model_contributions"],
            "feature_engineering": {
                "feature_count": 62,  # 增强特征工程的特征数量
                "enhancement_applied": True,
                "feature_selection": True
            },
            "training_info": {
                "training_samples": training_samples,
                "models_trained": len(self.ml_architecture.trained_models),
                "last_training": self.last_training_time.isoformat() if self.last_training_time else None,
                "training_results": training_results
            },
            "metadata": {
                "module_name": self.module_name,
                "prediction_time": datetime.now().isoformat(),
                "architecture": "三层分层架构",
                "version": "2.0_enhanced"
            }
        }
    
    def _extract_model_info(self, category: str) -> List[Dict[str, Any]]:
        """提取模型信息"""
        model_info = []
        
        if category in self.ml_architecture.model_hierarchy:
            for model_name, config in self.ml_architecture.model_hierarchy[category].items():
                info = {
                    "name": model_name,
                    "description": config["description"],
                    "weight": config["weight"],
                    "strategy": config["strategy"]
                }
                
                # 添加训练状态
                if model_name in self.ml_architecture.trained_models:
                    trained_info = self.ml_architecture.trained_models[model_name]
                    info["cv_score"] = trained_info.get("cv_score", 0.0)
                    info["trained"] = True
                else:
                    info["cv_score"] = 0.0
                    info["trained"] = False
                
                model_info.append(info)
        
        return model_info
    
    def _get_default_result(self) -> Dict[str, Any]:
        """获取默认结果"""
        return {
            "predicted_numbers": list(range(1, 17)),
            "confidence": 0.3,
            "hierarchical_analysis": {
                "primary_models": [],
                "auxiliary_models": [],
                "backup_models": []
            },
            "model_contributions": {
                "primary_weight": 0.0,
                "auxiliary_weight": 0.0,
                "backup_weight": 0.0
            },
            "feature_engineering": {
                "feature_count": 0,
                "enhancement_applied": False,
                "feature_selection": False
            },
            "training_info": {
                "training_samples": 0,
                "models_trained": 0,
                "last_training": None,
                "training_results": {}
            },
            "metadata": {
                "module_name": self.module_name,
                "prediction_time": datetime.now().isoformat(),
                "architecture": "默认模式",
                "version": "2.0_enhanced",
                "note": "数据不足，使用默认预测"
            }
        }
    
    def get_architecture_summary(self) -> str:
        """获取架构摘要"""
        return self.ml_architecture.get_model_architecture_summary()
    
    def run_backtest(self, start_date: str, end_date: str, window_size: int = 30) -> Dict[str, Any]:
        """运行回测"""
        print(f"🔄 {self.module_name}开始回测: {start_date} 到 {end_date}")
        
        # 获取测试日期
        test_dates = self._get_test_dates(start_date, end_date)
        results = []
        
        for test_date in test_dates:
            try:
                actual_number = self._get_actual_number(test_date)
                if actual_number is None:
                    continue
                
                # 使用增强预测
                prediction = self.predict(test_date, window_size)
                predicted_numbers = prediction["predicted_numbers"]
                
                hit = actual_number in predicted_numbers
                hit_rank = predicted_numbers.index(actual_number) + 1 if hit else None
                
                result = {
                    "test_date": test_date,
                    "actual_number": actual_number,
                    "predicted_numbers": predicted_numbers,
                    "hit": hit,
                    "hit_rank": hit_rank,
                    "confidence": prediction["confidence"],
                    "architecture_used": prediction["metadata"]["architecture"]
                }
                
                results.append(result)
                print(f"  {test_date}: 实际={actual_number}, 命中={'✅' if hit else '❌'}")
                
            except Exception as e:
                print(f"  {test_date}: 回测失败 - {e}")
        
        # 计算统计信息
        hit_count = sum(1 for r in results if r["hit"])
        hit_rate = hit_count / len(results) if results else 0
        
        print(f"✅ {self.module_name}回测完成: {hit_count}/{len(results)} = {hit_rate:.1%}")
        
        return {
            "results": results,
            "statistics": {
                "total_tests": len(results),
                "hit_count": hit_count,
                "hit_rate": hit_rate,
                "avg_confidence": np.mean([r["confidence"] for r in results]) if results else 0,
                "architecture": "三层分层架构"
            }
        }
    
    def _get_test_dates(self, start_date: str, end_date: str) -> List[str]:
        """获取测试日期"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT draw_date FROM lottery_results 
            WHERE draw_date BETWEEN ? AND ? 
            ORDER BY draw_date
        ''', (start_date, end_date))
        
        dates = [row[0] for row in cursor.fetchall()]
        conn.close()
        return dates
    
    def _get_actual_number(self, test_date: str) -> Optional[int]:
        """获取真实开奖号码"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT special_number FROM lottery_results 
            WHERE draw_date = ?
        ''', (test_date,))
        
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None

if __name__ == "__main__":
    # 测试增强ML模组
    enhanced_ml = EnhancedMachineLearningModule()
    
    print("🤖 增强机器学习模组架构:")
    print(enhanced_ml.get_architecture_summary())
    
    print("\n🧪 开始预测测试...")
    result = enhanced_ml.predict("2025-06-23", 100)
    
    print(f"\n📊 预测结果:")
    print(f"推荐号码: {result['predicted_numbers']}")
    print(f"置信度: {result['confidence']:.1%}")
    print(f"架构: {result['metadata']['architecture']}")
    print(f"特征数量: {result['feature_engineering']['feature_count']}")
    print(f"训练模型数: {result['training_info']['models_trained']}")
