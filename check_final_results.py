import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.perfect_prediction_system import PerfectPredictionSystem

def check_final_results_detail():
    """检查final_results的详细内容"""
    print("🔍 检查final_results的详细内容")
    print("=" * 40)
    
    try:
        # 初始化系统
        system = PerfectPredictionSystem()
        system.initialize_modules()
        
        # 运行预测
        result = system.run_complete_prediction("2025-06-23")
        
        # 检查final_results的详细内容
        final_results = result.get("final_results", {})
        print(f"📊 final_results详细内容:")
        print(f"   类型: {type(final_results)}")
        print(f"   键值: {list(final_results.keys()) if isinstance(final_results, dict) else 'Not a dict'}")
        
        if isinstance(final_results, dict):
            for key, value in final_results.items():
                print(f"   {key}: {value}")
                
                # 特别检查是否有生肖相关内容
                if "zodiac" in key.lower() or "生肖" in str(key):
                    print(f"      🐲 生肖相关: {value}")
        
        # 检查是否有final_numbers
        final_numbers = None
        if "final_numbers" in final_results:
            final_numbers = final_results["final_numbers"]
        elif "recommended_numbers" in final_results:
            final_numbers = final_results["recommended_numbers"]
        elif "numbers" in final_results:
            final_numbers = final_results["numbers"]
        
        print(f"\n📊 最终号码检查:")
        if final_numbers:
            print(f"   ✅ 找到最终号码: {final_numbers}")
            print(f"   📊 号码数量: {len(final_numbers)}")
            
            # 手动调用生肖预测
            if hasattr(system, '_predict_zodiacs'):
                try:
                    predicted_zodiacs = system._predict_zodiacs(final_numbers)
                    print(f"   🐲 生肖预测结果: {predicted_zodiacs}")
                except Exception as e:
                    print(f"   ❌ 生肖预测失败: {e}")
            
            # 检查特码生肖模组
            if hasattr(system, 'special_zodiac_module'):
                try:
                    special_zodiacs = system.special_zodiac_module.predict_zodiacs(final_numbers)
                    print(f"   🎯 特码生肖预测: {special_zodiacs}")
                except Exception as e:
                    print(f"   ❌ 特码生肖预测失败: {e}")
        else:
            print(f"   ❌ 未找到最终号码")
        
        # 检查完美预测系统的run_complete_prediction方法是否调用了生肖预测
        print(f"\n🔍 检查生肖预测调用:")
        
        # 查看系统是否有生肖映射
        if hasattr(system, 'zodiac_mapping'):
            print(f"   ✅ zodiac_mapping: 存在")
            print(f"   📊 映射数量: {len(system.zodiac_mapping)}")
        else:
            print(f"   ❌ zodiac_mapping: 不存在")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_final_results_detail()
