import re

# 读取完美预测系统文件
with open("src/perfect_prediction_system.py", "r", encoding="utf-8") as f:
    content = f.read()

print("🎯 完美预测系统核心预测方法分析")
print("=" * 60)

# 查找主要预测方法
methods = [
    "def predict_perfect_numbers",
    "def initialize_modules", 
    "def run_all_modules",
    "def fusion_predictions",
    "def generate_final_prediction"
]

for method in methods:
    if method in content:
        print(f"✅ 找到方法: {method}")
        
        # 提取方法内容
        start_idx = content.find(method)
        if start_idx != -1:
            # 找到方法的结束位置（下一个def或类结束）
            lines = content[start_idx:].split("\n")
            method_lines = []
            indent_level = None
            
            for line in lines:
                if line.strip().startswith("def ") and method not in line:
                    if indent_level is not None and len(line) - len(line.lstrip()) <= indent_level:
                        break
                elif line.strip().startswith("class "):
                    if indent_level is not None and len(line) - len(line.lstrip()) <= indent_level:
                        break
                
                method_lines.append(line)
                
                if indent_level is None and line.strip():
                    indent_level = len(line) - len(line.lstrip())
                
                if len(method_lines) > 50:  # 限制输出长度
                    method_lines.append("    # ... (方法内容过长，已截断)")
                    break
            
            print(f"   方法内容预览:")
            for i, line in enumerate(method_lines[:20]):
                print(f"   {i+1:2d}: {line}")
            print()
    else:
        print(f"❌ 未找到方法: {method}")

# 查找模组调用模式
print("\n🔍 模组调用模式分析:")
module_calls = [
    (r"traditional_module\..*?\(", "传统分析模组调用"),
    (r"ml_module\..*?\(", "机器学习模组调用"), 
    (r"zodiac_extended_module\..*?\(", "生肖扩展模组调用"),
    (r"special_zodiac_module\..*?\(", "特码生肖模组调用")
]

for pattern, name in module_calls:
    matches = re.findall(pattern, content)
    if matches:
        print(f"   ✅ {name}: {len(matches)}次")
        for match in matches[:3]:  # 显示前3个匹配
            print(f"      - {match}")
    else:
        print(f"   ❓ {name}: 未找到")
