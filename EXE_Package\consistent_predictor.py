"""
一致性特码预测系统 - 确保同一期预测结果一致
解决随机性导致的预测不一致问题
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple
from datetime import datetime, timedelta
import json
import hashlib
from collections import Counter, defaultdict

class ConsistentSpecialNumberPredictor:
    """一致性特码预测系统"""
    
    def __init__(self, seed_base: str = "lottery_prediction_2024"):
        """
        初始化一致性预测系统
        
        Args:
            seed_base: 基础种子字符串，用于生成确定性随机数
        """
        self.seed_base = seed_base
        
        # 生肖映射 (2024年农历)
        self.zodiac_mapping = {
            1: '鼠', 13: '鼠', 25: '鼠', 37: '鼠', 49: '鼠',
            2: '牛', 14: '牛', 26: '牛', 38: '牛',
            3: '虎', 15: '虎', 27: '虎', 39: '虎',
            4: '兔', 16: '兔', 28: '兔', 40: '兔',
            5: '龙', 17: '龙', 29: '龙', 41: '龙',
            6: '蛇', 18: '蛇', 30: '蛇', 42: '蛇',
            7: '马', 19: '马', 31: '马', 43: '马',
            8: '羊', 20: '羊', 32: '羊', 44: '羊',
            9: '猴', 21: '猴', 33: '猴', 45: '猴',
            10: '鸡', 22: '鸡', 34: '鸡', 46: '鸡',
            11: '狗', 23: '狗', 35: '狗', 47: '狗',
            12: '猪', 24: '猪', 36: '猪', 48: '猪'
        }
        
        # 反向映射：生肖到号码
        self.number_by_zodiac = defaultdict(list)
        for num, zodiac in self.zodiac_mapping.items():
            self.number_by_zodiac[zodiac].append(num)
        
        # 生肖列表
        self.zodiac_list = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪']
        
        print("🎯 一致性特码预测系统初始化完成")
        print(f"📊 生肖映射: {len(self.zodiac_mapping)} 个号码")
        print(f"📊 生肖种类: {len(self.zodiac_list)} 个")
        print(f"🔒 确定性种子: {self.seed_base}")
    
    def generate_period_seed(self, target_date: str) -> int:
        """
        为特定期号生成确定性种子
        
        Args:
            target_date: 目标预测日期 (YYYY-MM-DD)
            
        Returns:
            确定性种子值
        """
        # 组合种子字符串
        seed_string = f"{self.seed_base}_{target_date}"
        
        # 使用MD5哈希生成确定性种子
        hash_object = hashlib.md5(seed_string.encode())
        hash_hex = hash_object.hexdigest()
        
        # 转换为整数种子
        seed = int(hash_hex[:8], 16)
        
        return seed
    
    def set_deterministic_seed(self, target_date: str):
        """设置确定性随机种子"""
        seed = self.generate_period_seed(target_date)
        np.random.seed(seed)
        
        # 为了确保Python的random模块也使用相同种子
        import random
        random.seed(seed)
        
        print(f"🔒 设置确定性种子: {seed} (日期: {target_date})")
    
    def generate_deterministic_test_data(self, target_date: str, days: int = 100) -> List[Dict]:
        """生成确定性测试数据"""
        print(f"📊 生成确定性测试数据 (目标日期: {target_date})...")
        
        # 设置确定性种子
        self.set_deterministic_seed(f"test_data_{target_date}")
        
        data = []
        target_datetime = datetime.strptime(target_date, '%Y-%m-%d')
        base_date = target_datetime - timedelta(days=days)
        
        # 设置一些热门生肖和号码（确定性）
        hot_zodiacs = ['龙', '虎', '马', '鸡']
        hot_numbers = [7, 14, 21, 28, 35, 42, 49, 1, 8, 15]
        
        for i in range(days):
            current_date = base_date + timedelta(days=i)
            
            # 生成特码（确定性，基于日期）
            date_seed = self.generate_period_seed(current_date.strftime('%Y-%m-%d'))
            np.random.seed(date_seed % 10000)  # 使用日期相关的种子
            
            if np.random.random() < 0.6:  # 60%概率从热门号码选择
                special_number = np.random.choice(hot_numbers)
            else:
                special_number = np.random.randint(1, 50)
            
            record = {
                'draw_date': current_date.strftime('%Y-%m-%d'),
                'period_number': f"2024{i+1:03d}",
                'special_number': special_number,
                'zodiac': self.zodiac_mapping.get(special_number, '未知')
            }
            
            data.append(record)
        
        print(f"✅ 生成了 {len(data)} 条确定性测试数据")
        return data
    
    def deterministic_sample(self, population: List, k: int, seed_suffix: str = "") -> List:
        """确定性采样"""
        if len(population) <= k:
            return sorted(population)
        
        # 使用确定性方法选择
        population_sorted = sorted(population)
        indices = []
        
        # 基于种子和后缀生成确定性索引
        combined_seed = f"{self.seed_base}_{seed_suffix}"
        hash_object = hashlib.md5(combined_seed.encode())
        hash_int = int(hash_object.hexdigest()[:8], 16)
        
        np.random.seed(hash_int % 100000)
        indices = np.random.choice(len(population_sorted), size=k, replace=False)
        
        return [population_sorted[i] for i in sorted(indices)]
    
    def predict_special_numbers_deterministic(self, analysis: Dict[str, Any], target_date: str) -> List[int]:
        """确定性初选16-24个特码号码"""
        print("\n🎯 确定性初选特码号码 (目标: 16-24个)...")
        
        candidate_numbers = set()
        
        # 策略1: 基于整体频率 (确定性选择)
        hot_numbers_overall = analysis['hot_numbers_overall'][:12]
        candidate_numbers.update(hot_numbers_overall)
        
        # 策略2: 基于多周期分析 (确定性选择)
        period_analysis = analysis['period_analysis']
        for period_name, period_data in period_analysis.items():
            hot_numbers = period_data['hot_numbers'][:8]
            candidate_numbers.update(hot_numbers)
        
        # 策略3: 基于趋势分析 (确定性选择)
        trend = analysis['trend_analysis']
        recent_avg = trend['recent_avg']
        
        if trend['trend_direction'] == 'up':
            trend_numbers = [i for i in range(int(recent_avg), 50) if i <= 49][:8]
        else:
            trend_numbers = [i for i in range(1, int(recent_avg) + 1)][-8:]
        
        candidate_numbers.update(trend_numbers)
        
        # 策略4: 确定性补充
        all_numbers = set(range(1, 50))
        remaining_numbers = list(all_numbers - candidate_numbers)
        
        if len(candidate_numbers) < 24:
            additional_count = min(6, 24 - len(candidate_numbers))
            additional_numbers = self.deterministic_sample(
                remaining_numbers, additional_count, f"additional_{target_date}"
            )
            candidate_numbers.update(additional_numbers)
        
        # 确保在16-24个范围内 (确定性处理)
        initial_selection = list(candidate_numbers)
        if len(initial_selection) > 24:
            # 按频率排序选择前24个
            number_scores = {}
            for num in initial_selection:
                score = analysis['number_frequency'].get(num, 0)
                number_scores[num] = score
            
            sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
            initial_selection = [num for num, score in sorted_numbers[:24]]
        
        elif len(initial_selection) < 16:
            remaining = list(all_numbers - set(initial_selection))
            additional_needed = 16 - len(initial_selection)
            additional = self.deterministic_sample(
                remaining, additional_needed, f"fill_{target_date}"
            )
            initial_selection.extend(additional)
        
        initial_selection.sort()
        
        print(f"   ✅ 确定性初选完成: {len(initial_selection)} 个号码")
        print(f"   📋 初选号码: {initial_selection}")
        
        return initial_selection
    
    def cross_validate_deterministic(self, initial_selection: List[int], analysis: Dict[str, Any], target_date: str) -> List[int]:
        """确定性交叉验证并推荐12-16个号码"""
        print("\n🔄 确定性交叉验证推荐 (目标: 12-16个)...")
        
        # 为每个号码计算综合得分 (确定性计算)
        number_scores = {}
        
        for number in initial_selection:
            score = 0
            
            # 得分维度1: 整体频率得分 (权重30%)
            overall_freq = analysis['number_frequency'].get(number, 0)
            max_freq = max(analysis['number_frequency'].values()) if analysis['number_frequency'] else 1
            freq_score = (overall_freq / max_freq) * 30
            score += freq_score
            
            # 得分维度2: 多周期一致性得分 (权重25%)
            consistency_score = 0
            period_analysis = analysis['period_analysis']
            for period_data in period_analysis.values():
                if number in period_data['hot_numbers']:
                    consistency_score += 5
            score += min(consistency_score, 25)
            
            # 得分维度3: 趋势符合度得分 (权重20%)
            trend = analysis['trend_analysis']
            recent_avg = trend['recent_avg']
            
            if trend['trend_direction'] == 'up' and number >= recent_avg:
                trend_score = 20
            elif trend['trend_direction'] == 'down' and number <= recent_avg:
                trend_score = 20
            else:
                trend_score = 10
            score += trend_score
            
            # 得分维度4: 生肖热度得分 (权重15%)
            number_zodiac = self.zodiac_mapping.get(number, '未知')
            zodiac_freq = analysis['zodiac_frequency'].get(number_zodiac, 0)
            max_zodiac_freq = max(analysis['zodiac_frequency'].values()) if analysis['zodiac_frequency'] else 1
            zodiac_score = (zodiac_freq / max_zodiac_freq) * 15
            score += zodiac_score
            
            # 得分维度5: 最近出现得分 (权重10%)
            recent_numbers = analysis['trend_analysis']['recent_numbers']
            if number in recent_numbers[-5:]:
                recent_score = 10
            elif number in recent_numbers:
                recent_score = 5
            else:
                recent_score = 0
            score += recent_score
            
            number_scores[number] = score
        
        # 按得分排序 (确定性排序)
        sorted_numbers = sorted(number_scores.items(), key=lambda x: (x[1], x[0]), reverse=True)
        
        # 确定性选择前16个 (固定16个以满足用户要求)
        recommended_count = 16
        
        recommended_numbers = [num for num, score in sorted_numbers[:recommended_count]]
        
        print(f"   ✅ 确定性交叉验证完成: {len(recommended_numbers)} 个推荐号码")
        print(f"   📋 推荐号码: {recommended_numbers}")
        
        # 显示得分详情
        print(f"   📊 号码得分排序:")
        for i, (num, score) in enumerate(sorted_numbers[:recommended_count]):
            zodiac = self.zodiac_mapping.get(num, '未知')
            print(f"      {i+1:2d}. 号码 {num:2d} ({zodiac}) - 得分: {score:.1f}")
        
        return recommended_numbers

    def predict_top_zodiacs_deterministic(self, analysis: Dict[str, Any], target_date: str) -> List[Dict[str, Any]]:
        """确定性预测最高得分的4个生肖"""
        print("\n🐲 确定性预测最高得分的4个生肖...")

        zodiac_scores = {}

        for zodiac in self.zodiac_list:
            score = 0

            # 得分维度1: 整体频率得分 (权重35%)
            overall_freq = analysis['zodiac_frequency'].get(zodiac, 0)
            max_freq = max(analysis['zodiac_frequency'].values()) if analysis['zodiac_frequency'] else 1
            freq_score = (overall_freq / max_freq) * 35
            score += freq_score

            # 得分维度2: 多周期一致性得分 (权重30%)
            consistency_score = 0
            period_analysis = analysis['period_analysis']
            for period_data in period_analysis.values():
                if zodiac in period_data['hot_zodiacs']:
                    consistency_score += 6
            score += min(consistency_score, 30)

            # 得分维度3: 最近趋势得分 (权重25%)
            recent_zodiacs = []
            for record in analysis.get('recent_records', [])[-10:]:
                recent_zodiacs.append(record.get('zodiac', ''))

            recent_zodiac_freq = Counter(recent_zodiacs)
            recent_freq = recent_zodiac_freq.get(zodiac, 0)
            max_recent_freq = max(recent_zodiac_freq.values()) if recent_zodiac_freq else 1
            recent_score = (recent_freq / max_recent_freq) * 25
            score += recent_score

            # 得分维度4: 号码分布得分 (权重10%)
            zodiac_numbers = self.number_by_zodiac[zodiac]
            distribution_score = len(zodiac_numbers) * 2
            score += min(distribution_score, 10)

            zodiac_scores[zodiac] = score

        # 确定性排序，选择前4个
        sorted_zodiacs = sorted(zodiac_scores.items(), key=lambda x: (x[1], x[0]), reverse=True)
        top_4_zodiacs = []

        for i, (zodiac, score) in enumerate(sorted_zodiacs[:4]):
            zodiac_numbers = self.number_by_zodiac[zodiac]
            zodiac_info = {
                'rank': i + 1,
                'zodiac': zodiac,
                'score': score,
                'numbers': zodiac_numbers,
                'frequency': analysis['zodiac_frequency'].get(zodiac, 0),
                'confidence': 'high' if score > 60 else 'medium' if score > 40 else 'low'
            }
            top_4_zodiacs.append(zodiac_info)

        print(f"   ✅ 确定性生肖预测完成: 前4个最高得分生肖")
        for zodiac_info in top_4_zodiacs:
            print(f"      {zodiac_info['rank']}. {zodiac_info['zodiac']} - 得分: {zodiac_info['score']:.1f} "
                  f"(置信度: {zodiac_info['confidence']}) 号码: {zodiac_info['numbers']}")

        return top_4_zodiacs

    def run_consistent_prediction(self, target_date: str) -> Dict[str, Any]:
        """运行一致性预测"""
        print("🚀 开始一致性澳门六合彩特码预测...")
        print("="*60)
        print(f"🎯 目标预测日期: {target_date}")

        # 1. 生成确定性测试数据
        test_data = self.generate_deterministic_test_data(target_date, days=120)

        # 2. 分析历史模式 (使用原有的分析方法，因为它是确定性的)
        from special_number_predictor import SpecialNumberPredictor
        temp_predictor = SpecialNumberPredictor()
        analysis = temp_predictor.analyze_special_number_patterns(test_data)

        # 3. 确定性初选16-24个号码
        initial_selection = self.predict_special_numbers_deterministic(analysis, target_date)

        # 4. 确定性交叉验证推荐12-16个号码
        recommended_numbers = self.cross_validate_deterministic(initial_selection, analysis, target_date)

        # 5. 确定性预测最高得分的4个生肖
        top_zodiacs = self.predict_top_zodiacs_deterministic(analysis, target_date)

        # 6. 生成最终预测
        final_prediction = self.generate_final_prediction_deterministic(recommended_numbers, top_zodiacs, target_date)

        # 7. 显示结果
        self.display_prediction_results(final_prediction)

        print("\n✅ 一致性特码预测完成！")
        print(f"🔒 预测结果具有完全一致性 - 同一日期多次预测结果相同")

        return final_prediction

    def generate_final_prediction_deterministic(self, recommended_numbers: List[int], top_zodiacs: List[Dict], target_date: str) -> Dict[str, Any]:
        """生成确定性最终预测结果"""
        print("\n🎊 生成确定性最终预测结果...")

        # 从推荐号码中选择最终特码预测 (确定性选择) - 修改为16个
        final_special_numbers = recommended_numbers[:16] if len(recommended_numbers) >= 16 else recommended_numbers

        # 生肖维度的号码推荐
        zodiac_recommended_numbers = []
        for zodiac_info in top_zodiacs:
            zodiac_recommended_numbers.extend(zodiac_info['numbers'])

        # 去重并排序
        zodiac_recommended_numbers = sorted(list(set(zodiac_recommended_numbers)))

        # 融合推荐：结合号码分析和生肖分析
        fusion_numbers = list(set(final_special_numbers) | set(zodiac_recommended_numbers))
        fusion_numbers.sort()

        final_prediction = {
            'prediction_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'target_date': target_date,
            'prediction_seed': self.generate_period_seed(target_date),

            # 特码号码预测
            'special_number_prediction': {
                'final_recommendations': final_special_numbers,
                'confidence': 'high',
                'method': '确定性多维度交叉验证分析'
            },

            # 生肖维度预测
            'zodiac_prediction': {
                'top_4_zodiacs': [
                    {
                        'zodiac': z['zodiac'],
                        'score': z['score'],
                        'confidence': z['confidence'],
                        'numbers': z['numbers']
                    }
                    for z in top_zodiacs
                ],
                'method': '确定性多维度生肖得分分析'
            },

            # 融合预测
            'fusion_prediction': {
                'recommended_numbers': fusion_numbers[:16] if len(fusion_numbers) >= 16 else fusion_numbers,  # 修改为16个
                'method': '确定性号码分析与生肖分析融合',
                'confidence': 'very_high'
            }
        }

        return final_prediction

    def display_prediction_results(self, prediction: Dict[str, Any]):
        """显示预测结果"""
        print("\n" + "="*60)
        print("🎯 一致性澳门六合彩特码预测结果")
        print("="*60)

        print(f"\n📅 预测时间: {prediction['prediction_time']}")
        print(f"📅 目标日期: {prediction['target_date']}")
        print(f"🔒 预测种子: {prediction['prediction_seed']} (确保一致性)")

        # 特码号码预测
        special_pred = prediction['special_number_prediction']
        print(f"\n🎯 特码号码预测 (置信度: {special_pred['confidence']}):")
        print(f"   推荐号码: {special_pred['final_recommendations']}")
        print(f"   分析方法: {special_pred['method']}")

        # 生肖维度预测
        zodiac_pred = prediction['zodiac_prediction']
        print(f"\n🐲 生肖维度预测:")
        print(f"   分析方法: {zodiac_pred['method']}")
        print(f"   最高得分4个生肖:")

        for i, zodiac_info in enumerate(zodiac_pred['top_4_zodiacs']):
            print(f"      {i+1}. {zodiac_info['zodiac']} (得分: {zodiac_info['score']:.1f}, "
                  f"置信度: {zodiac_info['confidence']})")
            print(f"         对应号码: {zodiac_info['numbers']}")

        # 融合预测
        fusion_pred = prediction['fusion_prediction']
        print(f"\n🔀 融合预测 (置信度: {fusion_pred['confidence']}):")
        print(f"   最终推荐: {fusion_pred['recommended_numbers']}")
        print(f"   融合方法: {fusion_pred['method']}")

        print("\n" + "="*60)

def main():
    """主函数"""
    print("🎯 一致性澳门六合彩特码预测系统")
    print("🔒 解决方案: 确保同一期预测结果完全一致")
    print()

    # 创建一致性预测器
    predictor = ConsistentSpecialNumberPredictor()

    # 设置目标预测日期
    target_date = "2025-06-23"  # 可以修改为任何目标日期

    print(f"🎯 预测目标: {target_date}")
    print("🔒 多次运行将产生完全相同的结果")
    print()

    # 运行一致性预测
    prediction_result = predictor.run_consistent_prediction(target_date)

    return prediction_result

# 为了兼容性，创建别名
ConsistentPredictor = ConsistentSpecialNumberPredictor

if __name__ == "__main__":
    main()
