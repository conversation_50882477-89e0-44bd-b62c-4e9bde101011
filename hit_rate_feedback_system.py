#!/usr/bin/env python3
"""
命中率反馈学习系统 - 基于实际命中结果持续优化预测策略
"""

import numpy as np
import json
from collections import defaultdict, deque
from datetime import datetime, timedelta
import sqlite3

class HitRateFeedbackSystem:
    """命中率反馈学习系统"""
    
    def __init__(self, db_path="data/lottery.db"):
        self.db_path = db_path
        
        # 反馈学习配置
        self.learning_config = {
            "memory_window": 50,        # 记忆窗口大小
            "adaptation_rate": 0.1,     # 学习率
            "strategy_count": 5,        # 策略数量
            "performance_threshold": 0.2 # 性能阈值
        }
        
        # 策略性能历史
        self.strategy_performance = defaultdict(lambda: deque(maxlen=self.learning_config["memory_window"]))
        
        # 动态权重
        self.dynamic_weights = {
            "frequency_analysis": 0.25,
            "pattern_recognition": 0.20,
            "trend_following": 0.20,
            "hot_cold_balance": 0.20,
            "zodiac_correlation": 0.15
        }
        
        # 学习历史
        self.learning_history = []
        
    def record_prediction_result(self, prediction_data, actual_result):
        """记录预测结果用于学习"""
        print(f"📝 记录预测结果: {actual_result}")
        
        # 解析预测数据
        strategies_used = prediction_data.get("strategies_used", {})
        predicted_numbers = prediction_data.get("final_numbers", [])
        
        # 计算整体命中情况
        overall_hit = actual_result in predicted_numbers
        
        # 评估各策略表现
        strategy_evaluations = {}
        
        for strategy_name, strategy_result in strategies_used.items():
            strategy_numbers = strategy_result.get("numbers", [])
            strategy_hit = actual_result in strategy_numbers
            
            # 计算策略得分
            hit_score = 1.0 if strategy_hit else 0.0
            position_score = 0.0
            
            if strategy_hit and strategy_numbers:
                position = strategy_numbers.index(actual_result)
                position_score = (len(strategy_numbers) - position) / len(strategy_numbers)
            
            strategy_score = hit_score * 0.7 + position_score * 0.3
            
            # 记录策略性能
            self.strategy_performance[strategy_name].append({
                "date": datetime.now().isoformat(),
                "hit": strategy_hit,
                "score": strategy_score,
                "predicted_count": len(strategy_numbers),
                "actual_result": actual_result
            })
            
            strategy_evaluations[strategy_name] = {
                "hit": strategy_hit,
                "score": strategy_score,
                "numbers": strategy_numbers
            }
        
        # 记录学习历史
        learning_record = {
            "date": datetime.now().isoformat(),
            "prediction": prediction_data,
            "actual_result": actual_result,
            "overall_hit": overall_hit,
            "strategy_evaluations": strategy_evaluations,
            "weights_before": self.dynamic_weights.copy()
        }
        
        # 更新动态权重
        self._update_dynamic_weights()
        learning_record["weights_after"] = self.dynamic_weights.copy()
        
        self.learning_history.append(learning_record)
        
        # 保存学习数据
        self._save_learning_data()
        
        return learning_record
    
    def _update_dynamic_weights(self):
        """更新动态权重"""
        print("🔄 更新动态权重...")
        
        # 计算各策略的近期表现
        strategy_scores = {}
        
        for strategy_name in self.dynamic_weights.keys():
            if strategy_name in self.strategy_performance:
                recent_performance = list(self.strategy_performance[strategy_name])
                
                if recent_performance:
                    # 计算平均得分
                    avg_score = np.mean([p["score"] for p in recent_performance])
                    
                    # 计算稳定性（得分方差的倒数）
                    scores = [p["score"] for p in recent_performance]
                    stability = 1.0 / (np.var(scores) + 0.01)  # 避免除零
                    
                    # 计算趋势（最近表现是否在改善）
                    if len(recent_performance) >= 5:
                        recent_5 = recent_performance[-5:]
                        earlier_5 = recent_performance[-10:-5] if len(recent_performance) >= 10 else recent_performance[:-5]
                        
                        recent_avg = np.mean([p["score"] for p in recent_5])
                        earlier_avg = np.mean([p["score"] for p in earlier_5]) if earlier_5 else recent_avg
                        
                        trend = (recent_avg - earlier_avg) / (earlier_avg + 0.01)
                    else:
                        trend = 0.0
                    
                    # 综合评分
                    strategy_scores[strategy_name] = {
                        "avg_score": avg_score,
                        "stability": stability,
                        "trend": trend,
                        "composite_score": avg_score * 0.6 + stability * 0.2 + trend * 0.2
                    }
                else:
                    strategy_scores[strategy_name] = {
                        "avg_score": 0.5,
                        "stability": 1.0,
                        "trend": 0.0,
                        "composite_score": 0.5
                    }
            else:
                strategy_scores[strategy_name] = {
                    "avg_score": 0.5,
                    "stability": 1.0,
                    "trend": 0.0,
                    "composite_score": 0.5
                }
        
        # 计算新权重
        total_score = sum(scores["composite_score"] for scores in strategy_scores.values())
        
        if total_score > 0:
            new_weights = {}
            for strategy_name, scores in strategy_scores.items():
                # 基础权重 + 性能调整
                base_weight = 1.0 / len(self.dynamic_weights)  # 平均权重
                performance_adjustment = (scores["composite_score"] / total_score) - base_weight
                
                # 应用学习率
                adjustment = performance_adjustment * self.learning_config["adaptation_rate"]
                new_weight = self.dynamic_weights[strategy_name] + adjustment
                
                # 权重约束
                new_weight = max(0.05, min(0.5, new_weight))  # 权重范围 [0.05, 0.5]
                new_weights[strategy_name] = new_weight
            
            # 归一化权重
            total_weight = sum(new_weights.values())
            for strategy_name in new_weights:
                new_weights[strategy_name] /= total_weight
            
            # 更新权重
            old_weights = self.dynamic_weights.copy()
            self.dynamic_weights = new_weights
            
            # 输出权重变化
            print("  权重更新:")
            for strategy_name in self.dynamic_weights:
                old_w = old_weights[strategy_name]
                new_w = new_weights[strategy_name]
                change = new_w - old_w
                print(f"    {strategy_name}: {old_w:.3f} → {new_w:.3f} ({change:+.3f})")
    
    def get_optimized_strategy_weights(self):
        """获取优化后的策略权重"""
        return self.dynamic_weights.copy()
    
    def get_strategy_performance_report(self):
        """获取策略性能报告"""
        report = {
            "report_time": datetime.now().isoformat(),
            "total_predictions": len(self.learning_history),
            "strategy_performance": {},
            "overall_performance": {}
        }
        
        # 各策略性能统计
        for strategy_name, performance_history in self.strategy_performance.items():
            if performance_history:
                performances = list(performance_history)
                
                hit_count = sum(1 for p in performances if p["hit"])
                total_count = len(performances)
                hit_rate = hit_count / total_count if total_count > 0 else 0
                
                avg_score = np.mean([p["score"] for p in performances])
                score_std = np.std([p["score"] for p in performances])
                
                # 最近表现
                recent_10 = performances[-10:] if len(performances) >= 10 else performances
                recent_hit_rate = sum(1 for p in recent_10 if p["hit"]) / len(recent_10) if recent_10 else 0
                
                report["strategy_performance"][strategy_name] = {
                    "total_predictions": total_count,
                    "hit_count": hit_count,
                    "hit_rate": hit_rate,
                    "recent_hit_rate": recent_hit_rate,
                    "avg_score": avg_score,
                    "score_stability": 1.0 / (score_std + 0.01),
                    "current_weight": self.dynamic_weights.get(strategy_name, 0)
                }
        
        # 整体性能统计
        if self.learning_history:
            overall_hits = sum(1 for record in self.learning_history if record["overall_hit"])
            overall_hit_rate = overall_hits / len(self.learning_history)
            
            # 最近表现趋势
            recent_20 = self.learning_history[-20:] if len(self.learning_history) >= 20 else self.learning_history
            recent_hits = sum(1 for record in recent_20 if record["overall_hit"])
            recent_hit_rate = recent_hits / len(recent_20) if recent_20 else 0
            
            report["overall_performance"] = {
                "total_predictions": len(self.learning_history),
                "overall_hits": overall_hits,
                "overall_hit_rate": overall_hit_rate,
                "recent_hit_rate": recent_hit_rate,
                "improvement": recent_hit_rate - overall_hit_rate
            }
        
        return report
    
    def suggest_strategy_improvements(self):
        """建议策略改进"""
        suggestions = []
        
        performance_report = self.get_strategy_performance_report()
        strategy_perfs = performance_report.get("strategy_performance", {})
        
        for strategy_name, perf in strategy_perfs.items():
            hit_rate = perf["hit_rate"]
            recent_hit_rate = perf["recent_hit_rate"]
            current_weight = perf["current_weight"]
            
            # 分析并给出建议
            if hit_rate < 0.15:  # 命中率过低
                suggestions.append({
                    "strategy": strategy_name,
                    "issue": "命中率过低",
                    "current_hit_rate": hit_rate,
                    "suggestion": "考虑调整策略参数或降低权重",
                    "priority": "高"
                })
            
            elif recent_hit_rate < hit_rate * 0.7:  # 近期表现下降
                suggestions.append({
                    "strategy": strategy_name,
                    "issue": "近期表现下降",
                    "recent_vs_overall": f"{recent_hit_rate:.1%} vs {hit_rate:.1%}",
                    "suggestion": "检查策略逻辑，可能需要更新",
                    "priority": "中"
                })
            
            elif hit_rate > 0.25 and current_weight < 0.2:  # 表现好但权重低
                suggestions.append({
                    "strategy": strategy_name,
                    "issue": "表现优秀但权重偏低",
                    "hit_rate": hit_rate,
                    "current_weight": current_weight,
                    "suggestion": "考虑增加该策略的权重",
                    "priority": "中"
                })
        
        return suggestions
    
    def _save_learning_data(self):
        """保存学习数据"""
        try:
            learning_data = {
                "dynamic_weights": self.dynamic_weights,
                "strategy_performance": {
                    name: list(performances) 
                    for name, performances in self.strategy_performance.items()
                },
                "learning_history": self.learning_history[-100:],  # 只保存最近100条
                "last_update": datetime.now().isoformat()
            }
            
            with open("hit_rate_learning_data.json", "w", encoding="utf-8") as f:
                json.dump(learning_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"⚠️ 保存学习数据失败: {e}")
    
    def load_learning_data(self):
        """加载学习数据"""
        try:
            with open("hit_rate_learning_data.json", "r", encoding="utf-8") as f:
                learning_data = json.load(f)
            
            # 恢复动态权重
            if "dynamic_weights" in learning_data:
                self.dynamic_weights = learning_data["dynamic_weights"]
            
            # 恢复策略性能历史
            if "strategy_performance" in learning_data:
                for name, performances in learning_data["strategy_performance"].items():
                    self.strategy_performance[name] = deque(performances, maxlen=self.learning_config["memory_window"])
            
            # 恢复学习历史
            if "learning_history" in learning_data:
                self.learning_history = learning_data["learning_history"]
            
            print("✅ 学习数据加载成功")
            return True
            
        except FileNotFoundError:
            print("ℹ️ 未找到学习数据文件，使用默认配置")
            return False
        except Exception as e:
            print(f"⚠️ 加载学习数据失败: {e}")
            return False
    
    def reset_learning_system(self):
        """重置学习系统"""
        print("🔄 重置学习系统...")
        
        # 重置权重为默认值
        self.dynamic_weights = {
            "frequency_analysis": 0.25,
            "pattern_recognition": 0.20,
            "trend_following": 0.20,
            "hot_cold_balance": 0.20,
            "zodiac_correlation": 0.15
        }
        
        # 清空历史数据
        self.strategy_performance.clear()
        self.learning_history.clear()
        
        print("✅ 学习系统已重置")
    
    def export_performance_analysis(self):
        """导出性能分析报告"""
        report = self.get_strategy_performance_report()
        suggestions = self.suggest_strategy_improvements()
        
        analysis = {
            "export_time": datetime.now().isoformat(),
            "performance_report": report,
            "improvement_suggestions": suggestions,
            "current_weights": self.dynamic_weights,
            "learning_config": self.learning_config
        }
        
        filename = f"hit_rate_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(analysis, f, ensure_ascii=False, indent=2)
            
            print(f"📊 性能分析报告已导出: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ 导出失败: {e}")
            return None

if __name__ == "__main__":
    # 测试反馈学习系统
    feedback_system = HitRateFeedbackSystem()
    feedback_system.load_learning_data()
    
    print("🧠 命中率反馈学习系统测试")
    print(f"当前权重: {feedback_system.dynamic_weights}")
    
    # 模拟预测结果记录
    test_prediction = {
        "final_numbers": [1, 5, 12, 18, 23, 28, 34, 39, 42, 45, 47, 49, 3, 8, 15, 21],
        "strategies_used": {
            "frequency_analysis": {"numbers": [1, 5, 12, 18, 23, 28, 34, 39, 42, 45, 47, 49, 3, 8, 15, 21]},
            "pattern_recognition": {"numbers": [2, 6, 13, 19, 24, 29, 35, 40, 43, 46, 48, 1, 4, 9, 16, 22]},
            "trend_following": {"numbers": [3, 7, 14, 20, 25, 30, 36, 41, 44, 47, 49, 2, 5, 10, 17, 23]}
        }
    }
    
    # 记录一个测试结果
    feedback_system.record_prediction_result(test_prediction, 12)
    
    # 获取性能报告
    report = feedback_system.get_strategy_performance_report()
    print(f"\n📊 性能报告: {json.dumps(report, ensure_ascii=False, indent=2)}")
