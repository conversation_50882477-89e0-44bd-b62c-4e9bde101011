"""
特码生肖专项分析模块
专门针对特码的生肖分析，包括冷热分析、远近分析、周期分析等
"""
import sqlite3
from datetime import datetime, timedelta, date
from typing import List, Dict, Any, Tuple
import random
from collections import defaultdict, Counter
import math

class SpecialZodiacAnalyzer:
    """特码生肖专项分析器"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        
        # 生肖映射（2025年蛇年）
        self.zodiac_mappings = {
            2025: {
                '鼠': [6, 18, 30, 42],
                '牛': [5, 17, 29, 41],
                '虎': [4, 16, 28, 40],
                '兔': [3, 15, 27, 39],
                '龙': [2, 14, 26, 38],
                '蛇': [1, 13, 25, 37, 49],  # 头肖，5个号码
                '马': [12, 24, 36, 48],
                '羊': [11, 23, 35, 47],
                '猴': [10, 22, 34, 46],
                '鸡': [9, 21, 33, 45],
                '狗': [8, 20, 32, 44],
                '猪': [7, 19, 31, 43]
            },
            2024: {
                '鼠': [5, 17, 29, 41],
                '牛': [4, 16, 28, 40],
                '虎': [3, 15, 27, 39],
                '兔': [2, 14, 26, 38],
                '龙': [1, 13, 25, 37, 49],  # 头肖，5个号码
                '蛇': [12, 24, 36, 48],
                '马': [11, 23, 35, 47],
                '羊': [10, 22, 34, 46],
                '猴': [9, 21, 33, 45],
                '鸡': [8, 20, 32, 44],
                '狗': [7, 19, 31, 43],
                '猪': [6, 18, 30, 42]
            }
        }
        
        # 生肖分类
        self.zodiac_categories = {
            "琴棋书画": {
                "琴": ["鸡", "兔", "蛇"],
                "棋": ["鼠", "牛", "狗"],
                "书": ["虎", "龙", "马"],
                "画": ["羊", "猴", "猪"]
            },
            "季节": {
                "春": ["虎", "兔", "龙"],
                "夏": ["蛇", "马", "羊"],
                "秋": ["猴", "鸡", "狗"],
                "冬": ["猪", "鼠", "牛"]
            },
            "五行": {
                "金": ["猴", "鸡"],
                "木": ["虎", "兔"],
                "水": ["鼠", "猪"],
                "火": ["蛇", "马"],
                "土": ["牛", "龙", "羊", "狗"]
            },
            "阴阳": {
                "阳": ["鼠", "虎", "龙", "马", "猴", "狗"],
                "阴": ["牛", "兔", "蛇", "羊", "鸡", "猪"]
            }
        }
        
        self.zodiac_names = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪']
    
    def get_zodiac_by_number(self, number: int, year: int = 2025) -> str:
        """根据号码获取生肖"""
        if year not in self.zodiac_mappings:
            year = 2025  # 默认使用2025年
        
        year_mapping = self.zodiac_mappings[year]
        for zodiac, numbers in year_mapping.items():
            if number in numbers:
                return zodiac
        return '未知'
    
    def get_numbers_by_zodiac(self, zodiac: str, year: int = 2025) -> List[int]:
        """根据生肖获取号码列表"""
        if year not in self.zodiac_mappings:
            year = 2025
        
        return self.zodiac_mappings[year].get(zodiac, [])
    
    def get_historical_special_data(self, days: int = 100) -> List[Dict]:
        """获取历史特码数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取最近N天的特码数据
            cursor.execute('''
                SELECT draw_date, period_number, special_number
                FROM lottery_results
                ORDER BY draw_date DESC
                LIMIT ?
            ''', (days,))
            
            results = cursor.fetchall()
            conn.close()
            
            data = []
            for row in results:
                draw_date, period_number, special_number = row
                zodiac = self.get_zodiac_by_number(special_number)
                
                data.append({
                    'date': draw_date,
                    'period': period_number,
                    'special_number': special_number,
                    'zodiac': zodiac
                })
            
            return data
            
        except Exception as e:
            print(f"获取历史数据失败: {e}")
            return []
    
    def analyze_zodiac_hot_cold(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """分析生肖冷热度"""
        zodiac_count = Counter()
        total_records = len(historical_data)
        
        # 统计各生肖出现次数
        for record in historical_data:
            zodiac = record['zodiac']
            if zodiac != '未知':
                zodiac_count[zodiac] += 1
        
        # 计算期望频率（理论上每个生肖应该出现的次数）
        expected_freq = total_records / 12
        
        # 分析冷热度
        hot_zodiacs = []
        cold_zodiacs = []
        normal_zodiacs = []
        
        zodiac_analysis = {}
        
        for zodiac in self.zodiac_names:
            count = zodiac_count.get(zodiac, 0)
            frequency = count / total_records if total_records > 0 else 0
            deviation = count - expected_freq
            
            # 分类标准
            if deviation > expected_freq * 0.2:  # 超出期望20%为热
                category = "热门"
                hot_zodiacs.append(zodiac)
            elif deviation < -expected_freq * 0.2:  # 低于期望20%为冷
                category = "冷门"
                cold_zodiacs.append(zodiac)
            else:
                category = "正常"
                normal_zodiacs.append(zodiac)
            
            zodiac_analysis[zodiac] = {
                'count': count,
                'frequency': frequency,
                'expected': expected_freq,
                'deviation': deviation,
                'category': category,
                'heat_score': (count / expected_freq) if expected_freq > 0 else 0
            }
        
        return {
            'zodiac_analysis': zodiac_analysis,
            'hot_zodiacs': hot_zodiacs,
            'cold_zodiacs': cold_zodiacs,
            'normal_zodiacs': normal_zodiacs,
            'total_records': total_records,
            'analysis_method': '冷热度分析'
        }
    
    def analyze_zodiac_distance(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """分析生肖远近度（距离上次出现的间隔）"""
        zodiac_distances = {}
        zodiac_last_seen = {}
        
        # 初始化
        for zodiac in self.zodiac_names:
            zodiac_distances[zodiac] = []
            zodiac_last_seen[zodiac] = -1
        
        # 计算距离
        for i, record in enumerate(reversed(historical_data)):  # 从最早开始
            zodiac = record['zodiac']
            if zodiac != '未知':
                if zodiac_last_seen[zodiac] >= 0:
                    distance = i - zodiac_last_seen[zodiac]
                    zodiac_distances[zodiac].append(distance)
                zodiac_last_seen[zodiac] = i
        
        # 分析远近度
        distance_analysis = {}
        current_distances = {}
        
        for zodiac in self.zodiac_names:
            distances = zodiac_distances[zodiac]
            last_seen = zodiac_last_seen[zodiac]
            current_distance = len(historical_data) - 1 - last_seen if last_seen >= 0 else len(historical_data)
            
            if distances:
                avg_distance = sum(distances) / len(distances)
                min_distance = min(distances)
                max_distance = max(distances)
            else:
                avg_distance = current_distance
                min_distance = current_distance
                max_distance = current_distance
            
            # 远近分类
            if current_distance > avg_distance * 1.5:
                distance_category = "远期"
            elif current_distance < avg_distance * 0.5:
                distance_category = "近期"
            else:
                distance_category = "正常"
            
            distance_analysis[zodiac] = {
                'current_distance': current_distance,
                'average_distance': avg_distance,
                'min_distance': min_distance,
                'max_distance': max_distance,
                'distance_category': distance_category,
                'urgency_score': avg_distance / (current_distance + 1) if current_distance >= 0 else 0
            }
            
            current_distances[zodiac] = current_distance
        
        # 按当前距离排序
        far_zodiacs = [z for z, d in current_distances.items() if d > 10]
        near_zodiacs = [z for z, d in current_distances.items() if d <= 3]
        
        return {
            'distance_analysis': distance_analysis,
            'far_zodiacs': far_zodiacs,
            'near_zodiacs': near_zodiacs,
            'current_distances': current_distances,
            'analysis_method': '远近度分析'
        }
    
    def analyze_zodiac_cycles(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """分析生肖周期性"""
        zodiac_positions = defaultdict(list)
        
        # 记录每个生肖出现的位置
        for i, record in enumerate(historical_data):
            zodiac = record['zodiac']
            if zodiac != '未知':
                zodiac_positions[zodiac].append(i)
        
        cycle_analysis = {}
        
        for zodiac in self.zodiac_names:
            positions = zodiac_positions[zodiac]
            
            if len(positions) >= 2:
                # 计算间隔
                intervals = [positions[i] - positions[i-1] for i in range(1, len(positions))]
                avg_interval = sum(intervals) / len(intervals)
                
                # 预测下次出现
                last_position = positions[-1]
                predicted_next = last_position + avg_interval
                
                # 周期稳定性
                if intervals:
                    interval_variance = sum((x - avg_interval) ** 2 for x in intervals) / len(intervals)
                    stability = 1 / (1 + interval_variance / avg_interval) if avg_interval > 0 else 0
                else:
                    stability = 0
            else:
                avg_interval = len(historical_data) / 2
                predicted_next = len(positions) + avg_interval if positions else avg_interval
                stability = 0
            
            cycle_analysis[zodiac] = {
                'appearance_count': len(positions),
                'average_interval': avg_interval,
                'predicted_next_position': predicted_next,
                'cycle_stability': stability,
                'last_appearance': positions[-1] if positions else -1
            }
        
        return {
            'cycle_analysis': cycle_analysis,
            'analysis_method': '周期性分析'
        }

    def analyze_zodiac_categories(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """分析生肖分类趋势"""
        category_analysis = {}

        for category_name, categories in self.zodiac_categories.items():
            category_count = defaultdict(int)

            # 统计各分类的出现次数
            for record in historical_data:
                zodiac = record['zodiac']
                if zodiac != '未知':
                    for sub_category, zodiac_list in categories.items():
                        if zodiac in zodiac_list:
                            category_count[sub_category] += 1

            # 分析趋势
            total_count = sum(category_count.values())
            category_trends = {}

            for sub_category, count in category_count.items():
                frequency = count / total_count if total_count > 0 else 0
                expected_freq = 1 / len(categories)  # 期望频率
                trend_score = frequency / expected_freq if expected_freq > 0 else 0

                category_trends[sub_category] = {
                    'count': count,
                    'frequency': frequency,
                    'expected_frequency': expected_freq,
                    'trend_score': trend_score,
                    'zodiacs': categories[sub_category]
                }

            category_analysis[category_name] = category_trends

        return {
            'category_analysis': category_analysis,
            'analysis_method': '分类趋势分析'
        }

    def comprehensive_special_zodiac_prediction(self, days: int = 100) -> Dict[str, Any]:
        """综合特码生肖预测"""
        print(f"🔍 开始特码生肖专项分析（最近{days}天数据）...")

        # 获取历史数据
        historical_data = self.get_historical_special_data(days)

        if len(historical_data) < 10:
            print(f"⚠️ 历史数据不足，仅有{len(historical_data)}条记录")
            return self._get_default_prediction()

        print(f"📊 获取到{len(historical_data)}条历史特码数据")

        # 执行各种分析
        analyses = {}

        # 1. 冷热度分析
        hot_cold_result = self.analyze_zodiac_hot_cold(historical_data)
        analyses['hot_cold'] = hot_cold_result
        print(f"🔥 冷热度分析完成")

        # 2. 远近度分析
        distance_result = self.analyze_zodiac_distance(historical_data)
        analyses['distance'] = distance_result
        print(f"📏 远近度分析完成")

        # 3. 周期性分析
        cycle_result = self.analyze_zodiac_cycles(historical_data)
        analyses['cycles'] = cycle_result
        print(f"🔄 周期性分析完成")

        # 4. 分类趋势分析
        category_result = self.analyze_zodiac_categories(historical_data)
        analyses['categories'] = category_result
        print(f"📊 分类趋势分析完成")

        # 综合评分
        final_scores = self._calculate_comprehensive_scores(analyses)

        # 选择推荐生肖
        recommended_zodiacs = self._select_recommended_zodiacs(final_scores)

        # 生成推荐号码
        recommended_numbers = []
        for zodiac in recommended_zodiacs:
            numbers = self.get_numbers_by_zodiac(zodiac)
            recommended_numbers.extend(numbers)

        # 计算置信度
        confidence = self._calculate_confidence(final_scores, recommended_zodiacs)

        result = {
            'recommended_zodiacs': recommended_zodiacs,
            'recommended_numbers': sorted(recommended_numbers),
            'zodiac_scores': final_scores,
            'confidence': confidence,
            'analysis_details': analyses,
            'data_period': f"最近{days}天",
            'total_records': len(historical_data),
            'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'method': '特码生肖专项综合分析'
        }

        print(f"✅ 特码生肖专项分析完成")
        print(f"🎯 推荐生肖: {recommended_zodiacs}")
        print(f"📊 推荐号码: {sorted(recommended_numbers)}")
        print(f"🎪 置信度: {confidence:.1%}")

        return result

    def _calculate_comprehensive_scores(self, analyses: Dict[str, Any]) -> Dict[str, float]:
        """计算综合评分"""
        scores = {zodiac: 0.0 for zodiac in self.zodiac_names}

        # 权重配置
        weights = {
            'hot_cold': 0.25,      # 冷热度权重
            'distance': 0.30,      # 远近度权重
            'cycles': 0.25,        # 周期性权重
            'categories': 0.20     # 分类趋势权重
        }

        # 1. 冷热度评分（冷门生肖得分高）
        if 'hot_cold' in analyses:
            hot_cold = analyses['hot_cold']
            for zodiac in self.zodiac_names:
                zodiac_info = hot_cold['zodiac_analysis'].get(zodiac, {})
                heat_score = zodiac_info.get('heat_score', 1.0)

                # 冷门生肖（heat_score < 1）得分更高
                if heat_score < 0.8:
                    cold_score = (1.0 - heat_score) * 2  # 冷门加分
                elif heat_score > 1.2:
                    cold_score = 0.3  # 热门减分
                else:
                    cold_score = 0.6  # 正常分数

                scores[zodiac] += cold_score * weights['hot_cold']

        # 2. 远近度评分（远期生肖得分高）
        if 'distance' in analyses:
            distance = analyses['distance']
            for zodiac in self.zodiac_names:
                distance_info = distance['distance_analysis'].get(zodiac, {})
                urgency_score = distance_info.get('urgency_score', 0.5)
                current_distance = distance_info.get('current_distance', 0)

                # 距离越远，得分越高
                distance_score = min(1.0, current_distance / 15.0)  # 15天以上为满分
                scores[zodiac] += distance_score * weights['distance']

        # 3. 周期性评分
        if 'cycles' in analyses:
            cycles = analyses['cycles']
            for zodiac in self.zodiac_names:
                cycle_info = cycles['cycle_analysis'].get(zodiac, {})
                predicted_next = cycle_info.get('predicted_next_position', 0)
                stability = cycle_info.get('cycle_stability', 0)

                # 预测即将出现的生肖得分高
                current_position = len(analyses.get('hot_cold', {}).get('zodiac_analysis', {}))
                if predicted_next > 0:
                    cycle_score = max(0, 1.0 - abs(predicted_next - current_position) / 20.0)
                    cycle_score *= (0.5 + stability * 0.5)  # 稳定性加权
                else:
                    cycle_score = 0.3

                scores[zodiac] += cycle_score * weights['cycles']

        # 4. 分类趋势评分
        if 'categories' in analyses:
            categories = analyses['categories']
            for zodiac in self.zodiac_names:
                category_score = 0.5  # 基础分数

                # 检查各分类的趋势
                for category_name, category_data in categories['category_analysis'].items():
                    for sub_category, sub_data in category_data.items():
                        if zodiac in sub_data['zodiacs']:
                            trend_score = sub_data.get('trend_score', 1.0)
                            if trend_score < 0.8:  # 低频分类加分
                                category_score += 0.1
                            elif trend_score > 1.2:  # 高频分类减分
                                category_score -= 0.1

                scores[zodiac] += max(0, category_score) * weights['categories']

        # 归一化分数到0-1范围
        max_score = max(scores.values()) if scores.values() else 1.0
        if max_score > 0:
            scores = {zodiac: score / max_score for zodiac, score in scores.items()}

        return scores

    def _select_recommended_zodiacs(self, scores: Dict[str, float], count: int = 3) -> List[str]:
        """选择推荐的生肖"""
        # 按分数排序
        sorted_zodiacs = sorted(scores.items(), key=lambda x: x[1], reverse=True)

        # 选择前N个
        recommended = [zodiac for zodiac, score in sorted_zodiacs[:count]]

        return recommended

    def _calculate_confidence(self, scores: Dict[str, float], recommended: List[str]) -> float:
        """计算置信度"""
        if not recommended:
            return 0.0

        # 基于推荐生肖的平均分数
        avg_score = sum(scores.get(zodiac, 0) for zodiac in recommended) / len(recommended)

        # 基于分数差异
        all_scores = list(scores.values())
        score_std = (sum((s - avg_score) ** 2 for s in all_scores) / len(all_scores)) ** 0.5

        # 置信度计算
        confidence = avg_score * 0.7 + (1 - score_std) * 0.3

        return max(0.1, min(0.95, confidence))

    def _get_default_prediction(self) -> Dict[str, Any]:
        """获取默认预测结果"""
        default_zodiacs = ['龙', '虎', '马']  # 默认推荐
        default_numbers = []

        for zodiac in default_zodiacs:
            numbers = self.get_numbers_by_zodiac(zodiac)
            default_numbers.extend(numbers)

        return {
            'recommended_zodiacs': default_zodiacs,
            'recommended_numbers': sorted(default_numbers),
            'zodiac_scores': {zodiac: 0.5 for zodiac in self.zodiac_names},
            'confidence': 0.3,
            'analysis_details': {},
            'data_period': "数据不足",
            'total_records': 0,
            'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'method': '默认预测（数据不足）'
        }
