"""
模型管理系统主入口 - 统一管理所有模型相关功能
"""
import sys
sys.path.insert(0, 'src')

from typing import Dict, List, Any, Optional
import numpy as np
from datetime import datetime

# 导入各个子系统
from src.algorithm_layer.model_management.model_lifecycle.model_registry import ModelRegistry
from src.algorithm_layer.model_management.model_lifecycle.model_trainer import ModelTrainer
from src.algorithm_layer.model_management.model_lifecycle.model_validator import ModelValidator
from src.algorithm_layer.model_management.hyperparameter_optimization.bayesian_optimization import BayesianOptimizer
from src.algorithm_layer.model_management.model_ensemble.ensemble_builder import EnsembleBuilder
from src.algorithm_layer.model_management.automl_system.auto_feature_engineering import AutoFeatureEngineering

class ModelManagementSystem:
    def __init__(self):
        """初始化模型管理系统"""
        self.registry = ModelRegistry()
        self.trainer = ModelTrainer(self.registry)
        self.validator = ModelValidator()
        self.ensemble_builder = EnsembleBuilder()
        self.auto_feature_engineer = AutoFeatureEngineering()
        
        self.system_status = {
            'initialized_at': datetime.now(),
            'models_registered': 0,
            'training_sessions': 0,
            'validation_runs': 0,
            'optimization_runs': 0
        }
    
    def create_bayesian_optimizer(self, 
                                 model_class: Any,
                                 param_space: Dict,
                                 objective_metric: str = 'accuracy',
                                 n_trials: int = 100) -> BayesianOptimizer:
        """创建贝叶斯优化器"""
        return BayesianOptimizer(
            model_class=model_class,
            param_space=param_space,
            objective_metric=objective_metric,
            n_trials=n_trials
        )
    
    def auto_train_and_optimize(self,
                               model_class: Any,
                               param_space: Dict,
                               X_train: np.ndarray,
                               y_train: np.ndarray,
                               X_val: np.ndarray = None,
                               y_val: np.ndarray = None,
                               model_name: str = "auto_model",
                               optimize_hyperparams: bool = True,
                               auto_features: bool = True) -> Dict:
        """自动训练和优化模型"""
        
        results = {
            'model_name': model_name,
            'start_time': datetime.now(),
            'steps_completed': []
        }
        
        try:
            # 1. 自动特征工程
            if auto_features:
                print("🔧 执行自动特征工程...")
                feature_results = self.auto_feature_engineer.auto_feature_engineering_pipeline(
                    X_train, y_train,
                    n_features=min(50, X_train.shape[1] * 2)
                )
                X_train_processed = feature_results['X_final']
                
                if X_val is not None:
                    # 对验证集应用相同的特征工程（简化版）
                    X_val_processed = self.auto_feature_engineer.transform_features(X_val)
                else:
                    X_val_processed = None
                
                results['feature_engineering'] = feature_results['pipeline_summary']
                results['steps_completed'].append('feature_engineering')
            else:
                X_train_processed = X_train
                X_val_processed = X_val
            
            # 2. 超参数优化
            if optimize_hyperparams:
                print("🎯 执行超参数优化...")
                optimizer = self.create_bayesian_optimizer(
                    model_class=model_class,
                    param_space=param_space,
                    n_trials=50  # 减少试验次数以加快速度
                )
                
                opt_results = optimizer.optimize(X_train_processed, y_train)
                best_params = opt_results['best_params']
                
                results['hyperparameter_optimization'] = opt_results
                results['steps_completed'].append('hyperparameter_optimization')
                self.system_status['optimization_runs'] += 1
            else:
                # 使用默认参数
                best_params = {}
            
            # 3. 模型训练
            print("🚀 训练最优模型...")
            training_results = self.trainer.train_model(
                model_class=model_class,
                model_params=best_params,
                X_train=X_train_processed,
                y_train=y_train,
                X_val=X_val_processed,
                model_name=model_name,
                model_version="auto_v1.0"
            )
            
            trained_model = training_results['model']
            results['training'] = training_results['training_info']
            results['steps_completed'].append('training')
            self.system_status['training_sessions'] += 1
            
            # 4. 模型验证
            print("✅ 验证模型性能...")
            if X_val_processed is not None:
                validation_results = self.validator.comprehensive_validation(
                    model=trained_model,
                    X_train=X_train_processed,
                    y_train=y_train,
                    X_test=X_val_processed,
                    y_test=y_val if X_val is not None else y_train[-len(X_val_processed):]
                )
                
                results['validation'] = validation_results
                results['steps_completed'].append('validation')
                self.system_status['validation_runs'] += 1
            
            # 5. 更新系统状态
            self.system_status['models_registered'] += 1
            results['end_time'] = datetime.now()
            results['success'] = True
            results['final_model'] = trained_model
            
            print("🎊 自动训练和优化完成！")
            return results
            
        except Exception as e:
            results['end_time'] = datetime.now()
            results['success'] = False
            results['error'] = str(e)
            print(f"❌ 自动训练失败: {e}")
            return results
    
    def build_ensemble_models(self,
                            base_models: List[tuple],
                            X_train: np.ndarray,
                            y_train: np.ndarray,
                            ensemble_types: List[str] = None) -> Dict:
        """构建集成模型"""
        
        if ensemble_types is None:
            ensemble_types = ['voting', 'bagging', 'stacking']
        
        ensemble_results = {}
        
        # 构建不同类型的集成
        if 'voting' in ensemble_types:
            voting_ensemble = self.ensemble_builder.build_voting_ensemble(
                models=base_models,
                voting='soft'
            )
            voting_ensemble.fit(X_train, y_train)
            ensemble_results['voting'] = voting_ensemble
        
        if 'stacking' in ensemble_types:
            stacking_ensemble = self.ensemble_builder.build_stacking_ensemble(
                base_models=base_models,
                cv_folds=3
            )
            stacking_ensemble.fit(X_train, y_train)
            ensemble_results['stacking'] = stacking_ensemble
        
        # 比较集成性能
        comparison = self.ensemble_builder.compare_ensembles(X_train, y_train)
        
        return {
            'ensembles': ensemble_results,
            'comparison': comparison,
            'best_ensemble_type': comparison.get('best_ensemble'),
            'best_ensemble_score': comparison.get('best_score')
        }
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        # 更新模型注册数量
        registered_models = self.registry.list_models()
        self.system_status['models_registered'] = len(registered_models)
        
        return self.system_status
    
    def get_model_leaderboard(self, top_k: int = 10) -> List[Dict]:
        """获取模型排行榜"""
        models = self.registry.list_models()
        
        # 按性能排序（这里简化处理）
        leaderboard = []
        for model in models[:top_k]:
            leaderboard.append({
                'model_name': model['model_name'],
                'model_version': model['model_version'],
                'model_type': model['model_type'],
                'status': model['status'],
                'created_at': model['created_at']
            })
        
        return leaderboard
    
    def cleanup_old_models(self, keep_latest: int = 5):
        """清理旧模型"""
        models = self.registry.list_models()
        
        # 按模型名称分组
        model_groups = {}
        for model in models:
            name = model['model_name']
            if name not in model_groups:
                model_groups[name] = []
            model_groups[name].append(model)
        
        # 每个模型只保留最新的几个版本
        for name, versions in model_groups.items():
            if len(versions) > keep_latest:
                # 按创建时间排序
                versions.sort(key=lambda x: x['created_at'], reverse=True)
                
                # 标记旧版本为已废弃
                for old_version in versions[keep_latest:]:
                    self.registry.update_model_status(
                        old_version['model_name'],
                        old_version['model_version'],
                        'deprecated'
                    )
        
        print(f"模型清理完成，每个模型保留最新{keep_latest}个版本")


def main():
    """主函数 - 演示模型管理系统"""
    print("🤖 澳门六合彩预测系统 - 模型管理系统")
    print("=" * 60)
    
    # 初始化系统
    model_manager = ModelManagementSystem()
    
    # 显示系统状态
    status = model_manager.get_system_status()
    print(f"📊 系统状态:")
    print(f"   初始化时间: {status['initialized_at']}")
    print(f"   已注册模型: {status['models_registered']}")
    print(f"   训练会话: {status['training_sessions']}")
    print(f"   验证运行: {status['validation_runs']}")
    print(f"   优化运行: {status['optimization_runs']}")
    
    print("\n🎊 模型管理系统初始化完成！")
    print("系统包含以下功能模块:")
    print("   • 模型注册中心 - 版本管理和元数据存储")
    print("   • 模型训练器 - 自动化训练流程")
    print("   • 模型验证器 - 性能验证和稳定性测试")
    print("   • 超参数优化 - 贝叶斯优化")
    print("   • 模型集成 - 多种集成策略")
    print("   • 自动特征工程 - 特征生成和选择")

if __name__ == "__main__":
    main()
