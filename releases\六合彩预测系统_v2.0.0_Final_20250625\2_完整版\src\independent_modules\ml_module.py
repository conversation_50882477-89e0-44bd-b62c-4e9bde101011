"""
机器学习模组 - 升级版 v2.0
支持多种ML算法的集成预测，包含XGBoost和神经网络
"""
import sqlite3
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, RandomForestRegressor, GradientBoostingRegressor
from sklearn.svm import SVC, SVR
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier, MLPRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score
import warnings
warnings.filterwarnings('ignore')

# 尝试导入XGBoost
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("⚠️ XGBoost未安装，将使用RandomForest替代")

class MachineLearningModule:
    """机器学习模组 - 升级版 v2.0"""

    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.module_name = "机器学习模组 v2.0"

        # 升级的模型配置
        self.models = self._initialize_enhanced_models()

        # 特征配置
        self.feature_config = {
            'window_size': 10,
            'feature_count': 50,  # 升级到50维特征
            'enhanced_features': True,
            'version': 'v2.0'
        }

        # 初始化增强特征工程
        self.feature_engineer = self._initialize_feature_engineer()

        # 初始化标准化器
        self.scaler = StandardScaler()
        self.trained_models = {}

        print(f"🚀 {self.module_name}升级完成")
        print(f"📊 可用模型: {list(self.models.keys())}")
        print(f"🔬 XGBoost状态: {'✅ 可用' if XGBOOST_AVAILABLE else '❌ 不可用'}")

    def _initialize_enhanced_models(self) -> Dict[str, Dict]:
        """初始化增强模型配置"""
        models = {}

        # XGBoost - 最强性能 (使用回归模型)
        if XGBOOST_AVAILABLE:
            models['xgboost'] = {
                'model': xgb.XGBRegressor(
                    n_estimators=200,
                    max_depth=8,
                    learning_rate=0.1,
                    subsample=0.8,
                    colsample_bytree=0.8,
                    random_state=42,
                    n_jobs=-1
                ),
                'weight': 0.30
            }

        # 随机森林 - 稳定可靠 (回归模型)
        models['random_forest'] = {
            'model': RandomForestRegressor(
                n_estimators=150,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            ),
            'weight': 0.25 if XGBOOST_AVAILABLE else 0.35
        }

        # 梯度提升 - 高精度 (回归模型)
        models['gradient_boosting'] = {
            'model': GradientBoostingRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            ),
            'weight': 0.20
        }

        # 神经网络 - 复杂模式 (回归模型)
        models['neural_network'] = {
            'model': MLPRegressor(
                hidden_layer_sizes=(100, 50),
                activation='relu',
                solver='adam',
                alpha=0.001,
                max_iter=500,
                random_state=42
            ),
            'weight': 0.15
        }

        # SVM - 非线性回归
        models['svm'] = {
            'model': SVR(
                kernel='rbf',
                C=1.0,
                gamma='scale'
            ),
            'weight': 0.10
        }

        return models

    def _initialize_feature_engineer(self):
        """初始化特征工程器"""
        try:
            from ..enhanced_feature_engineering_v2 import EnhancedFeatureEngineering
            return EnhancedFeatureEngineering(self.db_path)
        except ImportError:
            print("⚠️ 增强特征工程模块不可用，使用基础特征")
            return None
        
        self.scaler = StandardScaler()
        self.trained_models = {}
        
        # 统一特征配置 - 修复维度不匹配问题
        self.feature_config = {
            'window_size': 10,
            'sequence_length': 8,
            'statistical_window': 15,
            'feature_count': 50,  # 统一为50维特征
            'enhanced_features': True,
            'use_zodiac_features': True,
            'use_time_features': True,
            'version': 'v2.0'
        }
    
    def predict(self, target_date: str, analysis_days: int = 100) -> Dict[str, Any]:
        """独立预测方法"""
        print(f"🤖 {self.module_name}开始预测 - 目标日期: {target_date}")
        
        # 获取历史数据
        historical_data = self._get_historical_data(target_date, analysis_days + 20)
        
        if len(historical_data) < 30:
            return self._get_default_result()
        
        # 特征工程
        features, targets = self._feature_engineering(historical_data[:analysis_days])
        
        if len(features) < 20:
            return self._get_default_result()
        
        # 训练模型
        self._train_models(features, targets)
        
        # 生成预测特征
        prediction_features = self._generate_prediction_features(historical_data, target_date)
        
        # 执行预测
        predictions = self._ensemble_predict(prediction_features)
        
        # 选择推荐号码
        recommended_numbers = self._select_numbers_by_probability(predictions)
        
        # 计算置信度
        confidence = self._calculate_enhanced_confidence(predictions, recommended_numbers)

        result = {
            "predicted_numbers": recommended_numbers,
            "confidence": confidence,
            "number_probabilities": predictions,
            "model_performance": self._get_model_performance(),
            "algorithm_info": {
                "version": "v2.0",
                "xgboost_enabled": XGBOOST_AVAILABLE,
                "total_models": len(self.models),
                "trained_models": len(self.trained_models)
            },
            "metadata": {
                "analysis_days": analysis_days,
                "training_samples": len(features),
                "models_used": list(self.trained_models.keys()),
                "prediction_time": datetime.now().isoformat()
            }
        }
        
        print(f"✅ {self.module_name}预测完成 - 推荐{len(recommended_numbers)}个号码")
        return result
    
    def predict_historical(self, target_date: str, window_size: int = 30) -> Dict[str, Any]:
        """历史预测方法（用于回测）"""
        end_date = datetime.strptime(target_date, "%Y-%m-%d") - timedelta(days=1)
        end_date_str = end_date.strftime("%Y-%m-%d")
        
        return self.predict(end_date_str, window_size)
    
    def run_backtest(self, start_date: str, end_date: str, window_size: int = 30) -> Dict[str, Any]:
        """运行回测"""
        print(f"🔄 {self.module_name}开始回测: {start_date} 到 {end_date}")
        
        test_dates = self._get_test_dates(start_date, end_date)
        results = []
        
        for test_date in test_dates:
            try:
                actual_number = self._get_actual_number(test_date)
                if actual_number is None:
                    continue
                
                prediction = self.predict_historical(test_date, window_size)
                predicted_numbers = prediction["predicted_numbers"]
                
                hit = actual_number in predicted_numbers
                hit_rank = predicted_numbers.index(actual_number) + 1 if hit else None
                
                result = {
                    "test_date": test_date,
                    "actual_number": actual_number,
                    "predicted_numbers": predicted_numbers,
                    "hit": hit,
                    "hit_rank": hit_rank,
                    "confidence": prediction["confidence"],
                    "model_performance": prediction["model_performance"]
                }
                
                results.append(result)
                print(f"  {test_date}: 实际={actual_number}, 命中={'✅' if hit else '❌'}")
                
            except Exception as e:
                print(f"  {test_date}: 回测失败 - {e}")
        
        hit_count = sum(1 for r in results if r["hit"])
        hit_rate = hit_count / len(results) if results else 0
        
        print(f"✅ {self.module_name}回测完成: {hit_count}/{len(results)} = {hit_rate:.1%}")
        
        return {
            "results": results,
            "statistics": {
                "total_tests": len(results),
                "hit_count": hit_count,
                "hit_rate": hit_rate,
                "avg_confidence": np.mean([r["confidence"] for r in results]) if results else 0
            }
        }
    
    def _get_historical_data(self, before_date: str, days: int) -> List[Dict[str, Any]]:
        """获取历史数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT draw_date, special_number, period_number
            FROM lottery_results 
            WHERE draw_date < ? 
            ORDER BY draw_date DESC 
            LIMIT ?
        ''', (before_date, days))
        
        data = []
        for row in cursor.fetchall():
            data.append({
                "draw_date": row[0],
                "special_number": row[1],
                "period_number": row[2]
            })
        
        conn.close()
        return data
    
    def _feature_engineering(self, historical_data: List[Dict]) -> tuple:
        """特征工程 - 升级版 v2.0"""
        features = []
        targets = []

        seq_len = self.feature_config.get('sequence_length', self.feature_config['window_size'])

        # 使用增强特征工程
        if self.feature_engineer:
            print("🔧 使用增强特征工程 v2.0...")

            for i in range(seq_len, len(historical_data)):
                current_record = historical_data[i]
                history_sequence = historical_data[i-seq_len:i]

                # 使用增强特征工程生成特征
                feature_result = self.feature_engineer.generate_enhanced_features(
                    history_sequence, current_record.get('draw_date', '')
                )

                if feature_result and 'features' in feature_result:
                    feature_dict = feature_result['features']
                    feature_vector = np.array(list(feature_dict.values()))

                    if len(feature_vector) > 0 and np.all(np.isfinite(feature_vector)):
                        features.append(feature_vector)
                        targets.append(current_record['special_number'])
        else:
            # 使用基础特征工程
            print("🔧 使用基础特征工程...")
            for i in range(seq_len, len(historical_data)):
                current_record = historical_data[i]
                history_sequence = historical_data[i-seq_len:i]

                feature_vector = self._generate_feature_vector(current_record, history_sequence)

                if feature_vector is not None:
                    features.append(feature_vector)
                    targets.append(current_record['special_number'])

        if not features:
            return np.array([]), np.array([])

        features_array = np.array(features)
        targets_array = np.array(targets)

        # 标准化特征
        features_scaled = self.scaler.fit_transform(features_array)

        print(f"✅ 特征工程完成: {features_array.shape[0]}样本 x {features_array.shape[1]}特征")

        return features_scaled, targets_array
    
    def _generate_feature_vector(self, current_record: Dict, history_sequence: List[Dict]) -> Optional[np.ndarray]:
        """生成特征向量"""
        features = []
        
        try:
            # 序列特征
            recent_numbers = [record['special_number'] for record in history_sequence]
            
            # 统计特征
            features.append(np.mean(recent_numbers))
            features.append(np.std(recent_numbers))
            features.append(np.min(recent_numbers))
            features.append(np.max(recent_numbers))
            features.append(np.median(recent_numbers))
            
            # 趋势特征
            if len(recent_numbers) >= 3:
                x = np.arange(len(recent_numbers))
                trend = np.polyfit(x, recent_numbers, 1)[0]
                features.append(trend)
            else:
                features.append(0)
            
            # 差分特征
            if len(recent_numbers) >= 2:
                diffs = np.diff(recent_numbers)
                features.append(np.mean(diffs))
                features.append(np.std(diffs))
            else:
                features.extend([0, 0])
            
            # 频率特征
            from collections import Counter
            number_counts = Counter(recent_numbers)
            max_freq = max(number_counts.values()) if number_counts else 0
            features.append(max_freq / len(recent_numbers) if recent_numbers else 0)
            
            # 大小单双特征
            big_count = sum(1 for num in recent_numbers if num > 24)
            odd_count = sum(1 for num in recent_numbers if num % 2 == 1)
            features.append(big_count / len(recent_numbers) if recent_numbers else 0)
            features.append(odd_count / len(recent_numbers) if recent_numbers else 0)
            
            # 生肖特征
            if self.feature_config['use_zodiac_features']:
                zodiac_features = self._extract_zodiac_features(recent_numbers)
                features.extend(zodiac_features)
            
            # 时间特征
            if self.feature_config['use_time_features']:
                time_features = self._extract_time_features(current_record)
                features.extend(time_features)
            
            return np.array(features, dtype=float)
            
        except Exception as e:
            print(f"特征生成失败: {e}")
            return None
    
    def _extract_zodiac_features(self, recent_numbers: List[int]) -> List[float]:
        """提取生肖特征"""
        features = []
        
        # 生肖映射
        zodiacs = ["蛇", "马", "羊", "猴", "鸡", "狗", "猪", "鼠", "牛", "虎", "兔", "龙"]
        zodiac_counts = {zodiac: 0 for zodiac in zodiacs}
        
        for number in recent_numbers:
            zodiac = zodiacs[(number - 1) % 12]
            zodiac_counts[zodiac] += 1
        
        total = len(recent_numbers)
        for zodiac in zodiacs:
            features.append(zodiac_counts[zodiac] / total if total > 0 else 0)
        
        return features
    
    def _extract_time_features(self, current_record: Dict) -> List[float]:
        """提取时间特征"""
        features = []
        
        try:
            draw_date = datetime.strptime(current_record['draw_date'], '%Y-%m-%d')
            
            features.append(draw_date.year % 100)
            features.append(draw_date.month)
            features.append(draw_date.day)
            features.append(draw_date.weekday())
            
            quarter = (draw_date.month - 1) // 3 + 1
            features.append(quarter)
            
        except:
            features.extend([0, 0, 0, 0, 0])
        
        return features
    
    def _train_models(self, features: np.ndarray, targets: np.ndarray):
        """训练模型"""
        self.trained_models = {}

        # 修复XGBoost类别标签问题 - 使用回归而不是分类
        # 标准化特征
        features_scaled = self.scaler.fit_transform(features)

        for model_name, model_config in self.models.items():
            try:
                model = model_config['model']

                # 所有模型都使用原始标签（数值）
                model.fit(features_scaled, targets)

                # 使用回归评分而不是分类评分
                if model_name == 'xgboost':
                    # XGBoost使用回归评分
                    cv_scores = cross_val_score(model, features_scaled, targets, cv=3, scoring='neg_mean_squared_error')
                    avg_score = -np.mean(cv_scores)  # 转换为正值
                    avg_score = max(0, 1 - avg_score/100)  # 转换为0-1范围的准确率
                else:
                    # 其他模型也使用回归评分
                    cv_scores = cross_val_score(model, features_scaled, targets, cv=3, scoring='neg_mean_squared_error')
                    avg_score = -np.mean(cv_scores)
                    avg_score = max(0, 1 - avg_score/100)

                self.trained_models[model_name] = {
                    'model': model,
                    'cv_score': avg_score,
                    'weight': model_config['weight']
                }

            except Exception as e:
                print(f"模型 {model_name} 训练失败: {e}")
    
    def _generate_prediction_features(self, historical_data: List[Dict], target_date: str) -> np.ndarray:
        """生成预测特征"""
        seq_len = self.feature_config.get('sequence_length', self.feature_config['window_size'])
        recent_sequence = historical_data[:seq_len]
        
        current_record = {
            'draw_date': target_date,
            'special_number': 0,
            'period_number': ''
        }
        
        feature_vector = self._generate_feature_vector(current_record, recent_sequence)
        
        if feature_vector is not None:
            # 确保特征维度正确
            expected_features = self.feature_config.get('feature_count', 50)
            if len(feature_vector) != expected_features:
                # 调整特征维度
                if len(feature_vector) < expected_features:
                    # 补零
                    feature_vector = np.pad(feature_vector, (0, expected_features - len(feature_vector)), 'constant')
                else:
                    # 截断
                    feature_vector = feature_vector[:expected_features]

            feature_scaled = self.scaler.transform(feature_vector.reshape(1, -1))
            return feature_scaled[0]
        else:
            expected_features = self.feature_config.get('feature_count', 50)
            return np.zeros(expected_features)
    
    def _ensemble_predict(self, features: np.ndarray) -> Dict[int, float]:
        """集成预测"""
        if not self.trained_models:
            return {i: 1/49 for i in range(1, 50)}
        
        all_predictions = {}
        total_weight = 0
        
        for model_name, model_info in self.trained_models.items():
            try:
                model = model_info['model']
                weight = model_info['weight']
                
                if hasattr(model, 'predict_proba'):
                    proba = model.predict_proba(features.reshape(1, -1))[0]
                    classes = model.classes_
                else:
                    prediction = model.predict(features.reshape(1, -1))[0]
                    proba = np.zeros(49)
                    if 1 <= prediction <= 49:
                        proba[prediction - 1] = 1.0
                    classes = list(range(1, 50))
                
                for i, class_label in enumerate(classes):
                    if 1 <= class_label <= 49:
                        if class_label not in all_predictions:
                            all_predictions[class_label] = 0
                        all_predictions[class_label] += proba[i] * weight
                
                total_weight += weight
                
            except Exception as e:
                print(f"模型 {model_name} 预测失败: {e}")
        
        # 归一化
        if total_weight > 0:
            for number in all_predictions:
                all_predictions[number] /= total_weight
        
        # 确保所有号码都有概率
        final_predictions = {}
        for number in range(1, 50):
            final_predictions[number] = all_predictions.get(number, 0.001)
        
        # 归一化到概率和为1
        total_prob = sum(final_predictions.values())
        if total_prob > 0:
            for number in final_predictions:
                final_predictions[number] /= total_prob
        
        return final_predictions
    
    def _select_numbers_by_probability(self, predictions: Dict[int, float]) -> List[int]:
        """根据概率选择号码"""
        sorted_numbers = sorted(predictions.items(), key=lambda x: x[1], reverse=True)
        
        # 选择前16个号码
        selected = [num for num, prob in sorted_numbers[:16]]
        
        return sorted(selected)
    
    def _calculate_enhanced_confidence(self, predictions: Dict[int, float],
                                     recommended_numbers: List[int]) -> float:
        """计算增强置信度"""
        if not recommended_numbers:
            return 0.0

        # 基础置信度计算
        recommended_probs = [predictions[num] for num in recommended_numbers]
        avg_recommended_prob = np.mean(recommended_probs)
        avg_all_prob = np.mean(list(predictions.values()))

        base_confidence = avg_recommended_prob / avg_all_prob if avg_all_prob > 0 else 1.0

        # 模型一致性加权
        model_consistency = self._calculate_model_consistency()

        # XGBoost加成
        xgb_bonus = 0.1 if XGBOOST_AVAILABLE and 'xgboost' in self.trained_models else 0.0

        # 训练样本数量影响
        sample_factor = min(1.0, len(self.trained_models) / len(self.models))

        # 综合置信度
        enhanced_confidence = base_confidence * (1 + model_consistency + xgb_bonus) * sample_factor

        # 限制在合理范围内
        return min(0.95, max(0.6, enhanced_confidence))

    def _calculate_model_consistency(self) -> float:
        """计算模型一致性"""
        if len(self.trained_models) < 2:
            return 0.0

        cv_scores = [info.get('cv_score', 0.0) for info in self.trained_models.values()]
        if not cv_scores:
            return 0.0

        # 计算CV分数的一致性（标准差越小越一致）
        consistency = 1.0 - (np.std(cv_scores) / np.mean(cv_scores)) if np.mean(cv_scores) > 0 else 0.0
        return max(0.0, min(0.2, consistency))  # 限制在0-0.2范围内
    
    def _get_model_performance(self) -> Dict[str, float]:
        """获取模型性能"""
        performance = {}
        for model_name, model_info in self.trained_models.items():
            performance[model_name] = model_info.get('cv_score', 0.0)
        return performance
    
    def _get_test_dates(self, start_date: str, end_date: str) -> List[str]:
        """获取测试日期"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT draw_date FROM lottery_results 
            WHERE draw_date BETWEEN ? AND ? 
            ORDER BY draw_date
        ''', (start_date, end_date))
        
        dates = [row[0] for row in cursor.fetchall()]
        conn.close()
        return dates
    
    def _get_actual_number(self, test_date: str) -> Optional[int]:
        """获取真实开奖号码"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT special_number FROM lottery_results 
            WHERE draw_date = ?
        ''', (test_date,))
        
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None
    
    def _get_default_result(self) -> Dict[str, Any]:
        """获取默认结果"""
        return {
            "predicted_numbers": list(range(1, 17)),
            "confidence": 0.4,
            "number_probabilities": {i: 1/49 for i in range(1, 50)},
            "model_performance": {},
            "metadata": {"note": "数据不足，使用默认预测"}
        }
