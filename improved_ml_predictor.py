import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.model_selection import train_test_split, StratifiedKFold
from collections import Counter
import warnings
warnings.filterwarnings("ignore")

class ImprovedMLPredictor:
    def __init__(self):
        self.models = {
            "random_forest": {
                "model": RandomForestClassifier(n_estimators=50, max_depth=8, random_state=42),
                "weight": 0.25
            },
            "gradient_boosting": {
                "model": GradientBoostingClassifier(n_estimators=50, max_depth=4, random_state=42),
                "weight": 0.25
            },
            "svm": {
                "model": SVC(kernel="rbf", probability=True, random_state=42),
                "weight": 0.20
            },
            "knn": {
                "model": KNeighborsClassifier(n_neighbors=3),
                "weight": 0.15
            },
            "naive_bayes": {
                "model": GaussianNB(),
                "weight": 0.15
            }
        }
        
        self.trained_models = {}
        self.training_success_rate = 0.0
    
    def smart_cross_validate(self, model, X, y):
        """智能交叉验证"""
        class_counts = Counter(y)
        min_class_size = min(class_counts.values())
        total_samples = len(X)
        
        try:
            if total_samples < 30:
                # 小数据集：使用留出验证
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=0.3, random_state=42
                )
                model.fit(X_train, y_train)
                score = model.score(X_test, y_test)
                return {"success": True, "score": score, "method": "holdout"}
                
            elif min_class_size < 2:
                # 类别样本太少：简单训练验证
                model.fit(X, y)
                score = model.score(X, y)
                return {"success": True, "score": score, "method": "simple"}
                
            else:
                # 正常情况：分层交叉验证
                cv_splits = min(3, min_class_size)
                cv = StratifiedKFold(n_splits=cv_splits, shuffle=True, random_state=42)
                
                # 手动实现交叉验证以避免错误
                scores = []
                for train_idx, test_idx in cv.split(X, y):
                    X_train_cv, X_test_cv = X[train_idx], X[test_idx]
                    y_train_cv, y_test_cv = y[train_idx], y[test_idx]
                    
                    model.fit(X_train_cv, y_train_cv)
                    score = model.score(X_test_cv, y_test_cv)
                    scores.append(score)
                
                mean_score = np.mean(scores)
                return {"success": True, "score": mean_score, "method": "stratified_cv"}
                
        except Exception as e:
            # 降级策略
            try:
                model.fit(X, y)
                score = model.score(X, y)
                return {"success": True, "score": score, "method": "fallback"}
            except:
                return {"success": False, "error": str(e)}
    
    def augment_small_dataset(self, X, y, target_size=80):
        """小数据集增强"""
        if len(X) >= target_size:
            return X, y
        
        current_size = len(X)
        needed_samples = target_size - current_size
        
        # 噪声注入增强
        feature_std = np.std(X, axis=0)
        noise_scale = 0.05  # 较小的噪声
        
        indices = np.random.choice(current_size, needed_samples, replace=True)
        X_noise = X[indices].copy()
        y_noise = y[indices].copy()
        
        noise = np.random.normal(0, noise_scale * feature_std, X_noise.shape)
        X_noise += noise
        
        X_augmented = np.vstack([X, X_noise])
        y_augmented = np.hstack([y, y_noise])
        
        return X_augmented, y_augmented
    
    def train_models(self, X, y):
        """改进的模型训练"""
        print(f"🤖 开始训练ML模型 (数据量: {len(X)})")
        
        # 数据增强（如果需要）
        if len(X) < 50:
            X, y = self.augment_small_dataset(X, y, target_size=80)
            print(f"   📈 数据增强后: {len(X)}样本")
        
        successful_models = 0
        total_models = len(self.models)
        
        for model_name, config in self.models.items():
            try:
                model = config["model"]
                
                # 智能交叉验证
                result = self.smart_cross_validate(model, X, y)
                
                if result["success"]:
                    # 在全部数据上重新训练
                    model.fit(X, y)
                    self.trained_models[model_name] = {
                        "model": model,
                        "weight": config["weight"],
                        "cv_score": result["score"],
                        "cv_method": result["method"]
                    }
                    successful_models += 1
                    print(f"   ✅ {model_name}: 成功 (得分={result['score']:.3f}, 方法={result['method']})")
                else:
                    print(f"   ❌ {model_name}: 失败 - {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"   ❌ {model_name}: 异常 - {str(e)[:50]}...")
        
        self.training_success_rate = successful_models / total_models
        print(f"   📊 训练成功率: {self.training_success_rate:.1%} ({successful_models}/{total_models})")
        
        return len(self.trained_models) > 0
    
    def ensemble_predict(self, X_pred):
        """集成预测"""
        if not self.trained_models:
            print("⚠️ 没有训练好的模型，使用默认预测")
            return np.ones(49) / 49  # 均匀概率
        
        # 收集所有模型的预测概率
        all_probabilities = []
        total_weight = 0
        
        for model_name, model_info in self.trained_models.items():
            try:
                model = model_info["model"]
                weight = model_info["weight"]
                
                if hasattr(model, "predict_proba"):
                    # 获取概率预测
                    proba = model.predict_proba(X_pred.reshape(1, -1))[0]
                    
                    # 确保概率对应1-49的号码
                    if len(proba) < 49:
                        # 扩展到49个号码
                        full_proba = np.zeros(49)
                        classes = model.classes_
                        for i, cls in enumerate(classes):
                            if 1 <= cls <= 49:
                                full_proba[cls-1] = proba[i]
                        proba = full_proba
                    
                    all_probabilities.append(proba * weight)
                    total_weight += weight
                    
                else:
                    # 对于没有概率预测的模型，使用硬预测
                    pred = model.predict(X_pred.reshape(1, -1))[0]
                    proba = np.zeros(49)
                    if 1 <= pred <= 49:
                        proba[pred-1] = 1.0
                    all_probabilities.append(proba * weight)
                    total_weight += weight
                    
            except Exception as e:
                print(f"⚠️ {model_name}预测失败: {str(e)[:30]}...")
                continue
        
        if not all_probabilities:
            return np.ones(49) / 49
        
        # 加权平均
        ensemble_proba = np.sum(all_probabilities, axis=0) / total_weight
        
        # 归一化
        if ensemble_proba.sum() > 0:
            ensemble_proba = ensemble_proba / ensemble_proba.sum()
        else:
            ensemble_proba = np.ones(49) / 49
        
        return ensemble_proba
    
    def predict_numbers(self, X_pred, n_predictions=16):
        """预测号码"""
        probabilities = self.ensemble_predict(X_pred)
        
        # 按概率排序选择号码
        sorted_indices = np.argsort(probabilities)[::-1]
        predicted_numbers = [i + 1 for i in sorted_indices[:n_predictions]]
        predicted_probabilities = [probabilities[i] for i in sorted_indices[:n_predictions]]
        
        return {
            "predicted_numbers": predicted_numbers,
            "probabilities": predicted_probabilities,
            "confidence": np.max(predicted_probabilities) * 100,
            "training_success_rate": self.training_success_rate,
            "n_trained_models": len(self.trained_models)
        }

# 测试改进的ML模块
print("🧪 测试改进的机器学习模块")
print("=" * 60)

# 创建测试数据
np.random.seed(42)
X_train = np.random.randn(60, 12)
y_train = np.random.randint(1, 50, 60)
X_pred = np.random.randn(12)

# 测试改进的ML预测器
ml_predictor = ImprovedMLPredictor()

# 训练模型
training_success = ml_predictor.train_models(X_train, y_train)
print(f"\n📊 训练结果: {'成功' if training_success else '失败'}")

if training_success:
    # 执行预测
    result = ml_predictor.predict_numbers(X_pred, n_predictions=16)
    
    print(f"\n🎯 预测结果:")
    print(f"   推荐号码: {result['predicted_numbers']}")
    print(f"   置信度: {result['confidence']:.1f}%")
    print(f"   训练成功率: {result['training_success_rate']:.1%}")
    print(f"   可用模型数: {result['n_trained_models']}")
    
    print(f"\n📈 前5个号码的概率:")
    for i in range(5):
        num = result["predicted_numbers"][i]
        prob = result["probabilities"][i]
        print(f"   {i+1}. 号码 {num}: {prob:.4f}")

print("\n✅ 改进的机器学习模块测试完成!")
