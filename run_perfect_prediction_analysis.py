import sys
import os
sys.path.append('src')

def run_and_analyze_perfect_prediction():
    """运行并分析完美预测的详细过程"""
    print("🎯 运行完美预测详细分析")
    print("=" * 50)
    
    try:
        from perfect_prediction_system import PerfectPredictionSystem
        
        # 创建并初始化系统
        system = PerfectPredictionSystem()
        system.initialize_modules()
        
        print("✅ 完美预测系统初始化完成")
        
        # 运行完美预测
        print(f"\n🚀 开始运行完美预测...")
        target_date = "2025-06-23"
        
        result = system.run_complete_prediction(target_date)
        
        print(f"✅ 完美预测执行完成")
        
        # 详细分析预测结果
        print(f"\n📊 完美预测结果详细分析:")
        print(f"   结果类型: {type(result)}")
        
        if isinstance(result, dict):
            print(f"   结果键值: {list(result.keys())}")
            
            # 分析各个部分
            print(f"\n📋 结果结构分析:")
            
            # 1. 预测ID和基本信息
            prediction_id = result.get('prediction_id', 'N/A')
            target_date_result = result.get('target_date', 'N/A')
            print(f"   🆔 预测ID: {prediction_id}")
            print(f"   📅 目标日期: {target_date_result}")
            
            # 2. 模组预测结果
            module_predictions = result.get('module_predictions', {})
            if module_predictions:
                print(f"\n🔧 各模组预测结果:")
                for module_name, prediction in module_predictions.items():
                    if isinstance(prediction, dict):
                        numbers = prediction.get('predicted_numbers', [])
                        confidence = prediction.get('confidence', 0)
                        method = prediction.get('method', 'unknown')
                        print(f"      {module_name}:")
                        print(f"         📊 号码数量: {len(numbers)}")
                        print(f"         📈 置信度: {confidence}")
                        print(f"         🔧 方法: {method}")
                        if len(numbers) <= 16:
                            print(f"         🎯 号码: {numbers}")
            else:
                print(f"   ❌ 未找到模组预测结果")
            
            # 3. 最终结果
            final_results = result.get('final_results', {})
            if final_results:
                print(f"\n🎯 最终预测结果:")
                
                # 推荐号码
                recommended_numbers = final_results.get('recommended_16_numbers', [])
                print(f"   📊 推荐号码 ({len(recommended_numbers)}个): {recommended_numbers}")
                
                # 推荐生肖
                recommended_zodiacs = final_results.get('recommended_4_zodiacs', [])
                print(f"   🐲 推荐生肖 ({len(recommended_zodiacs)}个): {recommended_zodiacs}")
                
                # 置信度指标
                overall_confidence = final_results.get('overall_confidence', 0)
                prediction_stability = final_results.get('prediction_stability', 0)
                diversity_score = final_results.get('diversity_score', 0)
                
                print(f"   �� 整体置信度: {overall_confidence}")
                print(f"   🔄 预测稳定性: {prediction_stability}")
                print(f"   🎲 多样性得分: {diversity_score}")
            else:
                print(f"   ❌ 未找到最终结果")
            
            # 4. 执行统计
            execution_stats = result.get('execution_stats', {})
            if execution_stats:
                print(f"\n⏱️ 执行统计:")
                for key, value in execution_stats.items():
                    print(f"   {key}: {value}")
            
            # 5. 检查是否使用了增强回测配置
            print(f"\n�� 检查是否使用了增强回测配置:")
            
            # 查找配置相关信息
            config_used = result.get('config_used', {})
            optimal_config = result.get('optimal_config', {})
            enhanced_config = result.get('enhanced_config', {})
            
            if config_used:
                print(f"   ✅ 使用了配置: {config_used}")
            elif optimal_config:
                print(f"   ✅ 使用了最优配置: {optimal_config}")
            elif enhanced_config:
                print(f"   ✅ 使用了增强配置: {enhanced_config}")
            else:
                print(f"   ❌ 未发现增强回测配置的使用")
            
            # 6. 分析预测质量
            print(f"\n📈 预测质量分析:")
            
            if final_results:
                # 号码分布分析
                numbers = final_results.get('recommended_16_numbers', [])
                if numbers:
                    print(f"   📊 号码范围: {min(numbers)} - {max(numbers)}")
                    print(f"   📊 号码分布: 奇数{sum(1 for n in numbers if n%2==1)}个, 偶数{sum(1 for n in numbers if n%2==0)}个")
                    print(f"   📊 大小分布: 大数{sum(1 for n in numbers if n>24)}个, 小数{sum(1 for n in numbers if n<=24)}个")
                
                # 生肖分析
                zodiacs = final_results.get('recommended_4_zodiacs', [])
                if zodiacs:
                    print(f"   🐲 生肖多样性: {len(set(zodiacs))}个不同生肖")
                
                # 置信度分析
                confidence = final_results.get('overall_confidence', 0)
                if confidence > 0.7:
                    print(f"   📈 置信度评级: 高 ({confidence})")
                elif confidence > 0.5:
                    print(f"   📈 置信度评级: 中等 ({confidence})")
                else:
                    print(f"   📈 置信度评级: 低 ({confidence})")
        
        return result
        
    except Exception as e:
        print(f"❌ 运行分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = run_and_analyze_perfect_prediction()
