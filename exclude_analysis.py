import re
import os

def search_exclude_filter():
    """搜索排除筛选相关代码"""
    print("�� 排除筛选代码分析")
    print("=" * 50)
    
    # 要搜索的文件
    files_to_search = [
        "src/perfect_prediction_system.py",
        "src/fusion_manager.py", 
        "hit_rate_feedback_system.py",
        "adaptive_optimizer.py"
    ]
    
    # 搜索关键词
    keywords = [
        "排除筛选", "exclude", "exclusion", "filter",
        "执行排除", "exclude_filter", "exclusion_filter"
    ]
    
    for filename in files_to_search:
        if os.path.exists(filename):
            print(f"\n🔍 搜索文件: {filename}")
            
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            found_features = []
            
            for i, line in enumerate(lines):
                for keyword in keywords:
                    if keyword in line.lower():
                        found_features.append({
                            'line_num': i + 1,
                            'keyword': keyword,
                            'content': line.strip()
                        })
            
            if found_features:
                print(f"   ✅ 找到 {len(found_features)} 个相关特性:")
                
                # 按关键词分组显示
                for feature in found_features[:10]:  # 显示前10个
                    line_num = feature['line_num']
                    keyword = feature['keyword']
                    content = feature['content'][:80]
                    print(f"      L{line_num} ({keyword}): {content}...")
            else:
                print(f"   ❌ 未找到排除筛选相关代码")
        else:
            print(f"\n❌ 文件不存在: {filename}")

def search_filter_methods():
    """搜索筛选方法"""
    print(f"\n🔍 搜索筛选方法")
    print("=" * 30)
    
    files_to_search = [
        "src/perfect_prediction_system.py",
        "hit_rate_feedback_system.py"
    ]
    
    filter_patterns = [
        r"def.*筛选.*\(",
        r"def.*filter.*\(",
        r"def.*exclude.*\(",
        r"def.*智能筛选.*\(",
        r"def.*exclusion.*\("
    ]
    
    for filename in files_to_search:
        if os.path.exists(filename):
            print(f"\n🔍 检查文件: {filename}")
            
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            found_methods = []
            
            for pattern in filter_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    # 找到匹配的行号
                    line_num = content[:match.start()].count('\n') + 1
                    line_content = content.split('\n')[line_num - 1].strip()
                    
                    found_methods.append({
                        'pattern': pattern,
                        'line_num': line_num,
                        'content': line_content
                    })
            
            if found_methods:
                print(f"   ✅ 找到 {len(found_methods)} 个筛选方法:")
                for method in found_methods:
                    line_num = method['line_num']
                    content = method['content'][:60]
                    print(f"      L{line_num}: {content}...")
            else:
                print(f"   ❌ 未找到筛选方法")

if __name__ == "__main__":
    search_exclude_filter()
    search_filter_methods()
