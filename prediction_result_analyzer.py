"""
预测结果分析工具
分析GUI和终端显示不同结果的原因
"""

def analyze_prediction_results():
    """分析预测结果差异"""
    print("🔍 澳门六合彩预测结果差异分析工具")
    print("=" * 80)
    
    # 用户报告的结果
    gui_numbers = [1, 8, 7, 6, 4, 13, 3, 2, 14, 37, 28, 9, 5, 29, 12, 10]
    terminal_numbers = [6, 7, 8, 18, 19, 20, 30, 31, 32, 42, 43, 44]
    
    print("📊 用户报告的结果:")
    print(f"  GUI界面: {gui_numbers} ({len(gui_numbers)}个)")
    print(f"  终端界面: {terminal_numbers} ({len(terminal_numbers)}个)")
    
    # 分析重叠
    overlap = set(gui_numbers) & set(terminal_numbers)
    print(f"\n🔍 号码重叠分析:")
    print(f"  重叠号码: {sorted(list(overlap))} ({len(overlap)}个)")
    print(f"  重叠率: {len(overlap)/len(gui_numbers)*100:.1f}% (基于GUI)")
    print(f"  重叠率: {len(overlap)/len(terminal_numbers)*100:.1f}% (基于终端)")
    
    # 运行实际预测系统
    print("\n🚀 运行实际预测系统验证:")
    print("=" * 50)
    
    # 1. 完美预测系统
    print("🎊 完美预测系统:")
    try:
        from src.perfect_prediction_system import PerfectPredictionSystem
        system = PerfectPredictionSystem()
        system.initialize_modules()
        result = system.run_complete_prediction('2025-06-25')
        
        perfect_numbers = result['final_results']['recommended_16_numbers']
        perfect_zodiacs = result['final_results']['recommended_4_zodiacs']
        
        print(f"  推荐号码: {perfect_numbers}")
        print(f"  推荐生肖: {perfect_zodiacs}")
        print(f"  与GUI匹配: {'✅' if perfect_numbers == gui_numbers else '❌'}")
        
    except Exception as e:
        print(f"  ❌ 失败: {e}")
    
    # 2. 特码生肖专项分析器
    print("\n⭐ 特码生肖专项分析器:")
    try:
        from special_zodiac_analyzer import SpecialZodiacAnalyzer
        analyzer = SpecialZodiacAnalyzer()
        result = analyzer.comprehensive_special_zodiac_prediction(days=100)
        
        special_numbers = result['recommended_numbers']
        special_zodiacs = result['recommended_zodiacs']
        
        print(f"  推荐号码: {special_numbers}")
        print(f"  推荐生肖: {special_zodiacs}")
        print(f"  与终端匹配: {'✅' if special_numbers == terminal_numbers else '❌'}")
        
    except Exception as e:
        print(f"  ❌ 失败: {e}")
    
    # 3. 特码预测器
    print("\n🎯 特码预测器:")
    try:
        from src.special_number_predictor import SpecialNumberPredictor
        predictor = SpecialNumberPredictor()
        result = predictor.predict('2025-06-25')
        
        predictor_numbers = result['predicted_numbers']
        
        print(f"  推荐号码: {predictor_numbers}")
        print(f"  与GUI匹配: {'✅' if predictor_numbers == gui_numbers else '❌'}")
        print(f"  与终端匹配: {'✅' if predictor_numbers == terminal_numbers else '❌'}")
        
    except Exception as e:
        print(f"  ❌ 失败: {e}")
    
    # 分析结论
    print("\n🎯 分析结论:")
    print("=" * 50)
    print("1. GUI显示的16个号码来自完美预测系统的final_results")
    print("2. 终端显示的12个号码来自特码生肖专项分析器")
    print("3. 这两个系统在GUI运行完美预测时都会被调用")
    print("4. GUI界面只显示完美预测系统的结果")
    print("5. 特码生肖专项分析器的结果只在终端打印")
    
    print("\n💡 解决建议:")
    print("=" * 50)
    print("1. 在GUI中添加特码生肖专项分析结果的显示")
    print("2. 明确标识不同预测系统的结果")
    print("3. 提供选项让用户选择查看哪个系统的结果")
    print("4. 在保存结果时包含所有预测系统的输出")

def simulate_gui_prediction_flow():
    """模拟GUI预测流程"""
    print("\n🔄 模拟GUI预测流程:")
    print("=" * 50)
    
    try:
        # 步骤1: 运行完美预测系统
        print("📊 步骤1: 运行完美预测系统...")
        from src.perfect_prediction_system import PerfectPredictionSystem
        system = PerfectPredictionSystem()
        system.initialize_modules()
        result = system.run_complete_prediction('2025-06-25')
        
        main_numbers = result['final_results']['recommended_16_numbers']
        print(f"  完美预测结果: {main_numbers} ({len(main_numbers)}个)")
        
        # 步骤2: 运行特码生肖专项分析
        print("\n⭐ 步骤2: 运行特码生肖专项分析...")
        from special_zodiac_analyzer import SpecialZodiacAnalyzer
        analyzer = SpecialZodiacAnalyzer()
        special_result = analyzer.comprehensive_special_zodiac_prediction(days=100)
        
        special_numbers = special_result['recommended_numbers']
        print(f"  特码生肖专项结果: {special_numbers} ({len(special_numbers)}个)")
        
        # 步骤3: 显示对比
        print("\n📋 步骤3: 结果对比...")
        print(f"  GUI显示 (完美预测): {main_numbers}")
        print(f"  终端打印 (特码生肖): {special_numbers}")
        
        overlap = set(main_numbers) & set(special_numbers)
        print(f"  重叠号码: {sorted(list(overlap))} ({len(overlap)}个)")
        
        print("\n✅ GUI预测流程模拟完成")
        print("🎯 确认: GUI和终端显示不同结果是因为调用了不同的预测系统")
        
    except Exception as e:
        print(f"❌ 模拟失败: {e}")

def create_unified_prediction_display():
    """创建统一的预测结果显示"""
    print("\n🎊 创建统一预测结果显示:")
    print("=" * 50)
    
    try:
        # 获取所有预测系统的结果
        results = {}
        
        # 完美预测系统
        from src.perfect_prediction_system import PerfectPredictionSystem
        system = PerfectPredictionSystem()
        system.initialize_modules()
        perfect_result = system.run_complete_prediction('2025-06-25')
        
        results['perfect_prediction'] = {
            'name': '🎊 完美预测系统',
            'numbers': perfect_result['final_results']['recommended_16_numbers'],
            'zodiacs': perfect_result['final_results']['recommended_4_zodiacs'],
            'confidence': perfect_result['final_results']['overall_confidence'],
            'count': len(perfect_result['final_results']['recommended_16_numbers'])
        }
        
        # 特码生肖专项分析器
        from special_zodiac_analyzer import SpecialZodiacAnalyzer
        analyzer = SpecialZodiacAnalyzer()
        special_result = analyzer.comprehensive_special_zodiac_prediction(days=100)
        
        results['special_zodiac'] = {
            'name': '⭐ 特码生肖专项分析',
            'numbers': special_result['recommended_numbers'],
            'zodiacs': special_result['recommended_zodiacs'],
            'confidence': special_result['confidence'],
            'count': len(special_result['recommended_numbers'])
        }
        
        # 特码预测器
        from src.special_number_predictor import SpecialNumberPredictor
        predictor = SpecialNumberPredictor()
        predictor_result = predictor.predict('2025-06-25')
        
        results['special_predictor'] = {
            'name': '🎯 特码预测器',
            'numbers': predictor_result['predicted_numbers'],
            'zodiacs': [],
            'confidence': predictor_result['confidence'],
            'count': len(predictor_result['predicted_numbers'])
        }
        
        # 显示统一结果
        print("📊 统一预测结果显示:")
        print("-" * 80)
        
        for system_id, info in results.items():
            print(f"\n{info['name']}:")
            print(f"  推荐号码: {info['numbers']}")
            if info['zodiacs']:
                print(f"  推荐生肖: {info['zodiacs']}")
            print(f"  号码数量: {info['count']}个")
            print(f"  置信度: {info['confidence']:.1%}")
        
        # 交叉分析
        print(f"\n🔍 交叉分析:")
        print("-" * 40)
        
        perfect_set = set(results['perfect_prediction']['numbers'])
        special_set = set(results['special_zodiac']['numbers'])
        predictor_set = set(results['special_predictor']['numbers'])
        
        print(f"完美预测 ∩ 特码生肖: {sorted(list(perfect_set & special_set))}")
        print(f"完美预测 ∩ 特码预测: {sorted(list(perfect_set & predictor_set))}")
        print(f"特码生肖 ∩ 特码预测: {sorted(list(special_set & predictor_set))}")
        
        # 融合建议
        all_numbers = perfect_set | special_set | predictor_set
        print(f"\n🔀 融合建议:")
        print(f"  所有系统号码并集: {sorted(list(all_numbers))} ({len(all_numbers)}个)")
        
        # 高置信度号码（出现在多个系统中）
        high_confidence = []
        for num in all_numbers:
            count = 0
            if num in perfect_set:
                count += 1
            if num in special_set:
                count += 1
            if num in predictor_set:
                count += 1
            if count >= 2:
                high_confidence.append(num)
        
        print(f"  高置信度号码 (≥2系统): {sorted(high_confidence)} ({len(high_confidence)}个)")
        
        return results
        
    except Exception as e:
        print(f"❌ 创建统一显示失败: {e}")
        return None

if __name__ == "__main__":
    # 运行分析
    analyze_prediction_results()
    
    # 模拟GUI流程
    simulate_gui_prediction_flow()
    
    # 创建统一显示
    unified_results = create_unified_prediction_display()
    
    print("\n" + "=" * 80)
    print("🎊 预测结果差异分析完成")
    print("✅ 问题根源: GUI运行了多个预测系统但只显示了主系统结果")
    print("💡 解决方案: 在GUI中显示所有预测系统的结果")
    print("=" * 80)
