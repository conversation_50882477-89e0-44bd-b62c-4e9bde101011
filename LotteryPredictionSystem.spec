# -*- mode: python ; coding: utf-8 -*-
# LotteryPredictionSystem PyInstaller配置文件
# 生成时间: 2025-06-24 17:11:35

import sys
import os

block_cipher = None

a = Analysis(
    ['lottery_prediction_gui.py'],
    pathex=[os.getcwd()],
    binaries=[],
    datas=[
        ('src', 'src'),
    ('config', 'config'),
    ('data', 'data'),
    ('requirements.txt', '.'),
    ('optimal_config.json', '.'),
    ('optimization_config.json', '.'),
    ('icon.ico', '.')
    ],
    hiddenimports=[
        'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'numpy',
    'pandas',
    'sklearn',
    'sklearn.ensemble',
    'sklearn.svm',
    'sklearn.neighbors',
    'sklearn.naive_bayes',
    'sklearn.model_selection',
    'sklearn.preprocessing',
    'sklearn.metrics',
    'matplotlib',
    'seaborn',
    'sqlite3',
    'json',
    'datetime',
    'pathlib',
    'collections',
    'hashlib',
    'random',
    'warnings',
    'logging',
    'src.perfect_prediction_system',
    'src.dynamic_fusion_manager_v3',
    'src.algorithm_layer.ml_models.ml_predictor',
    'src.independent_modules.traditional_module',
    'src.independent_modules.ml_module',
    'src.independent_modules.zodiac_extended_module',
    'src.independent_modules.special_zodiac_module'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'unittest',
        'test',
        'distutils',
        'setuptools',
        'pip',
        'wheel'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤掉不需要的文件
a.datas = [x for x in a.datas if not any(pattern in x[0] for pattern in [
    '__pycache__',
    '.pyc',
    '.pyo',
    '.git',
    'test_',
    '.md',
    '.txt'
])]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='LotteryPredictionSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 设置为True以便调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None
)
