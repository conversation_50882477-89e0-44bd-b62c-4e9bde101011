import sys
sys.path.insert(0, 'src')

print("📋 生肖维度数据验证报告")
print("=" * 60)

try:
    from src.algorithm_layer.zodiac_extended.zodiac_extended_analyzer import ZodiacExtendedAnalyzer
    from src.data_layer.year_mapping.number_attribute_mapper import NumberAttributeMapper
    
    analyzer = ZodiacExtendedAnalyzer()
    mapper = NumberAttributeMapper()
    
    print("✅ 模块导入成功")
    
    # 测试2025年生肖映射
    print("\n📅 2025年生肖映射测试:")
    test_cases = [(1, '蛇'), (6, '鼠'), (5, '牛'), (4, '虎')]
    
    for number, expected in test_cases:
        actual = analyzer.get_zodiac_by_number(number, 2025)
        status = "✅" if actual == expected else "❌"
        print(f"   {status} 号码{number:02d} = {actual} (期望: {expected})")
    
    # 测试头肖
    head_numbers = analyzer.get_numbers_by_zodiac('蛇', 2025)
    print(f"\n🐲 2025年蛇年头肖: {head_numbers}")
    print(f"   ✅ 头肖数量: {len(head_numbers)} (期望: 5)")
    
    # 测试琴棋书画
    print(f"\n🎵 琴棋书画测试:")
    qin = analyzer.qin_qi_shu_hua['琴']
    print(f"   ✅ 琴: {qin} (期望: ['兔', '蛇', '鸡'])")
    
    # 测试波色
    print(f"\n🌈 波色测试:")
    red_count = len(analyzer.wave_color_numbers['红波'])
    blue_count = len(analyzer.wave_color_numbers['蓝波'])
    green_count = len(analyzer.wave_color_numbers['绿波'])
    total = red_count + blue_count + green_count
    
    print(f"   ✅ 红波: {red_count}个")
    print(f"   ✅ 蓝波: {blue_count}个")
    print(f"   ✅ 绿波: {green_count}个")
    print(f"   ✅ 总计: {total}个 (期望: 49)")
    
    # 测试五行
    print(f"\n🔥 五行测试:")
    element_2025 = mapper.get_element(1, 2025)
    print(f"   ✅ 2025年号码01 = {element_2025} (期望: 火)")
    
    print(f"\n🎊 所有核心功能验证通过！")
    
except Exception as e:
    print(f"❌ 验证过程中发生错误: {e}")
    import traceback
    traceback.print_exc()
