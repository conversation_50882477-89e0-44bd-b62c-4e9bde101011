"""
诊断数据库状态问题
检查为什么数据库状态显示为打×
"""
import os
import sqlite3
from datetime import datetime

def check_directory_permissions():
    """检查目录权限"""
    print("📁 检查目录权限...")
    
    current_dir = os.getcwd()
    data_dir = os.path.join(current_dir, "data")
    
    print(f"   当前工作目录: {current_dir}")
    print(f"   数据目录路径: {data_dir}")
    
    # 检查当前目录权限
    try:
        test_file = os.path.join(current_dir, "test_write.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print(f"   ✅ 当前目录可写")
    except Exception as e:
        print(f"   ❌ 当前目录不可写: {e}")
        return False
    
    # 检查data目录是否存在
    if os.path.exists(data_dir):
        print(f"   ✅ data目录存在")
    else:
        print(f"   ⚠️ data目录不存在，尝试创建...")
        try:
            os.makedirs(data_dir, exist_ok=True)
            print(f"   ✅ data目录创建成功")
        except Exception as e:
            print(f"   ❌ data目录创建失败: {e}")
            return False
    
    # 检查data目录权限
    try:
        test_file = os.path.join(data_dir, "test_write.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print(f"   ✅ data目录可写")
    except Exception as e:
        print(f"   ❌ data目录不可写: {e}")
        return False
    
    return True

def check_database_file():
    """检查数据库文件"""
    print("\n💾 检查数据库文件...")
    
    db_path = "data/lottery.db"
    
    print(f"   数据库路径: {db_path}")
    
    if os.path.exists(db_path):
        print(f"   ✅ 数据库文件存在")
        
        # 检查文件大小
        file_size = os.path.getsize(db_path)
        print(f"   📊 文件大小: {file_size} 字节")
        
        # 检查文件权限
        try:
            with open(db_path, 'r+b') as f:
                pass
            print(f"   ✅ 数据库文件可读写")
        except Exception as e:
            print(f"   ❌ 数据库文件权限问题: {e}")
            return False
        
        return True
    else:
        print(f"   ⚠️ 数据库文件不存在")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🔗 测试数据库连接...")
    
    db_path = "data/lottery.db"
    
    try:
        # 确保目录存在
        os.makedirs("data", exist_ok=True)
        
        # 尝试连接数据库
        conn = sqlite3.connect(db_path)
        print(f"   ✅ 数据库连接成功")
        
        # 测试基本操作
        cursor = conn.cursor()
        
        # 检查是否可以执行查询
        cursor.execute("SELECT sqlite_version()")
        version = cursor.fetchone()[0]
        print(f"   📊 SQLite版本: {version}")
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        if tables:
            print(f"   📋 现有表: {[table[0] for table in tables]}")
        else:
            print(f"   ⚠️ 数据库中没有表")
        
        # 如果lottery_results表存在，检查数据
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='lottery_results'")
        if cursor.fetchone():
            cursor.execute("SELECT COUNT(*) FROM lottery_results")
            count = cursor.fetchone()[0]
            print(f"   📊 lottery_results表记录数: {count}")
            
            if count > 0:
                cursor.execute("SELECT draw_date, period_number FROM lottery_results ORDER BY draw_date DESC LIMIT 1")
                latest = cursor.fetchone()
                print(f"   📅 最新记录: {latest[0]} ({latest[1]})")
        else:
            print(f"   ⚠️ lottery_results表不存在")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 数据库连接失败: {e}")
        return False

def test_table_creation():
    """测试表创建"""
    print("\n🏗️ 测试表创建...")
    
    db_path = "data/lottery.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 尝试创建表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lottery_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                draw_date TEXT NOT NULL,
                period_number TEXT UNIQUE NOT NULL,
                regular_1 INTEGER NOT NULL,
                regular_2 INTEGER NOT NULL,
                regular_3 INTEGER NOT NULL,
                regular_4 INTEGER NOT NULL,
                regular_5 INTEGER NOT NULL,
                regular_6 INTEGER NOT NULL,
                special_number INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        print(f"   ✅ lottery_results表创建成功")
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_draw_date ON lottery_results(draw_date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_period ON lottery_results(period_number)')
        
        print(f"   ✅ 索引创建成功")
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 表创建失败: {e}")
        return False

def test_data_operations():
    """测试数据操作"""
    print("\n📊 测试数据操作...")
    
    db_path = "data/lottery.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 测试插入数据
        test_data = ('2024-06-22', 'TEST001', 1, 8, 15, 22, 29, 36, 42)
        
        cursor.execute('''
            INSERT OR REPLACE INTO lottery_results
            (draw_date, period_number, regular_1, regular_2, regular_3,
             regular_4, regular_5, regular_6, special_number)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', test_data)
        
        print(f"   ✅ 测试数据插入成功")
        
        # 测试查询数据
        cursor.execute('SELECT COUNT(*) FROM lottery_results WHERE period_number = ?', ('TEST001',))
        count = cursor.fetchone()[0]
        
        if count > 0:
            print(f"   ✅ 测试数据查询成功")
        else:
            print(f"   ❌ 测试数据查询失败")
        
        # 测试删除数据
        cursor.execute('DELETE FROM lottery_results WHERE period_number = ?', ('TEST001',))
        print(f"   ✅ 测试数据删除成功")
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 数据操作失败: {e}")
        return False

def check_python_modules():
    """检查Python模块"""
    print("\n🐍 检查Python模块...")
    
    required_modules = ['sqlite3', 'os', 'pandas']
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}: 已安装")
        except ImportError:
            print(f"   ❌ {module}: 未安装")
            return False
    
    return True

def simulate_gui_initialization():
    """模拟GUI初始化过程"""
    print("\n🖥️ 模拟GUI初始化过程...")
    
    try:
        print("   1. 检查目录权限...")
        if not check_directory_permissions():
            print("   ❌ 目录权限检查失败")
            return False
        
        print("   2. 初始化数据库连接...")
        import sqlite3
        import os
        
        # 确保数据目录存在
        os.makedirs("data", exist_ok=True)
        
        db_path = "data/lottery.db"
        db_connection = sqlite3.connect(db_path)
        
        print("   ✅ 数据库连接成功")
        
        print("   3. 创建数据库表...")
        cursor = db_connection.cursor()
        
        # 创建开奖数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lottery_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                draw_date TEXT NOT NULL,
                period_number TEXT UNIQUE NOT NULL,
                regular_1 INTEGER NOT NULL,
                regular_2 INTEGER NOT NULL,
                regular_3 INTEGER NOT NULL,
                regular_4 INTEGER NOT NULL,
                regular_5 INTEGER NOT NULL,
                regular_6 INTEGER NOT NULL,
                special_number INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_draw_date ON lottery_results(draw_date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_period ON lottery_results(period_number)')
        
        db_connection.commit()
        
        print("   ✅ 数据库表创建成功")
        
        print("   4. 刷新数据库状态...")
        
        # 获取记录总数
        cursor.execute('SELECT COUNT(*) FROM lottery_results')
        total_records = cursor.fetchone()[0]
        
        # 获取最新数据
        cursor.execute('SELECT draw_date, period_number FROM lottery_results ORDER BY draw_date DESC LIMIT 1')
        latest_result = cursor.fetchone()
        
        print(f"   📊 记录总数: {total_records}")
        
        if latest_result:
            print(f"   📅 最新数据: {latest_result[0]} ({latest_result[1]})")
        else:
            print(f"   📅 最新数据: 无数据")
        
        print("   ✅ 数据库状态刷新成功")
        
        db_connection.close()
        
        print("   🎊 GUI初始化模拟完成，数据库状态应该显示为 ✅")
        
        return True
        
    except Exception as e:
        print(f"   ❌ GUI初始化模拟失败: {e}")
        return False

def create_diagnosis_report():
    """创建诊断报告"""
    print("\n📋 创建诊断报告...")
    
    report = f"""
# 数据库状态诊断报告

## 诊断时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 问题描述
用户反馈：数据管理中的数据库状态显示为打×（❌）

## 可能原因分析

### 1. 目录权限问题
- 当前工作目录没有写权限
- data目录不存在或没有写权限
- 文件系统权限限制

### 2. 数据库文件问题
- 数据库文件不存在
- 数据库文件损坏
- 数据库文件被其他程序占用

### 3. SQLite模块问题
- sqlite3模块未正确安装
- Python环境问题
- 模块版本兼容性问题

### 4. 代码执行问题
- init_database_connection()函数执行失败
- 异常处理导致状态显示错误
- GUI初始化顺序问题

## 诊断步骤

### 步骤1: 检查目录权限
- 验证当前目录可写性
- 检查data目录存在性和权限
- 测试文件创建和删除

### 步骤2: 检查数据库文件
- 验证数据库文件存在性
- 检查文件大小和权限
- 测试文件读写操作

### 步骤3: 测试数据库连接
- 尝试连接SQLite数据库
- 检查SQLite版本
- 验证基本SQL操作

### 步骤4: 测试表创建
- 创建lottery_results表
- 创建必要的索引
- 验证表结构

### 步骤5: 测试数据操作
- 插入测试数据
- 查询测试数据
- 删除测试数据

### 步骤6: 模拟GUI初始化
- 完整模拟GUI数据库初始化过程
- 检查每个步骤的执行结果
- 验证最终状态

## 解决方案

### 方案1: 权限问题解决
1. 确保应用程序有足够的文件系统权限
2. 以管理员身份运行应用程序
3. 更改应用程序运行目录

### 方案2: 数据库重建
1. 删除现有的data/lottery.db文件
2. 重新启动应用程序
3. 让系统自动创建新的数据库

### 方案3: 手动初始化
1. 手动创建data目录
2. 运行数据库初始化脚本
3. 验证数据库连接

### 方案4: 环境检查
1. 检查Python环境
2. 重新安装必要的模块
3. 验证模块版本兼容性

## 预防措施

### 1. 错误处理改进
- 增强异常处理机制
- 提供详细的错误信息
- 添加自动恢复功能

### 2. 状态检查增强
- 定期检查数据库连接状态
- 提供手动重连功能
- 显示详细的状态信息

### 3. 用户指导
- 提供数据库问题解决指南
- 添加自诊断功能
- 改善错误提示信息

---
诊断完成，请根据测试结果采取相应的解决方案。
"""
    
    with open('DATABASE_STATUS_DIAGNOSIS.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("✅ 诊断报告已保存到: DATABASE_STATUS_DIAGNOSIS.md")

def main():
    """主诊断函数"""
    print("🔍 数据库状态问题诊断")
    print("=" * 60)
    
    print("📋 诊断项目:")
    print("  1. 目录权限检查")
    print("  2. 数据库文件检查")
    print("  3. 数据库连接测试")
    print("  4. 表创建测试")
    print("  5. 数据操作测试")
    print("  6. Python模块检查")
    print("  7. GUI初始化模拟")
    print("  8. 诊断报告生成")
    
    print("\n" + "=" * 60)
    
    try:
        # 运行所有诊断
        results = []
        
        results.append(("目录权限", check_directory_permissions()))
        results.append(("数据库文件", check_database_file()))
        results.append(("数据库连接", test_database_connection()))
        results.append(("表创建", test_table_creation()))
        results.append(("数据操作", test_data_operations()))
        results.append(("Python模块", check_python_modules()))
        results.append(("GUI初始化", simulate_gui_initialization()))
        
        create_diagnosis_report()
        
        print("\n" + "=" * 60)
        print("🎯 诊断结果总结:")
        
        all_passed = True
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if not result:
                all_passed = False
        
        if all_passed:
            print("\n🎊 所有诊断通过！数据库应该正常工作。")
            print("\n💡 如果GUI中仍显示❌，请尝试:")
            print("  1. 重新启动应用程序")
            print("  2. 检查应用程序运行权限")
            print("  3. 查看详细的错误信息")
        else:
            print("\n⚠️ 发现问题，请根据失败的测试项目进行修复。")
            print("\n🔧 常见解决方案:")
            print("  1. 以管理员身份运行应用程序")
            print("  2. 删除data目录后重新启动")
            print("  3. 检查磁盘空间和权限")
        
        print("\n📄 生成的文件:")
        print("  📄 DATABASE_STATUS_DIAGNOSIS.md - 详细诊断报告")
        
    except Exception as e:
        print(f"\n❌ 诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
