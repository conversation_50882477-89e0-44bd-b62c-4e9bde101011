import sys
import os
sys.path.append('src')

def check_perfect_prediction_modules():
    """检查完美预测系统的模组"""
    print("🔍 完美预测系统模组检查")
    print("=" * 50)
    
    try:
        from perfect_prediction_system import PerfectPredictionSystem
        
        # 创建系统实例
        system = PerfectPredictionSystem()
        print("✅ 完美预测系统创建成功")
        
        # 检查模组属性
        print(f"\n📋 检查系统模组属性:")
        
        # 检查各个分析模组
        modules_to_check = [
            ('traditional_analysis', '传统分析模组'),
            ('machine_learning', '机器学习模组'),
            ('zodiac_extended', '生肖扩展模组'),
            ('special_zodiac', '特码生肖模组'),
            ('fusion_manager', '融合管理器'),
            ('smart_filter', '智能筛选器')
        ]
        
        for attr_name, display_name in modules_to_check:
            if hasattr(system, attr_name):
                module = getattr(system, attr_name)
                print(f"   ✅ {display_name}: {type(module).__name__}")
                
                # 检查模组的关键方法
                if hasattr(module, 'predict'):
                    print(f"      📊 predict方法: 存在")
                elif hasattr(module, 'analyze'):
                    print(f"      📊 analyze方法: 存在")
                elif hasattr(module, 'filter_numbers'):
                    print(f"      📊 filter_numbers方法: 存在")
                else:
                    print(f"      ⚠️ 未找到标准预测方法")
            else:
                print(f"   ❌ {display_name}: 未找到")
        
        # 检查初始化方法
        print(f"\n🔧 检查初始化方法:")
        if hasattr(system, 'initialize_modules'):
            print(f"   ✅ initialize_modules方法: 存在")
            try:
                system.initialize_modules()
                print(f"   ✅ 模组初始化: 成功")
            except Exception as e:
                print(f"   ❌ 模组初始化失败: {e}")
        else:
            print(f"   ❌ initialize_modules方法: 不存在")
        
        # 检查预测方法
        print(f"\n🎯 检查预测方法:")
        prediction_methods = [
            'run_complete_prediction',
            'predict_numbers',
            'predict_zodiacs'
        ]
        
        for method in prediction_methods:
            if hasattr(system, method):
                print(f"   ✅ {method}: 存在")
            else:
                print(f"   ❌ {method}: 不存在")
        
        return system
        
    except ImportError as e:
        print(f"❌ 导入完美预测系统失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None

if __name__ == "__main__":
    system = check_perfect_prediction_modules()
