"""
四大分析模组管理器
支持独立预测、回测和融合交叉验证
"""
import sqlite3
import json
import numpy as np
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from loguru import logger
import pandas as pd

@dataclass
class PredictionResult:
    """预测结果数据类"""
    module_name: str
    predicted_numbers: List[int]
    confidence: float
    prediction_date: str
    target_date: str
    metadata: Dict[str, Any]
    execution_time: float

@dataclass
class BacktestResult:
    """回测结果数据类"""
    module_name: str
    test_date: str
    actual_number: int
    predicted_numbers: List[int]
    hit: bool
    hit_rank: Optional[int]
    confidence: float
    metadata: Dict[str, Any]

class ModuleManager:
    """四大分析模组管理器"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.modules = {}
        self.module_configs = {
            "traditional_analysis": {
                "name": "传统分析模组",
                "weight": 0.30,
                "enabled": True,
                "min_numbers": 16,
                "max_numbers": 24
            },
            "machine_learning": {
                "name": "机器学习模组", 
                "weight": 0.40,
                "enabled": True,
                "min_numbers": 16,
                "max_numbers": 24
            },
            "zodiac_extended": {
                "name": "生肖扩展模组",
                "weight": 0.20,
                "enabled": True,
                "min_numbers": 16,
                "max_numbers": 24
            },
            "special_zodiac": {
                "name": "特码生肖模组",
                "weight": 0.10,
                "enabled": True,
                "min_numbers": 12,
                "max_numbers": 16
            }
        }
        
        # 初始化模组
        self._initialize_modules()
        
        # 生肖映射
        self.zodiac_mapping = self._get_zodiac_mapping()
    
    def _initialize_modules(self):
        """初始化所有模组"""
        logger.info("初始化四大分析模组...")
        
        # 1. 传统分析模组
        self.modules["traditional_analysis"] = TraditionalAnalysisModule(self.db_path)
        
        # 2. 机器学习模组
        self.modules["machine_learning"] = MachineLearningModule(self.db_path)
        
        # 3. 生肖扩展模组
        self.modules["zodiac_extended"] = ZodiacExtendedModule(self.db_path)
        
        # 4. 特码生肖模组
        self.modules["special_zodiac"] = SpecialZodiacModule(self.db_path)
        
        logger.info(f"成功初始化 {len(self.modules)} 个分析模组")
    
    def run_single_module_prediction(self, module_name: str, target_date: str) -> PredictionResult:
        """运行单个模组的独立预测"""
        if module_name not in self.modules:
            raise ValueError(f"模组 {module_name} 不存在")
        
        if not self.module_configs[module_name]["enabled"]:
            raise ValueError(f"模组 {module_name} 未启用")
        
        logger.info(f"运行 {module_name} 独立预测，目标日期: {target_date}")
        
        start_time = datetime.now()
        
        try:
            module = self.modules[module_name]
            result = module.predict(target_date)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            prediction_result = PredictionResult(
                module_name=module_name,
                predicted_numbers=result["predicted_numbers"],
                confidence=result["confidence"],
                prediction_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                target_date=target_date,
                metadata=result.get("metadata", {}),
                execution_time=execution_time
            )
            
            logger.info(f"{module_name} 预测完成，推荐 {len(result['predicted_numbers'])} 个号码")
            return prediction_result
            
        except Exception as e:
            logger.error(f"{module_name} 预测失败: {e}")
            raise
    
    def run_all_modules_prediction(self, target_date: str) -> Dict[str, PredictionResult]:
        """运行所有模组的独立预测"""
        logger.info(f"运行所有模组独立预测，目标日期: {target_date}")
        
        results = {}
        for module_name in self.modules:
            if self.module_configs[module_name]["enabled"]:
                try:
                    results[module_name] = self.run_single_module_prediction(module_name, target_date)
                except Exception as e:
                    logger.error(f"模组 {module_name} 预测失败: {e}")
        
        logger.info(f"完成 {len(results)} 个模组的独立预测")
        return results
    
    def run_single_module_backtest(self, module_name: str, start_date: str, end_date: str, 
                                 window_size: int = 30) -> List[BacktestResult]:
        """运行单个模组的独立回测"""
        if module_name not in self.modules:
            raise ValueError(f"模组 {module_name} 不存在")
        
        logger.info(f"运行 {module_name} 独立回测: {start_date} 到 {end_date}")
        
        # 获取回测期间的真实数据
        test_dates = self._get_test_dates(start_date, end_date)
        results = []
        
        for test_date in test_dates:
            try:
                # 获取真实开奖号码
                actual_number = self._get_actual_number(test_date)
                if actual_number is None:
                    continue
                
                # 运行预测（使用历史数据）
                prediction_result = self.modules[module_name].predict_historical(test_date, window_size)
                
                # 计算命中情况
                predicted_numbers = prediction_result["predicted_numbers"]
                hit = actual_number in predicted_numbers
                hit_rank = predicted_numbers.index(actual_number) + 1 if hit else None
                
                backtest_result = BacktestResult(
                    module_name=module_name,
                    test_date=test_date,
                    actual_number=actual_number,
                    predicted_numbers=predicted_numbers,
                    hit=hit,
                    hit_rank=hit_rank,
                    confidence=prediction_result["confidence"],
                    metadata=prediction_result.get("metadata", {})
                )
                
                results.append(backtest_result)
                
            except Exception as e:
                logger.error(f"{module_name} 回测失败 {test_date}: {e}")
        
        logger.info(f"{module_name} 回测完成，测试 {len(results)} 期")
        return results
    
    def run_all_modules_backtest(self, start_date: str, end_date: str, 
                               window_size: int = 30) -> Dict[str, List[BacktestResult]]:
        """运行所有模组的独立回测"""
        logger.info(f"运行所有模组独立回测: {start_date} 到 {end_date}")
        
        results = {}
        for module_name in self.modules:
            if self.module_configs[module_name]["enabled"]:
                try:
                    results[module_name] = self.run_single_module_backtest(
                        module_name, start_date, end_date, window_size
                    )
                except Exception as e:
                    logger.error(f"模组 {module_name} 回测失败: {e}")
        
        logger.info(f"完成 {len(results)} 个模组的独立回测")
        return results
    
    def run_fusion_prediction(self, target_date: str, fusion_method: str = "weighted_voting") -> Dict[str, Any]:
        """运行融合预测"""
        logger.info(f"运行融合预测，目标日期: {target_date}，融合方法: {fusion_method}")
        
        # 获取所有模组的预测结果
        module_results = self.run_all_modules_prediction(target_date)
        
        if not module_results:
            raise ValueError("没有可用的模组预测结果")
        
        # 执行融合
        if fusion_method == "weighted_voting":
            fusion_result = self._weighted_voting_fusion(module_results)
        elif fusion_method == "confidence_weighted":
            fusion_result = self._confidence_weighted_fusion(module_results)
        elif fusion_method == "intersection":
            fusion_result = self._intersection_fusion(module_results)
        elif fusion_method == "union":
            fusion_result = self._union_fusion(module_results)
        else:
            raise ValueError(f"不支持的融合方法: {fusion_method}")
        
        # 添加融合元数据
        fusion_result["fusion_method"] = fusion_method
        fusion_result["module_count"] = len(module_results)
        fusion_result["individual_results"] = {
            name: {
                "numbers": result.predicted_numbers,
                "confidence": result.confidence
            } for name, result in module_results.items()
        }
        
        logger.info(f"融合预测完成，最终推荐 {len(fusion_result['final_numbers'])} 个号码")
        return fusion_result
    
    def run_cross_validation(self, start_date: str, end_date: str, 
                           fusion_methods: List[str] = None, k_folds: int = 5) -> Dict[str, Any]:
        """运行交叉验证"""
        if fusion_methods is None:
            fusion_methods = ["weighted_voting", "confidence_weighted", "intersection", "union"]
        
        logger.info(f"运行交叉验证: {start_date} 到 {end_date}, {k_folds}折")
        
        # 获取测试日期
        test_dates = self._get_test_dates(start_date, end_date)
        
        if len(test_dates) < k_folds:
            raise ValueError(f"测试数据不足，需要至少 {k_folds} 期数据")
        
        # 分割数据
        fold_size = len(test_dates) // k_folds
        cv_results = {}
        
        for method in fusion_methods:
            method_results = []
            
            for fold in range(k_folds):
                start_idx = fold * fold_size
                end_idx = start_idx + fold_size if fold < k_folds - 1 else len(test_dates)
                
                fold_dates = test_dates[start_idx:end_idx]
                fold_results = []
                
                for test_date in fold_dates:
                    try:
                        # 获取真实号码
                        actual_number = self._get_actual_number(test_date)
                        if actual_number is None:
                            continue
                        
                        # 运行融合预测
                        fusion_result = self.run_fusion_prediction(test_date, method)
                        predicted_numbers = fusion_result["final_numbers"]
                        
                        # 计算命中
                        hit = actual_number in predicted_numbers
                        hit_rank = predicted_numbers.index(actual_number) + 1 if hit else None
                        
                        fold_results.append({
                            "date": test_date,
                            "actual": actual_number,
                            "predicted": predicted_numbers,
                            "hit": hit,
                            "hit_rank": hit_rank,
                            "confidence": fusion_result["confidence"]
                        })
                        
                    except Exception as e:
                        logger.error(f"交叉验证失败 {test_date}: {e}")
                
                method_results.append(fold_results)
            
            cv_results[method] = method_results
        
        # 计算交叉验证统计
        cv_stats = self._calculate_cv_statistics(cv_results)
        
        logger.info(f"交叉验证完成，测试了 {len(fusion_methods)} 种融合方法")
        return {
            "cv_results": cv_results,
            "cv_statistics": cv_stats,
            "test_period": f"{start_date} 到 {end_date}",
            "k_folds": k_folds
        }
    
    def _weighted_voting_fusion(self, module_results: Dict[str, PredictionResult]) -> Dict[str, Any]:
        """加权投票融合"""
        number_scores = {}
        total_weight = 0
        
        for module_name, result in module_results.items():
            weight = self.module_configs[module_name]["weight"]
            confidence = result.confidence
            
            for number in result.predicted_numbers:
                if number not in number_scores:
                    number_scores[number] = 0
                number_scores[number] += weight * confidence
            
            total_weight += weight
        
        # 归一化分数
        for number in number_scores:
            number_scores[number] /= total_weight
        
        # 选择前16个号码
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        final_numbers = [num for num, score in sorted_numbers[:16]]
        
        # 计算整体置信度
        avg_confidence = np.mean([result.confidence for result in module_results.values()])
        
        return {
            "final_numbers": final_numbers,
            "number_scores": number_scores,
            "confidence": avg_confidence,
            "method": "weighted_voting"
        }
    
    def _confidence_weighted_fusion(self, module_results: Dict[str, PredictionResult]) -> Dict[str, Any]:
        """置信度加权融合"""
        number_scores = {}
        total_confidence = sum(result.confidence for result in module_results.values())
        
        for module_name, result in module_results.items():
            confidence_weight = result.confidence / total_confidence if total_confidence > 0 else 1/len(module_results)
            
            for number in result.predicted_numbers:
                if number not in number_scores:
                    number_scores[number] = 0
                number_scores[number] += confidence_weight
        
        # 选择前16个号码
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        final_numbers = [num for num, score in sorted_numbers[:16]]
        
        return {
            "final_numbers": final_numbers,
            "number_scores": number_scores,
            "confidence": total_confidence / len(module_results),
            "method": "confidence_weighted"
        }
    
    def _intersection_fusion(self, module_results: Dict[str, PredictionResult]) -> Dict[str, Any]:
        """交集融合"""
        all_numbers = [set(result.predicted_numbers) for result in module_results.values()]
        intersection = set.intersection(*all_numbers) if all_numbers else set()
        
        # 如果交集不足16个，添加出现频率最高的号码
        if len(intersection) < 16:
            number_counts = {}
            for result in module_results.values():
                for number in result.predicted_numbers:
                    number_counts[number] = number_counts.get(number, 0) + 1
            
            sorted_by_count = sorted(number_counts.items(), key=lambda x: x[1], reverse=True)
            final_numbers = list(intersection)
            
            for number, count in sorted_by_count:
                if number not in final_numbers and len(final_numbers) < 16:
                    final_numbers.append(number)
        else:
            final_numbers = list(intersection)[:16]
        
        avg_confidence = np.mean([result.confidence for result in module_results.values()])
        
        return {
            "final_numbers": sorted(final_numbers),
            "intersection_size": len(intersection),
            "confidence": avg_confidence * 0.9,  # 交集融合置信度稍低
            "method": "intersection"
        }
    
    def _union_fusion(self, module_results: Dict[str, PredictionResult]) -> Dict[str, Any]:
        """并集融合"""
        all_numbers = set()
        for result in module_results.values():
            all_numbers.update(result.predicted_numbers)
        
        # 按出现频率排序
        number_counts = {}
        for result in module_results.values():
            for number in result.predicted_numbers:
                number_counts[number] = number_counts.get(number, 0) + 1
        
        sorted_by_count = sorted(number_counts.items(), key=lambda x: x[1], reverse=True)
        final_numbers = [number for number, count in sorted_by_count[:16]]
        
        avg_confidence = np.mean([result.confidence for result in module_results.values()])
        
        return {
            "final_numbers": final_numbers,
            "union_size": len(all_numbers),
            "confidence": avg_confidence * 1.1,  # 并集融合置信度稍高
            "method": "union"
        }
    
    def _get_test_dates(self, start_date: str, end_date: str) -> List[str]:
        """获取测试日期列表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT draw_date FROM lottery_results 
            WHERE draw_date BETWEEN ? AND ? 
            ORDER BY draw_date
        ''', (start_date, end_date))
        
        dates = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        return dates
    
    def _get_actual_number(self, test_date: str) -> Optional[int]:
        """获取指定日期的真实开奖号码"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT special_number FROM lottery_results 
            WHERE draw_date = ?
        ''', (test_date,))
        
        result = cursor.fetchone()
        conn.close()
        
        return result[0] if result else None
    
    def _get_zodiac_mapping(self) -> Dict[int, str]:
        """获取生肖映射"""
        # 2025年生肖映射
        zodiac_mapping = {}
        zodiacs = ["蛇", "马", "羊", "猴", "鸡", "狗", "猪", "鼠", "牛", "虎", "兔", "龙"]
        
        for i in range(1, 50):
            zodiac_mapping[i] = zodiacs[(i - 1) % 12]
        
        return zodiac_mapping
    
    def _calculate_cv_statistics(self, cv_results: Dict[str, List[List[Dict]]]) -> Dict[str, Any]:
        """计算交叉验证统计"""
        stats = {}
        
        for method, folds in cv_results.items():
            all_results = []
            for fold in folds:
                all_results.extend(fold)
            
            if not all_results:
                continue
            
            hit_count = sum(1 for r in all_results if r["hit"])
            total_count = len(all_results)
            hit_rate = hit_count / total_count if total_count > 0 else 0
            
            confidences = [r["confidence"] for r in all_results]
            avg_confidence = np.mean(confidences) if confidences else 0
            
            hit_ranks = [r["hit_rank"] for r in all_results if r["hit_rank"] is not None]
            avg_hit_rank = np.mean(hit_ranks) if hit_ranks else None
            
            stats[method] = {
                "hit_rate": hit_rate,
                "hit_count": hit_count,
                "total_count": total_count,
                "avg_confidence": avg_confidence,
                "avg_hit_rank": avg_hit_rank,
                "fold_count": len(folds)
            }
        
        return stats
    
    def get_module_status(self) -> Dict[str, Any]:
        """获取模组状态"""
        status = {}
        for module_name, config in self.module_configs.items():
            status[module_name] = {
                "name": config["name"],
                "enabled": config["enabled"],
                "weight": config["weight"],
                "available": module_name in self.modules
            }
        return status
    
    def set_module_config(self, module_name: str, **kwargs):
        """设置模组配置"""
        if module_name not in self.module_configs:
            raise ValueError(f"模组 {module_name} 不存在")
        
        for key, value in kwargs.items():
            if key in self.module_configs[module_name]:
                self.module_configs[module_name][key] = value
        
        logger.info(f"更新模组 {module_name} 配置: {kwargs}")


# 模组基类和具体实现将在后续文件中定义
class BaseModule:
    """模组基类"""
    def __init__(self, db_path: str):
        self.db_path = db_path
    
    def predict(self, target_date: str) -> Dict[str, Any]:
        """预测方法"""
        raise NotImplementedError
    
    def predict_historical(self, target_date: str, window_size: int) -> Dict[str, Any]:
        """历史预测方法"""
        raise NotImplementedError
