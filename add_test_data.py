"""
添加更多测试数据
"""
import sys
sys.path.insert(0, 'src')

from datetime import date, datetime, timedelta
from src.data_layer.database.models import create_database_engine, get_session
from src.data_layer.year_mapping.lunar_year_manager import LunarYearManager
from src.data_layer.year_mapping.number_attribute_mapper import NumberAttributeMapper
from src.data_layer.data_import.lottery_data_processor import LotteryDataProcessor
from loguru import logger
import random

def add_more_test_data():
    """添加更多测试数据"""
    logger.info("开始添加更多测试数据")
    
    # 创建数据库连接
    engine = create_database_engine()
    session = get_session(engine)
    
    # 创建管理器
    lunar_manager = LunarYearManager(session)
    attr_mapper = NumberAttributeMapper()
    data_processor = LotteryDataProcessor(lunar_manager, attr_mapper, session)
    
    # 生成60期测试数据
    start_date = date(2023, 1, 1)
    
    for i in range(60):
        period_number = f"2023{i+1:03d}"
        draw_date = start_date + timedelta(days=i*3)  # 每3天一期
        special_number = random.randint(1, 49)
        regular_numbers = random.sample(range(1, 50), 6)
        
        success = data_processor.manual_input_record(
            period_number, 
            draw_date, 
            regular_numbers, 
            special_number
        )
        
        if success:
            logger.info(f"测试数据添加成功: {period_number} - 特码: {special_number}")
    
    session.close()
    logger.info("测试数据添加完成")

if __name__ == "__main__":
    add_more_test_data()
