import sqlite3
conn = sqlite3.connect("data/lottery.db")
cursor = conn.cursor()

cursor.execute("SELECT COUNT(*) FROM lottery_data")
total_records = cursor.fetchone()[0]

cursor.execute("SELECT MIN(draw_date), MAX(draw_date) FROM lottery_data")
date_range = cursor.fetchone()

cursor.execute("SELECT DISTINCT lunar_year FROM lottery_data ORDER BY lunar_year")
lunar_years = cursor.fetchall()

print(f"      ✅ 数据库记录: {total_records}期")
print(f"      ✅ 时间跨度: {date_range[0]} 至 {date_range[1]}")
print(f"      ✅ 农历年份: {', '.join([str(y[0]) for y in lunar_years])}")

conn.close()
