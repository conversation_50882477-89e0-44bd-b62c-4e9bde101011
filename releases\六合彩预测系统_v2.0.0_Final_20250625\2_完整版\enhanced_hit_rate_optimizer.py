"""
增强命中率优化器 - 支持回测功能 (修复版)
"""

import json
import random
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
import statistics

class EnhancedHitRateOptimizer:
    """增强命中率优化器 - 支持回测功能"""

    def __init__(self, db_path="data/lottery.db"):
        self.db_path = db_path

        # 回测配置
        self.backtest_config = {
            "default_iterations": 10,
            "min_data_points": 5,
            "confidence_threshold": 0.6,
            "hit_rate_threshold": 0.3
        }

        # 核心优化策略
        self.optimization_strategies = {
            "hot_cold_balance": self.hot_cold_balance_strategy,
            "pattern_recognition": self.pattern_recognition_strategy,
            "frequency_clustering": self.frequency_clustering_strategy,
            "trend_following": self.trend_following_strategy,
            "zodiac_correlation": self.zodiac_correlation_strategy
        }

        # 历史命中率统计
        self.hit_rate_history = {}

        print(f"✅ 增强命中率优化器初始化完成")

    def run_enhanced_backtest(self, start_date: str, end_date: str, 
                            window_size: int = 30, iterations: int = None) -> Dict[str, Any]:
        """运行增强回测"""
        if iterations is None:
            iterations = self.backtest_config["default_iterations"]

        print(f"🔄 开始增强回测...")
        print(f"📅 回测期间: {start_date} 到 {end_date}")
        print(f"🪟 训练窗口: {window_size}天")
        print(f"🔄 回测次数: {iterations}次")

        # 获取历史数据
        historical_data = self.get_historical_data(start_date, end_date)
        
        if len(historical_data) < self.backtest_config["min_data_points"]:
            print(f"⚠️ 历史数据不足 ({len(historical_data)} < {self.backtest_config['min_data_points']})，使用模拟数据")
            historical_data = self.generate_mock_data(start_date, end_date)

        print(f"📊 数据量: {len(historical_data)} 条记录")

        # 运行多次回测
        backtest_results = []
        
        for i in range(iterations):
            print(f"\n🔄 第{i+1}/{iterations}次回测...")
            
            # 为每次回测生成不同的策略参数
            strategy_params = self.generate_strategy_parameters(i)
            
            # 运行单次回测
            result = self.run_single_backtest(historical_data, window_size, strategy_params, i+1)
            
            if result:
                backtest_results.append(result)
                hit_rate = result.get('overall_hit_rate', 0)
                print(f"   📊 本次命中率: {hit_rate:.1%}")
            else:
                print(f"   ❌ 第{i+1}次回测失败")

        # 分析回测结果
        analysis_result = self.analyze_backtest_results(backtest_results)
        
        return analysis_result

    def get_historical_data(self, start_date: str, end_date: str) -> List[Dict]:
        """获取历史数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT draw_date, regular_1, regular_2, regular_3, 
                       regular_4, regular_5, regular_6, special_number
                FROM lottery_results 
                WHERE draw_date BETWEEN ? AND ?
                ORDER BY draw_date
            """, (start_date, end_date))
            
            rows = cursor.fetchall()
            conn.close()
            
            data = []
            for i, row in enumerate(rows):
                data.append({
                    'date': row[0],
                    'period': f"2025{i+1:03d}",
                    'regular_numbers': [row[1], row[2], row[3], row[4], row[5], row[6]],
                    'special_number': row[7]
                })
            
            return data
            
        except Exception as e:
            print(f"⚠️ 获取历史数据失败: {e}")
            return []

    def generate_mock_data(self, start_date: str, end_date: str) -> List[Dict]:
        """生成模拟数据"""
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        data = []
        current_date = start_dt
        period = 1
        
        while current_date <= end_dt:
            # 生成模拟的彩票数据
            regular_numbers = sorted(random.sample(range(1, 50), 6))
            special_number = random.randint(1, 49)
            
            data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'period': f"2025{period:03d}",
                'regular_numbers': regular_numbers,
                'special_number': special_number
            })
            
            current_date += timedelta(days=1)
            period += 1
        
        return data

    def generate_strategy_parameters(self, iteration: int) -> Dict[str, Any]:
        """生成策略参数"""
        # 设置随机种子确保可重现性
        random.seed(iteration * 9876 + 5432)
        
        params = {
            'iteration': iteration,
            'hot_cold_balance': {
                'hot_threshold': random.uniform(0.6, 0.9),
                'cold_threshold': random.uniform(0.1, 0.4),
                'balance_weight': random.uniform(0.3, 0.8)
            },
            'pattern_recognition': {
                'pattern_depth': random.randint(3, 8),
                'similarity_threshold': random.uniform(0.5, 0.9),
                'weight': random.uniform(0.4, 1.2)
            },
            'frequency_clustering': {
                'cluster_size': random.randint(5, 12),
                'frequency_weight': random.uniform(0.6, 1.4),
                'recency_factor': random.uniform(0.2, 0.8)
            },
            'trend_following': {
                'trend_window': random.randint(5, 15),
                'momentum_factor': random.uniform(0.3, 1.1),
                'reversal_sensitivity': random.uniform(0.1, 0.6)
            },
            'zodiac_correlation': {
                'zodiac_weight': random.uniform(0.2, 0.7),
                'seasonal_factor': random.uniform(0.4, 1.0),
                'correlation_threshold': random.uniform(0.3, 0.8)
            }
        }
        
        return params

    def run_single_backtest(self, historical_data: List[Dict], window_size: int, 
                          strategy_params: Dict[str, Any], iteration: int) -> Dict[str, Any]:
        """运行单次回测"""
        try:
            # 模拟回测过程
            predictions = []
            hits = 0
            total_predictions = 0
            
            # 使用滑动窗口进行回测
            for i in range(window_size, len(historical_data)):
                # 训练数据：前window_size天
                training_data = historical_data[i-window_size:i]
                
                # 目标数据：当天
                target_data = historical_data[i]
                
                # 使用策略进行预测
                predicted_numbers = self.apply_optimization_strategies(
                    training_data, strategy_params
                )
                
                # 检查命中情况
                actual_special = target_data['special_number']
                hit = actual_special in predicted_numbers
                
                predictions.append({
                    'date': target_data['date'],
                    'predicted': predicted_numbers,
                    'actual_special': actual_special,
                    'hit': hit
                })
                
                if hit:
                    hits += 1
                total_predictions += 1
            
            # 计算性能指标
            hit_rate = hits / total_predictions if total_predictions > 0 else 0
            
            # 计算其他指标
            confidence = self.calculate_confidence(predictions, strategy_params)
            stability = self.calculate_stability(predictions)
            
            result = {
                'iteration': iteration,
                'strategy_params': strategy_params,
                'total_predictions': total_predictions,
                'hits': hits,
                'overall_hit_rate': hit_rate,
                'confidence': confidence,
                'stability': stability,
                'predictions': predictions[-10:],  # 保留最后10个预测作为样本
                'performance_score': hit_rate * 0.6 + confidence * 0.2 + stability * 0.2
            }
            
            return result
            
        except Exception as e:
            print(f"❌ 单次回测失败: {e}")
            return None

    def apply_optimization_strategies(self, training_data: List[Dict], 
                                    strategy_params: Dict[str, Any]) -> List[int]:
        """应用优化策略"""
        # 提取特码历史
        special_numbers = [data['special_number'] for data in training_data]
        
        # 应用各种策略
        strategy_results = {}
        
        for strategy_name, strategy_func in self.optimization_strategies.items():
            if strategy_name in strategy_params:
                try:
                    result = strategy_func(special_numbers, strategy_params[strategy_name])
                    strategy_results[strategy_name] = result
                except Exception as e:
                    print(f"⚠️ 策略 {strategy_name} 执行失败: {e}")
                    strategy_results[strategy_name] = random.sample(range(1, 50), 16)
        
        # 融合策略结果
        final_numbers = self.fuse_strategy_results(strategy_results)
        
        return final_numbers[:16]  # 返回16个号码

    def hot_cold_balance_strategy(self, special_numbers: List[int], params: Dict) -> List[int]:
        """冷热号平衡策略"""
        from collections import Counter
        
        # 统计频率
        counter = Counter(special_numbers)
        total_count = len(special_numbers)
        
        hot_numbers = []
        cold_numbers = []
        
        for number in range(1, 50):
            frequency = counter.get(number, 0) / total_count if total_count > 0 else 0
            
            if frequency >= params['hot_threshold']:
                hot_numbers.append(number)
            elif frequency <= params['cold_threshold']:
                cold_numbers.append(number)
        
        # 平衡选择
        balance_weight = params['balance_weight']
        hot_count = int(16 * balance_weight)
        cold_count = 16 - hot_count
        
        selected = []
        selected.extend(random.sample(hot_numbers, min(hot_count, len(hot_numbers))))
        selected.extend(random.sample(cold_numbers, min(cold_count, len(cold_numbers))))
        
        # 补充随机号码
        remaining = [i for i in range(1, 50) if i not in selected]
        needed = 16 - len(selected)
        if needed > 0:
            selected.extend(random.sample(remaining, min(needed, len(remaining))))
        
        return selected

    def pattern_recognition_strategy(self, special_numbers: List[int], params: Dict) -> List[int]:
        """模式识别策略"""
        pattern_depth = params['pattern_depth']
        
        if len(special_numbers) < pattern_depth:
            return random.sample(range(1, 50), 16)
        
        # 寻找模式
        recent_pattern = special_numbers[-pattern_depth:]
        
        # 基于模式预测下一个可能的号码
        candidates = []
        
        # 简单的模式：相邻号码
        for num in recent_pattern:
            candidates.extend([num-1, num+1])
        
        # 过滤有效号码
        candidates = [n for n in candidates if 1 <= n <= 49]
        
        # 去重并补充
        candidates = list(set(candidates))
        remaining = [i for i in range(1, 50) if i not in candidates]
        needed = 16 - len(candidates)
        if needed > 0:
            candidates.extend(random.sample(remaining, min(needed, len(remaining))))
        
        return candidates[:16]

    def frequency_clustering_strategy(self, special_numbers: List[int], params: Dict) -> List[int]:
        """频率聚类策略"""
        from collections import Counter
        
        counter = Counter(special_numbers)
        
        # 按频率排序
        sorted_numbers = sorted(counter.items(), key=lambda x: x[1], reverse=True)
        
        # 选择高频号码
        cluster_size = params['cluster_size']
        high_freq_numbers = [num for num, freq in sorted_numbers[:cluster_size]]
        
        # 补充随机号码
        remaining = [i for i in range(1, 50) if i not in high_freq_numbers]
        needed = 16 - len(high_freq_numbers)
        if needed > 0:
            high_freq_numbers.extend(random.sample(remaining, min(needed, len(remaining))))
        
        return high_freq_numbers[:16]

    def trend_following_strategy(self, special_numbers: List[int], params: Dict) -> List[int]:
        """趋势跟踪策略"""
        trend_window = params['trend_window']
        
        if len(special_numbers) < trend_window:
            return random.sample(range(1, 50), 16)
        
        # 分析趋势
        recent_numbers = special_numbers[-trend_window:]
        
        # 计算趋势方向
        trend_candidates = []
        for i in range(1, len(recent_numbers)):
            diff = recent_numbers[i] - recent_numbers[i-1]
            next_num = recent_numbers[-1] + diff
            if 1 <= next_num <= 49:
                trend_candidates.append(next_num)
        
        # 补充相邻号码
        for num in recent_numbers[-3:]:
            trend_candidates.extend([num-1, num+1])
        
        # 过滤和补充
        trend_candidates = [n for n in trend_candidates if 1 <= n <= 49]
        trend_candidates = list(set(trend_candidates))
        
        remaining = [i for i in range(1, 50) if i not in trend_candidates]
        needed = 16 - len(trend_candidates)
        if needed > 0:
            trend_candidates.extend(random.sample(remaining, min(needed, len(remaining))))
        
        return trend_candidates[:16]

    def zodiac_correlation_strategy(self, special_numbers: List[int], params: Dict) -> List[int]:
        """生肖关联策略"""
        # 简化的生肖映射
        zodiac_mapping = {}
        for i in range(1, 50):
            zodiac_mapping[i] = i % 12
        
        # 统计生肖频率
        zodiac_freq = {}
        for num in special_numbers:
            zodiac = zodiac_mapping[num]
            zodiac_freq[zodiac] = zodiac_freq.get(zodiac, 0) + 1
        
        # 选择高频生肖对应的号码
        sorted_zodiacs = sorted(zodiac_freq.items(), key=lambda x: x[1], reverse=True)
        top_zodiacs = [zodiac for zodiac, freq in sorted_zodiacs[:4]]
        
        candidates = []
        for num in range(1, 50):
            if zodiac_mapping[num] in top_zodiacs:
                candidates.append(num)
        
        # 随机选择16个
        if len(candidates) >= 16:
            return random.sample(candidates, 16)
        else:
            remaining = [i for i in range(1, 50) if i not in candidates]
            needed = 16 - len(candidates)
            candidates.extend(random.sample(remaining, min(needed, len(remaining))))
            return candidates

    def fuse_strategy_results(self, strategy_results: Dict[str, List[int]]) -> List[int]:
        """融合策略结果"""
        if not strategy_results:
            return random.sample(range(1, 50), 16)

        # 统计每个号码被选择的次数
        number_votes = {}
        for strategy_name, numbers in strategy_results.items():
            for num in numbers:
                number_votes[num] = number_votes.get(num, 0) + 1

        # 按投票数排序
        sorted_numbers = sorted(number_votes.items(), key=lambda x: x[1], reverse=True)

        # 选择前16个
        final_numbers = [num for num, votes in sorted_numbers[:16]]

        # 如果不足16个，补充随机号码
        if len(final_numbers) < 16:
            remaining = [i for i in range(1, 50) if i not in final_numbers]
            needed = 16 - len(final_numbers)
            final_numbers.extend(random.sample(remaining, min(needed, len(remaining))))

        return final_numbers

    def calculate_confidence(self, predictions: List[Dict], strategy_params: Dict) -> float:
        """计算置信度"""
        if not predictions:
            return 0.0

        # 基于策略参数的一致性计算置信度
        confidence_factors = []

        for param_group in strategy_params.values():
            if isinstance(param_group, dict):
                for param_name, param_value in param_group.items():
                    if isinstance(param_value, (int, float)):
                        # 归一化参数值作为置信度因子
                        normalized_value = min(1.0, max(0.0, param_value))
                        confidence_factors.append(normalized_value)

        if confidence_factors:
            return statistics.mean(confidence_factors)
        else:
            return random.uniform(0.6, 0.9)

    def calculate_stability(self, predictions: List[Dict]) -> float:
        """计算稳定性"""
        if len(predictions) < 2:
            return 1.0

        # 计算命中率的稳定性
        hit_rates = []
        window_size = 5

        for i in range(window_size, len(predictions)):
            window_predictions = predictions[i-window_size:i]
            window_hits = sum(1 for p in window_predictions if p['hit'])
            window_hit_rate = window_hits / window_size
            hit_rates.append(window_hit_rate)

        if len(hit_rates) > 1:
            stability = 1.0 - statistics.stdev(hit_rates)
            return max(0.0, min(1.0, stability))
        else:
            return 1.0

    def analyze_backtest_results(self, backtest_results: List[Dict]) -> Dict[str, Any]:
        """分析回测结果"""
        if not backtest_results:
            return {'error': '没有有效的回测结果'}

        print(f"\n📊 分析{len(backtest_results)}次回测结果...")

        # 按性能评分排序
        sorted_results = sorted(backtest_results, key=lambda x: x['performance_score'], reverse=True)
        best_result = sorted_results[0]

        # 统计分析
        hit_rates = [r['overall_hit_rate'] for r in backtest_results]
        performance_scores = [r['performance_score'] for r in backtest_results]

        analysis = {
            'total_backtests': len(backtest_results),
            'best_iteration': best_result['iteration'],
            'best_hit_rate': best_result['overall_hit_rate'],
            'best_performance_score': best_result['performance_score'],
            'best_strategy_params': best_result['strategy_params'],
            'statistics': {
                'hit_rate_mean': statistics.mean(hit_rates),
                'hit_rate_std': statistics.stdev(hit_rates) if len(hit_rates) > 1 else 0,
                'hit_rate_min': min(hit_rates),
                'hit_rate_max': max(hit_rates),
                'performance_mean': statistics.mean(performance_scores),
                'performance_std': statistics.stdev(performance_scores) if len(performance_scores) > 1 else 0
            },
            'improvement_analysis': {
                'best_vs_average': best_result['overall_hit_rate'] - statistics.mean(hit_rates),
                'improvement_percentage': ((best_result['overall_hit_rate'] - statistics.mean(hit_rates)) / statistics.mean(hit_rates) * 100) if statistics.mean(hit_rates) > 0 else 0
            },
            'recommendations': self.generate_recommendations(best_result)
        }

        print(f"🏆 最佳结果: 第{analysis['best_iteration']}次回测")
        print(f"📊 最佳命中率: {analysis['best_hit_rate']:.1%}")
        print(f"⭐ 性能评分: {analysis['best_performance_score']:.3f}")
        print(f"📈 相比平均提升: {analysis['improvement_analysis']['improvement_percentage']:.1f}%")

        return analysis

    def generate_recommendations(self, best_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成建议"""
        recommendations = {
            'apply_to_system': True,
            'confidence_level': 'high' if best_result['performance_score'] > 0.6 else 'medium',
            'key_strategies': [],
            'parameter_suggestions': best_result['strategy_params'],
            'expected_improvement': f"{best_result['overall_hit_rate']:.1%}"
        }

        # 分析最有效的策略
        for strategy_name, params in best_result['strategy_params'].items():
            if strategy_name != 'iteration':
                recommendations['key_strategies'].append(strategy_name)

        return recommendations

    def save_optimization_results(self, results: Dict[str, Any],
                                filename: str = "enhanced_backtest_results.json") -> bool:
        """保存优化结果"""
        try:
            save_data = {
                'timestamp': datetime.now().isoformat(),
                'results': results,
                'optimizer_config': self.backtest_config
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)

            print(f"✅ 优化结果已保存到: {filename}")
            return True

        except Exception as e:
            print(f"❌ 保存优化结果失败: {e}")
            return False
