#!/usr/bin/env python3
"""
历史模拟预测器
用于历史数据的模拟预测和回测分析
"""

import sqlite3
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import defaultdict

class HistoricalSimulationPredictor:
    """历史模拟预测器"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.module_name = "历史模拟预测器"
        
        # 模拟配置
        self.simulation_config = {
            "prediction_methods": ["frequency", "pattern", "trend", "random"],
            "default_prediction_count": 16,
            "min_historical_data": 20,
            "confidence_threshold": 0.6
        }
        
        print(f"🎯 {self.module_name}初始化完成")
    
    def run_historical_simulation(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """运行历史模拟"""
        print(f"📊 开始历史模拟: {start_date} 到 {end_date}")
        
        try:
            # 获取模拟期间的数据
            simulation_data = self._get_simulation_data(start_date, end_date)
            
            if len(simulation_data) < 2:
                print(f"⚠️ 模拟数据不足")
                return {"error": "数据不足"}
            
            simulation_results = []
            total_hits = 0
            total_predictions = 0
            
            # 对每个日期进行模拟预测
            for i in range(1, len(simulation_data)):
                current_record = simulation_data[i]
                historical_records = simulation_data[i+1:]  # 之前的历史数据
                
                if len(historical_records) < self.simulation_config["min_historical_data"]:
                    continue
                
                # 执行模拟预测
                prediction_result = self._simulate_prediction(
                    historical_records, 
                    current_record["draw_date"]
                )
                
                # 计算命中情况
                actual_number = current_record["special_number"]
                predicted_numbers = prediction_result["predicted_numbers"]
                
                hit = actual_number in predicted_numbers
                if hit:
                    total_hits += 1
                total_predictions += 1
                
                simulation_result = {
                    "date": current_record["draw_date"],
                    "period": current_record.get("period_number", ""),
                    "actual_number": actual_number,
                    "predicted_numbers": predicted_numbers,
                    "hit": hit,
                    "confidence": prediction_result["confidence"],
                    "method": prediction_result["method"]
                }
                
                simulation_results.append(simulation_result)
                
                print(f"  {current_record['draw_date']}: {'✅' if hit else '❌'} 实际{actual_number}, 预测{len(predicted_numbers)}个")
            
            # 计算整体统计
            overall_hit_rate = total_hits / total_predictions if total_predictions > 0 else 0
            
            result = {
                "simulation_results": simulation_results,
                "overall_hit_rate": overall_hit_rate,
                "total_predictions": total_predictions,
                "total_hits": total_hits,
                "simulation_period": {"start": start_date, "end": end_date},
                "summary": {
                    "hit_rate": overall_hit_rate,
                    "prediction_count": total_predictions,
                    "success_count": total_hits,
                    "average_confidence": np.mean([r["confidence"] for r in simulation_results]) if simulation_results else 0
                }
            }
            
            print(f"✅ 历史模拟完成")
            print(f"📊 总预测次数: {total_predictions}")
            print(f"📊 命中次数: {total_hits}")
            print(f"📊 整体命中率: {overall_hit_rate:.1%}")
            
            return result
            
        except Exception as e:
            print(f"❌ 历史模拟失败: {e}")
            return {"error": str(e)}
    
    def _get_simulation_data(self, start_date: str, end_date: str) -> List[Dict]:
        """获取模拟数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = """
        SELECT draw_date, special_number, period_number
        FROM lottery_results 
        WHERE draw_date >= ? AND draw_date <= ?
        ORDER BY draw_date ASC
        """
        
        cursor.execute(query, (start_date, end_date))
        rows = cursor.fetchall()
        
        data = []
        for row in rows:
            data.append({
                "draw_date": row[0],
                "special_number": row[1],
                "period_number": row[2]
            })
        
        conn.close()
        return data
    
    def _simulate_prediction(self, historical_data: List[Dict], target_date: str) -> Dict[str, Any]:
        """模拟预测"""
        # 使用多种方法进行预测
        methods_results = {}
        
        # 方法1: 频率分析
        methods_results["frequency"] = self._frequency_prediction(historical_data)
        
        # 方法2: 模式识别
        methods_results["pattern"] = self._pattern_prediction(historical_data)
        
        # 方法3: 趋势分析
        methods_results["trend"] = self._trend_prediction(historical_data, target_date)
        
        # 方法4: 随机基准
        methods_results["random"] = self._random_prediction(target_date)
        
        # 选择最佳方法或融合结果
        best_method = self._select_best_method(methods_results)
        
        return {
            "predicted_numbers": best_method["numbers"],
            "confidence": best_method["confidence"],
            "method": best_method["name"],
            "all_methods": methods_results
        }
    
    def _frequency_prediction(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """频率分析预测"""
        frequency = defaultdict(int)
        
        for record in historical_data[-50:]:  # 使用最近50期数据
            special = record.get("special_number", 0)
            if 1 <= special <= 49:
                frequency[special] += 1
        
        # 选择频率最高的16个号码
        sorted_numbers = sorted(frequency.items(), key=lambda x: x[1], reverse=True)
        predicted_numbers = [num for num, freq in sorted_numbers[:16]]
        
        # 如果不足16个，补充其他号码
        if len(predicted_numbers) < 16:
            all_numbers = set(range(1, 50))
            remaining = list(all_numbers - set(predicted_numbers))
            predicted_numbers.extend(remaining[:16-len(predicted_numbers)])
        
        return {
            "numbers": predicted_numbers[:16],
            "confidence": 0.7,
            "name": "frequency"
        }
    
    def _pattern_prediction(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """模式识别预测"""
        pattern_candidates = set()
        
        # 分析最近10期的模式
        recent_data = historical_data[-10:]
        
        for record in recent_data:
            special = record.get("special_number", 0)
            if 1 <= special <= 49:
                # 连号模式
                if special > 1:
                    pattern_candidates.add(special - 1)
                if special < 49:
                    pattern_candidates.add(special + 1)
                
                # 间隔模式
                for gap in [3, 5, 7, 10]:
                    if special + gap <= 49:
                        pattern_candidates.add(special + gap)
                    if special - gap >= 1:
                        pattern_candidates.add(special - gap)
        
        predicted_numbers = list(pattern_candidates)[:16]
        
        # 如果不足16个，用频率补充
        if len(predicted_numbers) < 16:
            freq_result = self._frequency_prediction(historical_data)
            for num in freq_result["numbers"]:
                if num not in predicted_numbers:
                    predicted_numbers.append(num)
                    if len(predicted_numbers) >= 16:
                        break
        
        return {
            "numbers": predicted_numbers[:16],
            "confidence": 0.65,
            "name": "pattern"
        }
    
    def _trend_prediction(self, historical_data: List[Dict], target_date: str) -> Dict[str, Any]:
        """趋势分析预测"""
        # 设置确定性种子
        import hashlib
        seed = int(hashlib.md5(f"trend_{target_date}".encode()).hexdigest()[:8], 16) % 100000
        np.random.seed(seed)
        
        # 分析短期和长期趋势
        short_term = historical_data[-5:]
        long_term = historical_data[-20:]
        
        trend_scores = defaultdict(float)
        
        # 短期趋势权重更高
        for i, record in enumerate(short_term):
            special = record.get("special_number", 0)
            if 1 <= special <= 49:
                weight = (i + 1) / len(short_term)  # 越近期权重越高
                trend_scores[special] += weight * 2
        
        # 长期趋势
        for record in long_term:
            special = record.get("special_number", 0)
            if 1 <= special <= 49:
                trend_scores[special] += 0.5
        
        # 选择得分最高的号码
        sorted_trends = sorted(trend_scores.items(), key=lambda x: x[1], reverse=True)
        predicted_numbers = [num for num, score in sorted_trends[:16]]
        
        # 如果不足16个，随机补充
        if len(predicted_numbers) < 16:
            all_numbers = list(range(1, 50))
            remaining = [n for n in all_numbers if n not in predicted_numbers]
            np.random.shuffle(remaining)
            predicted_numbers.extend(remaining[:16-len(predicted_numbers)])
        
        return {
            "numbers": predicted_numbers[:16],
            "confidence": 0.6,
            "name": "trend"
        }
    
    def _random_prediction(self, target_date: str) -> Dict[str, Any]:
        """随机预测（基准方法）"""
        # 设置确定性种子
        import hashlib
        seed = int(hashlib.md5(f"random_{target_date}".encode()).hexdigest()[:8], 16) % 100000
        np.random.seed(seed)
        
        # 随机选择16个号码
        all_numbers = list(range(1, 50))
        predicted_numbers = np.random.choice(all_numbers, 16, replace=False).tolist()
        
        return {
            "numbers": predicted_numbers,
            "confidence": 0.5,
            "name": "random"
        }
    
    def _select_best_method(self, methods_results: Dict[str, Dict]) -> Dict[str, Any]:
        """选择最佳方法"""
        # 简单策略：选择置信度最高的方法
        best_method = None
        best_confidence = 0
        
        for method_name, result in methods_results.items():
            if result["confidence"] > best_confidence:
                best_confidence = result["confidence"]
                best_method = result
        
        return best_method if best_method else methods_results["frequency"]

def test_historical_simulation_predictor():
    """测试历史模拟预测器"""
    print("🧪 测试历史模拟预测器")
    print("=" * 50)
    
    try:
        predictor = HistoricalSimulationPredictor()
        
        # 测试历史模拟
        start_date = "2025-01-01"
        end_date = "2025-01-05"
        
        result = predictor.run_historical_simulation(start_date, end_date)
        
        if result and "simulation_results" in result:
            print(f"✅ 历史模拟测试成功")
            print(f"📊 模拟结果数: {len(result['simulation_results'])}")
            print(f"📊 整体命中率: {result['overall_hit_rate']:.1%}")
            print(f"📊 总预测次数: {result['total_predictions']}")
            print(f"📊 命中次数: {result['total_hits']}")
        else:
            print(f"❌ 历史模拟测试失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_historical_simulation_predictor()
    print(f"\n{'✅ 测试通过' if success else '❌ 测试失败'}")
