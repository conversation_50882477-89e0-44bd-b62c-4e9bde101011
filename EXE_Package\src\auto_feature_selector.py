#!/usr/bin/env python3
"""
自动特征选择器 - 第2阶段升级
智能选择最优特征组合
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# 尝试导入特征选择库
try:
    from sklearn.feature_selection import (
        SelectKBest, mutual_info_regression, f_regression,
        RFE, SelectFromModel, VarianceThreshold
    )
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.linear_model import LassoCV
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("⚠️ scikit-learn未安装，将使用简化特征选择")

class AutoFeatureSelector:
    """自动特征选择器"""
    
    def __init__(self, target_features: int = 50):
        self.target_features = target_features
        self.module_name = "自动特征选择器 v2.0"
        
        # 特征选择策略
        self.selection_strategies = self._initialize_selection_strategies()
        
        # 特征评估历史
        self.feature_history = defaultdict(list)
        self.selection_history = []
        
        print(f"🎯 {self.module_name}初始化完成")
        print(f"📊 目标特征数: {target_features}")
        print(f"🔬 sklearn状态: {'✅ 可用' if SKLEARN_AVAILABLE else '❌ 不可用'}")
    
    def _initialize_selection_strategies(self) -> Dict[str, Any]:
        """初始化特征选择策略"""
        strategies = {}
        
        if SKLEARN_AVAILABLE:
            # 1. 互信息选择
            strategies['mutual_info'] = {
                'selector': SelectKBest(score_func=mutual_info_regression, k=self.target_features),
                'weight': 0.3,
                'description': '基于互信息的特征选择'
            }
            
            # 2. F统计量选择
            strategies['f_regression'] = {
                'selector': SelectKBest(score_func=f_regression, k=self.target_features),
                'weight': 0.25,
                'description': '基于F统计量的特征选择'
            }
            
            # 3. 递归特征消除
            strategies['rfe'] = {
                'selector': RFE(
                    estimator=RandomForestRegressor(n_estimators=50, random_state=42),
                    n_features_to_select=self.target_features
                ),
                'weight': 0.25,
                'description': '递归特征消除'
            }
            
            # 4. 基于模型的特征选择
            strategies['model_based'] = {
                'selector': SelectFromModel(
                    LassoCV(cv=3, random_state=42),
                    max_features=self.target_features
                ),
                'weight': 0.2,
                'description': '基于Lasso模型的特征选择'
            }
        
        return strategies
    
    def select_features(self, features: Dict[str, float], targets: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """选择最优特征"""
        try:
            print(f"🔍 开始特征选择: {len(features)} → {self.target_features}")
            
            if len(features) <= self.target_features:
                print(f"✅ 特征数量已满足要求，无需选择")
                return self._format_selection_result(features, features, "no_selection_needed")
            
            # 准备数据
            feature_names = list(features.keys())
            feature_values = np.array(list(features.values())).reshape(1, -1)
            
            # 移除无效特征
            valid_features = self._remove_invalid_features(features)
            
            if len(valid_features) <= self.target_features:
                return self._format_selection_result(features, valid_features, "invalid_removal")
            
            # 执行多策略特征选择
            if SKLEARN_AVAILABLE and targets is not None:
                selected_features = self._multi_strategy_selection(valid_features, targets)
            else:
                selected_features = self._heuristic_selection(valid_features)
            
            # 记录选择历史
            self._record_selection_history(features, selected_features)
            
            print(f"✅ 特征选择完成: {len(features)} → {len(selected_features)}")
            
            return self._format_selection_result(features, selected_features, "multi_strategy")
            
        except Exception as e:
            print(f"❌ 特征选择失败: {e}")
            return self._fallback_selection(features)
    
    def _remove_invalid_features(self, features: Dict[str, float]) -> Dict[str, float]:
        """移除无效特征"""
        valid_features = {}
        
        for name, value in features.items():
            # 检查数值有效性
            if np.isfinite(value) and not np.isnan(value):
                # 检查方差
                if abs(value) > 1e-8:  # 避免零方差特征
                    valid_features[name] = value
        
        print(f"🧹 移除无效特征: {len(features)} → {len(valid_features)}")
        return valid_features
    
    def _multi_strategy_selection(self, features: Dict[str, float], targets: np.ndarray) -> Dict[str, float]:
        """多策略特征选择"""
        feature_scores = defaultdict(float)
        
        # 准备数据
        feature_names = list(features.keys())
        X = np.array(list(features.values())).reshape(1, -1)
        
        # 如果只有一个样本，创建虚拟数据进行选择
        if len(targets) == 1:
            # 扩展数据用于特征选择
            X_expanded = np.tile(X, (10, 1)) + np.random.normal(0, 0.01, (10, X.shape[1]))
            y_expanded = np.tile(targets, 10) + np.random.normal(0, 0.1, 10)
        else:
            X_expanded = X
            y_expanded = targets
        
        # 执行各种选择策略
        for strategy_name, strategy_info in self.selection_strategies.items():
            try:
                selector = strategy_info['selector']
                weight = strategy_info['weight']
                
                # 拟合选择器
                selector.fit(X_expanded, y_expanded)
                
                # 获取特征分数
                if hasattr(selector, 'scores_'):
                    scores = selector.scores_
                elif hasattr(selector, 'ranking_'):
                    # RFE返回排名，转换为分数
                    ranking = selector.ranking_
                    scores = 1.0 / ranking  # 排名越小分数越高
                elif hasattr(selector, 'estimator_') and hasattr(selector.estimator_, 'coef_'):
                    # 基于模型的选择器
                    scores = np.abs(selector.estimator_.coef_)
                else:
                    # 默认分数
                    scores = np.ones(len(feature_names))
                
                # 累加加权分数
                for i, score in enumerate(scores):
                    if i < len(feature_names):
                        feature_scores[feature_names[i]] += score * weight
                
                print(f"  ✅ {strategy_name}: {strategy_info['description']}")
                
            except Exception as e:
                print(f"  ❌ {strategy_name}失败: {e}")
        
        # 选择得分最高的特征
        sorted_features = sorted(feature_scores.items(), key=lambda x: x[1], reverse=True)
        selected_features = {}
        
        for name, score in sorted_features[:self.target_features]:
            selected_features[name] = features[name]
        
        return selected_features
    
    def _heuristic_selection(self, features: Dict[str, float]) -> Dict[str, float]:
        """启发式特征选择"""
        print("🧠 使用启发式特征选择...")
        
        # 特征重要性评分
        feature_scores = {}
        
        for name, value in features.items():
            score = 1.0  # 基础分数
            
            # 基于特征名称的启发式评分
            if any(keyword in name.lower() for keyword in ['mean', 'average', 'trend']):
                score *= 1.3  # 均值和趋势特征重要
            elif any(keyword in name.lower() for keyword in ['std', 'var', 'volatility']):
                score *= 1.2  # 波动性特征重要
            elif 'zodiac' in name.lower():
                score *= 1.15  # 生肖特征重要
            elif 'interaction' in name.lower():
                score *= 1.1  # 交互特征重要
            elif any(keyword in name.lower() for keyword in ['lag', 'diff']):
                score *= 1.05  # 时间序列特征重要
            
            # 基于数值大小的评分
            abs_value = abs(value)
            if abs_value > 1.0:
                score *= 1.1
            elif abs_value > 0.1:
                score *= 1.05
            elif abs_value < 0.001:
                score *= 0.8  # 很小的值重要性降低
            
            feature_scores[name] = score
        
        # 选择得分最高的特征
        sorted_features = sorted(feature_scores.items(), key=lambda x: x[1], reverse=True)
        selected_features = {}
        
        for name, score in sorted_features[:self.target_features]:
            selected_features[name] = features[name]
        
        return selected_features
    
    def _record_selection_history(self, original_features: Dict[str, float], selected_features: Dict[str, float]):
        """记录选择历史"""
        selection_record = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'original_count': len(original_features),
            'selected_count': len(selected_features),
            'selection_ratio': len(selected_features) / len(original_features),
            'selected_features': list(selected_features.keys())
        }
        
        self.selection_history.append(selection_record)
        
        # 更新特征历史
        for name in selected_features.keys():
            self.feature_history[name].append(1)  # 被选中
        
        for name in original_features.keys():
            if name not in selected_features:
                self.feature_history[name].append(0)  # 未被选中
    
    def _format_selection_result(self, original_features: Dict[str, float], 
                                selected_features: Dict[str, float], 
                                method: str) -> Dict[str, Any]:
        """格式化选择结果"""
        return {
            "selected_features": selected_features,
            "feature_count": len(selected_features),
            "feature_names": list(selected_features.keys()),
            "selection_info": {
                "original_count": len(original_features),
                "selected_count": len(selected_features),
                "selection_ratio": len(selected_features) / len(original_features) if original_features else 0,
                "method": method,
                "strategies_used": list(self.selection_strategies.keys()) if SKLEARN_AVAILABLE else ["heuristic"]
            },
            "feature_importance": self._calculate_feature_importance(selected_features),
            "metadata": {
                "selector_version": "v2.0",
                "selection_time": pd.Timestamp.now().isoformat(),
                "module_name": self.module_name
            }
        }
    
    def _calculate_feature_importance(self, features: Dict[str, float]) -> Dict[str, float]:
        """计算特征重要性"""
        importance = {}
        
        # 基于特征历史计算重要性
        for name in features.keys():
            history = self.feature_history.get(name, [])
            if history:
                # 历史选择频率
                selection_rate = sum(history) / len(history)
                importance[name] = selection_rate
            else:
                # 新特征默认重要性
                importance[name] = 0.5
        
        return importance
    
    def _fallback_selection(self, features: Dict[str, float]) -> Dict[str, Any]:
        """备用选择方案"""
        print("🔄 使用备用特征选择...")
        
        # 简单选择前N个特征
        feature_items = list(features.items())[:self.target_features]
        selected_features = dict(feature_items)
        
        return self._format_selection_result(features, selected_features, "fallback")
    
    def get_selection_statistics(self) -> Dict[str, Any]:
        """获取选择统计信息"""
        return {
            "total_selections": len(self.selection_history),
            "feature_history_size": len(self.feature_history),
            "most_selected_features": self._get_most_selected_features(),
            "average_selection_ratio": np.mean([h['selection_ratio'] for h in self.selection_history]) if self.selection_history else 0,
            "strategies_available": list(self.selection_strategies.keys()) if SKLEARN_AVAILABLE else ["heuristic"]
        }
    
    def _get_most_selected_features(self, top_k: int = 10) -> List[Tuple[str, float]]:
        """获取最常被选择的特征"""
        feature_rates = {}
        
        for name, history in self.feature_history.items():
            if history:
                feature_rates[name] = sum(history) / len(history)
        
        sorted_features = sorted(feature_rates.items(), key=lambda x: x[1], reverse=True)
        return sorted_features[:top_k]

def test_auto_feature_selector():
    """测试自动特征选择器"""
    print("🧪 测试自动特征选择器")
    print("=" * 50)
    
    try:
        # 初始化选择器
        selector = AutoFeatureSelector(target_features=20)
        
        # 模拟特征数据
        features = {}
        for i in range(60):
            features[f'feature_{i}'] = np.random.random()
        
        # 添加一些特殊特征
        features['mean_trend'] = 0.85
        features['std_volatility'] = 0.72
        features['zodiac_dragon'] = 0.68
        features['interaction_mean_std'] = 0.65
        
        # 模拟目标数据
        targets = np.random.randint(1, 50, 10)
        
        # 执行特征选择
        result = selector.select_features(features, targets)
        
        print(f"✅ 特征选择成功")
        print(f"📊 原始特征: {result['selection_info']['original_count']}")
        print(f"📊 选择特征: {result['selection_info']['selected_count']}")
        print(f"📊 选择比例: {result['selection_info']['selection_ratio']:.1%}")
        print(f"📊 选择方法: {result['selection_info']['method']}")
        
        # 显示选择的特征
        selected_names = result['feature_names'][:10]
        print(f"\n🔍 前10个选择的特征:")
        for name in selected_names:
            importance = result['feature_importance'].get(name, 0)
            print(f"  {name}: 重要性 {importance:.3f}")
        
        # 获取统计信息
        stats = selector.get_selection_statistics()
        print(f"\n📈 选择统计:")
        print(f"  总选择次数: {stats['total_selections']}")
        print(f"  特征历史数: {stats['feature_history_size']}")
        print(f"  可用策略: {stats['strategies_available']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_auto_feature_selector()
    print(f"\n{'✅ 测试通过' if success else '❌ 测试失败'}")
