import sqlite3

conn = sqlite3.connect("data/lottery.db")
cursor = conn.cursor()

print("🔍 详细检查春节生肖变换情况")
print("=" * 60)

# 检查2024年春节前后的数据（2024年2月10日是春节）
cursor.execute("""
    SELECT period_number, draw_date, natural_year, lunar_year, special_number, special_zodiac
    FROM lottery_data 
    WHERE draw_date BETWEEN '2024-02-05' AND '2024-02-15'
    ORDER BY draw_date
""")
spring_2024 = cursor.fetchall()

print(f"\n🎊 2024年春节前后数据 (2024-02-10是春节):")
for record in spring_2024:
    print(f"   {record[0]} | {record[1]} | 自然年:{record[2]} | 农历年:{record[3]} | 特码:{record[4]} | 生肖:{record[5]}")

# 检查2025年春节前后的数据（2025年1月29日是春节）
cursor.execute("""
    SELECT period_number, draw_date, natural_year, lunar_year, special_number, special_zodiac
    FROM lottery_data 
    WHERE draw_date BETWEEN '2025-01-25' AND '2025-02-05'
    ORDER BY draw_date
""")
spring_2025 = cursor.fetchall()

print(f"\n🎊 2025年春节前后数据 (2025-01-29是春节):")
for record in spring_2025:
    print(f"   {record[0]} | {record[1]} | 自然年:{record[2]} | 农历年:{record[3]} | 特码:{record[4]} | 生肖:{record[5]}")

print(f"\n🐲 生肖变换验证:")
print(f"   2023年: 兔年")
print(f"   2024年: 龙年 (春节: 2024-02-10)")
print(f"   2025年: 蛇年 (春节: 2025-01-29)")

conn.close()
