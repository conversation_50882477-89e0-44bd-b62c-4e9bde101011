import re

# 读取SmartNumberFilter文件
with open("smart_number_filter.py", "r", encoding="utf-8") as f:
    content = f.read()

print("🔍 排除筛选显示图标分析")
print("=" * 40)

# 查找排除筛选的print语句
exclusion_prints = []
lines = content.split('\n')

for i, line in enumerate(lines):
    if "执行排除筛选" in line and "print" in line:
        exclusion_prints.append({
            'line_num': i + 1,
            'content': line.strip()
        })

print(f"📊 找到 {len(exclusion_prints)} 个排除筛选打印语句:")
for print_stmt in exclusion_prints:
    line_num = print_stmt['line_num']
    content = print_stmt['content']
    print(f"   L{line_num}: {content}")

# 分析图标使用
print(f"\n🔍 图标使用分析:")
icon_patterns = [
    (r"❌.*执行.*筛选", "❌ 图标"),
    (r"✅.*执行.*筛选", "✅ 图标"),
    (r"🔍.*执行.*筛选", "🔍 图标"),
    (r"📊.*执行.*筛选", "📊 图标")
]

for pattern, desc in icon_patterns:
    matches = re.findall(pattern, content)
    if matches:
        print(f"   ✅ {desc}: {len(matches)} 处")
        for match in matches[:3]:
            print(f"      - {match}")
    else:
        print(f"   ❌ {desc}: 未找到")

# 查找其他筛选的图标
print(f"\n📋 其他筛选图标对比:")
other_filters = [
    "执行命中率筛选",
    "执行频率筛选", 
    "执行模式筛选",
    "融合筛选结果"
]

for filter_name in other_filters:
    filter_lines = []
    for i, line in enumerate(lines):
        if filter_name in line and "print" in line:
            filter_lines.append(line.strip())
    
    if filter_lines:
        print(f"   {filter_name}:")
        for line in filter_lines[:2]:
            print(f"      {line}")

print(f"\n💡 图标含义分析:")
print(f"   ❌ - 通常表示错误、失败或警告")
print(f"   ✅ - 通常表示成功、完成")
print(f"   🔍 - 通常表示正在执行、搜索")
print(f"   📊 - 通常表示数据处理、分析")

print(f"\n🔧 建议修改:")
print(f"   当前: print('  ❌ 执行排除筛选...')")
print(f"   建议: print('  🔍 执行排除筛选...')  # 表示正在执行")
print(f"   或者: print('  📊 执行排除筛选...')  # 表示数据处理")
