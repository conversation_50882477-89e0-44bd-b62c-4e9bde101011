"""
贝叶斯优化 - 使用Optuna进行超参数优化
"""
import optuna
import numpy as np
from typing import Dict, List, Any, Callable
from sklearn.model_selection import cross_val_score, TimeSeriesSplit
from sklearn.metrics import accuracy_score
import joblib
import json

class BayesianOptimizer:
    def __init__(self, 
                 model_class: Any,
                 param_space: Dict,
                 objective_metric: str = 'accuracy',
                 cv_folds: int = 5,
                 n_trials: int = 100):
        """初始化贝叶斯优化器"""
        self.model_class = model_class
        self.param_space = param_space
        self.objective_metric = objective_metric
        self.cv_folds = cv_folds
        self.n_trials = n_trials
        self.study = None
        self.best_params = None
        self.best_score = None
        
    def suggest_params(self, trial: optuna.Trial) -> Dict:
        """根据参数空间建议参数"""
        params = {}
        
        for param_name, param_config in self.param_space.items():
            param_type = param_config['type']
            
            if param_type == 'int':
                params[param_name] = trial.suggest_int(
                    param_name, 
                    param_config['low'], 
                    param_config['high'],
                    step=param_config.get('step', 1)
                )
            elif param_type == 'float':
                if param_config.get('log', False):
                    params[param_name] = trial.suggest_loguniform(
                        param_name,
                        param_config['low'],
                        param_config['high']
                    )
                else:
                    params[param_name] = trial.suggest_uniform(
                        param_name,
                        param_config['low'],
                        param_config['high']
                    )
            elif param_type == 'categorical':
                params[param_name] = trial.suggest_categorical(
                    param_name,
                    param_config['choices']
                )
        
        return params
    
    def objective(self, trial: optuna.Trial, X: np.ndarray, y: np.ndarray) -> float:
        """优化目标函数"""
        # 获取建议的参数
        params = self.suggest_params(trial)
        
        try:
            # 创建模型
            model = self.model_class(**params)
            
            # 时间序列交叉验证
            tscv = TimeSeriesSplit(n_splits=self.cv_folds)
            scores = cross_val_score(model, X, y, cv=tscv, scoring=self.objective_metric)
            
            return scores.mean()
            
        except Exception as e:
            # 如果参数组合无效，返回很低的分数
            return -1.0
    
    def optimize(self, 
                X: np.ndarray, 
                y: np.ndarray,
                direction: str = 'maximize',
                timeout: int = None) -> Dict:
        """执行优化"""
        
        # 创建研究
        self.study = optuna.create_study(direction=direction)
        
        # 定义目标函数
        objective_func = lambda trial: self.objective(trial, X, y)
        
        # 执行优化
        self.study.optimize(
            objective_func, 
            n_trials=self.n_trials,
            timeout=timeout,
            show_progress_bar=True
        )
        
        # 保存最佳结果
        self.best_params = self.study.best_params
        self.best_score = self.study.best_value
        
        return {
            'best_params': self.best_params,
            'best_score': self.best_score,
            'n_trials': len(self.study.trials),
            'optimization_history': [trial.value for trial in self.study.trials if trial.value is not None]
        }
    
    def get_best_model(self) -> Any:
        """获取最佳参数的模型"""
        if self.best_params is None:
            raise ValueError("请先执行优化")
        
        return self.model_class(**self.best_params)
    
    def get_optimization_history(self) -> List[Dict]:
        """获取优化历史"""
        if self.study is None:
            return []
        
        history = []
        for trial in self.study.trials:
            if trial.value is not None:
                history.append({
                    'trial_number': trial.number,
                    'value': trial.value,
                    'params': trial.params,
                    'datetime_start': trial.datetime_start.isoformat() if trial.datetime_start else None,
                    'datetime_complete': trial.datetime_complete.isoformat() if trial.datetime_complete else None
                })
        
        return history
    
    def save_study(self, filepath: str):
        """保存优化研究"""
        if self.study is None:
            raise ValueError("没有可保存的研究")
        
        study_data = {
            'best_params': self.best_params,
            'best_score': self.best_score,
            'trials': self.get_optimization_history(),
            'param_space': self.param_space,
            'objective_metric': self.objective_metric
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(study_data, f, indent=2, ensure_ascii=False)
    
    def load_study(self, filepath: str):
        """加载优化研究"""
        with open(filepath, 'r', encoding='utf-8') as f:
            study_data = json.load(f)
        
        self.best_params = study_data['best_params']
        self.best_score = study_data['best_score']
        self.param_space = study_data['param_space']
        self.objective_metric = study_data['objective_metric']
