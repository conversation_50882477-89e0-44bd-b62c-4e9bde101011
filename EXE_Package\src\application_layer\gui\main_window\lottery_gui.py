"""
澳门六合彩预测系统 - 主GUI应用
使用tkinter创建现代化界面
"""
import sys
sys.path.insert(0, 'src')

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import date, datetime
import threading
import json
from pathlib import Path

# 导入预测系统
from src.data_layer.database.models import create_database_engine, get_session, LotteryData
from src.data_layer.year_mapping.lunar_year_manager import LunarYearManager
from src.data_layer.year_mapping.number_attribute_mapper import NumberAttributeMapper
from src.algorithm_layer.model_fusion.cross_validation_fusion import CrossValidationFusion
from src.data_layer.data_import.lottery_data_processor import LotteryDataProcessor

class LotteryPredictionGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("澳门六合彩预测系统 v1.0")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # 设置图标和样式
        self.setup_styles()
        
        # 初始化预测系统
        self.init_prediction_system()
        
        # 创建界面
        self.create_widgets()
        
        # 加载数据状态
        self.load_data_status()
    
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#f0f0f0')
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'), background='#f0f0f0')
        style.configure('Info.TLabel', font=('Arial', 10), background='#f0f0f0')
        style.configure('Success.TLabel', font=('Arial', 10, 'bold'), foreground='green', background='#f0f0f0')
        style.configure('Error.TLabel', font=('Arial', 10, 'bold'), foreground='red', background='#f0f0f0')
        
        # 按钮样式
        style.configure('Action.TButton', font=('Arial', 10, 'bold'))
        style.configure('Predict.TButton', font=('Arial', 12, 'bold'), foreground='white')
        style.map('Predict.TButton', background=[('active', '#0066cc'), ('!active', '#0080ff')])
    
    def init_prediction_system(self):
        """初始化预测系统"""
        try:
            self.engine = create_database_engine()
            self.session = get_session(self.engine)
            self.lunar_manager = LunarYearManager(self.session)
            self.attr_mapper = NumberAttributeMapper()
            self.fusion_system = CrossValidationFusion(self.session, self.lunar_manager, self.attr_mapper)
            self.data_processor = LotteryDataProcessor(self.lunar_manager, self.attr_mapper, self.session)
            self.system_ready = True
        except Exception as e:
            self.system_ready = False
            messagebox.showerror("系统错误", f"预测系统初始化失败：{e}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = tk.Frame(self.root, bg='#f0f0f0', height=80)
        title_frame.pack(fill='x', padx=20, pady=10)
        title_frame.pack_propagate(False)
        
        title_label = ttk.Label(title_frame, text="🎯 澳门六合彩预测系统", style='Title.TLabel')
        title_label.pack(pady=20)
        
        # 创建主要内容区域
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # 左侧面板 - 数据管理
        self.create_data_panel(main_frame)
        
        # 右侧面板 - 预测分析
        self.create_prediction_panel(main_frame)
        
        # 底部状态栏
        self.create_status_bar()
    
    def create_data_panel(self, parent):
        """创建数据管理面板"""
        data_frame = tk.LabelFrame(parent, text="📊 数据管理", font=('Arial', 12, 'bold'), 
                                  bg='#f0f0f0', padx=10, pady=10)
        data_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        # 数据状态显示
        status_frame = tk.Frame(data_frame, bg='#f0f0f0')
        status_frame.pack(fill='x', pady=5)
        
        ttk.Label(status_frame, text="数据状态:", style='Header.TLabel').pack(anchor='w')
        
        self.data_count_label = ttk.Label(status_frame, text="数据库记录: 检查中...", style='Info.TLabel')
        self.data_count_label.pack(anchor='w')
        
        self.data_range_label = ttk.Label(status_frame, text="时间跨度: 检查中...", style='Info.TLabel')
        self.data_range_label.pack(anchor='w')
        
        self.data_quality_label = ttk.Label(status_frame, text="数据质量: 检查中...", style='Info.TLabel')
        self.data_quality_label.pack(anchor='w')
        
        # 分隔线
        ttk.Separator(data_frame, orient='horizontal').pack(fill='x', pady=10)
        
        # 数据导入区域
        import_frame = tk.Frame(data_frame, bg='#f0f0f0')
        import_frame.pack(fill='x', pady=5)
        
        ttk.Label(import_frame, text="数据导入:", style='Header.TLabel').pack(anchor='w')
        
        ttk.Button(import_frame, text="📁 导入CSV文件", 
                  command=self.import_csv_file, style='Action.TButton').pack(fill='x', pady=2)
        
        ttk.Button(import_frame, text="✏️ 手动输入数据", 
                  command=self.manual_input_dialog, style='Action.TButton').pack(fill='x', pady=2)
        
        ttk.Button(import_frame, text="🔄 刷新数据状态", 
                  command=self.load_data_status, style='Action.TButton').pack(fill='x', pady=2)
        
        # 分隔线
        ttk.Separator(data_frame, orient='horizontal').pack(fill='x', pady=10)
        
        # 最新开奖显示
        latest_frame = tk.Frame(data_frame, bg='#f0f0f0')
        latest_frame.pack(fill='x', pady=5)
        
        ttk.Label(latest_frame, text="最新开奖:", style='Header.TLabel').pack(anchor='w')
        
        self.latest_info_text = tk.Text(latest_frame, height=8, width=40, font=('Consolas', 9))
        self.latest_info_text.pack(fill='x', pady=5)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(latest_frame, orient="vertical", command=self.latest_info_text.yview)
        self.latest_info_text.configure(yscrollcommand=scrollbar.set)
    
    def create_prediction_panel(self, parent):
        """创建预测分析面板"""
        pred_frame = tk.LabelFrame(parent, text="🎯 预测分析", font=('Arial', 12, 'bold'), 
                                  bg='#f0f0f0', padx=10, pady=10)
        pred_frame.pack(side='right', fill='both', expand=True)
        
        # 预测参数设置
        param_frame = tk.Frame(pred_frame, bg='#f0f0f0')
        param_frame.pack(fill='x', pady=5)
        
        ttk.Label(param_frame, text="预测参数:", style='Header.TLabel').pack(anchor='w')
        
        # 分析期数
        period_frame = tk.Frame(param_frame, bg='#f0f0f0')
        period_frame.pack(fill='x', pady=2)
        ttk.Label(period_frame, text="分析期数:", style='Info.TLabel').pack(side='left')
        self.analysis_periods = tk.StringVar(value="100")
        ttk.Entry(period_frame, textvariable=self.analysis_periods, width=10).pack(side='right')
        
        # 预测日期
        date_frame = tk.Frame(param_frame, bg='#f0f0f0')
        date_frame.pack(fill='x', pady=2)
        ttk.Label(date_frame, text="预测日期:", style='Info.TLabel').pack(side='left')
        self.target_date = tk.StringVar(value=date.today().strftime('%Y-%m-%d'))
        ttk.Entry(date_frame, textvariable=self.target_date, width=15).pack(side='right')
        
        # 预测按钮
        predict_btn = ttk.Button(param_frame, text="�� 开始预测分析", 
                               command=self.start_prediction, style='Predict.TButton')
        predict_btn.pack(fill='x', pady=10)
        
        # 分隔线
        ttk.Separator(pred_frame, orient='horizontal').pack(fill='x', pady=10)
        
        # 预测结果显示
        result_frame = tk.Frame(pred_frame, bg='#f0f0f0')
        result_frame.pack(fill='both', expand=True, pady=5)
        
        ttk.Label(result_frame, text="预测结果:", style='Header.TLabel').pack(anchor='w')
        
        # 创建结果显示区域
        self.result_text = tk.Text(result_frame, height=20, font=('Consolas', 10))
        self.result_text.pack(fill='both', expand=True, pady=5)
        
        # 结果区域滚动条
        result_scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.result_text.yview)
        result_scrollbar.pack(side='right', fill='y')
        self.result_text.configure(yscrollcommand=result_scrollbar.set)
        
        # 导出结果按钮
        export_btn = ttk.Button(result_frame, text="💾 导出预测结果", 
                              command=self.export_results, style='Action.TButton')
        export_btn.pack(fill='x', pady=5)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = tk.Frame(self.root, bg='#e0e0e0', height=30)
        self.status_bar.pack(side='bottom', fill='x')
        self.status_bar.pack_propagate(False)
        
        self.status_label = ttk.Label(self.status_bar, text="系统就绪", 
                                     style='Info.TLabel', background='#e0e0e0')
        self.status_label.pack(side='left', padx=10, pady=5)
        
        # 系统状态指示
        status_text = "🟢 系统正常" if self.system_ready else "🔴 系统异常"
        self.system_status_label = ttk.Label(self.status_bar, text=status_text, 
                                           style='Success.TLabel' if self.system_ready else 'Error.TLabel',
                                           background='#e0e0e0')
        self.system_status_label.pack(side='right', padx=10, pady=5)
    
    def load_data_status(self):
        """加载数据状态"""
        def load_status():
            try:
                # 获取数据库统计
                total_records = self.session.query(LotteryData).count()
                
                if total_records > 0:
                    # 获取时间范围
                    date_range = self.session.query(LotteryData.draw_date).order_by(LotteryData.draw_date).all()
                    start_date = date_range[0][0]
                    end_date = date_range[-1][0]
                    
                    # 更新界面
                    self.root.after(0, lambda: self.update_data_status(total_records, start_date, end_date))
                    
                    # 获取最新开奖
                    latest_records = self.session.query(LotteryData).order_by(LotteryData.draw_date.desc()).limit(5).all()
                    self.root.after(0, lambda: self.update_latest_results(latest_records))
                else:
                    self.root.after(0, lambda: self.update_data_status(0, None, None))
                    
            except Exception as e:
                self.root.after(0, lambda: self.update_status(f"数据加载失败: {e}"))
        
        # 在后台线程中加载
        threading.Thread(target=load_status, daemon=True).start()
        self.update_status("正在加载数据状态...")
    
    def update_data_status(self, count, start_date, end_date):
        """更新数据状态显示"""
        self.data_count_label.config(text=f"数据库记录: {count}期")
        
        if start_date and end_date:
            self.data_range_label.config(text=f"时间跨度: {start_date} 至 {end_date}")
            days = (end_date - start_date).days
            self.data_quality_label.config(text=f"数据质量: 优秀 (跨度{days}天)", style='Success.TLabel')
        else:
            self.data_range_label.config(text="时间跨度: 无数据")
            self.data_quality_label.config(text="数据质量: 无数据", style='Error.TLabel')
    
    def update_latest_results(self, records):
        """更新最新开奖显示"""
        self.latest_info_text.delete(1.0, tk.END)
        
        if records:
            self.latest_info_text.insert(tk.END, "最新5期开奖结果:\n")
            self.latest_info_text.insert(tk.END, "=" * 40 + "\n")
            
            for record in records:
                regular_nums = json.loads(record.regular_numbers) if record.regular_numbers else []
                info = f"{record.period_number}\n"
                info += f"日期: {record.draw_date}\n"
                info += f"正码: {regular_nums}\n"
                info += f"特码: {record.special_number} ({record.special_zodiac})\n"
                info += "-" * 30 + "\n"
                self.latest_info_text.insert(tk.END, info)
        else:
            self.latest_info_text.insert(tk.END, "暂无开奖数据")
    
    def start_prediction(self):
        """开始预测分析"""
        if not self.system_ready:
            messagebox.showerror("系统错误", "预测系统未就绪，请检查系统状态")
            return
        
        def run_prediction():
            try:
                # 获取参数
                periods = int(self.analysis_periods.get())
                target_date_str = self.target_date.get()
                target_date_obj = datetime.strptime(target_date_str, '%Y-%m-%d').date()
                
                self.root.after(0, lambda: self.update_status("正在执行预测分析..."))
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, "🎯 开始预测分析...\n"))
                
                # 执行预测
                prediction_result = self.fusion_system.comprehensive_prediction(
                    recent_periods=periods,
                    target_date=target_date_obj
                )
                
                # 更新结果
                self.root.after(0, lambda: self.display_prediction_results(prediction_result))
                self.root.after(0, lambda: self.update_status("预测分析完成"))
                
            except Exception as e:
                self.root.after(0, lambda: self.update_status(f"预测失败: {e}"))
                self.root.after(0, lambda: messagebox.showerror("预测错误", f"预测分析失败：{e}"))
        
        # 在后台线程中运行预测
        threading.Thread(target=run_prediction, daemon=True).start()
    
    def display_prediction_results(self, result):
        """显示预测结果"""
        self.result_text.delete(1.0, tk.END)
        
        # 基本信息
        self.result_text.insert(tk.END, "🎯 澳门六合彩预测结果\n")
        self.result_text.insert(tk.END, "=" * 50 + "\n\n")
        
        # 预测统计
        final_numbers = result['final_recommended_numbers']
        confidence = result['confidence_level']
        
        self.result_text.insert(tk.END, f"📊 预测统计:\n")
        self.result_text.insert(tk.END, f"   • 推荐号码数量: {len(final_numbers)}个\n")
        self.result_text.insert(tk.END, f"   • 整体置信度: {confidence:.1%}\n")
        self.result_text.insert(tk.END, f"   • 预测时间: {result['prediction_time']}\n\n")
        
        # 推荐号码
        self.result_text.insert(tk.END, f"🎯 推荐号码 (前12个):\n")
        for i, number in enumerate(final_numbers[:12], 1):
            score = result['number_scores'][number]
            self.result_text.insert(tk.END, f"   {i:2d}. {number:2d} (得分: {score:.3f})\n")
        
        self.result_text.insert(tk.END, f"\n📋 完整推荐列表:\n")
        self.result_text.insert(tk.END, f"   {final_numbers}\n\n")
        
        # 各模块分析结果
        analysis_results = result['analysis_results']
        self.result_text.insert(tk.END, f"📊 各模块分析结果:\n")
        
        if analysis_results.get('zodiac_analysis'):
            zodiac_result = analysis_results['zodiac_analysis']
            predicted_zodiac = zodiac_result.get('predicted_zodiac', 'N/A')
            zodiac_confidence = zodiac_result.get('confidence_level', 0)
            self.result_text.insert(tk.END, f"   🐲 生肖分析: {predicted_zodiac} (置信度: {zodiac_confidence:.1%})\n")
        
        if analysis_results.get('traditional_analysis'):
            traditional_result = analysis_results['traditional_analysis']
            traditional_numbers = traditional_result.get('recommended_numbers', [])
            traditional_confidence = traditional_result.get('confidence_level', 0)
            self.result_text.insert(tk.END, f"   📈 传统分析: {len(traditional_numbers)}个号码 (置信度: {traditional_confidence:.1%})\n")
        
        if analysis_results.get('ml_prediction'):
            ml_result = analysis_results['ml_prediction']
            ml_numbers = ml_result.get('predicted_numbers', [])
            ml_confidence = ml_result.get('confidence_level', 0)
            self.result_text.insert(tk.END, f"   🤖 机器学习: {len(ml_numbers)}个号码 (置信度: {ml_confidence:.1%})\n")
        
        # 交叉验证结果
        if 'validation_results' in result:
            validation_results = result['validation_results']
            consensus = validation_results['consensus_analysis']
            
            self.result_text.insert(tk.END, f"\n🔍 交叉验证结果:\n")
            self.result_text.insert(tk.END, f"   • 一致推荐号码: {len(consensus['common_numbers'])}个\n")
            self.result_text.insert(tk.END, f"   • 号码重叠率: {validation_results['consistency_metrics']['number_overlap_rate']:.1%}\n")
            self.result_text.insert(tk.END, f"   • 整体一致性: {validation_results['consistency_metrics']['overall_consistency']:.1%}\n")
        
        # 保存结果供导出使用
        self.last_prediction_result = result
    
def import_csv_file(self):
        """导入CSV文件"""
        file_path = filedialog.askopenfilename(
            title="选择CSV文件",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        
        if file_path:
            def import_data():
                try:
                    self.root.after(0, lambda: self.update_status(f"正在导入文件: {Path(file_path).name}"))
                    
                    # 导入CSV/Excel文件
                    from src.data_layer.data_import.csv_loader import CSVLoader
                    from src.data_layer.data_import.excel_loader import ExcelLoader
                    
                    file_extension = Path(file_path).suffix.lower()
                    
                    if file_extension == '.csv':
                        # 使用CSV加载器
                        loader = CSVLoader()
                        raw_data = loader.load_csv_file(file_path)
                    elif file_extension in ['.xlsx', '.xls']:
                        # 使用Excel加载器
                        loader = ExcelLoader()
                        raw_data = loader.load_excel_file(file_path)
                    else:
                        raise ValueError(f"不支持的文件格式: {file_extension}")
                    
                    if not raw_data:
                        self.root.after(0, lambda: messagebox.showwarning("导入警告", "文件中没有找到有效数据"))
                        return
                    
                    # 处理数据
                    self.root.after(0, lambda: self.update_status("正在处理数据..."))
                    
                    success_count = 0
                    error_count = 0
                    
                    for record in raw_data:
                        try:
                            # 使用数据处理器处理每条记录
                            processed_record = self.data_processor.process_single_record(record)
                            if processed_record:
                                success_count += 1
                            else:
                                error_count += 1
                        except Exception as e:
                            error_count += 1
                            print(f"处理记录失败: {record}, 错误: {e}")
                    
                    # 提交数据库事务
                    self.session.commit()
                    
                    # 显示导入结果
                    result_message = f"文件导入完成！\n\n"
                    result_message += f"成功导入: {success_count} 条记录\n"
                    result_message += f"失败记录: {error_count} 条\n"
                    result_message += f"文件路径: {file_path}"
                    
                    self.root.after(0, lambda: messagebox.showinfo("导入完成", result_message))
                    self.root.after(0, lambda: self.load_data_status())
                    self.root.after(0, lambda: self.update_status("文件导入完成"))
                    
                except Exception as e:
                    self.session.rollback()
                    error_msg = f"文件导入失败：{str(e)}"
                    self.root.after(0, lambda: messagebox.showerror("导入错误", error_msg))
                    self.root.after(0, lambda: self.update_status("文件导入失败"))
            
            threading.Thread(target=import_data, daemon=True).start()
    
    def manual_input_dialog(self):
        """手动输入对话框"""
        messagebox.showinfo("功能开发中", "手动输入功能正在开发中...")
    
    def export_results(self):
        """导出预测结果"""
        if not hasattr(self, 'last_prediction_result'):
            messagebox.showwarning("导出警告", "没有可导出的预测结果")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存预测结果",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                content = self.result_text.get(1.0, tk.END)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("导出成功", f"预测结果已保存到：{file_path}")
            except Exception as e:
                messagebox.showerror("导出错误", f"导出失败：{e}")
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_label.config(text=message)
    
    def run(self):
        """运行GUI应用"""
        self.root.mainloop()
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'session'):
            self.session.close()

def main():
    """主函数"""
    app = LotteryPredictionGUI()
    app.run()

if __name__ == "__main__":
    main()

