"""
澳门六合彩预测系统 - GUI启动器
"""
import sys
import os
sys.path.insert(0, 'src')

# 检查依赖
try:
    import tkinter as tk
    from tkinter import ttk, messagebox
except ImportError:
    print("错误: 缺少tkinter模块，请安装Python的tkinter支持")
    sys.exit(1)

try:
    from src.application_layer.gui.main_window.lottery_gui import LotteryPredictionGUI
except ImportError as e:
    print(f"错误: 无法导入GUI模块: {e}")
    sys.exit(1)

def main():
    """主函数"""
    try:
        print("🎯 启动澳门六合彩预测系统GUI...")
        print("=" * 50)
        
        # 检查数据库
        if not os.path.exists("data/lottery.db"):
            print("⚠️ 警告: 数据库文件不存在，系统将以演示模式运行")
        
        # 启动GUI
        app = LotteryPredictionGUI()
        print("✅ GUI界面已启动")
        app.run()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        # 显示错误对话框
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("启动错误", f"GUI启动失败：{e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
