#!/usr/bin/env python3
"""
阶段一: 算法替换实施方案
优先级最高的核心算法真实化改进
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import Counter
import sqlite3

# 尝试导入机器学习库
try:
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
    from sklearn.svm import SVC
    from sklearn.neural_network import MLPClassifier
    from sklearn.model_selection import cross_val_score, TimeSeriesSplit
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import accuracy_score, precision_score, recall_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("⚠️ scikit-learn未安装，将使用模拟实现")

try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("⚠️ XGBoost未安装，将使用RandomForest替代")

class RealMachineLearningModule:
    """真实机器学习模组 - 替换Mock实现"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.module_name = "真实机器学习模组"
        
        # 初始化模型集合
        self.models = self._initialize_models()
        self.ensemble_weights = self._calculate_ensemble_weights()
        self.scaler = StandardScaler() if SKLEARN_AVAILABLE else None
        
        # 训练状态
        self.is_trained = False
        self.training_history = []
        
        print(f"✅ {self.module_name}初始化完成")
        print(f"📊 可用模型: {list(self.models.keys())}")
    
    def _initialize_models(self) -> Dict[str, Any]:
        """初始化模型集合"""
        models = {}
        
        if SKLEARN_AVAILABLE:
            # 随机森林 - 稳定可靠
            models['random_forest'] = RandomForestClassifier(
                n_estimators=150,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
            
            # 梯度提升 - 高精度
            models['gradient_boosting'] = GradientBoostingClassifier(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            )
            
            # 支持向量机 - 非线性分类
            models['svm'] = SVC(
                kernel='rbf',
                probability=True,
                C=1.0,
                gamma='scale',
                random_state=42
            )
            
            # 神经网络 - 复杂模式
            models['neural_network'] = MLPClassifier(
                hidden_layer_sizes=(100, 50),
                activation='relu',
                solver='adam',
                alpha=0.001,
                max_iter=500,
                random_state=42
            )
        
        # XGBoost - 最强性能
        if XGBOOST_AVAILABLE:
            models['xgboost'] = xgb.XGBClassifier(
                objective='multi:softprob',
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1,
                eval_metric='mlogloss'
            )
        
        # 如果没有可用的ML库，使用简化实现
        if not models:
            models['simple_classifier'] = SimpleClassifier()
        
        return models
    
    def _calculate_ensemble_weights(self) -> Dict[str, float]:
        """计算集成权重"""
        if XGBOOST_AVAILABLE and 'xgboost' in self.models:
            return {
                'xgboost': 0.35,
                'random_forest': 0.25,
                'gradient_boosting': 0.20,
                'svm': 0.12,
                'neural_network': 0.08
            }
        elif SKLEARN_AVAILABLE:
            return {
                'random_forest': 0.35,
                'gradient_boosting': 0.30,
                'svm': 0.20,
                'neural_network': 0.15
            }
        else:
            return {'simple_classifier': 1.0}
    
    def predict(self, target_date: str, analysis_days: int = 100) -> Dict[str, Any]:
        """执行预测"""
        try:
            print(f"🤖 {self.module_name}开始预测 - 目标日期: {target_date}")
            
            # 获取历史数据
            historical_data = self._get_historical_data(target_date, analysis_days + 50)
            
            if len(historical_data) < 50:
                print(f"⚠️ 历史数据不足，使用默认预测")
                return self._get_default_prediction(target_date)
            
            # 特征工程
            features, targets = self._extract_features(historical_data)
            
            if len(features) < 30:
                print(f"⚠️ 特征数据不足，使用默认预测")
                return self._get_default_prediction(target_date)
            
            # 训练模型
            self._train_models(features, targets)
            
            # 生成预测
            prediction_features = self._generate_prediction_features(historical_data, target_date)
            predictions = self._ensemble_predict(prediction_features)
            
            # 构建结果
            result = self._build_prediction_result(predictions, len(historical_data))
            
            print(f"✅ {self.module_name}预测完成 - 推荐{len(result['predicted_numbers'])}个号码")
            return result
            
        except Exception as e:
            print(f"❌ {self.module_name}预测失败: {e}")
            return self._get_default_prediction(target_date)
    
    def _get_historical_data(self, before_date: str, days: int) -> List[Dict[str, Any]]:
        """获取历史数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT draw_date, special_number, period_number
                FROM lottery_results 
                WHERE draw_date < ? 
                ORDER BY draw_date DESC 
                LIMIT ?
            ''', (before_date, days))
            
            data = []
            for row in cursor.fetchall():
                data.append({
                    "draw_date": row[0],
                    "special_number": row[1],
                    "period_number": row[2]
                })
            
            conn.close()
            return data
            
        except Exception as e:
            print(f"获取历史数据失败: {e}")
            return []
    
    def _extract_features(self, historical_data: List[Dict]) -> tuple:
        """提取特征"""
        features = []
        targets = []
        
        window_size = 10
        
        for i in range(window_size, len(historical_data)):
            # 当前记录
            current_record = historical_data[i]
            target = current_record['special_number']
            
            # 历史窗口
            window_data = historical_data[i-window_size:i]
            
            # 提取特征向量
            feature_vector = self._generate_feature_vector(window_data, current_record)
            
            if feature_vector is not None and 1 <= target <= 49:
                features.append(feature_vector)
                targets.append(target)
        
        return np.array(features), np.array(targets)
    
    def _generate_feature_vector(self, window_data: List[Dict], current_record: Dict) -> Optional[np.ndarray]:
        """生成特征向量"""
        try:
            features = []
            
            # 提取窗口内的特码
            window_numbers = [record['special_number'] for record in window_data]
            
            # 基础统计特征
            features.extend([
                np.mean(window_numbers),
                np.std(window_numbers),
                np.min(window_numbers),
                np.max(window_numbers),
                np.median(window_numbers)
            ])
            
            # 频率特征
            number_counts = Counter(window_numbers)
            max_freq = max(number_counts.values()) if number_counts else 0
            features.append(max_freq / len(window_numbers))
            
            # 趋势特征
            if len(window_numbers) >= 3:
                x = np.arange(len(window_numbers))
                trend = np.polyfit(x, window_numbers, 1)[0]
                features.append(trend)
            else:
                features.append(0)
            
            # 大小单双特征
            big_count = sum(1 for num in window_numbers if num > 24)
            odd_count = sum(1 for num in window_numbers if num % 2 == 1)
            features.extend([
                big_count / len(window_numbers),
                odd_count / len(window_numbers)
            ])
            
            # 区间分布特征
            ranges = [0, 0, 0, 0, 0]  # 1-10, 11-20, 21-30, 31-40, 41-49
            for num in window_numbers:
                if 1 <= num <= 10:
                    ranges[0] += 1
                elif 11 <= num <= 20:
                    ranges[1] += 1
                elif 21 <= num <= 30:
                    ranges[2] += 1
                elif 31 <= num <= 40:
                    ranges[3] += 1
                elif 41 <= num <= 49:
                    ranges[4] += 1
            
            # 归一化区间分布
            total = sum(ranges)
            if total > 0:
                features.extend([r / total for r in ranges])
            else:
                features.extend([0.2] * 5)
            
            # 时间特征
            try:
                draw_date = datetime.strptime(current_record['draw_date'], '%Y-%m-%d')
                features.extend([
                    draw_date.month / 12.0,
                    draw_date.day / 31.0,
                    draw_date.weekday() / 6.0
                ])
            except:
                features.extend([0.5, 0.5, 0.5])
            
            return np.array(features, dtype=float)
            
        except Exception as e:
            print(f"特征生成失败: {e}")
            return None
    
    def _train_models(self, features: np.ndarray, targets: np.ndarray):
        """训练模型"""
        try:
            print(f"🏋️ 开始训练{len(self.models)}个模型...")
            
            # 数据预处理
            if self.scaler and SKLEARN_AVAILABLE:
                features_scaled = self.scaler.fit_transform(features)
            else:
                features_scaled = features
            
            # 训练每个模型
            for model_name, model in self.models.items():
                try:
                    if hasattr(model, 'fit'):
                        model.fit(features_scaled, targets)
                        print(f"  ✅ {model_name}训练完成")
                    else:
                        model.train(features_scaled, targets)
                        print(f"  ✅ {model_name}训练完成")
                except Exception as e:
                    print(f"  ❌ {model_name}训练失败: {e}")
            
            self.is_trained = True
            self.training_history.append({
                'timestamp': datetime.now().isoformat(),
                'samples': len(features),
                'features': features.shape[1] if len(features.shape) > 1 else 0
            })
            
        except Exception as e:
            print(f"模型训练失败: {e}")
    
    def _ensemble_predict(self, prediction_features: np.ndarray) -> Dict[int, float]:
        """集成预测"""
        if not self.is_trained:
            return {i: 1/49 for i in range(1, 50)}
        
        try:
            # 预处理特征
            if self.scaler and SKLEARN_AVAILABLE:
                features_scaled = self.scaler.transform(prediction_features.reshape(1, -1))
            else:
                features_scaled = prediction_features.reshape(1, -1)
            
            # 收集各模型预测
            model_predictions = {}
            
            for model_name, model in self.models.items():
                try:
                    if hasattr(model, 'predict_proba'):
                        proba = model.predict_proba(features_scaled)[0]
                        if hasattr(model, 'classes_'):
                            classes = model.classes_
                        else:
                            classes = list(range(1, len(proba) + 1))
                    elif hasattr(model, 'predict'):
                        prediction = model.predict(features_scaled)[0]
                        proba = np.zeros(49)
                        if 1 <= prediction <= 49:
                            proba[prediction - 1] = 1.0
                        classes = list(range(1, 50))
                    else:
                        # 简化模型
                        proba = model.predict_proba(features_scaled)
                        classes = list(range(1, 50))
                    
                    # 转换为字典格式
                    pred_dict = {}
                    for i, class_label in enumerate(classes):
                        if 1 <= class_label <= 49:
                            pred_dict[class_label] = proba[i] if i < len(proba) else 0.0
                    
                    model_predictions[model_name] = pred_dict
                    
                except Exception as e:
                    print(f"模型{model_name}预测失败: {e}")
            
            # 加权融合
            final_predictions = {i: 0.0 for i in range(1, 50)}
            total_weight = 0.0
            
            for model_name, pred_dict in model_predictions.items():
                weight = self.ensemble_weights.get(model_name, 0.0)
                for number, prob in pred_dict.items():
                    final_predictions[number] += prob * weight
                total_weight += weight
            
            # 归一化
            if total_weight > 0:
                for number in final_predictions:
                    final_predictions[number] /= total_weight
            
            return final_predictions
            
        except Exception as e:
            print(f"集成预测失败: {e}")
            return {i: 1/49 for i in range(1, 50)}
    
    def _generate_prediction_features(self, historical_data: List[Dict], target_date: str) -> np.ndarray:
        """生成预测特征"""
        try:
            # 使用最近的数据生成特征
            recent_data = historical_data[:10]
            
            # 创建虚拟当前记录
            current_record = {
                'draw_date': target_date,
                'special_number': 0,
                'period_number': ''
            }
            
            feature_vector = self._generate_feature_vector(recent_data, current_record)
            return feature_vector if feature_vector is not None else np.zeros(20)
            
        except Exception as e:
            print(f"预测特征生成失败: {e}")
            return np.zeros(20)
    
    def _build_prediction_result(self, predictions: Dict[int, float], data_count: int) -> Dict[str, Any]:
        """构建预测结果"""
        # 选择概率最高的16个号码
        sorted_predictions = sorted(predictions.items(), key=lambda x: x[1], reverse=True)
        recommended_numbers = [num for num, prob in sorted_predictions[:16]]
        
        # 计算置信度
        top_16_probs = [prob for num, prob in sorted_predictions[:16]]
        avg_top_prob = np.mean(top_16_probs)
        avg_all_prob = np.mean(list(predictions.values()))
        confidence = min(0.95, max(0.6, avg_top_prob / avg_all_prob))
        
        return {
            "predicted_numbers": recommended_numbers,
            "confidence": confidence,
            "model_info": {
                "models_used": list(self.models.keys()),
                "ensemble_weights": self.ensemble_weights,
                "training_samples": data_count,
                "is_trained": self.is_trained
            },
            "prediction_details": {
                "top_probabilities": dict(sorted_predictions[:16]),
                "prediction_method": "ensemble_ml",
                "feature_count": 20
            },
            "metadata": {
                "module_name": self.module_name,
                "prediction_time": datetime.now().isoformat(),
                "version": "2.0_real"
            }
        }
    
    def _get_default_prediction(self, target_date: str) -> Dict[str, Any]:
        """获取默认预测"""
        # 生成确定性随机号码
        import hashlib
        seed = int(hashlib.md5(target_date.encode()).hexdigest()[:8], 16) % 100000
        np.random.seed(seed)
        
        recommended_numbers = sorted(np.random.choice(range(1, 50), 16, replace=False))
        
        return {
            "predicted_numbers": recommended_numbers.tolist(),
            "confidence": 0.65,
            "model_info": {
                "models_used": ["default"],
                "ensemble_weights": {"default": 1.0},
                "training_samples": 0,
                "is_trained": False
            },
            "prediction_details": {
                "prediction_method": "default_deterministic",
                "feature_count": 0
            },
            "metadata": {
                "module_name": self.module_name,
                "prediction_time": datetime.now().isoformat(),
                "version": "2.0_real",
                "note": "数据不足，使用默认预测"
            }
        }

class SimpleClassifier:
    """简化分类器 - 当ML库不可用时使用"""
    
    def __init__(self):
        self.trained_data = None
        self.trained_targets = None
    
    def fit(self, X, y):
        """训练"""
        self.trained_data = X
        self.trained_targets = y
    
    def predict_proba(self, X):
        """预测概率"""
        if self.trained_data is None:
            return np.ones(49) / 49
        
        # 简化的KNN预测
        try:
            distances = []
            for train_sample in self.trained_data:
                dist = np.linalg.norm(X[0] - train_sample)
                distances.append(dist)
            
            # 找到最近的5个样本
            nearest_indices = np.argsort(distances)[:5]
            nearest_targets = [self.trained_targets[i] for i in nearest_indices]
            
            # 计算概率分布
            proba = np.zeros(49)
            for target in nearest_targets:
                if 1 <= target <= 49:
                    proba[target - 1] += 1
            
            proba = proba / np.sum(proba) if np.sum(proba) > 0 else np.ones(49) / 49
            return proba
            
        except:
            return np.ones(49) / 49

def test_real_ml_module():
    """测试真实ML模组"""
    print("🧪 测试真实机器学习模组")
    print("=" * 50)
    
    try:
        # 初始化模组
        ml_module = RealMachineLearningModule()
        
        # 执行预测测试
        target_date = "2025-06-25"
        result = ml_module.predict(target_date, 100)
        
        print(f"\n📊 预测结果:")
        print(f"推荐号码: {result['predicted_numbers']}")
        print(f"置信度: {result['confidence']:.1%}")
        print(f"使用模型: {result['model_info']['models_used']}")
        print(f"训练状态: {result['model_info']['is_trained']}")
        print(f"预测方法: {result['prediction_details']['prediction_method']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_real_ml_module()
    print(f"\n{'✅ 测试通过' if success else '❌ 测试失败'}")
