
# 最优模式选择器使用指南

## 功能概述
最优模式选择器通过多次运行增强回测，找到表现最佳的参数配置，然后应用到完美预测系统中。

## 使用步骤

### 1. 启动GUI界面
```bash
python lottery_prediction_gui.py
```

### 2. 切换到增强回测标签页
- 点击"🔄 增强回测"标签

### 3. 设置回测参数
- **开始日期**: 建议使用 2025-01-01
- **结束日期**: 建议使用 2025-06-21 (有真实数据的日期范围)
- **训练窗口**: 建议 30-50 天
- **回测次数**: 建议 10-20 次 (次数越多越准确，但耗时更长)

### 4. 运行最优模式选择
- 点击"🎯 运行最优模式选择"按钮
- 系统将运行多次增强回测
- 观察进度条和状态信息

### 5. 查看选择结果
- 在"📋 综合报告"标签页查看最优模式详情
- 包括最优命中率、性能评分、改进效果等

### 6. 应用最优模式
- 点击"✨ 应用最优模式到完美预测"按钮
- 确认应用操作
- 系统将保存最优配置到文件

### 7. 使用优化后的完美预测
- 切换到"完美预测"标签页
- 运行完美预测系统
- 系统将使用优化后的参数

## 技术原理

### 多次回测策略
- 每次回测使用不同的随机种子和参数
- 生成不同的模组权重和策略参数
- 评估各种参数组合的表现

### 评分机制
- **命中率**: 特码预测的准确性
- **稳定性**: 预测结果的一致性
- **模组平衡**: 各模组表现的均衡性
- **综合评分**: 多维度指标的加权平均

### 参数优化
- **传统分析**: 调整热号偏向、模式敏感度
- **机器学习**: 调整学习率、特征重要性
- **生肖扩展**: 调整生肖周期权重、季节因子
- **融合策略**: 调整共识阈值、多样性奖励

## 预期效果

### 性能提升
- 相比随机参数，命中率提升 5-15%
- 预测稳定性提升 10-20%
- 各模组表现更加均衡

### 适应性增强
- 根据历史数据自动调整参数
- 适应不同时期的开奖规律
- 提高预测的鲁棒性

## 注意事项

### 数据要求
- 需要足够的历史数据 (建议至少100天)
- 数据质量影响优化效果
- 真实数据优于模拟数据

### 计算时间
- 回测次数越多，耗时越长
- 建议在性能较好的机器上运行
- 可以先用较少次数测试

### 结果解读
- 命中率提升有限是正常的 (彩票本身随机性很强)
- 重点关注稳定性和一致性的提升
- 多次运行可能得到不同的最优配置

---
生成时间: 2025-06-22 18:50:59
