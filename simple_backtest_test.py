#!/usr/bin/env python3
"""
简化的回测功能测试
快速验证增强回测系统的核心功能
"""

import sys
from datetime import datetime

def test_enhanced_hit_rate_optimizer():
    """测试增强命中率优化器"""
    print("🎯 测试增强命中率优化器")
    print("-" * 50)
    
    try:
        from enhanced_hit_rate_optimizer import EnhancedHitRateOptimizer
        
        # 初始化优化器
        optimizer = EnhancedHitRateOptimizer()
        print("✅ 增强命中率优化器初始化成功")
        
        # 测试简单回测
        start_date = "2025-01-01"
        end_date = "2025-01-05"
        iterations = 2
        
        print(f"🔧 开始增强回测: {start_date} 到 {end_date}, {iterations}次迭代")
        
        result = optimizer.run_enhanced_backtest(start_date, end_date, iterations)
        
        if result and "best_config" in result:
            best_hit_rate = result.get("best_hit_rate", 0)
            iteration_results = result.get("iteration_results", [])
            
            print(f"✅ 增强回测完成")
            print(f"📊 最佳命中率: {best_hit_rate:.1%}")
            print(f"📊 迭代次数: {len(iteration_results)}")
            
            return True, {
                "success": True,
                "best_hit_rate": best_hit_rate,
                "iterations": len(iteration_results)
            }
        else:
            print(f"❌ 增强回测结果格式异常")
            return False, {"error": "结果格式异常"}
            
    except Exception as e:
        print(f"❌ 增强命中率优化器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, {"error": str(e)}

def test_historical_simulation_predictor():
    """测试历史模拟预测器"""
    print("\n📊 测试历史模拟预测器")
    print("-" * 50)
    
    try:
        from src.historical_simulation_predictor import HistoricalSimulationPredictor
        
        # 初始化预测器
        predictor = HistoricalSimulationPredictor()
        print("✅ 历史模拟预测器初始化成功")
        
        # 测试历史模拟
        start_date = "2025-01-01"
        end_date = "2025-01-10"  # 扩大日期范围
        
        print(f"🔧 开始历史模拟: {start_date} 到 {end_date}")
        
        result = predictor.run_historical_simulation(start_date, end_date)
        
        if result and "simulation_results" in result:
            simulation_results = result.get("simulation_results", [])
            overall_hit_rate = result.get("overall_hit_rate", 0)
            
            print(f"✅ 历史模拟完成")
            print(f"📊 模拟结果数: {len(simulation_results)}")
            print(f"📊 整体命中率: {overall_hit_rate:.1%}")
            
            return True, {
                "success": True,
                "simulation_count": len(simulation_results),
                "overall_hit_rate": overall_hit_rate
            }
        else:
            print(f"❌ 历史模拟结果格式异常")
            print(f"结果: {result}")
            return False, {"error": "结果格式异常"}
            
    except Exception as e:
        print(f"❌ 历史模拟预测器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, {"error": str(e)}

def test_backtest_data_availability():
    """测试回测数据可用性"""
    print("\n📊 测试回测数据可用性")
    print("-" * 50)
    
    try:
        import sqlite3
        
        # 连接数据库
        conn = sqlite3.connect("data/lottery.db")
        cursor = conn.cursor()
        
        # 检查数据表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        
        print(f"✅ 数据库连接成功")
        print(f"📊 数据表数量: {len(tables)}")
        
        # 检查主要数据表
        if "lottery_results" in table_names:
            cursor.execute("SELECT COUNT(*) FROM lottery_results")
            total_records = cursor.fetchone()[0]
            
            cursor.execute("SELECT MIN(draw_date), MAX(draw_date) FROM lottery_results")
            date_range = cursor.fetchone()
            
            print(f"✅ lottery_results表: {total_records}条记录")
            print(f"📊 日期范围: {date_range[0]} 到 {date_range[1]}")
            
            # 检查2025年数据
            cursor.execute("SELECT COUNT(*) FROM lottery_results WHERE draw_date >= '2025-01-01'")
            recent_records = cursor.fetchone()[0]
            
            print(f"📊 2025年数据: {recent_records}条")
            
            conn.close()
            
            return True, {
                "success": True,
                "total_records": total_records,
                "recent_records": recent_records,
                "date_range": date_range
            }
        else:
            print(f"❌ lottery_results表不存在")
            conn.close()
            return False, {"error": "主数据表不存在"}
            
    except Exception as e:
        print(f"❌ 回测数据检查失败: {e}")
        return False, {"error": str(e)}

def test_backtest_configuration():
    """测试回测配置"""
    print("\n⚙️ 测试回测配置")
    print("-" * 50)
    
    try:
        import os
        import json
        
        config_files = [
            "optimization_config.json",
            "optimal_config.json"
        ]
        
        config_status = {}
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    
                    config_status[config_file] = {
                        "exists": True,
                        "valid_json": True,
                        "keys": list(config_data.keys()),
                        "size": len(config_data)
                    }
                    print(f"✅ {config_file}: 存在且有效 ({len(config_data)}个配置项)")
                    
                except json.JSONDecodeError:
                    config_status[config_file] = {
                        "exists": True,
                        "valid_json": False,
                        "error": "JSON格式错误"
                    }
                    print(f"❌ {config_file}: JSON格式错误")
            else:
                config_status[config_file] = {"exists": False}
                print(f"⚠️ {config_file}: 文件不存在")
        
        return True, config_status
        
    except Exception as e:
        print(f"❌ 回测配置检查失败: {e}")
        return False, {"error": str(e)}

def generate_simple_backtest_report(test_results):
    """生成简化回测报告"""
    print("\n" + "=" * 60)
    print("📊 简化回测功能测试报告")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 统计测试结果
    total_tests = len(test_results)
    successful_tests = sum(1 for result in test_results.values() if result.get("success", False))
    
    print(f"📊 测试统计:")
    print(f"  总测试项: {total_tests}")
    print(f"  成功项目: {successful_tests}")
    print(f"  成功率: {successful_tests/total_tests:.1%}" if total_tests > 0 else "  成功率: 0%")
    print()
    
    # 详细结果
    print("📋 详细测试结果:")
    for test_name, result in test_results.items():
        status = "✅" if result.get("success", False) else "❌"
        print(f"  {status} {test_name}")
        
        if result.get("success", False):
            if "best_hit_rate" in result:
                print(f"    最佳命中率: {result['best_hit_rate']:.1%}")
            if "simulation_count" in result:
                print(f"    模拟次数: {result['simulation_count']}")
            if "total_records" in result:
                print(f"    数据记录: {result['total_records']}条")
    
    print()
    
    # 系统状态评估
    if successful_tests == total_tests:
        status = "🎊 优秀"
        description = "所有回测功能正常运行"
    elif successful_tests >= total_tests * 0.75:
        status = "👍 良好"
        description = "大部分回测功能正常"
    elif successful_tests >= total_tests * 0.5:
        status = "⚠️ 一般"
        description = "部分回测功能存在问题"
    else:
        status = "❌ 较差"
        description = "多个回测功能需要修复"
    
    print(f"🏥 回测系统状态: {status}")
    print(f"   {description}")
    print()
    
    # 功能可用性
    enhanced_optimizer = test_results.get("enhanced_hit_rate_optimizer", {}).get("success", False)
    historical_simulation = test_results.get("historical_simulation_predictor", {}).get("success", False)
    data_availability = test_results.get("backtest_data_availability", {}).get("success", False)
    
    print("🔍 功能可用性:")
    print(f"  增强命中率优化器: {'✅ 可用' if enhanced_optimizer else '❌ 不可用'}")
    print(f"  历史模拟预测器: {'✅ 可用' if historical_simulation else '❌ 不可用'}")
    print(f"  回测数据: {'✅ 可用' if data_availability else '❌ 不可用'}")
    print()
    
    # 建议
    print("💡 建议:")
    if successful_tests == total_tests:
        print("  • 所有回测功能正常，可以进行回测分析")
        print("  • 建议定期使用回测验证预测效果")
        print("  • 可以尝试更长期的回测来优化参数")
    else:
        failed_tests = [name for name, result in test_results.items() if not result.get("success", False)]
        if failed_tests:
            print(f"  • 需要修复: {', '.join(failed_tests)}")
        print("  • 建议检查相关模块的依赖和配置")
    
    print()
    print("🎯 总结:")
    print("  简化回测功能测试完成")
    print(f"  系统状态: {status.split()[1]}")
    if successful_tests >= total_tests * 0.75:
        print("  回测系统基本可用")
    else:
        print("  回测系统需要进一步修复")

def main():
    """主测试函数"""
    print("🎊 简化回测功能测试开始")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_results = {}
    
    try:
        # 测试增强命中率优化器
        success1, result1 = test_enhanced_hit_rate_optimizer()
        test_results["enhanced_hit_rate_optimizer"] = result1
        
        # 测试历史模拟预测器
        success2, result2 = test_historical_simulation_predictor()
        test_results["historical_simulation_predictor"] = result2
        
        # 测试回测数据可用性
        success3, result3 = test_backtest_data_availability()
        test_results["backtest_data_availability"] = result3
        
        # 测试回测配置
        success4, result4 = test_backtest_configuration()
        test_results["backtest_configuration"] = result4
        
        # 生成测试报告
        generate_simple_backtest_report(test_results)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
