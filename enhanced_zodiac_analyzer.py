"""
增强多维生肖扩展分析工具
将五行、春夏秋冬等维度转化为实用的冷热、平衡、交叉分析工具
"""

import numpy as np
from datetime import datetime, date
from typing import Dict, List, Any, Tuple
from collections import Counter, defaultdict
import sqlite3
import json

class EnhancedZodiacAnalyzer:
    """增强的多维生肖分析器"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.zodiac_names = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
        
        # 初始化多维度分析配置
        self.init_zodiac_dimensions()
        self.init_analysis_weights()
        self.init_seasonal_mapping()
        
    def init_zodiac_dimensions(self):
        """初始化生肖多维度分类"""
        self.zodiac_dimensions = {
            "五行": {
                "金": ["猴", "鸡"],
                "木": ["虎", "兔"], 
                "水": ["鼠", "猪"],
                "火": ["马", "蛇"],
                "土": ["牛", "龙", "羊", "狗"]
            },
            "四季": {
                "春": ["虎", "兔", "龙"],
                "夏": ["蛇", "马", "羊"],
                "秋": ["猴", "鸡", "狗"],
                "冬": ["猪", "鼠", "牛"]
            },
            "波色": {
                "红肖": ["鼠", "兔", "马", "鸡"],
                "绿肖": ["牛", "龙", "羊", "狗"],
                "蓝肖": ["虎", "蛇", "猴", "猪"]
            },
            "阴阳": {
                "阳肖": ["鼠", "虎", "龙", "马", "猴", "狗"],
                "阴肖": ["牛", "兔", "蛇", "羊", "鸡", "猪"]
            },
            "昼夜": {
                "日肖": ["兔", "龙", "蛇", "马", "羊", "猴"],
                "夜肖": ["鼠", "牛", "虎", "鸡", "狗", "猪"]
            },
            "家野": {
                "家肖": ["牛", "马", "羊", "狗", "鸡", "猪"],
                "野肖": ["鼠", "虎", "兔", "龙", "蛇", "猴"]
            }
        }
        
    def init_analysis_weights(self):
        """初始化分析权重"""
        self.analysis_weights = {
            "冷热分析": 0.30,    # 冷热号分析
            "平衡分析": 0.25,    # 维度平衡分析
            "交叉分析": 0.20,    # 多维度交叉分析
            "季节分析": 0.15,    # 季节性分析
            "周期分析": 0.10     # 周期性分析
        }
        
    def init_seasonal_mapping(self):
        """初始化季节映射"""
        self.seasonal_mapping = {
            1: "冬", 2: "冬", 3: "春",
            4: "春", 5: "春", 6: "夏", 
            7: "夏", 8: "夏", 9: "秋",
            10: "秋", 11: "秋", 12: "冬"
        }
        
    def comprehensive_analysis(self, target_date: str, analysis_days: int = 100) -> Dict[str, Any]:
        """综合多维度分析"""
        print(f"🔍 开始增强多维生肖分析 - 目标日期: {target_date}")
        
        # 获取历史数据
        historical_data = self._get_historical_data(target_date, analysis_days)
        
        if len(historical_data) < 30:
            return self._get_default_result()
            
        # 执行各种分析
        analysis_results = {}
        
        # 1. 冷热分析
        analysis_results["冷热分析"] = self._hot_cold_analysis(historical_data)
        
        # 2. 平衡分析
        analysis_results["平衡分析"] = self._balance_analysis(historical_data)
        
        # 3. 交叉分析
        analysis_results["交叉分析"] = self._cross_analysis(historical_data)
        
        # 4. 季节分析
        analysis_results["季节分析"] = self._seasonal_analysis(historical_data, target_date)
        
        # 5. 周期分析
        analysis_results["周期分析"] = self._cycle_analysis(historical_data)
        
        # 综合评分
        final_scores = self._calculate_comprehensive_scores(analysis_results)
        
        # 选择推荐生肖
        recommended_zodiacs = self._select_recommended_zodiacs(final_scores)
        
        # 生成分析报告
        analysis_report = self._generate_analysis_report(analysis_results, final_scores)
        
        return {
            "recommended_zodiacs": recommended_zodiacs,
            "zodiac_scores": final_scores,
            "analysis_details": analysis_results,
            "analysis_report": analysis_report,
            "confidence": self._calculate_confidence(final_scores, recommended_zodiacs),
            "method": "增强多维生肖分析",
            "analysis_date": target_date,
            "data_periods": len(historical_data)
        }
        
    def _hot_cold_analysis(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """冷热分析：识别冷门和热门生肖"""
        # 统计生肖出现频率
        zodiac_counts = Counter([record['special_zodiac'] for record in historical_data])
        total_count = len(historical_data)
        
        # 计算频率
        zodiac_frequencies = {}
        for zodiac in self.zodiac_names:
            count = zodiac_counts.get(zodiac, 0)
            zodiac_frequencies[zodiac] = count / total_count if total_count > 0 else 0
            
        # 理论平均频率
        avg_frequency = 1 / 12
        
        # 分类冷热生肖
        hot_zodiacs = []    # 热门生肖
        cold_zodiacs = []   # 冷门生肖
        normal_zodiacs = [] # 正常生肖
        
        hot_cold_scores = {}
        
        for zodiac, frequency in zodiac_frequencies.items():
            if frequency > avg_frequency * 1.4:  # 热门
                hot_zodiacs.append(zodiac)
                # 热门生肖给予较低分数（回避）
                hot_cold_scores[zodiac] = 0.3
            elif frequency < avg_frequency * 0.6:  # 冷门
                cold_zodiacs.append(zodiac)
                # 冷门生肖给予较高分数（回补理论）
                hot_cold_scores[zodiac] = 0.8
            else:  # 正常
                normal_zodiacs.append(zodiac)
                hot_cold_scores[zodiac] = 0.5
                
        return {
            "frequencies": zodiac_frequencies,
            "hot_zodiacs": hot_zodiacs,
            "cold_zodiacs": cold_zodiacs,
            "normal_zodiacs": normal_zodiacs,
            "scores": hot_cold_scores,
            "analysis_type": "冷热分析"
        }
        
    def _balance_analysis(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """平衡分析：分析各维度的平衡性"""
        balance_scores = {zodiac: 0.5 for zodiac in self.zodiac_names}
        dimension_stats = {}
        
        # 分析最近期数的维度分布
        recent_window = min(20, len(historical_data))
        recent_data = historical_data[:recent_window]
        
        for dimension_name, categories in self.zodiac_dimensions.items():
            dimension_stats[dimension_name] = {}
            
            # 统计各类别的出现次数
            for category_name, zodiac_list in categories.items():
                count = sum(1 for record in recent_data 
                           if record['special_zodiac'] in zodiac_list)
                dimension_stats[dimension_name][category_name] = {
                    "count": count,
                    "frequency": count / recent_window if recent_window > 0 else 0,
                    "zodiacs": zodiac_list
                }
                
        # 计算平衡得分
        for zodiac in self.zodiac_names:
            balance_score = 0.5
            
            for dimension_name, categories in self.zodiac_dimensions.items():
                for category_name, zodiac_list in categories.items():
                    if zodiac in zodiac_list:
                        category_stats = dimension_stats[dimension_name][category_name]
                        expected_frequency = 1 / len(categories)  # 期望频率
                        actual_frequency = category_stats["frequency"]
                        
                        # 如果该类别出现不足，给予加分
                        if actual_frequency < expected_frequency * 0.7:
                            balance_score += 0.1
                        # 如果该类别出现过多，给予减分
                        elif actual_frequency > expected_frequency * 1.3:
                            balance_score -= 0.1
                            
            balance_scores[zodiac] = max(0.1, min(0.9, balance_score))
            
        return {
            "dimension_stats": dimension_stats,
            "scores": balance_scores,
            "analysis_window": recent_window,
            "analysis_type": "平衡分析"
        }
        
    def _cross_analysis(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """交叉分析：多维度交叉验证"""
        cross_scores = {zodiac: 0.5 for zodiac in self.zodiac_names}
        cross_patterns = {}
        
        # 分析最近几期的多维度模式
        recent_window = min(10, len(historical_data))
        recent_data = historical_data[:recent_window]
        
        # 统计各维度组合的出现情况
        dimension_combinations = {}
        
        for record in recent_data:
            zodiac = record['special_zodiac']
            
            # 找出该生肖所属的各个维度类别
            zodiac_dimensions = {}
            for dim_name, categories in self.zodiac_dimensions.items():
                for cat_name, zodiac_list in categories.items():
                    if zodiac in zodiac_list:
                        zodiac_dimensions[dim_name] = cat_name
                        break
                        
            # 记录维度组合
            combination_key = tuple(sorted(zodiac_dimensions.items()))
            if combination_key not in dimension_combinations:
                dimension_combinations[combination_key] = 0
            dimension_combinations[combination_key] += 1
            
        # 为每个生肖计算交叉得分
        for zodiac in self.zodiac_names:
            # 获取该生肖的维度组合
            zodiac_dims = {}
            for dim_name, categories in self.zodiac_dimensions.items():
                for cat_name, zodiac_list in categories.items():
                    if zodiac in zodiac_list:
                        zodiac_dims[dim_name] = cat_name
                        break
                        
            combination_key = tuple(sorted(zodiac_dims.items()))
            combination_count = dimension_combinations.get(combination_key, 0)
            
            # 基于组合出现频率调整得分
            if combination_count == 0:  # 该组合未出现
                cross_scores[zodiac] = 0.7  # 给予较高分数
            elif combination_count >= 3:  # 该组合出现频繁
                cross_scores[zodiac] = 0.3  # 给予较低分数
            else:
                cross_scores[zodiac] = 0.5  # 正常分数
                
        cross_patterns["dimension_combinations"] = dimension_combinations
        cross_patterns["recent_window"] = recent_window
        
        return {
            "scores": cross_scores,
            "patterns": cross_patterns,
            "analysis_type": "交叉分析"
        }

    def _seasonal_analysis(self, historical_data: List[Dict], target_date: str) -> Dict[str, Any]:
        """季节分析：基于当前季节的生肖预测"""
        seasonal_scores = {zodiac: 0.5 for zodiac in self.zodiac_names}

        # 获取目标日期的月份
        target_month = int(target_date.split('-')[1])
        current_season = self.seasonal_mapping[target_month]

        # 统计各季节生肖的历史表现
        seasonal_stats = {}
        for season, zodiac_list in self.zodiac_dimensions["四季"].items():
            seasonal_stats[season] = {
                "zodiacs": zodiac_list,
                "recent_count": 0,
                "total_count": 0
            }

        # 统计最近期数各季节的出现情况
        recent_window = min(20, len(historical_data))
        for record in historical_data[:recent_window]:
            zodiac = record['special_zodiac']
            for season, zodiac_list in self.zodiac_dimensions["四季"].items():
                if zodiac in zodiac_list:
                    seasonal_stats[season]["recent_count"] += 1
                    break

        # 统计总体各季节的出现情况
        for record in historical_data:
            zodiac = record['special_zodiac']
            for season, zodiac_list in self.zodiac_dimensions["四季"].items():
                if zodiac in zodiac_list:
                    seasonal_stats[season]["total_count"] += 1
                    break

        # 计算季节得分
        current_season_zodiacs = self.zodiac_dimensions["四季"][current_season]
        current_season_recent = seasonal_stats[current_season]["recent_count"]
        expected_recent = recent_window / 4  # 期望值

        for zodiac in self.zodiac_names:
            if zodiac in current_season_zodiacs:
                # 当前季节生肖
                if current_season_recent < expected_recent * 0.8:
                    # 当前季节生肖出现不足，给予加分
                    seasonal_scores[zodiac] = 0.7
                else:
                    seasonal_scores[zodiac] = 0.5
            else:
                # 非当前季节生肖，给予较低分数
                seasonal_scores[zodiac] = 0.4

        return {
            "current_season": current_season,
            "seasonal_stats": seasonal_stats,
            "scores": seasonal_scores,
            "target_month": target_month,
            "analysis_type": "季节分析"
        }

    def _cycle_analysis(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """周期分析：基于生肖出现的周期性"""
        cycle_scores = {zodiac: 0.5 for zodiac in self.zodiac_names}
        cycle_patterns = {}

        for zodiac in self.zodiac_names:
            # 找到该生肖的所有出现位置
            positions = []
            for i, record in enumerate(historical_data):
                if record['special_zodiac'] == zodiac:
                    positions.append(i)

            if not positions:
                cycle_scores[zodiac] = 0.6  # 从未出现，给予较高分数
                cycle_patterns[zodiac] = {
                    "last_position": None,
                    "intervals": [],
                    "avg_interval": None,
                    "should_appear": True
                }
                continue

            # 计算间隔
            intervals = []
            for i in range(1, len(positions)):
                intervals.append(positions[i-1] - positions[i])

            last_position = positions[0]  # 最近出现位置

            if intervals:
                avg_interval = np.mean(intervals)

                # 基于平均间隔预测
                if last_position >= avg_interval * 0.8:
                    cycle_scores[zodiac] = 0.8  # 接近预期出现时间
                elif last_position < avg_interval * 0.3:
                    cycle_scores[zodiac] = 0.2  # 刚刚出现过
                else:
                    cycle_scores[zodiac] = 0.5  # 正常
            else:
                # 只出现过一次
                if last_position > 10:
                    cycle_scores[zodiac] = 0.7  # 很久没出现
                else:
                    cycle_scores[zodiac] = 0.3  # 最近出现过

            cycle_patterns[zodiac] = {
                "last_position": last_position,
                "intervals": intervals,
                "avg_interval": np.mean(intervals) if intervals else None,
                "should_appear": last_position > 8
            }

        return {
            "scores": cycle_scores,
            "patterns": cycle_patterns,
            "analysis_type": "周期分析"
        }

    def _calculate_comprehensive_scores(self, analysis_results: Dict[str, Dict]) -> Dict[str, float]:
        """计算综合得分"""
        final_scores = {zodiac: 0.0 for zodiac in self.zodiac_names}

        for zodiac in self.zodiac_names:
            total_score = 0.0

            for analysis_type, weight in self.analysis_weights.items():
                if analysis_type in analysis_results:
                    method_score = analysis_results[analysis_type]["scores"].get(zodiac, 0.5)
                    total_score += method_score * weight

            final_scores[zodiac] = total_score

        return final_scores

    def _select_recommended_zodiacs(self, scores: Dict[str, float], count: int = 4) -> List[str]:
        """选择推荐的生肖"""
        sorted_zodiacs = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        return [zodiac for zodiac, score in sorted_zodiacs[:count]]

    def _calculate_confidence(self, scores: Dict[str, float], recommended: List[str]) -> float:
        """计算预测置信度"""
        if not recommended:
            return 0.0

        top_scores = [scores[zodiac] for zodiac in recommended]
        avg_top_score = np.mean(top_scores)

        all_scores = list(scores.values())
        avg_all_score = np.mean(all_scores)

        # 置信度基于推荐生肖与平均分的差距
        confidence = min(0.95, max(0.1, (avg_top_score - avg_all_score) * 3 + 0.5))
        return confidence

    def _generate_analysis_report(self, analysis_results: Dict, final_scores: Dict[str, float]) -> Dict[str, Any]:
        """生成详细的分析报告"""
        report = {
            "summary": {},
            "detailed_analysis": {},
            "recommendations": {}
        }

        # 汇总信息
        report["summary"] = {
            "total_analysis_methods": len(analysis_results),
            "analysis_methods": list(analysis_results.keys()),
            "top_zodiac": max(final_scores.items(), key=lambda x: x[1])[0],
            "top_score": max(final_scores.values()),
            "score_range": {
                "min": min(final_scores.values()),
                "max": max(final_scores.values()),
                "avg": np.mean(list(final_scores.values()))
            }
        }

        # 详细分析
        for analysis_type, results in analysis_results.items():
            if analysis_type == "冷热分析":
                report["detailed_analysis"]["冷热分析"] = {
                    "热门生肖": results["hot_zodiacs"],
                    "冷门生肖": results["cold_zodiacs"],
                    "正常生肖": results["normal_zodiacs"],
                    "建议": "优先考虑冷门生肖，回避热门生肖"
                }
            elif analysis_type == "平衡分析":
                report["detailed_analysis"]["平衡分析"] = {
                    "分析窗口": results["analysis_window"],
                    "维度统计": results["dimension_stats"],
                    "建议": "选择维度分布不均衡的生肖"
                }
            elif analysis_type == "季节分析":
                report["detailed_analysis"]["季节分析"] = {
                    "当前季节": results["current_season"],
                    "目标月份": results["target_month"],
                    "季节统计": results["seasonal_stats"],
                    "建议": f"当前{results['current_season']}季，优先考虑{results['current_season']}季生肖"
                }

        # 推荐策略
        sorted_scores = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        report["recommendations"] = {
            "强烈推荐": [zodiac for zodiac, score in sorted_scores[:2]],
            "一般推荐": [zodiac for zodiac, score in sorted_scores[2:4]],
            "备选推荐": [zodiac for zodiac, score in sorted_scores[4:6]],
            "不推荐": [zodiac for zodiac, score in sorted_scores[-3:]]
        }

        return report

    def _get_historical_data(self, target_date: str, analysis_days: int) -> List[Dict]:
        """获取历史数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 查询历史数据
            query = """
            SELECT period_number, draw_date, special_number, special_zodiac, lunar_year,
                   zodiac_color_wave, zodiac_season, zodiac_day_night, zodiac_yin_yang
            FROM lottery_data
            WHERE draw_date < ?
            ORDER BY draw_date DESC
            LIMIT ?
            """

            cursor.execute(query, (target_date, analysis_days))
            records = cursor.fetchall()

            historical_data = []
            for record in records:
                historical_data.append({
                    'period_number': record[0],
                    'draw_date': record[1],
                    'special_number': record[2],
                    'special_zodiac': record[3],
                    'lunar_year': record[4],
                    'zodiac_color_wave': record[5],
                    'zodiac_season': record[6],
                    'zodiac_day_night': record[7],
                    'zodiac_yin_yang': record[8]
                })

            conn.close()
            return historical_data

        except Exception as e:
            print(f"获取历史数据失败: {e}")
            return []

    def _get_default_result(self) -> Dict[str, Any]:
        """获取默认结果（数据不足时）"""
        default_zodiacs = ["龙", "虎", "马", "鸡"]  # 传统吉祥生肖

        return {
            "recommended_zodiacs": default_zodiacs,
            "zodiac_scores": {zodiac: 0.5 for zodiac in self.zodiac_names},
            "analysis_details": {},
            "analysis_report": {
                "summary": {"note": "数据不足，使用默认推荐"},
                "detailed_analysis": {},
                "recommendations": {"默认推荐": default_zodiacs}
            },
            "confidence": 0.3,
            "method": "默认推荐（数据不足）",
            "analysis_date": "",
            "data_periods": 0
        }

    def get_dimension_analysis(self, target_date: str, dimension: str = "五行") -> Dict[str, Any]:
        """获取特定维度的详细分析"""
        if dimension not in self.zodiac_dimensions:
            return {"error": f"不支持的维度: {dimension}"}

        historical_data = self._get_historical_data(target_date, 50)

        if not historical_data:
            return {"error": "无法获取历史数据"}

        dimension_categories = self.zodiac_dimensions[dimension]
        analysis_result = {}

        # 统计各类别的出现情况
        for category, zodiac_list in dimension_categories.items():
            category_count = sum(1 for record in historical_data[:20]
                               if record['special_zodiac'] in zodiac_list)

            analysis_result[category] = {
                "zodiacs": zodiac_list,
                "recent_count": category_count,
                "frequency": category_count / 20 if len(historical_data) >= 20 else 0,
                "status": self._get_category_status(category_count, 20, len(dimension_categories))
            }

        return {
            "dimension": dimension,
            "analysis": analysis_result,
            "recommendation": self._get_dimension_recommendation(analysis_result),
            "analysis_date": target_date
        }

    def _get_category_status(self, count: int, total: int, categories_num: int) -> str:
        """获取类别状态"""
        expected = total / categories_num

        if count < expected * 0.7:
            return "冷门"
        elif count > expected * 1.3:
            return "热门"
        else:
            return "正常"

    def _get_dimension_recommendation(self, analysis: Dict) -> List[str]:
        """获取维度推荐"""
        cold_categories = [cat for cat, data in analysis.items()
                          if data["status"] == "冷门"]

        recommended_zodiacs = []
        for category in cold_categories:
            recommended_zodiacs.extend(analysis[category]["zodiacs"])

        return recommended_zodiacs[:4]  # 最多推荐4个
