#!/usr/bin/env python3
"""
详细模组功能测试
测试每个分析模组的具体功能和预测能力
"""

import sys
from datetime import datetime
from typing import Dict, List, Any

class DetailedModuleFunctionalityTester:
    """详细模组功能测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.target_date = "2025-06-25"
        
    def run_detailed_tests(self):
        """运行详细测试"""
        print("🧪 详细模组功能测试")
        print("=" * 60)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"目标日期: {self.target_date}")
        print()
        
        # 测试核心预测模组
        self.test_core_prediction_modules()
        
        # 测试独立分析模组
        self.test_independent_analysis_modules()
        
        # 测试优化增强模组
        self.test_optimization_modules()
        
        # 测试系统集成模组
        self.test_integration_modules()
        
        # 生成详细报告
        self.generate_detailed_report()
    
    def test_core_prediction_modules(self):
        """测试核心预测模组"""
        print("🎯 核心预测模组测试")
        print("-" * 40)
        
        # 测试特码预测系统
        print("1. 特码预测系统:")
        try:
            from src.lottery_prediction_system import LotteryPredictionSystem
            
            system = LotteryPredictionSystem()
            
            # 测试特码预测
            special_result = system.predict_special_number(self.target_date)
            numbers = special_result.get("recommended_numbers", [])
            confidence = special_result.get("confidence", 0)
            
            print(f"   ✅ 特码预测: {len(numbers)}个号码, 置信度{confidence:.1%}")
            
            # 测试生肖预测
            zodiac_result = system.predict_zodiac(self.target_date)
            zodiacs = zodiac_result.get("recommended_zodiacs", [])
            
            print(f"   ✅ 生肖预测: {len(zodiacs)}个生肖")
            
            self.test_results["特码预测系统"] = {
                "status": "success",
                "special_numbers": len(numbers),
                "zodiacs": len(zodiacs),
                "confidence": confidence
            }
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            self.test_results["特码预测系统"] = {"status": "failed", "error": str(e)}
        
        # 测试一致性预测器
        print("2. 一致性预测器:")
        try:
            from src.consistency_predictor import ConsistencyPredictor
            
            predictor = ConsistencyPredictor()
            
            # 测试一致性预测
            result = predictor.predict_with_consistency(self.target_date)
            numbers = result.get("recommended_numbers", [])
            zodiacs = result.get("recommended_zodiacs", [])
            confidence = result.get("confidence", 0)
            
            print(f"   ✅ 一致性预测: {len(numbers)}个号码, {len(zodiacs)}个生肖")
            print(f"   ✅ 置信度: {confidence:.1%}")
            
            # 测试一致性验证
            verification = predictor.verify_consistency(self.target_date, 3)
            consistency = verification.get("overall_consistent", False)
            
            print(f"   ✅ 一致性验证: {'通过' if consistency else '失败'}")
            
            self.test_results["一致性预测器"] = {
                "status": "success",
                "numbers": len(numbers),
                "zodiacs": len(zodiacs),
                "confidence": confidence,
                "consistency": consistency
            }
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            self.test_results["一致性预测器"] = {"status": "failed", "error": str(e)}
        
        # 测试完美预测系统
        print("3. 完美预测系统:")
        try:
            from src.perfect_prediction_system import PerfectPredictionSystem
            
            system = PerfectPredictionSystem()
            system.initialize_modules()
            
            # 测试完整预测
            result = system.run_complete_prediction(self.target_date)
            
            if result and "final_results" in result:
                final_results = result["final_results"]
                numbers = final_results.get("recommended_16_numbers", [])
                zodiacs = final_results.get("recommended_4_zodiacs", [])
                confidence = final_results.get("overall_confidence", 0)
                stability = final_results.get("prediction_stability", 0)
                
                print(f"   ✅ 完美预测: {len(numbers)}个号码, {len(zodiacs)}个生肖")
                print(f"   ✅ 置信度: {confidence:.1%}, 稳定性: {stability:.1%}")
                
                self.test_results["完美预测系统"] = {
                    "status": "success",
                    "numbers": len(numbers),
                    "zodiacs": len(zodiacs),
                    "confidence": confidence,
                    "stability": stability
                }
            else:
                print(f"   ❌ 预测结果格式错误")
                self.test_results["完美预测系统"] = {"status": "failed", "error": "结果格式错误"}
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            self.test_results["完美预测系统"] = {"status": "failed", "error": str(e)}
        
        print()
    
    def test_independent_analysis_modules(self):
        """测试独立分析模组"""
        print("🔬 独立分析模组测试")
        print("-" * 40)
        
        # 测试传统分析模组
        print("1. 传统分析模组:")
        try:
            from src.independent_modules.traditional_module import TraditionalAnalysisModule
            
            module = TraditionalAnalysisModule()
            result = module.predict(self.target_date)
            
            numbers = result.get("predicted_numbers", [])
            confidence = result.get("confidence", 0)
            
            print(f"   ✅ 传统分析: {len(numbers)}个号码, 置信度{confidence:.1%}")
            
            self.test_results["传统分析模组"] = {
                "status": "success",
                "numbers": len(numbers),
                "confidence": confidence
            }
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            self.test_results["传统分析模组"] = {"status": "failed", "error": str(e)}
        
        # 测试机器学习模组
        print("2. 机器学习模组:")
        try:
            from src.independent_modules.ml_module import MachineLearningModule
            
            module = MachineLearningModule()
            result = module.predict(self.target_date)
            
            numbers = result.get("predicted_numbers", [])
            confidence = result.get("confidence", 0)
            
            print(f"   ✅ 机器学习: {len(numbers)}个号码, 置信度{confidence:.1%}")
            
            self.test_results["机器学习模组"] = {
                "status": "success",
                "numbers": len(numbers),
                "confidence": confidence
            }
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            self.test_results["机器学习模组"] = {"status": "failed", "error": str(e)}
        
        # 测试生肖扩展模组
        print("3. 生肖扩展模组:")
        try:
            from src.independent_modules.zodiac_extended_module import ZodiacExtendedModule
            
            module = ZodiacExtendedModule()
            result = module.predict(self.target_date)
            
            numbers = result.get("predicted_numbers", [])
            confidence = result.get("confidence", 0)
            
            print(f"   ✅ 生肖扩展: {len(numbers)}个号码, 置信度{confidence:.1%}")
            
            self.test_results["生肖扩展模组"] = {
                "status": "success",
                "numbers": len(numbers),
                "confidence": confidence
            }
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            self.test_results["生肖扩展模组"] = {"status": "failed", "error": str(e)}
        
        # 测试特殊生肖模组
        print("4. 特殊生肖模组:")
        try:
            from src.independent_modules.special_zodiac_module import SpecialZodiacModule
            
            module = SpecialZodiacModule()
            result = module.predict(self.target_date)
            
            numbers = result.get("predicted_numbers", [])
            confidence = result.get("confidence", 0)
            
            print(f"   ✅ 特殊生肖: {len(numbers)}个号码, 置信度{confidence:.1%}")
            
            self.test_results["特殊生肖模组"] = {
                "status": "success",
                "numbers": len(numbers),
                "confidence": confidence
            }
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            self.test_results["特殊生肖模组"] = {"status": "failed", "error": str(e)}
        
        print()
    
    def test_optimization_modules(self):
        """测试优化增强模组"""
        print("⚡ 优化增强模组测试")
        print("-" * 40)
        
        # 测试高级融合优化
        print("1. 高级融合优化:")
        try:
            from advanced_fusion_optimizer import AdvancedFusionOptimizer
            
            optimizer = AdvancedFusionOptimizer()
            
            # 模拟预测结果
            mock_predictions = {
                "traditional": {"numbers": [1, 5, 12, 18, 23, 28], "confidence": 0.75},
                "ml": {"numbers": [2, 6, 13, 19, 24, 29], "confidence": 0.84},
                "zodiac": {"numbers": [3, 7, 14, 20, 25, 30], "confidence": 0.78}
            }
            
            result = optimizer.optimize_fusion(mock_predictions, self.target_date)
            numbers = result.get("final_16_numbers", [])
            
            print(f"   ✅ 融合优化: {len(numbers)}个号码")
            
            self.test_results["高级融合优化"] = {
                "status": "success",
                "numbers": len(numbers)
            }
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            self.test_results["高级融合优化"] = {"status": "failed", "error": str(e)}
        
        # 测试命中率优化器
        print("2. 命中率优化器:")
        try:
            from enhanced_hit_rate_optimizer import HitRateOptimizer
            
            optimizer = HitRateOptimizer()
            
            # 模拟历史数据
            mock_historical = [
                {"special_number": 15, "draw_date": "2025-06-20"},
                {"special_number": 28, "draw_date": "2025-06-18"},
                {"special_number": 42, "draw_date": "2025-06-15"}
            ]
            
            result = optimizer.optimize_number_selection(
                mock_historical, self.target_date, [1, 5, 12, 18, 23, 28]
            )
            
            numbers = result.get("optimized_numbers", [])
            confidence = result.get("fusion_confidence", 0)
            
            print(f"   ✅ 命中率优化: {len(numbers)}个号码, 置信度{confidence:.1%}")
            
            self.test_results["命中率优化器"] = {
                "status": "success",
                "numbers": len(numbers),
                "confidence": confidence
            }
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            self.test_results["命中率优化器"] = {"status": "failed", "error": str(e)}
        
        print()
    
    def test_integration_modules(self):
        """测试系统集成模组"""
        print("🔗 系统集成模组测试")
        print("-" * 40)
        
        # 测试融合管理器
        print("1. 融合管理器:")
        try:
            from src.fusion_manager import FusionManager
            
            manager = FusionManager()
            
            # 模拟模组预测结果
            mock_predictions = {
                "traditional_analysis": {
                    "numbers": [1, 5, 12, 18, 23, 28, 34, 39, 42, 45, 47, 49, 3, 8, 15, 21],
                    "confidence": 0.75
                },
                "machine_learning": {
                    "numbers": [2, 6, 13, 19, 24, 29, 35, 40, 43, 46, 48, 1, 4, 9, 16, 22],
                    "confidence": 0.84
                }
            }
            
            result = manager.fuse_predictions(mock_predictions)
            numbers = result.get("final_16_numbers", [])
            confidence = result.get("fusion_confidence", 0)
            
            print(f"   ✅ 融合管理: {len(numbers)}个号码, 置信度{confidence:.1%}")
            
            self.test_results["融合管理器"] = {
                "status": "success",
                "numbers": len(numbers),
                "confidence": confidence
            }
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            self.test_results["融合管理器"] = {"status": "failed", "error": str(e)}
        
        print()
    
    def generate_detailed_report(self):
        """生成详细报告"""
        print("=" * 60)
        print("📊 详细模组功能测试报告")
        print("=" * 60)
        
        # 统计结果
        total_modules = len(self.test_results)
        success_modules = sum(1 for result in self.test_results.values() if result["status"] == "success")
        failed_modules = total_modules - success_modules
        
        print(f"📊 测试统计:")
        print(f"  总模组数: {total_modules}")
        print(f"  成功模组: {success_modules}")
        print(f"  失败模组: {failed_modules}")
        print(f"  成功率: {success_modules/total_modules:.1%}")
        print()
        
        # 成功模组详情
        print("✅ 成功模组详情:")
        for module_name, result in self.test_results.items():
            if result["status"] == "success":
                details = []
                if "numbers" in result:
                    details.append(f"{result['numbers']}个号码")
                if "zodiacs" in result:
                    details.append(f"{result['zodiacs']}个生肖")
                if "confidence" in result:
                    details.append(f"置信度{result['confidence']:.1%}")
                if "consistency" in result:
                    details.append(f"一致性{'通过' if result['consistency'] else '失败'}")
                
                detail_str = ", ".join(details) if details else "基本功能正常"
                print(f"  • {module_name}: {detail_str}")
        
        # 失败模组详情
        if failed_modules > 0:
            print("\n❌ 失败模组详情:")
            for module_name, result in self.test_results.items():
                if result["status"] == "failed":
                    error = result.get("error", "未知错误")
                    print(f"  • {module_name}: {error}")
        
        print()
        print("💡 总结:")
        if success_modules == total_modules:
            print("🎊 所有模组功能测试通过，系统运行完美！")
        elif success_modules >= total_modules * 0.8:
            print("👍 大部分模组功能正常，系统状态良好！")
        elif success_modules >= total_modules * 0.6:
            print("⚠️ 多数模组正常，少数模组需要修复")
        else:
            print("❌ 多个模组存在功能问题，需要系统性修复")
        
        return success_modules == total_modules

def main():
    """主函数"""
    tester = DetailedModuleFunctionalityTester()
    success = tester.run_detailed_tests()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
