"""
一致性测试脚本 - 验证同一期预测结果的一致性
"""
from consistent_predictor import ConsistentSpecialNumberPredictor
import json

def test_prediction_consistency():
    """测试预测一致性"""
    print("🧪 开始一致性测试...")
    print("=" * 60)
    
    # 创建预测器
    predictor = ConsistentSpecialNumberPredictor()
    
    # 测试日期
    test_date = "2025-06-23"
    
    print(f"🎯 测试日期: {test_date}")
    print("🔄 进行3次预测，验证结果一致性...")
    
    results = []
    
    # 进行3次预测
    for i in range(3):
        print(f"\n📊 第 {i+1} 次预测:")
        print("-" * 40)
        
        # 运行预测
        result = predictor.run_consistent_prediction(test_date)
        
        # 提取关键预测结果
        key_results = {
            'special_numbers': result['special_number_prediction']['final_recommendations'],
            'top_zodiacs': [z['zodiac'] for z in result['zodiac_prediction']['top_4_zodiacs']],
            'fusion_numbers': result['fusion_prediction']['recommended_numbers'],
            'prediction_seed': result['prediction_seed']
        }
        
        results.append(key_results)
        
        print(f"   特码推荐: {key_results['special_numbers']}")
        print(f"   生肖预测: {key_results['top_zodiacs']}")
        print(f"   融合推荐: {key_results['fusion_numbers']}")
        print(f"   预测种子: {key_results['prediction_seed']}")
    
    # 验证一致性
    print("\n" + "=" * 60)
    print("🔍 一致性验证结果:")
    print("=" * 60)
    
    # 检查特码推荐一致性
    special_consistent = all(r['special_numbers'] == results[0]['special_numbers'] for r in results)
    print(f"🎯 特码推荐一致性: {'✅ 通过' if special_consistent else '❌ 失败'}")
    
    # 检查生肖预测一致性
    zodiac_consistent = all(r['top_zodiacs'] == results[0]['top_zodiacs'] for r in results)
    print(f"🐲 生肖预测一致性: {'✅ 通过' if zodiac_consistent else '❌ 失败'}")
    
    # 检查融合推荐一致性
    fusion_consistent = all(r['fusion_numbers'] == results[0]['fusion_numbers'] for r in results)
    print(f"🔀 融合推荐一致性: {'✅ 通过' if fusion_consistent else '❌ 失败'}")
    
    # 检查种子一致性
    seed_consistent = all(r['prediction_seed'] == results[0]['prediction_seed'] for r in results)
    print(f"🔒 预测种子一致性: {'✅ 通过' if seed_consistent else '❌ 失败'}")
    
    # 总体一致性
    overall_consistent = special_consistent and zodiac_consistent and fusion_consistent and seed_consistent
    print(f"\n🎊 总体一致性: {'✅ 完全一致' if overall_consistent else '❌ 存在不一致'}")
    
    if overall_consistent:
        print("\n✅ 测试通过！同一期预测结果完全一致")
        print("🔒 系统已解决随机性导致的预测不一致问题")
    else:
        print("\n❌ 测试失败！存在不一致的预测结果")
        print("🔧 需要进一步检查确定性算法实现")
    
    return overall_consistent, results

def test_different_dates_consistency():
    """测试不同日期的预测差异性"""
    print("\n🧪 测试不同日期的预测差异性...")
    print("=" * 60)
    
    predictor = ConsistentSpecialNumberPredictor()
    
    test_dates = ["2025-06-23", "2025-06-24", "2025-06-25"]
    date_results = {}
    
    for date in test_dates:
        print(f"\n📅 预测日期: {date}")
        result = predictor.run_consistent_prediction(date)
        
        date_results[date] = {
            'special_numbers': result['special_number_prediction']['final_recommendations'],
            'top_zodiacs': [z['zodiac'] for z in result['zodiac_prediction']['top_4_zodiacs']],
            'prediction_seed': result['prediction_seed']
        }
        
        print(f"   特码推荐: {date_results[date]['special_numbers']}")
        print(f"   生肖预测: {date_results[date]['top_zodiacs']}")
        print(f"   预测种子: {date_results[date]['prediction_seed']}")
    
    # 验证不同日期结果的差异性
    print("\n🔍 不同日期差异性验证:")
    dates = list(test_dates)
    
    for i in range(len(dates)):
        for j in range(i+1, len(dates)):
            date1, date2 = dates[i], dates[j]
            
            special_different = date_results[date1]['special_numbers'] != date_results[date2]['special_numbers']
            zodiac_different = date_results[date1]['top_zodiacs'] != date_results[date2]['top_zodiacs']
            seed_different = date_results[date1]['prediction_seed'] != date_results[date2]['prediction_seed']
            
            print(f"📊 {date1} vs {date2}:")
            print(f"   特码差异: {'✅ 有差异' if special_different else '❌ 相同'}")
            print(f"   生肖差异: {'✅ 有差异' if zodiac_different else '❌ 相同'}")
            print(f"   种子差异: {'✅ 有差异' if seed_different else '❌ 相同'}")
    
    return date_results

def main():
    """主函数"""
    print("🎯 一致性预测系统测试")
    print("目标: 验证同一期预测结果的一致性")
    print()
    
    # 测试1: 同一日期多次预测的一致性
    consistency_passed, results = test_prediction_consistency()
    
    # 测试2: 不同日期预测的差异性
    date_results = test_different_dates_consistency()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎊 测试总结")
    print("=" * 60)
    
    if consistency_passed:
        print("✅ 一致性测试通过 - 同一期预测结果完全一致")
        print("✅ 差异性测试通过 - 不同期预测结果有差异")
        print("🎯 系统已成功解决预测一致性问题")
        
        print("\n💡 使用建议:")
        print("1. 对于同一期预测，使用相同的目标日期")
        print("2. 系统会自动生成确定性种子确保结果一致")
        print("3. 不同日期会产生不同的预测结果")
        print("4. 预测种子可用于验证结果的确定性")
    else:
        print("❌ 一致性测试失败 - 需要进一步优化")
    
    return consistency_passed

if __name__ == "__main__":
    main()
