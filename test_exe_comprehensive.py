"""
全面测试打包的EXE文件
"""

import sys
import os
import time
import subprocess
import threading
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer, QThread, pyqtSignal
from PyQt5.QtTest import QTest

class EXETestRunner(QThread):
    """EXE测试运行器"""
    test_completed = pyqtSignal(str, bool, str)  # 测试名称, 是否成功, 详细信息
    
    def __init__(self):
        super().__init__()
        self.exe_path = None
        self.test_results = {}
        
    def set_exe_path(self, path):
        """设置EXE路径"""
        self.exe_path = Path(path)
        
    def run(self):
        """运行测试"""
        print("🧪 开始全面测试EXE文件")
        print("=" * 60)
        
        if not self.exe_path or not self.exe_path.exists():
            self.test_completed.emit("EXE文件检查", False, "EXE文件不存在")
            return
        
        # 1. 启动EXE并测试基础功能
        self.test_exe_startup()
        
        # 2. 等待GUI加载
        time.sleep(5)
        
        # 3. 测试各个功能模块
        self.test_all_modules()
        
        # 4. 生成测试报告
        self.generate_test_report()
        
    def test_exe_startup(self):
        """测试EXE启动"""
        try:
            print("\n1️⃣ 测试EXE启动...")
            
            # 启动EXE进程
            self.exe_process = subprocess.Popen(
                [str(self.exe_path)],
                cwd=self.exe_path.parent,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待启动
            time.sleep(3)
            
            # 检查进程是否还在运行
            if self.exe_process.poll() is None:
                print("✅ EXE启动成功")
                self.test_results['exe_startup'] = True
                self.test_completed.emit("EXE启动", True, "程序成功启动")
            else:
                stdout, stderr = self.exe_process.communicate()
                error_msg = f"启动失败\nSTDOUT: {stdout.decode('utf-8', errors='ignore')}\nSTDERR: {stderr.decode('utf-8', errors='ignore')}"
                print(f"❌ EXE启动失败: {error_msg}")
                self.test_results['exe_startup'] = False
                self.test_completed.emit("EXE启动", False, error_msg)
                
        except Exception as e:
            error_msg = f"启动异常: {e}"
            print(f"❌ EXE启动异常: {error_msg}")
            self.test_results['exe_startup'] = False
            self.test_completed.emit("EXE启动", False, error_msg)
    
    def test_all_modules(self):
        """测试所有模块"""
        print("\n2️⃣ 开始功能模块测试...")
        
        # 由于无法直接与EXE的GUI交互，我们通过检查进程状态和日志来判断
        test_modules = [
            ("数据管理", self.test_data_management),
            ("特码预测", self.test_special_prediction),
            ("完美预测系统", self.test_perfect_prediction),
            ("性能监控", self.test_performance_monitor),
            ("增强回测", self.test_enhanced_backtest),
            ("融合配置", self.test_fusion_config)
        ]
        
        for module_name, test_func in test_modules:
            try:
                print(f"\n🔍 测试 {module_name}...")
                success, details = test_func()
                self.test_results[module_name] = success
                self.test_completed.emit(module_name, success, details)
                
                # 等待一段时间让程序处理
                time.sleep(2)
                
            except Exception as e:
                error_msg = f"测试异常: {e}"
                print(f"❌ {module_name} 测试失败: {error_msg}")
                self.test_results[module_name] = False
                self.test_completed.emit(module_name, False, error_msg)
    
    def test_data_management(self):
        """测试数据管理"""
        # 检查数据库文件是否创建
        db_path = self.exe_path.parent / "data" / "lottery.db"
        if db_path.exists():
            return True, "数据库文件存在"
        else:
            return False, "数据库文件不存在"
    
    def test_special_prediction(self):
        """测试特码预测"""
        # 检查配置文件
        config_path = self.exe_path.parent / "config" / "special_predictor_config.json"
        if config_path.exists():
            return True, "特码预测配置文件存在"
        else:
            return False, "特码预测配置文件不存在"
    
    def test_perfect_prediction(self):
        """测试完美预测系统"""
        # 检查完美预测配置
        config_path = self.exe_path.parent / "config" / "perfect_prediction_config.json"
        if config_path.exists():
            return True, "完美预测配置文件存在"
        else:
            return False, "完美预测配置文件不存在"
    
    def test_performance_monitor(self):
        """测试性能监控"""
        # 检查日志目录
        logs_path = self.exe_path.parent / "logs"
        if logs_path.exists():
            return True, "日志目录存在，性能监控可能正常"
        else:
            return False, "日志目录不存在"
    
    def test_enhanced_backtest(self):
        """测试增强回测"""
        # 检查增强回测配置
        config_path = self.exe_path.parent / "config" / "enhanced_backtest_config.json"
        if config_path.exists():
            return True, "增强回测配置文件存在"
        else:
            return False, "增强回测配置文件不存在"
    
    def test_fusion_config(self):
        """测试融合配置"""
        # 检查主配置文件
        config_path = self.exe_path.parent / "config" / "master_config.json"
        if config_path.exists():
            return True, "主配置文件存在"
        else:
            return False, "主配置文件不存在"
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n3️⃣ 生成测试报告...")
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        success_rate = passed_tests / total_tests if total_tests > 0 else 0
        
        report = f"""
EXE测试报告
==========

测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
EXE路径: {self.exe_path}

测试结果:
总测试项: {total_tests}
通过测试: {passed_tests}
失败测试: {total_tests - passed_tests}
成功率: {success_rate:.1%}

详细结果:
"""
        
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            report += f"{test_name}: {status}\n"
        
        # 保存报告
        report_path = self.exe_path.parent / "exe_test_report.txt"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📄 测试报告已保存: {report_path}")
        self.test_completed.emit("测试报告", True, f"报告已保存到 {report_path}")

def find_exe_file():
    """查找EXE文件"""
    possible_paths = [
        Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版/六合彩预测系统_v2.0.0_Final/六合彩预测系统_v2.0.0.exe"),
        Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版/dist/六合彩预测系统_v2.0.0_Fixed.exe"),
        Path("releases/六合彩预测系统_v2.0.0_Final_20250625/1_便携版/六合彩预测系统_v2.0.0.exe")
    ]
    
    for path in possible_paths:
        if path.exists():
            return path
    
    return None

def run_manual_test():
    """运行手动测试"""
    print("🎯 六合彩预测系统EXE全面测试")
    print("=" * 70)
    
    # 查找EXE文件
    exe_path = find_exe_file()
    if not exe_path:
        print("❌ 找不到EXE文件")
        print("请检查以下路径:")
        for path in [
            "releases/六合彩预测系统_v2.0.0_20250624_Windows版/六合彩预测系统_v2.0.0_Final/",
            "releases/六合彩预测系统_v2.0.0_Final_20250625/1_便携版/"
        ]:
            print(f"  - {path}")
        return False
    
    print(f"✅ 找到EXE文件: {exe_path}")
    print(f"📊 文件大小: {exe_path.stat().st_size / (1024*1024):.1f} MB")
    
    # 启动EXE进行测试
    print(f"\n🚀 启动EXE进行测试...")
    print(f"📁 工作目录: {exe_path.parent}")
    
    try:
        # 启动EXE
        process = subprocess.Popen(
            [str(exe_path)],
            cwd=exe_path.parent,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        print(f"✅ EXE已启动 (PID: {process.pid})")
        print(f"⏳ 等待程序加载...")
        
        # 等待程序启动
        time.sleep(10)
        
        # 检查进程状态
        if process.poll() is None:
            print(f"✅ 程序正在运行")
            
            # 等待用户手动测试
            print(f"\n📋 请手动测试以下功能:")
            print(f"  1. 📊 数据管理 - 导入数据功能")
            print(f"  2. 🎯 特码预测 - 所有预测模式")
            print(f"  3. ⭐ 完美预测系统 - 融合预测")
            print(f"  4. 📈 性能监控 - 更新数据和刷新图表")
            print(f"  5. 🔄 增强回测 - 所有三个功能按钮")
            print(f"  6. ⚖️ 融合配置 - 权重配置和测试")
            
            print(f"\n⚠️ 重点测试项目:")
            print(f"  🔥 性能监控 - 点击'更新性能数据'和'刷新图表'")
            print(f"  🔥 增强回测 - 运行'最优模式选择'然后'应用最优模式'")
            print(f"  🔥 完美预测 - 运行融合预测并查看结果")
            
            input(f"\n按回车键继续监控程序状态...")
            
            # 继续监控
            while process.poll() is None:
                print(f"📊 程序状态: 运行中")
                time.sleep(5)
                
                # 检查是否有用户输入
                try:
                    user_input = input("输入 'q' 退出监控，或回车继续: ")
                    if user_input.lower() == 'q':
                        break
                except:
                    break
            
            # 程序结束
            if process.poll() is not None:
                stdout, stderr = process.communicate()
                print(f"\n📄 程序输出:")
                if stdout:
                    print(f"STDOUT: {stdout.decode('utf-8', errors='ignore')}")
                if stderr:
                    print(f"STDERR: {stderr.decode('utf-8', errors='ignore')}")
                print(f"🔚 程序已退出 (返回码: {process.returncode})")
            else:
                print(f"🔚 监控结束，程序仍在运行")
                
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 程序启动失败")
            print(f"返回码: {process.returncode}")
            if stdout:
                print(f"STDOUT: {stdout.decode('utf-8', errors='ignore')}")
            if stderr:
                print(f"STDERR: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"❌ 启动EXE时出错: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🧪 EXE全面测试工具")
    print("=" * 70)
    print("📋 测试范围:")
    print("  - EXE启动测试")
    print("  - 数据管理功能")
    print("  - 特码预测功能")
    print("  - 完美预测系统")
    print("  - 性能监控功能")
    print("  - 增强回测功能")
    print("  - 融合配置功能")
    
    success = run_manual_test()
    
    print("\n" + "="*70)
    if success:
        print("🎉 EXE测试完成！")
        print("📋 请根据手动测试结果评估系统状态")
    else:
        print("❌ EXE测试失败")
        print("请检查错误信息并修复问题")
    
    return success

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
