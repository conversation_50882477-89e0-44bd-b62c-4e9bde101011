"""
模型验证器 - 模型性能验证和稳定性测试
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.model_selection import cross_val_score, TimeSeriesSplit
from scipy import stats
import warnings

class ModelValidator:
    def __init__(self):
        """初始化模型验证器"""
        self.validation_results = {}
        
    def validate_model_performance(self, 
                                 model: Any,
                                 X_test: np.ndarray,
                                 y_test: np.ndarray,
                                 metrics: List[str] = None) -> Dict:
        """验证模型性能"""
        if metrics is None:
            metrics = ['accuracy', 'precision', 'recall', 'f1']
        
        y_pred = model.predict(X_test)
        
        results = {}
        for metric in metrics:
            if metric == 'accuracy':
                results[metric] = accuracy_score(y_test, y_pred)
            elif metric == 'precision':
                results[metric] = precision_score(y_test, y_pred, average='weighted')
            elif metric == 'recall':
                results[metric] = recall_score(y_test, y_pred, average='weighted')
            elif metric == 'f1':
                results[metric] = f1_score(y_test, y_pred, average='weighted')
        
        return results
    
    def cross_validate_model(self,
                           model: Any,
                           X: np.ndarray,
                           y: np.ndarray,
                           cv_folds: int = 5,
                           scoring: str = 'accuracy') -> Dict:
        """交叉验证模型"""
        
        # 时间序列交叉验证（适合彩票数据）
        tscv = TimeSeriesSplit(n_splits=cv_folds)
        
        scores = cross_val_score(model, X, y, cv=tscv, scoring=scoring)
        
        return {
            'cv_scores': scores.tolist(),
            'mean_score': scores.mean(),
            'std_score': scores.std(),
            'min_score': scores.min(),
            'max_score': scores.max()
        }
    
    def detect_data_drift(self,
                         X_train: np.ndarray,
                         X_new: np.ndarray,
                         threshold: float = 0.05) -> Dict:
        """检测数据漂移"""
        
        drift_results = {}
        n_features = X_train.shape[1]
        
        for i in range(n_features):
            # 使用Kolmogorov-Smirnov测试
            statistic, p_value = stats.ks_2samp(X_train[:, i], X_new[:, i])
            
            drift_results[f'feature_{i}'] = {
                'ks_statistic': statistic,
                'p_value': p_value,
                'drift_detected': p_value < threshold
            }
        
        # 整体漂移评估
        drift_count = sum(1 for result in drift_results.values() if result['drift_detected'])
        drift_ratio = drift_count / n_features
        
        return {
            'feature_drift': drift_results,
            'overall_drift_ratio': drift_ratio,
            'significant_drift': drift_ratio > 0.3  # 30%以上特征漂移认为显著
        }
    
    def test_model_stability(self,
                           model: Any,
                           X_test: np.ndarray,
                           y_test: np.ndarray,
                           n_runs: int = 10,
                           noise_level: float = 0.01) -> Dict:
        """测试模型稳定性"""
        
        base_predictions = model.predict(X_test)
        base_accuracy = accuracy_score(y_test, base_predictions)
        
        stability_scores = []
        
        for run in range(n_runs):
            # 添加小量噪声
            X_noisy = X_test + np.random.normal(0, noise_level, X_test.shape)
            
            try:
                noisy_predictions = model.predict(X_noisy)
                noisy_accuracy = accuracy_score(y_test, noisy_predictions)
                stability_scores.append(noisy_accuracy)
            except Exception as e:
                warnings.warn(f"稳定性测试第{run+1}轮失败: {e}")
                continue
        
        if stability_scores:
            stability_scores = np.array(stability_scores)
            
            return {
                'base_accuracy': base_accuracy,
                'stability_scores': stability_scores.tolist(),
                'mean_stability': stability_scores.mean(),
                'std_stability': stability_scores.std(),
                'stability_coefficient': stability_scores.std() / stability_scores.mean() if stability_scores.mean() > 0 else float('inf'),
                'min_accuracy': stability_scores.min(),
                'max_accuracy': stability_scores.max()
            }
        else:
            return {
                'base_accuracy': base_accuracy,
                'stability_test_failed': True
            }
    
    def ab_test_models(self,
                      model_a: Any,
                      model_b: Any,
                      X_test: np.ndarray,
                      y_test: np.ndarray,
                      confidence_level: float = 0.95) -> Dict:
        """A/B测试比较两个模型"""
        
        # 获取预测结果
        pred_a = model_a.predict(X_test)
        pred_b = model_b.predict(X_test)
        
        # 计算准确率
        acc_a = accuracy_score(y_test, pred_a)
        acc_b = accuracy_score(y_test, pred_b)
        
        # 计算每个样本的正确性
        correct_a = (pred_a == y_test).astype(int)
        correct_b = (pred_b == y_test).astype(int)
        
        # 配对t检验
        statistic, p_value = stats.ttest_rel(correct_a, correct_b)
        
        # 效应大小（Cohen's d）
        diff = correct_a - correct_b
        cohens_d = diff.mean() / diff.std() if diff.std() > 0 else 0
        
        # 置信区间
        alpha = 1 - confidence_level
        t_critical = stats.t.ppf(1 - alpha/2, len(diff) - 1)
        margin_error = t_critical * (diff.std() / np.sqrt(len(diff)))
        
        return {
            'model_a_accuracy': acc_a,
            'model_b_accuracy': acc_b,
            'accuracy_difference': acc_b - acc_a,
            't_statistic': statistic,
            'p_value': p_value,
            'significant_difference': p_value < (1 - confidence_level),
            'cohens_d': cohens_d,
            'confidence_interval': [diff.mean() - margin_error, diff.mean() + margin_error],
            'better_model': 'B' if acc_b > acc_a else 'A' if acc_a > acc_b else 'Equal'
        }
    
    def comprehensive_validation(self,
                               model: Any,
                               X_train: np.ndarray,
                               y_train: np.ndarray,
                               X_test: np.ndarray,
                               y_test: np.ndarray) -> Dict:
        """综合验证"""
        
        results = {}
        
        # 性能验证
        results['performance'] = self.validate_model_performance(model, X_test, y_test)
        
        # 交叉验证
        results['cross_validation'] = self.cross_validate_model(model, X_train, y_train)
        
        # 数据漂移检测
        results['data_drift'] = self.detect_data_drift(X_train, X_test)
        
        # 稳定性测试
        results['stability'] = self.test_model_stability(model, X_test, y_test)
        
        # 综合评分
        performance_score = results['performance']['accuracy']
        cv_score = results['cross_validation']['mean_score']
        stability_score = 1 - results['stability'].get('stability_coefficient', 1)
        drift_penalty = 1 - results['data_drift']['overall_drift_ratio']
        
        overall_score = (performance_score * 0.4 + 
                        cv_score * 0.3 + 
                        stability_score * 0.2 + 
                        drift_penalty * 0.1)
        
        results['overall_score'] = overall_score
        results['validation_passed'] = overall_score > 0.7  # 70%阈值
        
        return results
