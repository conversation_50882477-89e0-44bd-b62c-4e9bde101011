import sys
import os
sys.path.append('src')

def analyze_prediction_modes():
    """分析预测模式"""
    print("🔍 特码预测模式分析")
    print("=" * 50)
    
    try:
        from perfect_prediction_system import PerfectPredictionSystem
        
        # 创建系统实例
        system = PerfectPredictionSystem()
        system.initialize_modules()
        
        print("✅ 完美预测系统初始化成功")
        
        # 检查预测模式相关的方法和配置
        print(f"\n📋 检查预测模式相关功能:")
        
        # 检查系统配置中的预测模式
        if hasattr(system, 'config'):
            config = system.config
            print(f"✅ 系统配置存在")
            
            # 查找预测模式相关配置
            if 'prediction_modes' in config:
                modes = config['prediction_modes']
                print(f"   �� 预测模式配置: {modes}")
            else:
                print(f"   ⚠️ 未找到预测模式配置")
            
            # 查找一致性相关配置
            if 'consistency' in config:
                consistency = config['consistency']
                print(f"   🔄 一致性配置: {consistency}")
            else:
                print(f"   ⚠️ 未找到一致性配置")
        
        # 检查预测方法
        print(f"\n🎯 检查预测方法:")
        prediction_methods = [
            'run_complete_prediction',
            'run_standard_prediction', 
            'run_consistency_prediction',
            'validate_consistency'
        ]
        
        for method in prediction_methods:
            if hasattr(system, method):
                print(f"   ✅ {method}: 存在")
            else:
                print(f"   ❌ {method}: 不存在")
        
        # 测试标准预测
        print(f"\n📊 测试标准预测:")
        try:
            result = system.run_complete_prediction("2025-06-23")
            print(f"   ✅ 标准预测成功")
            
            final_results = result.get('final_results', {})
            if final_results:
                numbers = final_results.get('recommended_16_numbers', [])
                confidence = final_results.get('overall_confidence', 0)
                print(f"   📊 推荐号码: {len(numbers)} 个")
                print(f"   📈 置信度: {confidence}")
                
                # 检查预测模式标识
                prediction_mode = result.get('prediction_mode', 'unknown')
                print(f"   🎯 预测模式: {prediction_mode}")
        except Exception as e:
            print(f"   ❌ 标准预测失败: {e}")
        
        # 检查一致性验证器
        print(f"\n🔄 检查一致性验证器:")
        try:
            # 查找一致性验证器文件
            consistency_files = [
                'consistency_validator.py',
                'consistency_verification.py'
            ]
            
            for filename in consistency_files:
                if os.path.exists(filename):
                    print(f"   ✅ 找到一致性验证器: {filename}")
                    
                    # 导入一致性验证器
                    if filename == 'consistency_validator.py':
                        from consistency_validator import ConsistencyValidator
                        validator = ConsistencyValidator()
                        print(f"   ✅ ConsistencyValidator创建成功")
                        
                        # 测试一致性验证
                        test_predictions = [
                            {'predicted_numbers': [1, 2, 3, 4, 5]},
                            {'predicted_numbers': [1, 2, 3, 6, 7]},
                            {'predicted_numbers': [1, 2, 8, 9, 10]}
                        ]
                        
                        consistency_result = validator.validate_predictions(test_predictions)
                        print(f"   📊 一致性验证测试: {consistency_result}")
                        break
            else:
                print(f"   ❌ 未找到一致性验证器文件")
        
        except Exception as e:
            print(f"   ❌ 一致性验证器测试失败: {e}")
        
        return system
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    system = analyze_prediction_modes()
