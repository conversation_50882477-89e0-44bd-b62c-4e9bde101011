"""
诊断数据预览问题
检查为什么数据预览界面只显示正码没有特码
"""
import sqlite3
import os
from datetime import datetime

def check_database_structure():
    """检查数据库结构"""
    print("🔍 检查数据库结构...")
    
    db_path = "data/lottery.db"
    
    if not os.path.exists(db_path):
        print(f"   ❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='lottery_results'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print(f"   ❌ 表 'lottery_results' 不存在")
            return False
        
        print(f"   ✅ 表 'lottery_results' 存在")
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(lottery_results)")
        columns = cursor.fetchall()
        
        print(f"   📋 表结构:")
        for col in columns:
            print(f"      {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")
        
        # 检查是否包含特码列
        column_names = [col[1] for col in columns]
        if 'special_number' in column_names:
            print(f"   ✅ 特码列 'special_number' 存在")
        else:
            print(f"   ❌ 特码列 'special_number' 不存在")
            print(f"   📋 现有列: {column_names}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 检查数据库结构失败: {e}")
        return False

def check_database_data():
    """检查数据库数据"""
    print("\n📊 检查数据库数据...")
    
    db_path = "data/lottery.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查数据总数
        cursor.execute("SELECT COUNT(*) FROM lottery_results")
        total_count = cursor.fetchone()[0]
        print(f"   📊 总记录数: {total_count}")
        
        if total_count == 0:
            print(f"   ⚠️ 数据库为空，没有数据可显示")
            conn.close()
            return False
        
        # 检查最新几条记录
        cursor.execute('''
            SELECT id, draw_date, period_number, regular_1, regular_2, regular_3,
                   regular_4, regular_5, regular_6, special_number
            FROM lottery_results
            ORDER BY draw_date DESC
            LIMIT 5
        ''')
        
        results = cursor.fetchall()
        
        print(f"   📋 最新5条记录:")
        print(f"      {'ID':<5} {'日期':<12} {'期号':<10} {'正码1':<5} {'正码2':<5} {'正码3':<5} {'正码4':<5} {'正码5':<5} {'正码6':<5} {'特码':<5}")
        print(f"      {'-'*5} {'-'*12} {'-'*10} {'-'*5} {'-'*5} {'-'*5} {'-'*5} {'-'*5} {'-'*5} {'-'*5}")
        
        for row in results:
            print(f"      {row[0]:<5} {row[1]:<12} {row[2]:<10} {row[3]:<5} {row[4]:<5} {row[5]:<5} {row[6]:<5} {row[7]:<5} {row[8]:<5} {row[9]:<5}")
        
        # 检查特码列是否有数据
        cursor.execute("SELECT COUNT(*) FROM lottery_results WHERE special_number IS NOT NULL AND special_number != ''")
        special_count = cursor.fetchone()[0]
        print(f"   📊 有特码数据的记录数: {special_count}")
        
        if special_count == 0:
            print(f"   ⚠️ 所有记录的特码字段都为空")
        elif special_count < total_count:
            print(f"   ⚠️ 部分记录的特码字段为空 ({total_count - special_count} 条)")
        else:
            print(f"   ✅ 所有记录都有特码数据")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 检查数据库数据失败: {e}")
        return False

def test_query_execution():
    """测试查询执行"""
    print("\n🔍 测试查询执行...")
    
    db_path = "data/lottery.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 执行与GUI相同的查询
        query = '''
            SELECT id, draw_date, period_number, regular_1, regular_2, regular_3,
                   regular_4, regular_5, regular_6, special_number
            FROM lottery_results
            ORDER BY draw_date DESC
            LIMIT 50
        '''
        
        print(f"   📋 执行查询: {query}")
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        print(f"   📊 查询结果: {len(results)} 条记录")
        
        if results:
            # 检查第一条记录的结构
            first_row = results[0]
            print(f"   📋 第一条记录结构:")
            print(f"      字段数量: {len(first_row)}")
            print(f"      字段值: {first_row}")
            
            # 检查特码字段位置
            if len(first_row) >= 10:
                special_value = first_row[9]  # 特码在第10个位置（索引9）
                print(f"   📊 特码值: {special_value} (类型: {type(special_value)})")
                
                if special_value is None or special_value == '':
                    print(f"   ⚠️ 特码字段为空")
                else:
                    print(f"   ✅ 特码字段有值")
            else:
                print(f"   ❌ 记录字段数量不足，期望10个字段，实际{len(first_row)}个")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 测试查询执行失败: {e}")
        return False

def check_gui_table_setup():
    """检查GUI表格设置"""
    print("\n🖥️ 检查GUI表格设置...")
    
    # 模拟GUI表格设置
    expected_columns = ["ID", "日期", "期号", "正码1", "正码2", "正码3", "正码4", "正码5", "正码6", "特码"]
    expected_column_count = 9  # 实际应该是10列
    
    print(f"   📋 期望的列标题: {expected_columns}")
    print(f"   📊 期望的列数量: {len(expected_columns)}")
    print(f"   📊 GUI设置的列数量: {expected_column_count}")
    
    if len(expected_columns) != expected_column_count:
        print(f"   ❌ 列数量不匹配！期望{len(expected_columns)}列，GUI设置{expected_column_count}列")
        print(f"   🔧 建议修复: 将GUI表格列数设置为{len(expected_columns)}")
        return False
    else:
        print(f"   ✅ 列数量匹配")
    
    # 检查特码列位置
    special_column_index = expected_columns.index("特码")
    print(f"   📊 特码列位置: 第{special_column_index + 1}列 (索引{special_column_index})")
    
    return True

def create_test_data():
    """创建测试数据"""
    print("\n🧪 创建测试数据...")
    
    db_path = "data/lottery.db"
    
    try:
        # 确保目录存在
        os.makedirs("data", exist_ok=True)
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建表（如果不存在）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lottery_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                draw_date TEXT NOT NULL,
                period_number TEXT NOT NULL UNIQUE,
                regular_1 INTEGER NOT NULL,
                regular_2 INTEGER NOT NULL,
                regular_3 INTEGER NOT NULL,
                regular_4 INTEGER NOT NULL,
                regular_5 INTEGER NOT NULL,
                regular_6 INTEGER NOT NULL,
                special_number INTEGER NOT NULL
            )
        ''')
        
        # 插入测试数据
        test_data = [
            ('2024-06-20', '2024001', 1, 8, 15, 22, 29, 36, 42),
            ('2024-06-21', '2024002', 3, 10, 17, 24, 31, 38, 45),
            ('2024-06-22', '2024003', 5, 12, 19, 26, 33, 40, 47),
        ]
        
        for data in test_data:
            cursor.execute('''
                INSERT OR REPLACE INTO lottery_results
                (draw_date, period_number, regular_1, regular_2, regular_3,
                 regular_4, regular_5, regular_6, special_number)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', data)
        
        conn.commit()
        
        print(f"   ✅ 成功插入 {len(test_data)} 条测试数据")
        
        # 验证插入的数据
        cursor.execute('''
            SELECT id, draw_date, period_number, regular_1, regular_2, regular_3,
                   regular_4, regular_5, regular_6, special_number
            FROM lottery_results
            ORDER BY draw_date DESC
            LIMIT 3
        ''')
        
        results = cursor.fetchall()
        
        print(f"   📋 验证插入的数据:")
        for row in results:
            print(f"      {row}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 创建测试数据失败: {e}")
        return False

def diagnose_column_count_issue():
    """诊断列数问题"""
    print("\n🔧 诊断列数问题...")
    
    # GUI代码中的设置
    gui_column_count = 9  # 从代码中看到的设置
    gui_headers = ["ID", "日期", "期号", "正码1", "正码2", "正码3", "正码4", "正码5", "正码6", "特码"]
    
    print(f"   📋 GUI表头定义: {gui_headers}")
    print(f"   📊 GUI表头数量: {len(gui_headers)}")
    print(f"   📊 GUI设置列数: {gui_column_count}")
    
    if len(gui_headers) != gui_column_count:
        print(f"   ❌ 发现问题：表头数量({len(gui_headers)})与设置列数({gui_column_count})不匹配")
        print(f"   🔧 修复建议：将GUI代码中的setColumnCount改为{len(gui_headers)}")
        return False
    else:
        print(f"   ✅ 表头数量与设置列数匹配")
    
    # 检查数据库查询字段数量
    db_fields = ["id", "draw_date", "period_number", "regular_1", "regular_2", "regular_3", "regular_4", "regular_5", "regular_6", "special_number"]
    print(f"   📋 数据库查询字段: {db_fields}")
    print(f"   📊 数据库字段数量: {len(db_fields)}")
    
    if len(db_fields) != len(gui_headers):
        print(f"   ❌ 数据库字段数量({len(db_fields)})与GUI表头数量({len(gui_headers)})不匹配")
        return False
    else:
        print(f"   ✅ 数据库字段数量与GUI表头数量匹配")
    
    return True

def create_fix_summary():
    """创建修复总结"""
    print("\n📋 创建修复总结...")
    
    summary = f"""
# 数据预览界面特码显示问题诊断报告

## 诊断时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 问题描述
用户反馈：数据管理的数据预览界面只看到正码，没看到特码

## 可能原因分析

### 1. GUI表格列数设置问题 ⚠️
- GUI代码中设置：setColumnCount(9)
- 实际表头数量：10个 (ID, 日期, 期号, 正码1-6, 特码)
- **问题**：列数设置少了1列，导致特码列被截断

### 2. 数据库结构问题
- 检查表是否存在 lottery_results
- 检查是否包含 special_number 字段
- 检查字段数据类型是否正确

### 3. 数据内容问题
- 检查是否有实际数据
- 检查特码字段是否为空
- 检查数据格式是否正确

### 4. 查询语句问题
- 检查SQL查询是否包含特码字段
- 检查字段顺序是否正确
- 检查查询结果是否完整

## 修复方案

### 主要修复：更正GUI表格列数
```python
# 原代码（错误）
self.data_preview_table.setColumnCount(9)

# 修复后（正确）
self.data_preview_table.setColumnCount(10)
```

### 验证修复
1. 确保表头数量与列数设置一致
2. 确保数据库查询字段数量与表格列数一致
3. 测试数据显示是否完整

## 测试建议

### 1. 数据库测试
- 创建包含特码的测试数据
- 验证查询结果包含所有字段
- 检查特码字段值是否正确

### 2. GUI测试
- 启动应用程序
- 切换到数据管理标签页
- 检查数据预览表格是否显示特码列
- 验证特码数据是否正确显示

### 3. 功能测试
- 测试数据导入功能
- 测试手动添加数据功能
- 测试数据刷新功能

## 预防措施

### 1. 代码审查
- 确保表格列数与表头数量一致
- 确保数据库字段与GUI显示一致
- 添加数据验证和错误处理

### 2. 测试覆盖
- 添加GUI显示测试
- 添加数据完整性测试
- 添加边界条件测试

---
诊断完成，建议立即修复GUI表格列数设置问题。
"""
    
    with open('DATA_PREVIEW_DIAGNOSIS.md', 'w', encoding='utf-8') as f:
        f.write(summary)
    
    print("✅ 诊断报告已保存到: DATA_PREVIEW_DIAGNOSIS.md")

def main():
    """主诊断函数"""
    print("🔍 数据预览界面特码显示问题诊断")
    print("=" * 60)
    
    print("📋 诊断项目:")
    print("  1. 数据库结构检查")
    print("  2. 数据库数据检查")
    print("  3. 查询执行测试")
    print("  4. GUI表格设置检查")
    print("  5. 列数问题诊断")
    print("  6. 创建测试数据")
    print("  7. 生成修复总结")
    
    print("\n" + "=" * 60)
    
    try:
        # 运行所有诊断
        db_structure_ok = check_database_structure()
        db_data_ok = check_database_data()
        query_ok = test_query_execution()
        gui_setup_ok = check_gui_table_setup()
        column_count_ok = diagnose_column_count_issue()
        
        # 如果数据库为空，创建测试数据
        if db_structure_ok and not db_data_ok:
            create_test_data()
        
        create_fix_summary()
        
        print("\n" + "=" * 60)
        
        # 总结诊断结果
        print("🎯 诊断结果总结:")
        
        if not column_count_ok:
            print("❌ 发现主要问题：GUI表格列数设置错误")
            print("   🔧 修复方案：将 setColumnCount(9) 改为 setColumnCount(10)")
            print("   📋 原因：表头定义了10列，但只设置了9列，导致特码列被截断")
        
        if db_structure_ok:
            print("✅ 数据库结构正常")
        else:
            print("❌ 数据库结构有问题")
        
        if db_data_ok:
            print("✅ 数据库数据正常")
        else:
            print("⚠️ 数据库数据为空或有问题")
        
        if query_ok:
            print("✅ 查询执行正常")
        else:
            print("❌ 查询执行有问题")
        
        print("\n💡 修复建议:")
        print("  1. 立即修复GUI表格列数设置：setColumnCount(10)")
        print("  2. 确保数据库中有测试数据")
        print("  3. 重新启动应用程序测试修复效果")
        print("  4. 验证特码列是否正确显示")
        
        print("\n📄 生成的文件:")
        print("  📄 DATA_PREVIEW_DIAGNOSIS.md - 诊断报告")
        
    except Exception as e:
        print(f"\n❌ 诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
