"""
修复特征维度不匹配问题
"""

import os
import sys
from pathlib import Path
import json

def fix_feature_mismatch():
    """修复特征维度不匹配问题"""
    print("🔧 修复特征维度不匹配问题")
    print("=" * 50)
    
    # 切换到Windows版本目录
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    original_dir = Path.cwd()
    
    if not windows_dir.exists():
        print("❌ Windows版本目录不存在")
        return False
    
    try:
        os.chdir(windows_dir)
        sys.path.insert(0, str(Path.cwd()))
        sys.path.insert(0, str(Path.cwd() / "src"))
        
        print(f"📁 当前目录: {Path.cwd()}")
        
        # 1. 检查增强特征工程配置
        print("\n1️⃣ 检查增强特征工程配置...")
        
        enhanced_feature_file = Path("src/enhanced_feature_engineering_v2.py")
        if enhanced_feature_file.exists():
            with open(enhanced_feature_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找目标特征数配置
            if "target_features = 50" in content:
                print("❌ 发现问题：目标特征数设置为50")
                # 修复：改为20
                content = content.replace("target_features = 50", "target_features = 20")
                
                with open(enhanced_feature_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print("✅ 已修复：目标特征数改为20")
            else:
                print("⚠️ 未找到目标特征数配置")
        
        # 2. 检查机器学习模块配置
        print("\n2️⃣ 检查机器学习模块配置...")
        
        ml_module_file = Path("src/machine_learning_module_v2.py")
        if ml_module_file.exists():
            with open(ml_module_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找特征数配置
            if "n_features = 50" in content:
                print("❌ 发现问题：机器学习模块期望50个特征")
                content = content.replace("n_features = 50", "n_features = 20")
                
                with open(ml_module_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print("✅ 已修复：机器学习模块期望20个特征")
            else:
                print("⚠️ 未找到特征数配置")
        
        # 3. 检查配置文件
        print("\n3️⃣ 检查配置文件...")
        
        config_files = [
            "optimal_config.json",
            "optimization_config.json",
            "config/enhanced_config.json"
        ]
        
        for config_file in config_files:
            config_path = Path(config_file)
            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # 检查特征相关配置
                    updated = False
                    
                    if 'feature_config' in config:
                        if config['feature_config'].get('target_features') == 50:
                            config['feature_config']['target_features'] = 20
                            updated = True
                            print(f"  修复 {config_file}: target_features 50 -> 20")
                    
                    if 'machine_learning' in config:
                        if config['machine_learning'].get('n_features') == 50:
                            config['machine_learning']['n_features'] = 20
                            updated = True
                            print(f"  修复 {config_file}: n_features 50 -> 20")
                    
                    if updated:
                        with open(config_path, 'w', encoding='utf-8') as f:
                            json.dump(config, f, indent=2, ensure_ascii=False)
                        print(f"✅ 已更新配置文件: {config_file}")
                    else:
                        print(f"  {config_file}: 无需修改")
                        
                except Exception as e:
                    print(f"  ⚠️ 处理 {config_file} 失败: {e}")
        
        # 4. 创建特征维度修复脚本
        print("\n4️⃣ 创建特征维度修复脚本...")
        
        fix_script = '''# -*- coding: utf-8 -*-
"""
特征维度修复脚本
"""

def fix_feature_dimensions():
    """修复特征维度"""
    import sys
    import os
    from pathlib import Path
    
    # 添加路径
    sys.path.insert(0, str(Path.cwd()))
    sys.path.insert(0, str(Path.cwd() / "src"))
    
    try:
        # 重新训练模型以匹配新的特征维度
        from src.machine_learning_module_v2 import MachineLearningModule
        
        print("正在重新训练模型以匹配20个特征...")
        
        ml_module = MachineLearningModule()
        
        # 生成示例数据进行训练
        import numpy as np
        
        # 创建20个特征的示例数据
        X_sample = np.random.rand(100, 20)
        y_sample = np.random.randint(1, 50, 100)
        
        # 重新训练所有模型
        for model_name in ml_module.models.keys():
            try:
                model = ml_module.models[model_name]
                model.fit(X_sample, y_sample)
                print(f"✅ 模型 {model_name} 重新训练完成")
            except Exception as e:
                print(f"❌ 模型 {model_name} 训练失败: {e}")
        
        print("✅ 所有模型已重新训练完成")
        return True
        
    except Exception as e:
        print(f"❌ 特征维度修复失败: {e}")
        return False

if __name__ == "__main__":
    fix_feature_dimensions()
'''
        
        fix_script_path = Path("fix_dimensions.py")
        with open(fix_script_path, 'w', encoding='utf-8') as f:
            f.write(fix_script)
        
        print(f"✅ 特征维度修复脚本已创建: {fix_script_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False
    finally:
        os.chdir(original_dir)

def create_feature_config_fix():
    """创建特征配置修复"""
    print("\n🔧 创建特征配置修复")
    print("-" * 30)
    
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    
    # 创建统一的特征配置
    feature_config = {
        "feature_engineering": {
            "target_features": 20,
            "use_zodiac_features": True,
            "use_statistical_features": True,
            "use_pattern_features": True,
            "feature_selection": True
        },
        "machine_learning": {
            "n_features": 20,
            "feature_scaling": True,
            "cross_validation": True
        }
    }
    
    config_path = windows_dir / "feature_config.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(feature_config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 特征配置文件已创建: {config_path}")

def main():
    """主函数"""
    print("🎯 特征维度不匹配修复工具")
    print("=" * 70)
    
    # 1. 修复特征维度不匹配
    success = fix_feature_mismatch()
    
    # 2. 创建特征配置修复
    create_feature_config_fix()
    
    if success:
        print("\n🎉 特征维度修复完成！")
        print("✅ 目标特征数已统一为20")
        print("✅ 机器学习模型配置已更新")
        print("✅ 配置文件已修复")
        print("✅ 创建了维度修复脚本")
        
        print("\n📋 下一步:")
        print("  1. 重新启动程序")
        print("  2. 运行特征维度修复脚本")
        print("  3. 测试机器学习预测功能")
        
        print("\n⚠️ 重要提示:")
        print("  - 所有模型需要重新训练以匹配20个特征")
        print("  - 建议运行增强回测重新优化参数")
        print("  - 如果问题持续，可能需要清除旧的模型文件")
    else:
        print("\n❌ 特征维度修复失败")
        print("请检查错误信息并重试")
    
    return success

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
