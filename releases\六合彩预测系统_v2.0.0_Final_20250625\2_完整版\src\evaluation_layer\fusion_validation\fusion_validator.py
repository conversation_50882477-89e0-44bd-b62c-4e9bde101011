"""
融合策略验证机制 - 验证多模型融合策略的有效性
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Callable
from scipy import stats
import json
import warnings
from itertools import combinations

class FusionValidator:
    """融合策略验证器"""
    
    def __init__(self):
        """初始化融合策略验证器"""
        self.fusion_methods = {
            'simple_average': self._simple_average_fusion,
            'weighted_average': self._weighted_average_fusion,
            'voting': self._voting_fusion,
            'stacking': self._stacking_fusion,
            'dynamic_weighting': self._dynamic_weighting_fusion
        }
        
        self.validation_results = {}
    
    def validate_fusion_strategy(self, 
                                individual_predictions: Dict[str, List[Dict]],
                                actual_results: List[Dict],
                                fusion_method: str = 'simple_average',
                                fusion_params: Dict = None) -> Dict[str, Any]:
        """
        验证融合策略
        
        Args:
            individual_predictions: 各个模型的预测结果 {model_name: predictions}
            actual_results: 实际结果
            fusion_method: 融合方法
            fusion_params: 融合参数
        """
        
        print(f"🔄 验证融合策略: {fusion_method}")
        
        if fusion_method not in self.fusion_methods:
            raise ValueError(f"不支持的融合方法: {fusion_method}")
        
        # 检查输入数据
        if not individual_predictions or not actual_results:
            return {'error': '输入数据为空'}
        
        # 确保所有模型的预测数量一致
        prediction_lengths = [len(preds) for preds in individual_predictions.values()]
        if len(set(prediction_lengths)) > 1:
            warnings.warn("各模型预测数量不一致，将截取到最小长度")
            min_length = min(prediction_lengths)
            for model_name in individual_predictions:
                individual_predictions[model_name] = individual_predictions[model_name][:min_length]
            actual_results = actual_results[:min_length]
        
        # 执行融合
        fusion_params = fusion_params or {}
        fused_predictions = self.fusion_methods[fusion_method](
            individual_predictions, fusion_params
        )
        
        # 计算融合策略性能
        from src.evaluation_layer.performance_metrics.performance_metrics import PerformanceMetrics
        metrics_calculator = PerformanceMetrics()
        
        fusion_performance = metrics_calculator.evaluate_predictions(
            fused_predictions, actual_results
        )
        
        # 计算各个模型的性能
        individual_performances = {}
        for model_name, predictions in individual_predictions.items():
            performance = metrics_calculator.evaluate_predictions(
                predictions, actual_results
            )
            individual_performances[model_name] = performance
        
        # 分析融合效果
        fusion_analysis = self._analyze_fusion_effectiveness(
            individual_performances, fusion_performance
        )
        
        # 保存验证结果
        validation_result = {
            'fusion_method': fusion_method,
            'fusion_params': fusion_params,
            'fusion_performance': fusion_performance,
            'individual_performances': individual_performances,
            'fusion_analysis': fusion_analysis,
            'fused_predictions': fused_predictions,
            'model_count': len(individual_predictions),
            'prediction_count': len(fused_predictions)
        }
        
        self.validation_results[f"{fusion_method}_{len(self.validation_results)}"] = validation_result
        
        return validation_result
    
    def _simple_average_fusion(self, 
                              individual_predictions: Dict[str, List[Dict]], 
                              params: Dict) -> List[Dict]:
        """简单平均融合"""
        
        model_names = list(individual_predictions.keys())
        prediction_count = len(individual_predictions[model_names[0]])
        
        fused_predictions = []
        
        for i in range(prediction_count):
            # 收集所有模型在第i个预测的号码
            all_predicted_numbers = []
            all_confidences = []
            
            for model_name in model_names:
                pred = individual_predictions[model_name][i]
                pred_numbers = pred.get('predicted_numbers', [])
                confidence = pred.get('confidence', 0.5)
                
                all_predicted_numbers.extend(pred_numbers)
                all_confidences.append(confidence)
            
            # 统计号码频率
            number_freq = {}
            for num in all_predicted_numbers:
                number_freq[num] = number_freq.get(num, 0) + 1
            
            # 选择频率最高的号码
            if number_freq:
                sorted_numbers = sorted(number_freq.items(), key=lambda x: x[1], reverse=True)
                fused_numbers = [num for num, freq in sorted_numbers[:7]]
                
                # 如果不足7个，随机补充
                if len(fused_numbers) < 7:
                    remaining = [j for j in range(1, 50) if j not in fused_numbers]
                    additional = np.random.choice(remaining, 7 - len(fused_numbers), replace=False)
                    fused_numbers.extend(additional.tolist())
                
                fused_numbers = sorted(fused_numbers[:7])
            else:
                fused_numbers = sorted(np.random.choice(range(1, 50), 7, replace=False).tolist())
            
            # 平均置信度
            avg_confidence = np.mean(all_confidences) if all_confidences else 0.5
            
            fused_prediction = {
                'predicted_numbers': fused_numbers,
                'confidence': avg_confidence,
                'fusion_method': 'simple_average',
                'contributing_models': model_names,
                'prediction_date': individual_predictions[model_names[0]][i].get('prediction_date')
            }
            
            fused_predictions.append(fused_prediction)
        
        return fused_predictions
    
    def _weighted_average_fusion(self, 
                                individual_predictions: Dict[str, List[Dict]], 
                                params: Dict) -> List[Dict]:
        """加权平均融合"""
        
        # 获取权重，默认等权重
        weights = params.get('weights', {})
        model_names = list(individual_predictions.keys())
        
        # 如果没有指定权重，使用等权重
        if not weights:
            equal_weight = 1.0 / len(model_names)
            weights = {name: equal_weight for name in model_names}
        
        # 归一化权重
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {name: w / total_weight for name, w in weights.items()}
        
        prediction_count = len(individual_predictions[model_names[0]])
        fused_predictions = []
        
        for i in range(prediction_count):
            # 加权号码选择
            weighted_number_scores = {}
            weighted_confidence = 0.0
            
            for model_name in model_names:
                pred = individual_predictions[model_name][i]
                pred_numbers = pred.get('predicted_numbers', [])
                confidence = pred.get('confidence', 0.5)
                weight = weights.get(model_name, 0.0)
                
                # 加权置信度
                weighted_confidence += confidence * weight
                
                # 加权号码分数
                for num in pred_numbers:
                    if num not in weighted_number_scores:
                        weighted_number_scores[num] = 0.0
                    weighted_number_scores[num] += weight
            
            # 选择分数最高的号码
            if weighted_number_scores:
                sorted_numbers = sorted(weighted_number_scores.items(), key=lambda x: x[1], reverse=True)
                fused_numbers = [num for num, score in sorted_numbers[:7]]
                
                # 补充号码
                if len(fused_numbers) < 7:
                    remaining = [j for j in range(1, 50) if j not in fused_numbers]
                    additional = np.random.choice(remaining, 7 - len(fused_numbers), replace=False)
                    fused_numbers.extend(additional.tolist())
                
                fused_numbers = sorted(fused_numbers[:7])
            else:
                fused_numbers = sorted(np.random.choice(range(1, 50), 7, replace=False).tolist())
            
            fused_prediction = {
                'predicted_numbers': fused_numbers,
                'confidence': weighted_confidence,
                'fusion_method': 'weighted_average',
                'weights': weights,
                'contributing_models': model_names,
                'prediction_date': individual_predictions[model_names[0]][i].get('prediction_date')
            }
            
            fused_predictions.append(fused_prediction)
        
        return fused_predictions
    
    def _voting_fusion(self, 
                      individual_predictions: Dict[str, List[Dict]], 
                      params: Dict) -> List[Dict]:
        """投票融合"""
        
        vote_threshold = params.get('vote_threshold', 0.5)  # 投票阈值
        model_names = list(individual_predictions.keys())
        prediction_count = len(individual_predictions[model_names[0]])
        
        fused_predictions = []
        
        for i in range(prediction_count):
            # 统计每个号码的投票数
            number_votes = {}
            confidences = []
            
            for model_name in model_names:
                pred = individual_predictions[model_name][i]
                pred_numbers = pred.get('predicted_numbers', [])
                confidence = pred.get('confidence', 0.5)
                
                confidences.append(confidence)
                
                for num in pred_numbers:
                    if num not in number_votes:
                        number_votes[num] = 0
                    number_votes[num] += 1
            
            # 选择投票数超过阈值的号码
            min_votes = max(1, int(len(model_names) * vote_threshold))
            voted_numbers = [num for num, votes in number_votes.items() if votes >= min_votes]
            
            # 如果投票选出的号码不足，按投票数排序补充
            if len(voted_numbers) < 7:
                sorted_by_votes = sorted(number_votes.items(), key=lambda x: x[1], reverse=True)
                for num, votes in sorted_by_votes:
                    if num not in voted_numbers and len(voted_numbers) < 7:
                        voted_numbers.append(num)
            
            # 如果仍然不足，随机补充
            if len(voted_numbers) < 7:
                remaining = [j for j in range(1, 50) if j not in voted_numbers]
                additional = np.random.choice(remaining, 7 - len(voted_numbers), replace=False)
                voted_numbers.extend(additional.tolist())
            
            fused_numbers = sorted(voted_numbers[:7])
            avg_confidence = np.mean(confidences) if confidences else 0.5
            
            fused_prediction = {
                'predicted_numbers': fused_numbers,
                'confidence': avg_confidence,
                'fusion_method': 'voting',
                'vote_threshold': vote_threshold,
                'number_votes': number_votes,
                'contributing_models': model_names,
                'prediction_date': individual_predictions[model_names[0]][i].get('prediction_date')
            }
            
            fused_predictions.append(fused_prediction)
        
        return fused_predictions
    
    def _stacking_fusion(self, 
                        individual_predictions: Dict[str, List[Dict]], 
                        params: Dict) -> List[Dict]:
        """堆叠融合（简化版）"""
        
        # 这里实现简化的堆叠融合
        # 实际应用中可能需要训练一个元学习器
        
        model_names = list(individual_predictions.keys())
        prediction_count = len(individual_predictions[model_names[0]])
        
        # 简化：使用性能加权的方式
        model_weights = params.get('model_weights', {})
        if not model_weights:
            # 如果没有提供权重，使用等权重
            equal_weight = 1.0 / len(model_names)
            model_weights = {name: equal_weight for name in model_names}
        
        return self._weighted_average_fusion(individual_predictions, {'weights': model_weights})
    
    def _dynamic_weighting_fusion(self, 
                                 individual_predictions: Dict[str, List[Dict]], 
                                 params: Dict) -> List[Dict]:
        """动态权重融合"""
        
        model_names = list(individual_predictions.keys())
        prediction_count = len(individual_predictions[model_names[0]])
        
        # 动态权重参数
        window_size = params.get('window_size', 10)  # 滑动窗口大小
        
        fused_predictions = []
        
        for i in range(prediction_count):
            # 计算动态权重（基于最近的性能）
            if i < window_size:
                # 初期使用等权重
                weights = {name: 1.0 / len(model_names) for name in model_names}
            else:
                # 基于最近窗口的性能计算权重
                weights = self._calculate_dynamic_weights(
                    individual_predictions, i, window_size
                )
            
            # 使用动态权重进行融合
            weighted_number_scores = {}
            weighted_confidence = 0.0
            
            for model_name in model_names:
                pred = individual_predictions[model_name][i]
                pred_numbers = pred.get('predicted_numbers', [])
                confidence = pred.get('confidence', 0.5)
                weight = weights.get(model_name, 0.0)
                
                weighted_confidence += confidence * weight
                
                for num in pred_numbers:
                    if num not in weighted_number_scores:
                        weighted_number_scores[num] = 0.0
                    weighted_number_scores[num] += weight
            
            # 选择分数最高的号码
            if weighted_number_scores:
                sorted_numbers = sorted(weighted_number_scores.items(), key=lambda x: x[1], reverse=True)
                fused_numbers = [num for num, score in sorted_numbers[:7]]
                
                if len(fused_numbers) < 7:
                    remaining = [j for j in range(1, 50) if j not in fused_numbers]
                    additional = np.random.choice(remaining, 7 - len(fused_numbers), replace=False)
                    fused_numbers.extend(additional.tolist())
                
                fused_numbers = sorted(fused_numbers[:7])
            else:
                fused_numbers = sorted(np.random.choice(range(1, 50), 7, replace=False).tolist())
            
            fused_prediction = {
                'predicted_numbers': fused_numbers,
                'confidence': weighted_confidence,
                'fusion_method': 'dynamic_weighting',
                'dynamic_weights': weights,
                'window_size': window_size,
                'contributing_models': model_names,
                'prediction_date': individual_predictions[model_names[0]][i].get('prediction_date')
            }
            
            fused_predictions.append(fused_prediction)
        
        return fused_predictions
    
    def _calculate_dynamic_weights(self, 
                                  individual_predictions: Dict[str, List[Dict]], 
                                  current_index: int, 
                                  window_size: int) -> Dict[str, float]:
        """计算动态权重"""
        
        model_names = list(individual_predictions.keys())
        
        # 简化的动态权重计算
        # 实际应用中可能需要更复杂的性能评估
        
        weights = {}
        for model_name in model_names:
            # 计算最近窗口的"性能"（这里简化为置信度）
            recent_confidences = []
            start_idx = max(0, current_index - window_size)
            
            for j in range(start_idx, current_index):
                pred = individual_predictions[model_name][j]
                confidence = pred.get('confidence', 0.5)
                recent_confidences.append(confidence)
            
            avg_confidence = np.mean(recent_confidences) if recent_confidences else 0.5
            weights[model_name] = avg_confidence
        
        # 归一化权重
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {name: w / total_weight for name, w in weights.items()}
        else:
            equal_weight = 1.0 / len(model_names)
            weights = {name: equal_weight for name in model_names}
        
        return weights
    
    def _analyze_fusion_effectiveness(self, 
                                    individual_performances: Dict[str, Dict], 
                                    fusion_performance: Dict) -> Dict[str, Any]:
        """分析融合效果"""
        
        analysis = {
            'improvement_analysis': {},
            'best_individual_model': None,
            'fusion_vs_best': {},
            'fusion_vs_average': {},
            'diversity_analysis': {}
        }
        
        # 找到最佳个体模型
        if individual_performances:
            best_model = max(individual_performances.items(), 
                           key=lambda x: x[1].get('hit_rate', 0))
            analysis['best_individual_model'] = best_model[0]
            best_performance = best_model[1]
            
            # 融合 vs 最佳个体
            for metric in fusion_performance:
                if metric in best_performance:
                    fusion_value = fusion_performance[metric]
                    best_value = best_performance[metric]
                    
                    if best_value != 0:
                        improvement = (fusion_value - best_value) / best_value
                        analysis['fusion_vs_best'][metric] = {
                            'fusion_value': fusion_value,
                            'best_individual_value': best_value,
                            'improvement_ratio': improvement,
                            'is_better': fusion_value > best_value
                        }
            
            # 融合 vs 平均
            avg_performances = {}
            for metric in fusion_performance:
                metric_values = [perf.get(metric, 0) for perf in individual_performances.values()]
                avg_performances[metric] = np.mean(metric_values) if metric_values else 0
            
            for metric in fusion_performance:
                if metric in avg_performances:
                    fusion_value = fusion_performance[metric]
                    avg_value = avg_performances[metric]
                    
                    if avg_value != 0:
                        improvement = (fusion_value - avg_value) / avg_value
                        analysis['fusion_vs_average'][metric] = {
                            'fusion_value': fusion_value,
                            'average_individual_value': avg_value,
                            'improvement_ratio': improvement,
                            'is_better': fusion_value > avg_value
                        }
            
            # 多样性分析
            hit_rates = [perf.get('hit_rate', 0) for perf in individual_performances.values()]
            if len(hit_rates) > 1:
                analysis['diversity_analysis'] = {
                    'performance_std': np.std(hit_rates),
                    'performance_range': max(hit_rates) - min(hit_rates),
                    'coefficient_of_variation': np.std(hit_rates) / np.mean(hit_rates) if np.mean(hit_rates) > 0 else 0
                }
        
        return analysis
    
    def compare_fusion_methods(self, 
                              individual_predictions: Dict[str, List[Dict]],
                              actual_results: List[Dict],
                              methods: List[str] = None) -> Dict[str, Any]:
        """比较不同融合方法"""
        
        if methods is None:
            methods = list(self.fusion_methods.keys())
        
        print(f"📊 比较 {len(methods)} 种融合方法...")
        
        comparison_results = {}
        
        for method in methods:
            try:
                result = self.validate_fusion_strategy(
                    individual_predictions, actual_results, method
                )
                comparison_results[method] = result
            except Exception as e:
                print(f"❌ 融合方法 {method} 验证失败: {e}")
                continue
        
        # 生成比较报告
        comparison_report = self._generate_fusion_comparison_report(comparison_results)
        
        return {
            'individual_results': comparison_results,
            'comparison_report': comparison_report
        }
    
    def _generate_fusion_comparison_report(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成融合比较报告"""
        
        if not results:
            return {}
        
        report = {
            'method_count': len(results),
            'performance_comparison': {},
            'rankings': {},
            'recommendations': []
        }
        
        # 性能比较
        all_performances = {}
        for method, result in results.items():
            performance = result.get('fusion_performance', {})
            all_performances[method] = performance
        
        # 比较各项指标
        if all_performances:
            metric_names = set()
            for performance in all_performances.values():
                metric_names.update(performance.keys())
            
            for metric in metric_names:
                metric_values = {}
                for method, performance in all_performances.items():
                    if metric in performance:
                        metric_values[method] = performance[metric]
                
                if metric_values:
                    report['performance_comparison'][metric] = {
                        'values': metric_values,
                        'best_method': max(metric_values.items(), key=lambda x: x[1])[0],
                        'worst_method': min(metric_values.items(), key=lambda x: x[1])[0],
                        'average': np.mean(list(metric_values.values())),
                        'std': np.std(list(metric_values.values()))
                    }
        
        # 综合排名
        if 'hit_rate' in report['performance_comparison']:
            hit_rate_values = report['performance_comparison']['hit_rate']['values']
            sorted_methods = sorted(hit_rate_values.items(), key=lambda x: x[1], reverse=True)
            report['rankings']['by_hit_rate'] = [method for method, _ in sorted_methods]
        
        # 推荐
        if report['rankings'].get('by_hit_rate'):
            best_method = report['rankings']['by_hit_rate'][0]
            report['recommendations'].append(f"基于命中率，推荐使用 {best_method} 融合方法")
        
        return report
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """获取验证总结"""
        
        if not self.validation_results:
            return {'message': '暂无验证结果'}
        
        summary = {
            'total_validations': len(self.validation_results),
            'fusion_methods_tested': list(set(result['fusion_method'] for result in self.validation_results.values())),
            'best_fusion_result': None
        }
        
        # 找到最佳融合结果
        best_result = None
        best_hit_rate = 0
        
        for validation_id, result in self.validation_results.items():
            hit_rate = result.get('fusion_performance', {}).get('hit_rate', 0)
            if hit_rate > best_hit_rate:
                best_hit_rate = hit_rate
                best_result = {
                    'validation_id': validation_id,
                    'fusion_method': result['fusion_method'],
                    'hit_rate': hit_rate,
                    'model_count': result['model_count']
                }
        
        summary['best_fusion_result'] = best_result
        
        return summary
