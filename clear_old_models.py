"""
清理旧的机器学习模型文件
"""

import os
import shutil
from pathlib import Path

def clear_old_models():
    """清理旧的机器学习模型文件"""
    print("🧹 清理旧的机器学习模型文件")
    print("=" * 50)
    
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    
    if not windows_dir.exists():
        print("❌ Windows版本目录不存在")
        return False
    
    # 需要清理的文件和目录
    files_to_clear = [
        "models",
        "model_cache",
        "*.pkl",
        "*.joblib",
        "*.model",
        "ml_models",
        "trained_models",
        "model_weights",
        "scaler.pkl",
        "feature_selector.pkl"
    ]
    
    cleared_count = 0
    
    try:
        os.chdir(windows_dir)
        
        # 清理模型文件
        for pattern in files_to_clear:
            if pattern.startswith("*"):
                # 处理通配符模式
                import glob
                matching_files = glob.glob(pattern)
                for file_path in matching_files:
                    try:
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                            print(f"  🗑️ 删除文件: {file_path}")
                            cleared_count += 1
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path)
                            print(f"  🗑️ 删除目录: {file_path}")
                            cleared_count += 1
                    except Exception as e:
                        print(f"  ⚠️ 删除 {file_path} 失败: {e}")
            else:
                # 处理具体文件/目录
                target_path = Path(pattern)
                if target_path.exists():
                    try:
                        if target_path.is_file():
                            target_path.unlink()
                            print(f"  🗑️ 删除文件: {target_path}")
                            cleared_count += 1
                        elif target_path.is_dir():
                            shutil.rmtree(target_path)
                            print(f"  🗑️ 删除目录: {target_path}")
                            cleared_count += 1
                    except Exception as e:
                        print(f"  ⚠️ 删除 {target_path} 失败: {e}")
        
        # 清理src目录下的模型文件
        src_dir = Path("src")
        if src_dir.exists():
            for root, dirs, files in os.walk(src_dir):
                for file in files:
                    if file.endswith(('.pkl', '.joblib', '.model')):
                        file_path = Path(root) / file
                        try:
                            file_path.unlink()
                            print(f"  🗑️ 删除模型文件: {file_path}")
                            cleared_count += 1
                        except Exception as e:
                            print(f"  ⚠️ 删除 {file_path} 失败: {e}")
        
        print(f"\n📊 清理完成，共删除 {cleared_count} 个文件/目录")
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False

def reset_model_cache():
    """重置模型缓存"""
    print("\n🔄 重置模型缓存")
    print("-" * 30)
    
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    
    try:
        os.chdir(windows_dir)
        
        # 清理Python缓存
        import glob
        
        # 清理__pycache__目录
        pycache_dirs = glob.glob("**/__pycache__", recursive=True)
        for cache_dir in pycache_dirs:
            try:
                shutil.rmtree(cache_dir)
                print(f"  🗑️ 清理缓存: {cache_dir}")
            except Exception as e:
                print(f"  ⚠️ 清理 {cache_dir} 失败: {e}")
        
        # 清理.pyc文件
        pyc_files = glob.glob("**/*.pyc", recursive=True)
        for pyc_file in pyc_files:
            try:
                os.remove(pyc_file)
                print(f"  🗑️ 删除缓存文件: {pyc_file}")
            except Exception as e:
                print(f"  ⚠️ 删除 {pyc_file} 失败: {e}")
        
        print("✅ 模型缓存重置完成")
        return True
        
    except Exception as e:
        print(f"❌ 重置缓存失败: {e}")
        return False

def create_model_reset_flag():
    """创建模型重置标志文件"""
    print("\n🏷️ 创建模型重置标志")
    print("-" * 30)
    
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    
    flag_content = """# 模型重置标志文件
# 此文件表示模型已重置为20维特征
# 创建时间: 2025-06-26
# 特征维度: 20
# 状态: 需要重新训练

FEATURE_DIMENSION=20
MODEL_RESET=True
RETRAIN_REQUIRED=True
"""
    
    flag_file = windows_dir / "model_reset.flag"
    with open(flag_file, 'w', encoding='utf-8') as f:
        f.write(flag_content)
    
    print(f"✅ 模型重置标志已创建: {flag_file}")

def main():
    """主函数"""
    print("🎯 机器学习模型清理工具")
    print("=" * 70)
    
    print("⚠️ 重要提示:")
    print("  此操作将删除所有旧的机器学习模型文件")
    print("  模型将需要重新训练以匹配新的20维特征")
    print("  建议在执行前备份重要数据")
    
    # 1. 清理旧模型
    success1 = clear_old_models()
    
    # 2. 重置模型缓存
    success2 = reset_model_cache()
    
    # 3. 创建重置标志
    create_model_reset_flag()
    
    if success1 and success2:
        print("\n🎉 模型清理完成！")
        print("✅ 旧的50维特征模型已清理")
        print("✅ 模型缓存已重置")
        print("✅ 系统已准备好使用20维特征")
        
        print("\n📋 下一步:")
        print("  1. 重新启动程序")
        print("  2. 运行机器学习预测以重新训练模型")
        print("  3. 运行增强回测优化参数")
        print("  4. 测试所有预测功能")
        
        print("\n💡 提示:")
        print("  - 首次运行可能需要更长时间（重新训练模型）")
        print("  - 建议先导入足够的历史数据")
        print("  - 可以运行增强回测来优化新的20维特征配置")
    else:
        print("\n❌ 模型清理失败")
        print("请检查错误信息并重试")
    
    return success1 and success2

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
