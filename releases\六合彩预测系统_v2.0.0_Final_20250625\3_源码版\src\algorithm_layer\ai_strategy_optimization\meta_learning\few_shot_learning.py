"""
少样本学习 - 实现原型网络、匹配网络等少样本学习算法
"""
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Any, Optional
from abc import ABC, abstractmethod
import random

class FewShotLearner(ABC):
    """少样本学习基类"""
    
    @abstractmethod
    def train_episode(self, support_set: <PERSON><PERSON>, query_set: <PERSON><PERSON>) -> float:
        """训练一个episode"""
        pass
    
    @abstractmethod
    def predict(self, support_set: Tuple, query_samples: torch.Tensor) -> torch.Tensor:
        """预测查询样本"""
        pass

class PrototypicalNetwork(nn.Module, FewShotLearner):
    """原型网络实现"""
    
    def __init__(self, 
                 input_dim: int,
                 hidden_dims: List[int] = [128, 64],
                 output_dim: int = 32):
        super(PrototypicalNetwork, self).__init__()
        
        # 构建编码器网络
        layers = []
        current_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(current_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            current_dim = hidden_dim
        
        layers.append(nn.Linear(current_dim, output_dim))
        
        self.encoder = nn.Sequential(*layers)
        self.optimizer = torch.optim.Adam(self.parameters(), lr=0.001)
        
    def forward(self, x):
        """前向传播"""
        return self.encoder(x)
    
    def compute_prototypes(self, support_embeddings: torch.Tensor, support_labels: torch.Tensor) -> torch.Tensor:
        """计算类原型"""
        unique_labels = torch.unique(support_labels)
        prototypes = []
        
        for label in unique_labels:
            # 找到属于该类的样本
            mask = (support_labels == label)
            class_embeddings = support_embeddings[mask]
            
            # 计算原型（类中心）
            prototype = class_embeddings.mean(dim=0)
            prototypes.append(prototype)
        
        return torch.stack(prototypes)
    
    def compute_distances(self, query_embeddings: torch.Tensor, prototypes: torch.Tensor) -> torch.Tensor:
        """计算查询样本到原型的距离"""
        # 使用欧几里得距离
        distances = torch.cdist(query_embeddings, prototypes)
        return distances
    
    def train_episode(self, support_set: Tuple, query_set: Tuple) -> float:
        """训练一个episode"""
        support_data, support_labels = support_set
        query_data, query_labels = query_set
        
        # 转换为tensor
        support_data = torch.FloatTensor(support_data)
        support_labels = torch.LongTensor(support_labels)
        query_data = torch.FloatTensor(query_data)
        query_labels = torch.LongTensor(query_labels)
        
        # 编码
        support_embeddings = self.forward(support_data)
        query_embeddings = self.forward(query_data)
        
        # 计算原型
        prototypes = self.compute_prototypes(support_embeddings, support_labels)
        
        # 计算距离和概率
        distances = self.compute_distances(query_embeddings, prototypes)
        log_probabilities = F.log_softmax(-distances, dim=1)
        
        # 计算损失
        loss = F.nll_loss(log_probabilities, query_labels)
        
        # 反向传播
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
        
        return loss.item()
    
    def predict(self, support_set: Tuple, query_samples: torch.Tensor) -> torch.Tensor:
        """预测查询样本"""
        support_data, support_labels = support_set
        
        support_data = torch.FloatTensor(support_data)
        support_labels = torch.LongTensor(support_labels)
        
        with torch.no_grad():
            # 编码
            support_embeddings = self.forward(support_data)
            query_embeddings = self.forward(query_samples)
            
            # 计算原型
            prototypes = self.compute_prototypes(support_embeddings, support_labels)
            
            # 计算距离和概率
            distances = self.compute_distances(query_embeddings, prototypes)
            probabilities = F.softmax(-distances, dim=1)
            
            return probabilities

class MatchingNetwork(nn.Module, FewShotLearner):
    """匹配网络实现"""
    
    def __init__(self, 
                 input_dim: int,
                 hidden_dim: int = 64,
                 lstm_layers: int = 1):
        super(MatchingNetwork, self).__init__()
        
        # 特征编码器
        self.feature_encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # 上下文编码器（LSTM）
        self.context_encoder = nn.LSTM(
            hidden_dim, hidden_dim, 
            num_layers=lstm_layers, 
            batch_first=True
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(hidden_dim, num_heads=4)
        
        self.optimizer = torch.optim.Adam(self.parameters(), lr=0.001)
    
    def encode_support_set(self, support_data: torch.Tensor) -> torch.Tensor:
        """编码支持集"""
        # 特征编码
        features = self.feature_encoder(support_data)
        
        # 上下文编码
        context_features, _ = self.context_encoder(features.unsqueeze(0))
        
        return context_features.squeeze(0)
    
    def compute_attention_weights(self, 
                                query_embedding: torch.Tensor, 
                                support_embeddings: torch.Tensor) -> torch.Tensor:
        """计算注意力权重"""
        # 使用多头注意力
        query = query_embedding.unsqueeze(0).unsqueeze(0)  # [1, 1, hidden_dim]
        key = value = support_embeddings.unsqueeze(0)      # [1, n_support, hidden_dim]
        
        attended_output, attention_weights = self.attention(query, key, value)
        
        return attention_weights.squeeze(0).squeeze(0)  # [n_support]
    
    def train_episode(self, support_set: Tuple, query_set: Tuple) -> float:
        """训练一个episode"""
        support_data, support_labels = support_set
        query_data, query_labels = query_set
        
        support_data = torch.FloatTensor(support_data)
        support_labels = torch.LongTensor(support_labels)
        query_data = torch.FloatTensor(query_data)
        query_labels = torch.LongTensor(query_labels)
        
        # 编码支持集
        support_embeddings = self.encode_support_set(support_data)
        
        total_loss = 0
        for i, query_sample in enumerate(query_data):
            # 编码查询样本
            query_embedding = self.feature_encoder(query_sample)
            
            # 计算注意力权重
            attention_weights = self.compute_attention_weights(query_embedding, support_embeddings)
            
            # 计算预测概率
            unique_labels = torch.unique(support_labels)
            class_probabilities = []
            
            for label in unique_labels:
                mask = (support_labels == label)
                class_attention = attention_weights[mask].sum()
                class_probabilities.append(class_attention)
            
            class_probabilities = torch.stack(class_probabilities)
            class_probabilities = F.softmax(class_probabilities, dim=0)
            
            # 计算损失
            target_class_idx = (unique_labels == query_labels[i]).nonzero(as_tuple=True)[0]
            loss = F.cross_entropy(class_probabilities.unsqueeze(0), target_class_idx)
            total_loss += loss
        
        avg_loss = total_loss / len(query_data)
        
        # 反向传播
        self.optimizer.zero_grad()
        avg_loss.backward()
        self.optimizer.step()
        
        return avg_loss.item()
    
    def predict(self, support_set: Tuple, query_samples: torch.Tensor) -> torch.Tensor:
        """预测查询样本"""
        support_data, support_labels = support_set
        
        support_data = torch.FloatTensor(support_data)
        support_labels = torch.LongTensor(support_labels)
        
        with torch.no_grad():
            # 编码支持集
            support_embeddings = self.encode_support_set(support_data)
            unique_labels = torch.unique(support_labels)
            
            predictions = []
            for query_sample in query_samples:
                # 编码查询样本
                query_embedding = self.feature_encoder(query_sample)
                
                # 计算注意力权重
                attention_weights = self.compute_attention_weights(query_embedding, support_embeddings)
                
                # 计算类概率
                class_probabilities = []
                for label in unique_labels:
                    mask = (support_labels == label)
                    class_attention = attention_weights[mask].sum()
                    class_probabilities.append(class_attention)
                
                class_probabilities = torch.stack(class_probabilities)
                class_probabilities = F.softmax(class_probabilities, dim=0)
                predictions.append(class_probabilities)
            
            return torch.stack(predictions)

class MAML(nn.Module, FewShotLearner):
    """模型无关元学习(MAML)实现"""
    
    def __init__(self, 
                 input_dim: int,
                 hidden_dims: List[int] = [128, 64],
                 output_dim: int = 2,
                 inner_lr: float = 0.01,
                 meta_lr: float = 0.001):
        super(MAML, self).__init__()
        
        # 构建基础网络
        layers = []
        current_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(current_dim, hidden_dim),
                nn.ReLU()
            ])
            current_dim = hidden_dim
        
        layers.append(nn.Linear(current_dim, output_dim))
        
        self.base_network = nn.Sequential(*layers)
        
        self.inner_lr = inner_lr
        self.meta_optimizer = torch.optim.Adam(self.parameters(), lr=meta_lr)
    
    def forward(self, x, params=None):
        """前向传播，可以使用自定义参数"""
        if params is None:
            return self.base_network(x)
        else:
            # 使用自定义参数进行前向传播
            x = F.linear(x, params['0.weight'], params['0.bias'])
            x = F.relu(x)
            x = F.linear(x, params['2.weight'], params['2.bias'])
            x = F.relu(x)
            x = F.linear(x, params['4.weight'], params['4.bias'])
            return x
    
    def inner_update(self, support_set: Tuple, num_steps: int = 1) -> Dict[str, torch.Tensor]:
        """内循环更新"""
        support_data, support_labels = support_set
        support_data = torch.FloatTensor(support_data)
        support_labels = torch.LongTensor(support_labels)
        
        # 复制当前参数
        params = {}
        for name, param in self.base_network.named_parameters():
            params[name] = param.clone()
        
        # 内循环梯度更新
        for step in range(num_steps):
            # 前向传播
            logits = self.forward(support_data, params)
            loss = F.cross_entropy(logits, support_labels)
            
            # 计算梯度
            grads = torch.autograd.grad(loss, params.values(), create_graph=True)
            
            # 更新参数
            for (name, param), grad in zip(params.items(), grads):
                params[name] = param - self.inner_lr * grad
        
        return params
    
    def train_episode(self, support_set: Tuple, query_set: Tuple) -> float:
        """训练一个episode"""
        query_data, query_labels = query_set
        query_data = torch.FloatTensor(query_data)
        query_labels = torch.LongTensor(query_labels)
        
        # 内循环更新
        adapted_params = self.inner_update(support_set)
        
        # 在查询集上计算损失
        query_logits = self.forward(query_data, adapted_params)
        meta_loss = F.cross_entropy(query_logits, query_labels)
        
        # 元优化器更新
        self.meta_optimizer.zero_grad()
        meta_loss.backward()
        self.meta_optimizer.step()
        
        return meta_loss.item()
    
    def predict(self, support_set: Tuple, query_samples: torch.Tensor) -> torch.Tensor:
        """预测查询样本"""
        with torch.no_grad():
            # 在支持集上适应
            adapted_params = self.inner_update(support_set)
            
            # 预测查询样本
            logits = self.forward(query_samples, adapted_params)
            probabilities = F.softmax(logits, dim=1)
            
            return probabilities

class FewShotLearningManager:
    """少样本学习管理器"""
    
    def __init__(self):
        self.models = {}
        self.training_history = []
    
    def create_model(self, 
                    model_type: str,
                    model_name: str,
                    input_dim: int,
                    **kwargs) -> FewShotLearner:
        """创建少样本学习模型"""
        
        if model_type == 'prototypical':
            model = PrototypicalNetwork(input_dim, **kwargs)
        elif model_type == 'matching':
            model = MatchingNetwork(input_dim, **kwargs)
        elif model_type == 'maml':
            model = MAML(input_dim, **kwargs)
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        self.models[model_name] = model
        return model
    
    def generate_episode(self, 
                        data: np.ndarray,
                        labels: np.ndarray,
                        n_way: int = 5,
                        k_shot: int = 1,
                        n_query: int = 15) -> Tuple[Tuple, Tuple]:
        """生成训练episode"""
        
        unique_labels = np.unique(labels)
        if len(unique_labels) < n_way:
            raise ValueError(f"数据中的类别数({len(unique_labels)})少于n_way({n_way})")
        
        # 随机选择n_way个类别
        selected_classes = np.random.choice(unique_labels, n_way, replace=False)
        
        support_data, support_labels = [], []
        query_data, query_labels = [], []
        
        for i, class_label in enumerate(selected_classes):
            # 获取该类的所有样本
            class_indices = np.where(labels == class_label)[0]
            
            if len(class_indices) < k_shot + n_query:
                raise ValueError(f"类别{class_label}的样本数不足")
            
            # 随机选择样本
            selected_indices = np.random.choice(class_indices, k_shot + n_query, replace=False)
            
            # 分配给支持集和查询集
            support_indices = selected_indices[:k_shot]
            query_indices = selected_indices[k_shot:]
            
            support_data.extend(data[support_indices])
            support_labels.extend([i] * k_shot)  # 重新标记为0, 1, 2, ...
            
            query_data.extend(data[query_indices])
            query_labels.extend([i] * n_query)
        
        support_set = (np.array(support_data), np.array(support_labels))
        query_set = (np.array(query_data), np.array(query_labels))
        
        return support_set, query_set
    
    def train_model(self, 
                   model_name: str,
                   data: np.ndarray,
                   labels: np.ndarray,
                   n_episodes: int = 1000,
                   n_way: int = 5,
                   k_shot: int = 1,
                   n_query: int = 15) -> List[float]:
        """训练模型"""
        
        if model_name not in self.models:
            raise ValueError(f"模型{model_name}不存在")
        
        model = self.models[model_name]
        losses = []
        
        for episode in range(n_episodes):
            # 生成episode
            support_set, query_set = self.generate_episode(
                data, labels, n_way, k_shot, n_query
            )
            
            # 训练
            loss = model.train_episode(support_set, query_set)
            losses.append(loss)
            
            # 记录进度
            if episode % 100 == 0:
                avg_loss = np.mean(losses[-100:])
                print(f"Episode {episode}, Average Loss: {avg_loss:.4f}")
        
        # 记录训练历史
        self.training_history.append({
            'model_name': model_name,
            'n_episodes': n_episodes,
            'losses': losses,
            'final_loss': losses[-1] if losses else 0
        })
        
        return losses
    
    def evaluate_model(self, 
                      model_name: str,
                      data: np.ndarray,
                      labels: np.ndarray,
                      n_episodes: int = 100,
                      n_way: int = 5,
                      k_shot: int = 1,
                      n_query: int = 15) -> Dict[str, float]:
        """评估模型"""
        
        if model_name not in self.models:
            raise ValueError(f"模型{model_name}不存在")
        
        model = self.models[model_name]
        accuracies = []
        
        for episode in range(n_episodes):
            # 生成测试episode
            support_set, query_set = self.generate_episode(
                data, labels, n_way, k_shot, n_query
            )
            
            query_data, query_labels = query_set
            query_data = torch.FloatTensor(query_data)
            
            # 预测
            predictions = model.predict(support_set, query_data)
            predicted_labels = torch.argmax(predictions, dim=1)
            
            # 计算准确率
            accuracy = (predicted_labels == torch.LongTensor(query_labels)).float().mean().item()
            accuracies.append(accuracy)
        
        return {
            'mean_accuracy': np.mean(accuracies),
            'std_accuracy': np.std(accuracies),
            'min_accuracy': np.min(accuracies),
            'max_accuracy': np.max(accuracies)
        }
