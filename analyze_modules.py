"""
分析Mock模组与真实模组的差异，制定替换方案
"""
import sys
import os
sys.path.append('src')

def analyze_mock_vs_real_modules():
    """分析Mock模组与真实模组的差异"""
    print("🔍 分析Mock模组与真实模组的差异")
    print("=" * 50)
    
    # 1. 检查当前Mock模组
    print("📋 当前Mock模组分析:")
    
    try:
        with open("src/perfect_prediction_system.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 查找Mock类定义
        mock_classes = []
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'class Mock' in line and 'Module' in line:
                mock_classes.append({
                    'name': line.strip(),
                    'line': i + 1
                })
        
        print(f"   找到 {len(mock_classes)} 个Mock类:")
        for mock in mock_classes:
            print(f"      {mock['name']} (第{mock['line']}行)")
        
    except Exception as e:
        print(f"   ❌ 分析Mock类失败: {e}")
    
    # 2. 检查真实模组文件
    print(f"\n📊 真实模组文件检查:")
    
    real_modules = [
        {
            'name': '传统统计分析',
            'file': 'src/algorithm_layer/traditional_analysis/traditional_statistical_fixed.py',
            'class': 'TraditionalStatisticalAnalysis',
            'mock': 'MockTraditionalModule'
        },
        {
            'name': '机器学习预测',
            'file': 'src/algorithm_layer/ml_models/ml_predictor.py', 
            'class': 'MachineLearningPredictor',
            'mock': 'MockMLModule'
        },
        {
            'name': '生肖扩展分析',
            'file': 'src/algorithm_layer/zodiac_analysis/zodiac_extended_analysis.py',
            'class': 'ZodiacExtendedAnalysis',
            'mock': 'MockZodiacExtendedModule'
        },
        {
            'name': '特码生肖模组',
            'file': 'src/algorithm_layer/zodiac_analysis/special_zodiac_predictor.py',
            'class': 'SpecialZodiacPredictor', 
            'mock': 'MockSpecialZodiacModule'
        }
    ]
    
    module_status = []
    for module in real_modules:
        status = {
            'name': module['name'],
            'file': module['file'],
            'class': module['class'],
            'mock': module['mock'],
            'exists': os.path.exists(module['file']),
            'importable': False,
            'methods': []
        }
        
        if status['exists']:
            print(f"   ✅ {module['name']}: 文件存在")
            
            # 检查是否可导入
            try:
                # 动态导入检查
                module_path = module['file'].replace('/', '.').replace('\\', '.').replace('.py', '')
                if module_path.startswith('src.'):
                    module_path = module_path[4:]  # 移除'src.'前缀
                
                print(f"      尝试导入: {module_path}")
                
                # 读取文件检查类和方法
                with open(module['file'], 'r', encoding='utf-8') as f:
                    file_content = f.read()
                
                if f"class {module['class']}" in file_content:
                    status['importable'] = True
                    print(f"      ✅ 类 {module['class']} 存在")
                    
                    # 查找关键方法
                    key_methods = ['predict', '__init__', 'analyze', 'train']
                    for method in key_methods:
                        if f"def {method}" in file_content:
                            status['methods'].append(method)
                    
                    print(f"      📊 关键方法: {status['methods']}")
                else:
                    print(f"      ❌ 类 {module['class']} 不存在")
                    
            except Exception as e:
                print(f"      ❌ 导入检查失败: {e}")
        else:
            print(f"   ❌ {module['name']}: 文件不存在 ({module['file']})")
        
        module_status.append(status)
    
    return module_status

def analyze_interface_compatibility():
    """分析接口兼容性"""
    print(f"\n🔧 分析接口兼容性:")
    
    # 分析Mock模组的接口
    mock_interfaces = {
        'MockTraditionalModule': {
            'methods': ['predict'],
            'predict_signature': 'predict(self, target_date: str)',
            'predict_return': 'Dict[str, Any]',
            'required_fields': ['predicted_numbers', 'confidence']
        },
        'MockMLModule': {
            'methods': ['predict'],
            'predict_signature': 'predict(self, target_date: str)', 
            'predict_return': 'Dict[str, Any]',
            'required_fields': ['predicted_numbers', 'confidence']
        },
        'MockZodiacExtendedModule': {
            'methods': ['predict'],
            'predict_signature': 'predict(self, target_date=None, historical_data=None)',
            'predict_return': 'Dict[str, Any]',
            'required_fields': ['predicted_numbers', 'confidence']
        },
        'MockSpecialZodiacModule': {
            'methods': ['predict_zodiacs'],
            'predict_signature': 'predict_zodiacs(self, predicted_numbers: List[int])',
            'predict_return': 'List[str]',
            'required_fields': ['zodiacs']
        }
    }
    
    print("📋 Mock模组接口规范:")
    for mock_name, interface in mock_interfaces.items():
        print(f"   {mock_name}:")
        print(f"      方法: {interface['methods']}")
        print(f"      签名: {interface['predict_signature']}")
        print(f"      返回: {interface['predict_return']}")
        print(f"      必需字段: {interface['required_fields']}")
    
    return mock_interfaces

def create_replacement_plan(module_status, mock_interfaces):
    """创建替换计划"""
    print(f"\n📋 创建模组替换计划:")
    
    replacement_plan = {
        'immediate_replaceable': [],  # 可立即替换
        'needs_adaptation': [],       # 需要适配
        'needs_creation': [],         # 需要创建
        'priority_order': []          # 优先级顺序
    }
    
    for module in module_status:
        if module['exists'] and module['importable']:
            if 'predict' in module['methods']:
                replacement_plan['immediate_replaceable'].append(module)
                print(f"   ✅ {module['name']}: 可立即替换")
            else:
                replacement_plan['needs_adaptation'].append(module)
                print(f"   ⚠️ {module['name']}: 需要接口适配")
        else:
            replacement_plan['needs_creation'].append(module)
            print(f"   ❌ {module['name']}: 需要创建或修复")
    
    # 设置优先级
    priority_mapping = {
        '传统统计分析': 1,
        '机器学习预测': 2, 
        '特码生肖模组': 3,
        '生肖扩展分析': 4
    }
    
    all_modules = (replacement_plan['immediate_replaceable'] + 
                  replacement_plan['needs_adaptation'] + 
                  replacement_plan['needs_creation'])
    
    replacement_plan['priority_order'] = sorted(
        all_modules, 
        key=lambda x: priority_mapping.get(x['name'], 999)
    )
    
    print(f"\n🎯 替换优先级顺序:")
    for i, module in enumerate(replacement_plan['priority_order'], 1):
        status = "✅可替换" if module in replacement_plan['immediate_replaceable'] else \
                "⚠️需适配" if module in replacement_plan['needs_adaptation'] else "❌需创建"
        print(f"   {i}. {module['name']} - {status}")
    
    return replacement_plan

if __name__ == "__main__":
    # 分析模组状态
    module_status = analyze_mock_vs_real_modules()
    
    # 分析接口兼容性
    mock_interfaces = analyze_interface_compatibility()
    
    # 创建替换计划
    replacement_plan = create_replacement_plan(module_status, mock_interfaces)
    
    print(f"\n🎊 分析完成!")
    print(f"📊 可立即替换: {len(replacement_plan['immediate_replaceable'])}个")
    print(f"⚠️ 需要适配: {len(replacement_plan['needs_adaptation'])}个") 
    print(f"❌ 需要创建: {len(replacement_plan['needs_creation'])}个")
