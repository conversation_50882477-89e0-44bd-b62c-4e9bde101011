"""
传统统计分析模块
实现五行、波色、大小单双、尾数等传统分析方法
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from datetime import date, datetime, timedelta
from collections import Counter, defaultdict
from loguru import logger
import json

class TraditionalStatisticalAnalysis:
    """传统统计分析器"""
    
    def __init__(self, db_session, number_attribute_mapper):
        self.db = db_session
        self.attr_mapper = number_attribute_mapper
        
        # 分析方法权重
        self.method_weights = {
            "frequency_analysis": 0.20,     # 频率分析
            "hot_cold_analysis": 0.15,      # 冷热号分析
            "tail_analysis": 0.15,          # 尾数分析
            "size_parity_analysis": 0.15,   # 大小单双分析
            "wave_color_analysis": 0.10,    # 波色分析
            "segment_analysis": 0.10,       # 段位分析
            "prime_analysis": 0.05,         # 质合分析
            "pattern_analysis": 0.10        # 模式分析
        }
    
    def comprehensive_analysis(self, recent_periods: int = 50, target_date: date = None) -> Dict[str, Any]:
        """综合传统统计分析"""
        logger.info(f"开始传统统计分析，分析最近 {recent_periods} 期数据")
        
        if target_date is None:
            target_date = date.today()
        
        # 获取历史数据
        historical_data = self._get_historical_data(recent_periods, target_date)
        
        if len(historical_data) < 10:
            logger.warning(f"历史数据不足，仅有 {len(historical_data)} 期")
            return self._get_default_analysis()
        
        # 执行各种传统分析
        analysis_results = {}
        
        # 1. 频率分析
        analysis_results["frequency"] = self._frequency_analysis(historical_data)
        
        # 2. 冷热号分析
        analysis_results["hot_cold"] = self._hot_cold_analysis(historical_data)
        
        # 3. 尾数分析
        analysis_results["tail"] = self._tail_analysis(historical_data)
        
        # 4. 大小单双分析
        analysis_results["size_parity"] = self._size_parity_analysis(historical_data)
        
        # 5. 波色分析
        analysis_results["wave_color"] = self._wave_color_analysis(historical_data)
        
        # 6. 段位分析
        analysis_results["segment"] = self._segment_analysis(historical_data)
        
        # 7. 质合分析
        analysis_results["prime"] = self._prime_analysis(historical_data)
        
        # 8. 模式分析
        analysis_results["pattern"] = self._pattern_analysis(historical_data)
        
        # 综合评分
        final_scores = self._calculate_comprehensive_scores(analysis_results)
        
        # 选择推荐号码
        recommended_numbers = self._select_recommended_numbers(final_scores)
        
        # 生成分析结果
        result = {
            "recommended_numbers": recommended_numbers,
            "number_scores": final_scores,
            "analysis_details": analysis_results,
            "confidence_level": self._calculate_confidence_level(final_scores, recommended_numbers),
            "analysis_time": datetime.now(),
            "data_periods": len(historical_data),
            "method_weights": self.method_weights
        }
        
        logger.info(f"传统统计分析完成，推荐号码数量: {len(recommended_numbers)}")
        return result
    
    def _get_historical_data(self, recent_periods: int, target_date: date) -> List[Dict]:
        """获取历史数据"""
        from src.data_layer.database.models import LotteryData
        
        query = self.db.query(LotteryData).filter(
            LotteryData.draw_date < target_date
        ).order_by(LotteryData.draw_date.desc()).limit(recent_periods)
        
        records = query.all()
        
        historical_data = []
        for record in records:
            historical_data.append({
                'period_number': record.period_number,
                'draw_date': record.draw_date,
                'special_number': record.special_number,
                'special_size': record.special_size,
                'special_parity': record.special_parity,
                'special_prime': record.special_prime,
                'special_tail': record.special_tail,
                'special_wave_color': record.special_wave_color,
                'special_positive_negative': record.special_positive_negative,
                'special_segment': record.special_segment
            })
        
        return historical_data
    
    def _frequency_analysis(self, historical_data: List[Dict]) -> Dict[str, float]:
        """频率分析"""
        number_counts = Counter([record['special_number'] for record in historical_data])
        total_count = len(historical_data)
        
        frequency_scores = {}
        for number in range(1, 50):
            count = number_counts.get(number, 0)
            frequency = count / total_count if total_count > 0 else 0
            
            # 基于频率给分：低频号码给高分（回补理论）
            if frequency == 0:  # 从未出现
                score = 0.9
            elif frequency < 0.01:  # 极低频
                score = 0.8
            elif frequency < 0.02:  # 低频
                score = 0.7
            elif frequency > 0.05:  # 高频
                score = 0.3
            else:  # 正常频率
                score = 0.5
            
            frequency_scores[number] = score
        
        return frequency_scores
    
    def _hot_cold_analysis(self, historical_data: List[Dict]) -> Dict[str, float]:
        """冷热号分析"""
        hot_cold_scores = {}
        
        # 分析最近出现位置
        for number in range(1, 50):
            last_position = None
            for i, record in enumerate(historical_data):
                if record['special_number'] == number:
                    last_position = i
                    break
            
            if last_position is None:
                # 从未出现过
                score = 0.9
            elif last_position == 0:
                # 上期刚出现
                score = 0.1
            elif last_position <= 3:
                # 最近3期内出现
                score = 0.2
            elif last_position <= 10:
                # 最近10期内出现
                score = 0.5
            else:
                # 很久没出现（冷号）
                score = 0.8
            
            hot_cold_scores[number] = score
        
        return hot_cold_scores
    
    def _tail_analysis(self, historical_data: List[Dict]) -> Dict[str, float]:
        """尾数分析"""
        tail_counts = Counter([record['special_tail'] for record in historical_data])
        total_count = len(historical_data)
        
        # 计算各尾数的出现频率
        tail_frequencies = {}
        for tail in range(10):
            count = tail_counts.get(tail, 0)
            tail_frequencies[tail] = count / total_count if total_count > 0 else 0
        
        # 为每个号码基于尾数频率评分
        tail_scores = {}
        for number in range(1, 50):
            tail = number % 10
            frequency = tail_frequencies[tail]
            
            # 低频尾数给高分
            if frequency < 0.05:
                score = 0.8
            elif frequency > 0.15:
                score = 0.3
            else:
                score = 0.5
            
            tail_scores[number] = score
        
        return tail_scores
    
    def _size_parity_analysis(self, historical_data: List[Dict]) -> Dict[str, float]:
        """大小单双分析"""
        size_counts = Counter([record['special_size'] for record in historical_data])
        parity_counts = Counter([record['special_parity'] for record in historical_data])
        
        total_count = len(historical_data)
        
        # 计算大小单双的频率
        size_freq = {
            '大': size_counts.get('大', 0) / total_count,
            '小': size_counts.get('小', 0) / total_count
        }
        parity_freq = {
            '单': parity_counts.get('单', 0) / total_count,
            '双': parity_counts.get('双', 0) / total_count
        }
        
        size_parity_scores = {}
        for number in range(1, 50):
            size = '小' if number <= 24 else '大'
            parity = '单' if number % 2 == 1 else '双'
            
            # 基于大小单双的平衡性评分
            size_score = 0.7 if size_freq[size] < 0.4 else 0.3
            parity_score = 0.7 if parity_freq[parity] < 0.4 else 0.3
            
            # 综合评分
            score = (size_score + parity_score) / 2
            size_parity_scores[number] = score
        
        return size_parity_scores
    
    def _wave_color_analysis(self, historical_data: List[Dict]) -> Dict[str, float]:
        """波色分析"""
        wave_counts = Counter([record['special_wave_color'] for record in historical_data])
        total_count = len(historical_data)
        
        # 计算各波色频率
        wave_freq = {}
        for color in ['红', '绿', '蓝']:
            wave_freq[color] = wave_counts.get(color, 0) / total_count
        
        wave_scores = {}
        for number in range(1, 50):
            wave_color = self.attr_mapper.get_number_attributes(number)['wave_color']
            frequency = wave_freq.get(wave_color, 0)
            
            # 低频波色给高分
            if frequency < 0.25:
                score = 0.8
            elif frequency > 0.4:
                score = 0.3
            else:
                score = 0.5
            
            wave_scores[number] = score
        
        return wave_scores
    
    def _segment_analysis(self, historical_data: List[Dict]) -> Dict[str, float]:
        """段位分析"""
        segment_counts = Counter([record['special_segment'] for record in historical_data])
        total_count = len(historical_data)
        
        # 计算各段位频率
        segment_freq = {}
        for segment in range(1, 8):
            segment_freq[segment] = segment_counts.get(segment, 0) / total_count
        
        segment_scores = {}
        for number in range(1, 50):
            segment = self.attr_mapper.get_number_attributes(number)['segment']
            frequency = segment_freq.get(segment, 0)
            
            # 低频段位给高分
            expected_freq = 1/7  # 期望频率
            if frequency < expected_freq * 0.5:
                score = 0.8
            elif frequency > expected_freq * 1.5:
                score = 0.3
            else:
                score = 0.5
            
            segment_scores[number] = score
        
        return segment_scores
    
    def _prime_analysis(self, historical_data: List[Dict]) -> Dict[str, float]:
        """质合分析"""
        prime_counts = Counter([record['special_prime'] for record in historical_data])
        total_count = len(historical_data)
        
        # 计算质合频率
        prime_freq = {
            '质': prime_counts.get('质', 0) / total_count,
            '合': prime_counts.get('合', 0) / total_count
        }
        
        prime_scores = {}
        for number in range(1, 50):
            prime_type = self.attr_mapper.get_number_attributes(number)['prime']
            frequency = prime_freq.get(prime_type, 0)
            
            # 基于质合平衡性评分
            if frequency < 0.4:
                score = 0.7
            elif frequency > 0.6:
                score = 0.3
            else:
                score = 0.5
            
            prime_scores[number] = score
        
        return prime_scores
    
    def _pattern_analysis(self, historical_data: List[Dict]) -> Dict[str, float]:
        """模式分析"""
        pattern_scores = {}
        
        # 分析连号模式
        consecutive_patterns = self._analyze_consecutive_patterns(historical_data)
        
        # 分析跳号模式
        jump_patterns = self._analyze_jump_patterns(historical_data)
        
        for number in range(1, 50):
            score = 0.5  # 基础分数
            
            # 连号模式评分
            if number in consecutive_patterns:
                if consecutive_patterns[number]:
                    score += 0.2
                else:
                    score -= 0.1
            
            # 跳号模式评分
            if number in jump_patterns:
                if jump_patterns[number]:
                    score += 0.2
                else:
                    score -= 0.1
            
            pattern_scores[number] = max(0.1, min(0.9, score))
        
        return pattern_scores
    
    def _analyze_consecutive_patterns(self, historical_data: List[Dict]) -> Dict[int, bool]:
        """分析连号模式"""
        patterns = {}
        
        recent_numbers = [record['special_number'] for record in historical_data[:5]]
        
        for number in range(1, 50):
            # 检查相邻号码是否最近出现过
            adjacent_numbers = []
            if number > 1:
                adjacent_numbers.append(number - 1)
            if number < 49:
                adjacent_numbers.append(number + 1)
            
            adjacent_appeared = any(adj in recent_numbers for adj in adjacent_numbers)
            patterns[number] = adjacent_appeared
        
        return patterns
    
    def _analyze_jump_patterns(self, historical_data: List[Dict]) -> Dict[int, bool]:
        """分析跳号模式"""
        patterns = {}
        
        if len(historical_data) < 3:
            return {number: False for number in range(1, 50)}
        
        # 分析最近几期的跳跃模式
        recent_numbers = [record['special_number'] for record in historical_data[:3]]
        
        for number in range(1, 50):
            # 简单的跳跃模式：检查是否符合某种间隔规律
            should_appear = False
            
            # 检查是否在某种间隔序列中
            for interval in [7, 12, 15]:  # 常见间隔
                for start in recent_numbers:
                    if abs(number - start) == interval:
                        should_appear = True
                        break
                if should_appear:
                    break
            
            patterns[number] = should_appear
        
        return patterns
    
    def _calculate_comprehensive_scores(self, analysis_results: Dict[str, Dict[str, float]]) -> Dict[int, float]:
        """计算综合得分"""
        comprehensive_scores = {}
        
        for number in range(1, 50):
            total_score = 0
            
            for method, weight in self.method_weights.items():
                if method in analysis_results:
                    method_score = analysis_results[method].get(number, 0.5)
                    total_score += method_score * weight
            
            comprehensive_scores[number] = total_score
        
        return comprehensive_scores
    
    def _select_recommended_numbers(self, scores: Dict[int, float], min_count: int = 16, max_count: int = 24) -> List[int]:
        """选择推荐号码"""
        # 按得分排序
        sorted_numbers = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        # 动态选择数量：基于得分分布
        score_threshold = np.percentile(list(scores.values()), 75)  # 75分位数
        
        recommended = []
        for number, score in sorted_numbers:
            if score >= score_threshold and len(recommended) < max_count:
                recommended.append(number)
            elif len(recommended) >= min_count:
                break
        
        # 确保至少有min_count个号码
        if len(recommended) < min_count:
            recommended = [number for number, score in sorted_numbers[:min_count]]
        
        return sorted(recommended)
    
    def _calculate_confidence_level(self, scores: Dict[int, float], recommended_numbers: List[int]) -> float:
        """计算置信度"""
        if not recommended_numbers:
            return 0.0
        
        recommended_scores = [scores[number] for number in recommended_numbers]
        avg_recommended_score = np.mean(recommended_scores)
        
        all_scores = list(scores.values())
        avg_all_score = np.mean(all_scores)
        
        # 置信度基于推荐号码与平均分的差距
        confidence = min(0.95, max(0.1, (avg_recommended_score - avg_all_score) * 3 + 0.5))
        return confidence
    
    def _get_default_analysis(self) -> Dict[str, Any]:
        """获取默认分析结果"""
        # 基于经验的默认推荐
        default_numbers = list(range(1, 25))  # 默认推荐前24个号码
        
        return {
            "recommended_numbers": default_numbers,
            "number_scores": {number: 0.5 for number in range(1, 50)},
            "analysis_details": {},
            "confidence_level": 0.3,
            "analysis_time": datetime.now(),
            "data_periods": 0,
            "method_weights": self.method_weights,
            "note": "数据不足，使用默认分析"
        }
    
    def get_analysis_report(self, analysis_result: Dict[str, Any]) -> str:
        """生成分析报告"""
        report = []
        report.append("=== 传统统计分析报告 ===")
        report.append(f"分析时间: {analysis_result['analysis_time']}")
        report.append(f"分析期数: {analysis_result['data_periods']}")
        report.append(f"置信度: {analysis_result['confidence_level']:.2%}")
        report.append("")
        
        report.append(f"推荐号码 ({len(analysis_result['recommended_numbers'])}个):")
        for i, number in enumerate(analysis_result['recommended_numbers'], 1):
            score = analysis_result['number_scores'][number]
            report.append(f"  {i:2d}. {number:2d} (得分: {score:.3f})")
        
        report.append("")
        report.append("分析方法权重:")
        for method, weight in analysis_result['method_weights'].items():
            report.append(f"  {method}: {weight:.1%}")
        
        return "\n".join(report)
