"""
融合交叉验证管理器
支持多种融合策略和交叉验证方法
"""
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import json
import sqlite3

from .traditional_module import TraditionalAnalysisModule
from .ml_module import MachineLearningModule
from .zodiac_extended_module import ZodiacExtendedModule
from .special_zodiac_module import SpecialZodiacModule

@dataclass
class FusionResult:
    """融合结果数据类"""
    final_numbers: List[int]
    confidence: float
    fusion_method: str
    individual_results: Dict[str, Any]
    fusion_metadata: Dict[str, Any]

class FusionValidator:
    """融合交叉验证管理器"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        
        # 初始化四大模组
        self.modules = {
            "traditional": TraditionalAnalysisModule(db_path),
            "machine_learning": MachineLearningModule(db_path),
            "zodiac_extended": ZodiacExtendedModule(db_path),
            "special_zodiac": SpecialZodiacModule(db_path)
        }
        
        # 模组权重配置
        self.module_weights = {
            "traditional": 0.30,
            "machine_learning": 0.40,
            "zodiac_extended": 0.20,
            "special_zodiac": 0.10
        }
        
        # 融合方法配置
        self.fusion_methods = {
            "weighted_voting": self._weighted_voting_fusion,
            "confidence_weighted": self._confidence_weighted_fusion,
            "intersection": self._intersection_fusion,
            "union": self._union_fusion,
            "adaptive": self._adaptive_fusion,
            "consensus": self._consensus_fusion
        }
    
    def run_independent_predictions(self, target_date: str, enabled_modules: List[str] = None) -> Dict[str, Any]:
        """运行独立预测"""
        if enabled_modules is None:
            enabled_modules = list(self.modules.keys())
        
        print(f"🔄 运行独立预测 - 目标日期: {target_date}")
        print(f"📊 启用模组: {enabled_modules}")
        
        results = {}
        
        for module_name in enabled_modules:
            if module_name in self.modules:
                try:
                    print(f"  🔍 运行 {module_name} 模组...")
                    result = self.modules[module_name].predict(target_date)
                    results[module_name] = result
                    print(f"  ✅ {module_name} 完成 - 推荐{len(result['predicted_numbers'])}个号码")
                except Exception as e:
                    print(f"  ❌ {module_name} 失败: {e}")
        
        print(f"✅ 独立预测完成，成功运行 {len(results)} 个模组")
        return results
    
    def run_independent_backtests(self, start_date: str, end_date: str, 
                                window_size: int = 30, enabled_modules: List[str] = None) -> Dict[str, Any]:
        """运行独立回测"""
        if enabled_modules is None:
            enabled_modules = list(self.modules.keys())
        
        print(f"🔄 运行独立回测 - 期间: {start_date} 到 {end_date}")
        print(f"📊 启用模组: {enabled_modules}")
        
        results = {}
        
        for module_name in enabled_modules:
            if module_name in self.modules:
                try:
                    print(f"  🔍 回测 {module_name} 模组...")
                    result = self.modules[module_name].run_backtest(start_date, end_date, window_size)
                    results[module_name] = result
                    
                    stats = result["statistics"]
                    print(f"  ✅ {module_name} 回测完成 - 命中率: {stats['hit_rate']:.1%}")
                    
                except Exception as e:
                    print(f"  ❌ {module_name} 回测失败: {e}")
        
        print(f"✅ 独立回测完成，成功回测 {len(results)} 个模组")
        return results
    
    def run_fusion_prediction(self, target_date: str, fusion_method: str = "weighted_voting", 
                            enabled_modules: List[str] = None) -> FusionResult:
        """运行融合预测"""
        print(f"🔀 运行融合预测 - 方法: {fusion_method}")
        
        # 获取独立预测结果
        individual_results = self.run_independent_predictions(target_date, enabled_modules)
        
        if not individual_results:
            raise ValueError("没有可用的模组预测结果")
        
        # 执行融合
        if fusion_method not in self.fusion_methods:
            raise ValueError(f"不支持的融合方法: {fusion_method}")
        
        fusion_func = self.fusion_methods[fusion_method]
        fusion_result = fusion_func(individual_results)
        
        print(f"✅ 融合预测完成 - 最终推荐 {len(fusion_result.final_numbers)} 个号码")
        return fusion_result
    
    def run_cross_validation(self, start_date: str, end_date: str, 
                           fusion_methods: List[str] = None, k_folds: int = 5,
                           enabled_modules: List[str] = None) -> Dict[str, Any]:
        """运行交叉验证"""
        if fusion_methods is None:
            fusion_methods = ["weighted_voting", "confidence_weighted", "intersection", "union"]
        
        print(f"🎯 运行交叉验证 - {k_folds}折")
        print(f"📊 测试期间: {start_date} 到 {end_date}")
        print(f"🔀 融合方法: {fusion_methods}")
        
        # 获取测试日期
        test_dates = self._get_test_dates(start_date, end_date)
        
        if len(test_dates) < k_folds:
            raise ValueError(f"测试数据不足，需要至少 {k_folds} 期数据")
        
        # 分割数据
        fold_size = len(test_dates) // k_folds
        cv_results = {}
        
        for method in fusion_methods:
            print(f"  🔍 测试融合方法: {method}")
            method_results = []
            
            for fold in range(k_folds):
                start_idx = fold * fold_size
                end_idx = start_idx + fold_size if fold < k_folds - 1 else len(test_dates)
                
                fold_dates = test_dates[start_idx:end_idx]
                fold_results = []
                
                print(f"    📁 第 {fold+1} 折: {len(fold_dates)} 期测试")
                
                for test_date in fold_dates:
                    try:
                        # 获取真实号码
                        actual_number = self._get_actual_number(test_date)
                        if actual_number is None:
                            continue
                        
                        # 运行融合预测（使用历史数据）
                        fusion_result = self._run_historical_fusion_prediction(
                            test_date, method, enabled_modules
                        )
                        
                        predicted_numbers = fusion_result.final_numbers
                        
                        # 计算命中
                        hit = actual_number in predicted_numbers
                        hit_rank = predicted_numbers.index(actual_number) + 1 if hit else None
                        
                        fold_results.append({
                            "date": test_date,
                            "actual": actual_number,
                            "predicted": predicted_numbers,
                            "hit": hit,
                            "hit_rank": hit_rank,
                            "confidence": fusion_result.confidence,
                            "individual_results": fusion_result.individual_results
                        })
                        
                    except Exception as e:
                        print(f"      ❌ {test_date} 失败: {e}")
                
                method_results.append(fold_results)
                
                # 计算本折统计
                fold_hits = sum(1 for r in fold_results if r["hit"])
                fold_hit_rate = fold_hits / len(fold_results) if fold_results else 0
                print(f"    ✅ 第 {fold+1} 折完成 - 命中率: {fold_hit_rate:.1%}")
            
            cv_results[method] = method_results
        
        # 计算交叉验证统计
        cv_statistics = self._calculate_cv_statistics(cv_results)
        
        print(f"✅ 交叉验证完成")
        self._print_cv_summary(cv_statistics)
        
        return {
            "cv_results": cv_results,
            "cv_statistics": cv_statistics,
            "test_period": f"{start_date} 到 {end_date}",
            "k_folds": k_folds,
            "fusion_methods": fusion_methods
        }
    
    def _run_historical_fusion_prediction(self, target_date: str, fusion_method: str, 
                                        enabled_modules: List[str] = None) -> FusionResult:
        """运行历史融合预测（用于回测）"""
        if enabled_modules is None:
            enabled_modules = list(self.modules.keys())
        
        # 获取历史预测结果
        individual_results = {}
        
        for module_name in enabled_modules:
            if module_name in self.modules:
                try:
                    result = self.modules[module_name].predict_historical(target_date)
                    individual_results[module_name] = result
                except Exception as e:
                    print(f"      ⚠️ {module_name} 历史预测失败: {e}")
        
        if not individual_results:
            raise ValueError("没有可用的历史预测结果")
        
        # 执行融合
        fusion_func = self.fusion_methods[fusion_method]
        return fusion_func(individual_results)
    
    def _weighted_voting_fusion(self, individual_results: Dict[str, Any]) -> FusionResult:
        """加权投票融合"""
        number_scores = {}
        total_weight = 0
        
        for module_name, result in individual_results.items():
            weight = self.module_weights.get(module_name, 0.25)
            confidence = result["confidence"]
            
            for number in result["predicted_numbers"]:
                if number not in number_scores:
                    number_scores[number] = 0
                number_scores[number] += weight * confidence
            
            total_weight += weight
        
        # 归一化分数
        if total_weight > 0:
            for number in number_scores:
                number_scores[number] /= total_weight
        
        # 选择前16个号码
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        final_numbers = [num for num, score in sorted_numbers[:16]]
        
        # 计算整体置信度
        avg_confidence = np.mean([result["confidence"] for result in individual_results.values()])
        
        return FusionResult(
            final_numbers=final_numbers,
            confidence=avg_confidence,
            fusion_method="weighted_voting",
            individual_results=individual_results,
            fusion_metadata={"number_scores": number_scores, "total_weight": total_weight}
        )
    
    def _confidence_weighted_fusion(self, individual_results: Dict[str, Any]) -> FusionResult:
        """置信度加权融合"""
        number_scores = {}
        total_confidence = sum(result["confidence"] for result in individual_results.values())
        
        for module_name, result in individual_results.items():
            confidence_weight = result["confidence"] / total_confidence if total_confidence > 0 else 1/len(individual_results)
            
            for number in result["predicted_numbers"]:
                if number not in number_scores:
                    number_scores[number] = 0
                number_scores[number] += confidence_weight
        
        # 选择前16个号码
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        final_numbers = [num for num, score in sorted_numbers[:16]]
        
        return FusionResult(
            final_numbers=final_numbers,
            confidence=total_confidence / len(individual_results),
            fusion_method="confidence_weighted",
            individual_results=individual_results,
            fusion_metadata={"number_scores": number_scores}
        )
    
    def _intersection_fusion(self, individual_results: Dict[str, Any]) -> FusionResult:
        """交集融合"""
        all_numbers = [set(result["predicted_numbers"]) for result in individual_results.values()]
        intersection = set.intersection(*all_numbers) if all_numbers else set()
        
        # 如果交集不足16个，添加出现频率最高的号码
        if len(intersection) < 16:
            number_counts = {}
            for result in individual_results.values():
                for number in result["predicted_numbers"]:
                    number_counts[number] = number_counts.get(number, 0) + 1
            
            sorted_by_count = sorted(number_counts.items(), key=lambda x: x[1], reverse=True)
            final_numbers = list(intersection)
            
            for number, count in sorted_by_count:
                if number not in final_numbers and len(final_numbers) < 16:
                    final_numbers.append(number)
        else:
            final_numbers = list(intersection)[:16]
        
        avg_confidence = np.mean([result["confidence"] for result in individual_results.values()])
        
        return FusionResult(
            final_numbers=sorted(final_numbers),
            confidence=avg_confidence * 0.9,  # 交集融合置信度稍低
            fusion_method="intersection",
            individual_results=individual_results,
            fusion_metadata={"intersection_size": len(intersection)}
        )
    
    def _union_fusion(self, individual_results: Dict[str, Any]) -> FusionResult:
        """并集融合"""
        all_numbers = set()
        for result in individual_results.values():
            all_numbers.update(result["predicted_numbers"])
        
        # 按出现频率排序
        number_counts = {}
        for result in individual_results.values():
            for number in result["predicted_numbers"]:
                number_counts[number] = number_counts.get(number, 0) + 1
        
        sorted_by_count = sorted(number_counts.items(), key=lambda x: x[1], reverse=True)
        final_numbers = [number for number, count in sorted_by_count[:16]]
        
        avg_confidence = np.mean([result["confidence"] for result in individual_results.values()])
        
        return FusionResult(
            final_numbers=final_numbers,
            confidence=avg_confidence * 1.1,  # 并集融合置信度稍高
            fusion_method="union",
            individual_results=individual_results,
            fusion_metadata={"union_size": len(all_numbers), "number_counts": number_counts}
        )
    
    def _adaptive_fusion(self, individual_results: Dict[str, Any]) -> FusionResult:
        """自适应融合"""
        # 根据各模组的置信度动态调整权重
        confidences = {name: result["confidence"] for name, result in individual_results.items()}
        total_confidence = sum(confidences.values())
        
        # 动态权重 = 基础权重 * 置信度权重
        dynamic_weights = {}
        for module_name in individual_results.keys():
            base_weight = self.module_weights.get(module_name, 0.25)
            confidence_factor = confidences[module_name] / (total_confidence / len(confidences)) if total_confidence > 0 else 1
            dynamic_weights[module_name] = base_weight * confidence_factor
        
        # 归一化权重
        total_weight = sum(dynamic_weights.values())
        if total_weight > 0:
            for module_name in dynamic_weights:
                dynamic_weights[module_name] /= total_weight
        
        # 加权投票
        number_scores = {}
        for module_name, result in individual_results.items():
            weight = dynamic_weights[module_name]
            
            for number in result["predicted_numbers"]:
                if number not in number_scores:
                    number_scores[number] = 0
                number_scores[number] += weight
        
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        final_numbers = [num for num, score in sorted_numbers[:16]]
        
        return FusionResult(
            final_numbers=final_numbers,
            confidence=np.mean(list(confidences.values())),
            fusion_method="adaptive",
            individual_results=individual_results,
            fusion_metadata={"dynamic_weights": dynamic_weights, "number_scores": number_scores}
        )
    
    def _consensus_fusion(self, individual_results: Dict[str, Any]) -> FusionResult:
        """共识融合"""
        # 只选择至少被2个模组推荐的号码
        number_counts = {}
        for result in individual_results.values():
            for number in result["predicted_numbers"]:
                number_counts[number] = number_counts.get(number, 0) + 1
        
        # 筛选共识号码
        consensus_threshold = max(2, len(individual_results) // 2)
        consensus_numbers = [num for num, count in number_counts.items() if count >= consensus_threshold]
        
        # 如果共识号码不足，降低阈值
        if len(consensus_numbers) < 12:
            consensus_numbers = [num for num, count in number_counts.items() if count >= 2]
        
        # 如果还是不足，按频率选择
        if len(consensus_numbers) < 12:
            sorted_by_count = sorted(number_counts.items(), key=lambda x: x[1], reverse=True)
            consensus_numbers = [num for num, count in sorted_by_count[:16]]
        
        final_numbers = sorted(consensus_numbers[:16])
        
        # 置信度基于共识程度
        avg_consensus = np.mean([number_counts[num] for num in final_numbers]) / len(individual_results)
        base_confidence = np.mean([result["confidence"] for result in individual_results.values()])
        
        return FusionResult(
            final_numbers=final_numbers,
            confidence=base_confidence * avg_consensus,
            fusion_method="consensus",
            individual_results=individual_results,
            fusion_metadata={"consensus_threshold": consensus_threshold, "number_counts": number_counts}
        )
    
    def _get_test_dates(self, start_date: str, end_date: str) -> List[str]:
        """获取测试日期"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT draw_date FROM lottery_results 
            WHERE draw_date BETWEEN ? AND ? 
            ORDER BY draw_date
        ''', (start_date, end_date))
        
        dates = [row[0] for row in cursor.fetchall()]
        conn.close()
        return dates
    
    def _get_actual_number(self, test_date: str) -> Optional[int]:
        """获取真实开奖号码"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT special_number FROM lottery_results 
            WHERE draw_date = ?
        ''', (test_date,))
        
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None
    
    def _calculate_cv_statistics(self, cv_results: Dict[str, List[List[Dict]]]) -> Dict[str, Any]:
        """计算交叉验证统计"""
        stats = {}
        
        for method, folds in cv_results.items():
            all_results = []
            for fold in folds:
                all_results.extend(fold)
            
            if not all_results:
                continue
            
            hit_count = sum(1 for r in all_results if r["hit"])
            total_count = len(all_results)
            hit_rate = hit_count / total_count if total_count > 0 else 0
            
            confidences = [r["confidence"] for r in all_results]
            avg_confidence = np.mean(confidences) if confidences else 0
            
            hit_ranks = [r["hit_rank"] for r in all_results if r["hit_rank"] is not None]
            avg_hit_rank = np.mean(hit_ranks) if hit_ranks else None
            
            # 计算各折的命中率
            fold_hit_rates = []
            for fold in folds:
                if fold:
                    fold_hits = sum(1 for r in fold if r["hit"])
                    fold_hit_rate = fold_hits / len(fold)
                    fold_hit_rates.append(fold_hit_rate)
            
            hit_rate_std = np.std(fold_hit_rates) if fold_hit_rates else 0
            
            stats[method] = {
                "hit_rate": hit_rate,
                "hit_count": hit_count,
                "total_count": total_count,
                "avg_confidence": avg_confidence,
                "avg_hit_rank": avg_hit_rank,
                "hit_rate_std": hit_rate_std,
                "fold_count": len(folds)
            }
        
        return stats
    
    def _print_cv_summary(self, cv_statistics: Dict[str, Any]):
        """打印交叉验证摘要"""
        print("\n📊 交叉验证结果摘要:")
        print("=" * 60)
        
        for method, stats in cv_statistics.items():
            print(f"🔀 {method}:")
            print(f"   命中率: {stats['hit_rate']:.1%} ± {stats['hit_rate_std']:.1%}")
            print(f"   命中数: {stats['hit_count']}/{stats['total_count']}")
            print(f"   平均置信度: {stats['avg_confidence']:.1%}")
            if stats['avg_hit_rank']:
                print(f"   平均命中排名: {stats['avg_hit_rank']:.1f}")
            print()
        
        # 找出最佳方法
        best_method = max(cv_statistics.items(), key=lambda x: x[1]['hit_rate'])
        print(f"🏆 最佳融合方法: {best_method[0]} (命中率: {best_method[1]['hit_rate']:.1%})")
    
    def set_module_weights(self, weights: Dict[str, float]):
        """设置模组权重"""
        total_weight = sum(weights.values())
        if total_weight > 0:
            for module_name, weight in weights.items():
                if module_name in self.module_weights:
                    self.module_weights[module_name] = weight / total_weight
        
        print(f"✅ 更新模组权重: {self.module_weights}")
    
    def get_module_status(self) -> Dict[str, Any]:
        """获取模组状态"""
        status = {}
        for module_name, module in self.modules.items():
            status[module_name] = {
                "name": module.module_name,
                "weight": self.module_weights.get(module_name, 0),
                "available": True
            }
        return status
