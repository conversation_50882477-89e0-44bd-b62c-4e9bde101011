@echo off
chcp 65001 >nul
title 完整EXE打包流程

echo.
echo 🔧 完整EXE打包流程启动
echo ==================================================

REM 检查Python环境
echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python版本: %PYTHON_VERSION%

REM 检查PyInstaller
echo 检查PyInstaller...
python -m PyInstaller --version >nul 2>&1
if errorlevel 1 (
    echo ❌ PyInstaller未安装
    echo 正在安装PyInstaller...
    python -m pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
    echo ✅ PyInstaller安装成功
) else (
    for /f "tokens=*" %%i in ('python -m PyInstaller --version 2^>^&1') do set PYINSTALLER_VERSION=%%i
    echo ✅ PyInstaller版本: %PYINSTALLER_VERSION%
)

REM 检查项目文件
echo 检查项目文件...
set MISSING_FILES=

if not exist "lottery_prediction_gui.py" (
    echo ❌ lottery_prediction_gui.py: 不存在
    set MISSING_FILES=1
) else (
    echo ✅ lottery_prediction_gui.py: 存在
)

if not exist "complete_exe_package_builder.py" (
    echo ❌ complete_exe_package_builder.py: 不存在
    set MISSING_FILES=1
) else (
    echo ✅ complete_exe_package_builder.py: 存在
)

if defined MISSING_FILES (
    echo 缺少必要文件，无法继续
    pause
    exit /b 1
)

REM 运行完整打包流程
echo.
echo 🚀 开始EXE打包流程...
echo ==================================================

python build_complete_exe.py

if errorlevel 1 (
    echo.
    echo ❌ EXE打包失败
    echo 📋 请查看 exe_build.log 了解详细信息
) else (
    echo.
    echo 🎉 EXE打包成功完成！
    echo 📁 请查看 releases/ 目录中的发布包
    
    REM 检查生成的文件
    if exist "releases" (
        echo.
        echo 📋 生成的发布包:
        dir /b "releases" 2>nul
    )
    
    echo.
    echo 🔧 下一步操作:
    echo   1. 进入 releases/ 目录
    echo   2. 解压ZIP文件
    echo   3. 运行 debug_start.bat 测试EXE
)

echo.
pause
