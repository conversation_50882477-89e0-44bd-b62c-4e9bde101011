"""
修复Unicode编码问题
"""

import re
import os
from pathlib import Path

def fix_unicode_encoding():
    """修复Unicode编码问题"""
    print("🔧 修复Unicode编码问题")
    print("=" * 50)
    
    # 设置路径
    source_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    gui_file = source_dir / "lottery_prediction_gui.py"
    
    if not gui_file.exists():
        print("❌ GUI文件不存在")
        return False
    
    print(f"📁 处理文件: {gui_file}")
    
    # 定义emoji替换映射
    emoji_replacements = {
        # 基础状态
        '✅': '[OK]',
        '❌': '[ERROR]',
        '⚠️': '[WARNING]',
        'ℹ️': '[INFO]',
        '🔴': '[RED]',
        '🟢': '[GREEN]',
        '🟡': '[YELLOW]',
        '🔄': '[REFRESH]',
        
        # 功能图标
        '🎯': '[TARGET]',
        '📊': '[CHART]',
        '📈': '[TREND]',
        '📋': '[LIST]',
        '🚀': '[ROCKET]',
        '🔧': '[TOOL]',
        '💡': '[IDEA]',
        '⭐': '[STAR]',
        '🏆': '[TROPHY]',
        '💾': '[SAVE]',
        '📁': '[FOLDER]',
        '📂': '[OPEN_FOLDER]',
        '📄': '[FILE]',
        '📤': '[EXPORT]',
        '📥': '[IMPORT]',
        '🗑️': '[DELETE]',
        '🔍': '[SEARCH]',
        '⚙️': '[SETTINGS]',
        '⚖️': '[BALANCE]',
        '🔒': '[LOCK]',
        '🔓': '[UNLOCK]',
        
        # 数字和序号
        '1️⃣': '1.',
        '2️⃣': '2.',
        '3️⃣': '3.',
        '4️⃣': '4.',
        '5️⃣': '5.',
        '6️⃣': '6.',
        '7️⃣': '7.',
        '8️⃣': '8.',
        '9️⃣': '9.',
        '🔟': '10.',
        
        # 方向和控制
        '⏮️': '[FIRST]',
        '⬅️': '[LEFT]',
        '➡️': '[RIGHT]',
        '⏭️': '[LAST]',
        '⏹️': '[STOP]',
        '▶️': '[PLAY]',
        '⏸️': '[PAUSE]',
        
        # 动物和生肖
        '🐉': '[DRAGON]',
        '🐲': '[ZODIAC]',
        '🎭': '[MASK]',
        
        # 季节和自然
        '🌸': '[SEASON]',
        '🔥': '[HOT]',
        '❄️': '[COLD]',
        
        # 其他
        '🎊': '[CELEBRATION]',
        '💪': '[STRONG]',
        '🔀': '[SHUFFLE]',
        '📏': '[RULER]',
        '🔬': '[SCIENCE]',
        '📞': '[PHONE]',
        '⏱️': '[TIMER]',
        '⚡': '[LIGHTNING]',
        '🥇': '[GOLD]',
        '🥈': '[SILVER]',
        '🥉': '[BRONZE]',
        '🤖': '[ROBOT]',
        '✏️': '[PENCIL]',
        '🔌': '[PLUG]',
        '💻': '[COMPUTER]',
    }
    
    try:
        # 读取文件内容
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 原始文件大小: {len(content)} 字符")
        
        # 统计替换次数
        replacement_count = 0
        
        # 执行替换
        for emoji, replacement in emoji_replacements.items():
            if emoji in content:
                count = content.count(emoji)
                content = content.replace(emoji, replacement)
                replacement_count += count
                print(f"  替换 {emoji} -> {replacement}: {count} 次")
        
        print(f"📊 总替换次数: {replacement_count}")
        
        # 检查是否还有其他emoji字符
        emoji_pattern = re.compile(r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF\U00002600-\U000027BF\U0001F900-\U0001F9FF\U0001F018-\U0001F270]')
        remaining_emojis = emoji_pattern.findall(content)
        
        if remaining_emojis:
            print(f"⚠️ 发现未处理的emoji: {set(remaining_emojis)}")
            # 替换剩余的emoji为通用标记
            content = emoji_pattern.sub('[EMOJI]', content)
            print(f"📝 已将剩余emoji替换为 [EMOJI]")
        
        # 备份原文件
        backup_file = gui_file.with_suffix('.py.backup')
        with open(backup_file, 'w', encoding='utf-8') as f:
            with open(gui_file, 'r', encoding='utf-8') as original:
                f.write(original.read())
        print(f"💾 已备份原文件: {backup_file}")
        
        # 写入修复后的内容
        with open(gui_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 文件修复完成")
        print(f"📄 修复后文件大小: {len(content)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def test_encoding_fix():
    """测试编码修复"""
    print("\n🧪 测试编码修复")
    print("-" * 30)
    
    source_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    gui_file = source_dir / "lottery_prediction_gui.py"
    
    try:
        # 尝试以GBK编码读取文件
        with open(gui_file, 'r', encoding='gbk') as f:
            content = f.read(1000)  # 只读取前1000字符测试
        
        print("✅ GBK编码读取成功")
        return True
        
    except UnicodeDecodeError as e:
        print(f"❌ GBK编码读取失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def main():
    """主函数"""
    print("🎯 Unicode编码修复工具")
    print("=" * 70)
    
    # 执行修复
    success = fix_unicode_encoding()
    
    if success:
        print("\n🧪 测试修复效果...")
        test_success = test_encoding_fix()
        
        if test_success:
            print("\n🎉 编码修复完成！")
            print("✅ 所有emoji字符已替换为ASCII字符")
            print("✅ 文件可以在GBK编码环境下正常运行")
            print("✅ 原文件已备份")
            
            print("\n📋 下一步:")
            print("  1. 重新启动EXE程序")
            print("  2. 测试所有功能是否正常")
            print("  3. 如果需要，重新打包EXE")
        else:
            print("\n⚠️ 修复可能不完整")
            print("请检查是否还有其他编码问题")
    else:
        print("\n❌ 编码修复失败")
        print("请检查错误信息并重试")
    
    return success

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
