{"test_time": "2025-06-24T07:36:57.804735", "total_tests": 12, "passed_tests": 11, "success_rate": 0.9166666666666666, "test_duration": "0:00:01.648213", "detailed_results": {"GUI进程状态": {"passed": true, "details": ["✅ GUI进程运行中 (PID: 3344)", "📊 CPU使用率: 0.0%", "💾 内存使用: 265.8 MB", "⏱️ 运行时间: 0:04:02.256705"]}, "数据库连接": {"passed": true, "details": ["✅ 数据库文件存在", "📊 数据库表: 8个", "  • lunar_year_mapping: 51条记录", "  • prediction_results: 0条记录", "  • model_performance: 0条记录", "  • number_zodiac_mapping: 0条记录", "  • lottery_data: 900条记录", "  • lottery_results: 905条记录", "  • sqlite_sequence: 1条记录", "  • test_table: 0条记录", "✅ 历史数据: 905条记录", "📅 数据范围: 2023-01-01 到 2025-06-23"]}, "核心预测模块": {"passed": true, "details": ["✅ 特码预测系统加载成功", "✅ 特码预测: 16个号码, 置信度65.0%", "✅ 生肖预测: 4个生肖"]}, "完美预测系统": {"passed": false, "details": ["✅ 完美预测系统加载成功", "✅ 模块初始化完成", "✅ 完美预测成功:", "  📊 推荐号码: 16个", "  🐉 推荐生肖: 4个", "  💪 整体置信度: 50.0%", "  🔒 预测稳定性: 0.0%", "❌ 一致性验证失败"]}, "一致性验证": {"passed": true, "details": ["✅ 一致性预测器加载成功", "  第1次: 16个号码", "  第2次: 16个号码", "  第3次: 16个号码", "✅ 一致性验证通过: 所有结果完全一致"]}, "历史回测": {"passed": true, "details": ["✅ 回测文件存在: historical_backtest.py", "✅ 回测功能可用", "📊 支持日期范围回测", "📈 支持命中率统计", "🔄 支持批量回测"]}, "增强回测": {"passed": true, "details": ["✅ enhanced_feature_engineering.py", "✅ advanced_fusion_optimizer.py", "✅ enhanced_hit_rate_optimizer.py", "✅ 配置文件: optimization_config.json", "  📊 配置项: 6个", "✅ 配置文件: optimal_config.json", "  📊 配置项: 11个"]}, "配置管理": {"passed": true, "details": ["✅ 配置保存测试通过", "✅ 配置读取测试通过", "✅ 配置文件清理完成"]}, "数据管理": {"passed": true, "details": ["✅ 数据目录存在: data", "📁 数据文件: 5个", "  • lottery.db: 420.0 KB", "  • lottery_test.db: 24.0 KB", "  • models: 0.0 KB", "  • processed: 0.0 KB", "  • raw: 0.0 KB", "✅ 支持CSV数据导入", "✅ 支持数据预览", "✅ 支持数据导出"]}, "功能测试": {"passed": true, "details": ["✅ 测试文件: test_perfect_prediction.py", "✅ 测试文件: gui_interaction_test.py", "✅ 功能测试模块可用", "📊 支持系统完整性检查", "🔄 支持模块交互测试"]}, "文件系统": {"passed": true, "details": ["✅ 目录存在: src", "✅ 目录存在: data", "✅ lottery_prediction_gui.py: 315.1 KB", "✅ src/perfect_prediction_system.py: 37.0 KB", "✅ src/lottery_prediction_system.py: 9.9 KB"]}, "模块导入": {"passed": true, "details": ["✅ PyQt5.QtWidgets", "✅ sqlite3", "✅ numpy", "✅ pandas", "✅ datetime", "✅ src.lottery_prediction_system", "✅ src.perfect_prediction_system"]}}}