# -*- coding: utf-8 -*-
"""
特征维度修复脚本
"""

def fix_feature_dimensions():
    """修复特征维度"""
    import sys
    import os
    from pathlib import Path
    
    # 添加路径
    sys.path.insert(0, str(Path.cwd()))
    sys.path.insert(0, str(Path.cwd() / "src"))
    
    try:
        # 重新训练模型以匹配新的特征维度
        from src.machine_learning_module_v2 import MachineLearningModule
        
        print("正在重新训练模型以匹配20个特征...")
        
        ml_module = MachineLearningModule()
        
        # 生成示例数据进行训练
        import numpy as np
        
        # 创建20个特征的示例数据
        X_sample = np.random.rand(100, 20)
        y_sample = np.random.randint(1, 50, 100)
        
        # 重新训练所有模型
        for model_name in ml_module.models.keys():
            try:
                model = ml_module.models[model_name]
                model.fit(X_sample, y_sample)
                print(f"✅ 模型 {model_name} 重新训练完成")
            except Exception as e:
                print(f"❌ 模型 {model_name} 训练失败: {e}")
        
        print("✅ 所有模型已重新训练完成")
        return True
        
    except Exception as e:
        print(f"❌ 特征维度修复失败: {e}")
        return False

if __name__ == "__main__":
    fix_feature_dimensions()
