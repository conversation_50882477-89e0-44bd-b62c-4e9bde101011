"""
六合彩预测系统多版本封装发布工具
创建便携版、源码版和Windows版发布包
"""

import os
import shutil
import zipfile
import json
import subprocess
import sys
from datetime import datetime
from pathlib import Path

class LotterySystemPackager:
    """六合彩预测系统打包器"""

    def __init__(self):
        self.version = "v2.0.0"
        self.release_date = datetime.now().strftime("%Y%m%d")
        self.base_name = f"六合彩预测系统_{self.version}_{self.release_date}"

        # 创建发布目录
        self.release_dir = Path("releases")
        self.release_dir.mkdir(exist_ok=True)

        print(f"🎊 六合彩预测系统多版本封装工具")
        print(f"📦 版本: {self.version}")
        print(f"📅 发布日期: {self.release_date}")
        print(f"📁 发布目录: {self.release_dir}")

    def create_all_packages(self):
        """创建所有版本的发布包"""
        print("\n🚀 开始创建多版本发布包...")

        try:
            # 1. 创建便携版
            portable_success = self.create_portable_version()

            # 2. 创建源码版
            source_success = self.create_source_version()

            # 3. 创建Windows版
            windows_success = self.create_windows_version()

            # 总结
            self.print_summary(portable_success, source_success, windows_success)

        except Exception as e:
            print(f"❌ 封装过程出错: {e}")

    def create_portable_version(self):
        """创建便携版"""
        print("\n📦 创建便携版...")
        print("-" * 50)

        try:
            portable_name = f"{self.base_name}_便携版"
            portable_dir = self.release_dir / portable_name

            # 清理旧版本
            if portable_dir.exists():
                shutil.rmtree(portable_dir)
            portable_dir.mkdir()

            # 复制核心文件
            self._copy_core_files(portable_dir)

            # 复制依赖库
            self._copy_dependencies(portable_dir)

            # 创建启动脚本
            self._create_portable_launcher(portable_dir)

            # 创建配置文件
            self._create_portable_config(portable_dir)

            # 创建说明文档
            self._create_portable_readme(portable_dir)

            # 打包为ZIP
            zip_path = self.release_dir / f"{portable_name}.zip"
            self._create_zip(portable_dir, zip_path)

            print(f"✅ 便携版创建成功: {zip_path}")
            return True

        except Exception as e:
            print(f"❌ 便携版创建失败: {e}")
            return False

    def create_source_version(self):
        """创建源码版"""
        print("\n📦 创建源码版...")
        print("-" * 50)

        try:
            source_name = f"{self.base_name}_源码版"
            source_dir = self.release_dir / source_name

            # 清理旧版本
            if source_dir.exists():
                shutil.rmtree(source_dir)
            source_dir.mkdir()

            # 复制所有源码
            self._copy_source_files(source_dir)

            # 创建requirements.txt
            self._create_requirements_file(source_dir)

            # 创建安装脚本
            self._create_install_scripts(source_dir)

            # 创建开发文档
            self._create_development_docs(source_dir)

            # 打包为ZIP
            zip_path = self.release_dir / f"{source_name}.zip"
            self._create_zip(source_dir, zip_path)

            print(f"✅ 源码版创建成功: {zip_path}")
            return True

        except Exception as e:
            print(f"❌ 源码版创建失败: {e}")
            return False

    def create_windows_version(self):
        """创建Windows版"""
        print("\n📦 创建Windows版...")
        print("-" * 50)

        try:
            windows_name = f"{self.base_name}_Windows版"
            windows_dir = self.release_dir / windows_name

            # 清理旧版本
            if windows_dir.exists():
                shutil.rmtree(windows_dir)
            windows_dir.mkdir()

            # 使用PyInstaller打包
            exe_success = self._create_executable(windows_dir)

            if exe_success:
                # 复制资源文件
                self._copy_resources(windows_dir)

                # 创建Windows启动器
                self._create_windows_launcher(windows_dir)

                # 创建卸载程序
                self._create_uninstaller(windows_dir)

                # 创建Windows说明
                self._create_windows_readme(windows_dir)

                # 打包为ZIP
                zip_path = self.release_dir / f"{windows_name}.zip"
                self._create_zip(windows_dir, zip_path)

                print(f"✅ Windows版创建成功: {zip_path}")
                return True
            else:
                print(f"❌ Windows版可执行文件创建失败")
                return False

        except Exception as e:
            print(f"❌ Windows版创建失败: {e}")
            return False

    def _copy_core_files(self, target_dir):
        """复制核心文件"""
        print("📁 复制核心文件...")

        core_files = [
            "lottery_prediction_gui.py",
            "consistent_predictor.py",
            "special_number_predictor.py",
            "enhanced_backtest.py",
            "comprehensive_evaluation.py"
        ]

        for file_name in core_files:
            if os.path.exists(file_name):
                shutil.copy2(file_name, target_dir)
                print(f"  ✅ {file_name}")

        # 复制src目录
        if os.path.exists("src"):
            shutil.copytree("src", target_dir / "src")
            print(f"  ✅ src/")

        # 复制data目录
        if os.path.exists("data"):
            shutil.copytree("data", target_dir / "data")
            print(f"  ✅ data/")

    def _copy_dependencies(self, target_dir):
        """复制依赖库"""
        print("📦 复制依赖库...")

        # 创建lib目录
        lib_dir = target_dir / "lib"
        lib_dir.mkdir(exist_ok=True)

        # 获取已安装的包
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "list", "--format=freeze"],
                                  capture_output=True, text=True)

            if result.returncode == 0:
                requirements = result.stdout.strip().split('\n')

                # 过滤核心依赖
                core_deps = []
                for req in requirements:
                    if any(pkg in req.lower() for pkg in ['pyqt5', 'numpy', 'pandas', 'scikit-learn', 'sqlite']):
                        core_deps.append(req)

                # 保存依赖列表
                with open(lib_dir / "requirements.txt", "w", encoding="utf-8") as f:
                    f.write("\n".join(core_deps))

                print(f"  ✅ 依赖列表已保存 ({len(core_deps)}个包)")

        except Exception as e:
            print(f"  ⚠️ 依赖库复制失败: {e}")

    def _create_portable_launcher(self, target_dir):
        """创建便携版启动脚本"""
        print("🚀 创建启动脚本...")

        # Windows批处理文件
        bat_content = f'''@echo off
chcp 65001 > nul
title 六合彩预测系统 {self.version}

echo 🎊 六合彩预测系统 {self.version}
echo ==========================================
echo 正在启动系统...

REM 检查Python环境
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

REM 安装依赖
echo 📦 检查依赖包...
pip install -r lib\\requirements.txt --quiet

REM 启动程序
echo 🚀 启动GUI界面...
python lottery_prediction_gui.py

pause
'''

        with open(target_dir / "启动系统.bat", "w", encoding="gbk") as f:
            f.write(bat_content)

        # Linux/Mac shell脚本
        sh_content = f'''#!/bin/bash
echo "🎊 六合彩预测系统 {self.version}"
echo "=========================================="
echo "正在启动系统..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3环境"
    echo "请先安装Python 3.8或更高版本"
    exit 1
fi

# 安装依赖
echo "📦 检查依赖包..."
pip3 install -r lib/requirements.txt --quiet

# 启动程序
echo "🚀 启动GUI界面..."
python3 lottery_prediction_gui.py
'''

        sh_file = target_dir / "启动系统.sh"
        with open(sh_file, "w", encoding="utf-8") as f:
            f.write(sh_content)

        # 设置执行权限
        try:
            os.chmod(sh_file, 0o755)
        except:
            pass

        print("  ✅ 启动脚本已创建")

    def _create_portable_config(self, target_dir):
        """创建便携版配置"""
        print("⚙️ 创建配置文件...")

        config = {
            "version": self.version,
            "release_date": self.release_date,
            "package_type": "portable",
            "database": {
                "path": "data/lottery.db",
                "auto_backup": True
            },
            "gui": {
                "theme": "default",
                "language": "zh_CN"
            },
            "prediction": {
                "default_modules": ["traditional", "ml", "zodiac", "special"],
                "auto_save_results": True
            }
        }

        with open(target_dir / "config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        print("  ✅ 配置文件已创建")

    def _create_portable_readme(self, target_dir):
        """创建便携版说明文档"""
        print("📖 创建说明文档...")

        readme_content = f'''# 六合彩预测系统 {self.version} - 便携版

## 📋 版本信息
- 版本号: {self.version}
- 发布日期: {self.release_date}
- 包类型: 便携版 (免安装)

## 🚀 快速开始

### Windows用户
1. 双击 `启动系统.bat` 文件
2. 等待依赖包安装完成
3. 系统将自动启动GUI界面

### Linux/Mac用户
1. 打开终端，进入解压目录
2. 运行: `./启动系统.sh`
3. 等待依赖包安装完成
4. 系统将自动启动GUI界面

## 📦 系统要求
- Python 3.8 或更高版本
- 至少 2GB 可用内存
- 至少 500MB 可用磁盘空间

## 🔧 主要功能
- ✅ 特码预测 (16个推荐号码)
- ✅ 一致性预测 (确定性算法)
- ✅ 完美预测系统 (4模组融合)
- ✅ 历史回测分析
- ✅ 增强回测优化
- ✅ 综合评估系统
- ✅ 数据管理功能

## 📁 目录结构
```
六合彩预测系统_{self.version}_{self.release_date}_便携版/
├── 启动系统.bat          # Windows启动脚本
├── 启动系统.sh           # Linux/Mac启动脚本
├── config.json          # 系统配置文件
├── README.md            # 说明文档
├── lottery_prediction_gui.py  # 主程序
├── src/                 # 源码目录
├── data/                # 数据目录
└── lib/                 # 依赖库
    └── requirements.txt # 依赖列表
```

## 🎯 使用说明
1. **数据导入**: 在"数据管理"页面导入历史开奖数据
2. **特码预测**: 使用"特码预测"功能获取推荐号码
3. **完美预测**: 使用"完美预测"获取多模组融合结果
4. **回测分析**: 使用"历史回测"验证预测效果
5. **结果保存**: 预测结果自动保存到TXT文件

## ⚠️ 注意事项
- 首次运行需要联网安装依赖包
- 建议定期备份数据目录
- 预测结果仅供参考，请理性对待

## 📞 技术支持
如有问题，请检查：
1. Python环境是否正确安装
2. 网络连接是否正常
3. 依赖包是否安装成功

---
© 2025 六合彩预测系统 {self.version} - 便携版
'''

        with open(target_dir / "README.md", "w", encoding="utf-8") as f:
            f.write(readme_content)

        print("  ✅ 说明文档已创建")

    def _copy_source_files(self, target_dir):
        """复制源码文件"""
        print("📁 复制源码文件...")

        # 要排除的文件和目录
        exclude_patterns = [
            "__pycache__",
            "*.pyc",
            ".git",
            ".vscode",
            "releases",
            "*.log",
            "temp_*"
        ]

        # 复制所有Python文件
        for file_path in Path(".").rglob("*.py"):
            if not any(pattern in str(file_path) for pattern in exclude_patterns):
                relative_path = file_path.relative_to(".")
                target_file = target_dir / relative_path
                target_file.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(file_path, target_file)
                print(f"  ✅ {relative_path}")

        # 复制其他重要文件
        other_files = ["*.json", "*.txt", "*.md", "*.db"]
        for pattern in other_files:
            for file_path in Path(".").glob(pattern):
                if file_path.name not in exclude_patterns:
                    shutil.copy2(file_path, target_dir)
                    print(f"  ✅ {file_path.name}")

    def _create_requirements_file(self, target_dir):
        """创建requirements.txt文件"""
        print("📦 创建requirements.txt...")

        requirements = [
            "PyQt5>=5.15.0",
            "numpy>=1.21.0",
            "pandas>=1.3.0",
            "scikit-learn>=1.0.0",
            "xgboost>=1.5.0",
            "matplotlib>=3.5.0",
            "seaborn>=0.11.0",
            "sqlite3",  # 内置模块
            "datetime",  # 内置模块
            "json",      # 内置模块
            "logging",   # 内置模块
            "pathlib",   # 内置模块
            "collections",  # 内置模块
            "hashlib",   # 内置模块
            "random",    # 内置模块
            "warnings"   # 内置模块
        ]

        # 过滤内置模块
        pip_requirements = [req for req in requirements if not req in ["sqlite3", "datetime", "json", "logging", "pathlib", "collections", "hashlib", "random", "warnings"]]

        with open(target_dir / "requirements.txt", "w", encoding="utf-8") as f:
            f.write("\n".join(pip_requirements))

        print(f"  ✅ requirements.txt已创建 ({len(pip_requirements)}个包)")

    def _create_install_scripts(self, target_dir):
        """创建安装脚本"""
        print("🔧 创建安装脚本...")

        # Windows安装脚本
        install_bat = f'''@echo off
chcp 65001 > nul
title 六合彩预测系统 {self.version} - 源码版安装

echo 🎊 六合彩预测系统 {self.version} - 源码版
echo ==========================================
echo 正在安装系统...

REM 检查Python环境
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 升级pip
echo 📦 升级pip...
python -m pip install --upgrade pip

REM 安装依赖
echo 📦 安装依赖包...
pip install -r requirements.txt

REM 创建数据目录
if not exist "data" mkdir data

REM 初始化数据库
echo 🗄️ 初始化数据库...
python -c "import sqlite3; conn = sqlite3.connect('data/lottery.db'); conn.close()"

echo ✅ 安装完成！
echo 运行 python lottery_prediction_gui.py 启动系统
pause
'''

        with open(target_dir / "install.bat", "w", encoding="gbk") as f:
            f.write(install_bat)

        # Linux/Mac安装脚本
        install_sh = f'''#!/bin/bash
echo "🎊 六合彩预测系统 {self.version} - 源码版"
echo "=========================================="
echo "正在安装系统..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3环境"
    echo "请先安装Python 3.8或更高版本"
    exit 1
fi

# 升级pip
echo "📦 升级pip..."
python3 -m pip install --upgrade pip

# 安装依赖
echo "📦 安装依赖包..."
pip3 install -r requirements.txt

# 创建数据目录
mkdir -p data

# 初始化数据库
echo "🗄️ 初始化数据库..."
python3 -c "import sqlite3; conn = sqlite3.connect('data/lottery.db'); conn.close()"

echo "✅ 安装完成！"
echo "运行 python3 lottery_prediction_gui.py 启动系统"
'''

        sh_file = target_dir / "install.sh"
        with open(sh_file, "w", encoding="utf-8") as f:
            f.write(install_sh)

        # 设置执行权限
        try:
            os.chmod(sh_file, 0o755)
        except:
            pass

        print("  ✅ 安装脚本已创建")

    def _create_development_docs(self, target_dir):
        """创建开发文档"""
        print("📖 创建开发文档...")

        dev_readme = f'''# 六合彩预测系统 {self.version} - 源码版

## 📋 版本信息
- 版本号: {self.version}
- 发布日期: {self.release_date}
- 包类型: 源码版 (开发者版本)

## 🚀 安装说明

### 自动安装 (推荐)
#### Windows
```bash
install.bat
```

#### Linux/Mac
```bash
chmod +x install.sh
./install.sh
```

### 手动安装
1. 安装Python 3.8+
2. 安装依赖: `pip install -r requirements.txt`
3. 创建数据目录: `mkdir data`
4. 运行程序: `python lottery_prediction_gui.py`

## 🏗️ 项目结构
```
六合彩预测系统_{self.version}_{self.release_date}_源码版/
├── install.bat                    # Windows安装脚本
├── install.sh                     # Linux/Mac安装脚本
├── requirements.txt               # Python依赖
├── README.md                      # 说明文档
├── DEVELOPMENT.md                 # 开发文档
├── lottery_prediction_gui.py      # 主GUI程序
├── consistent_predictor.py        # 一致性预测器
├── special_number_predictor.py    # 特码预测器
├── enhanced_backtest.py           # 增强回测
├── comprehensive_evaluation.py    # 综合评估
├── src/                          # 核心源码
│   ├── perfect_prediction_system.py
│   ├── dynamic_fusion_manager_v3.py
│   ├── algorithm_layer/
│   ├── independent_modules/
│   └── ...
└── data/                         # 数据目录
    └── lottery.db               # SQLite数据库
```

## 🔧 开发环境
- Python 3.8+
- PyQt5 5.15+
- NumPy 1.21+
- Pandas 1.3+
- Scikit-learn 1.0+
- XGBoost 1.5+

## 📚 核心模块说明

### 1. GUI界面 (`lottery_prediction_gui.py`)
- 主界面控制器
- 多页面管理
- 用户交互处理

### 2. 预测系统 (`src/perfect_prediction_system.py`)
- 4大分析模组集成
- 动态融合管理
- 结果优化处理

### 3. 机器学习 (`src/algorithm_layer/`)
- 5种ML算法集成
- 特征工程系统
- 模型训练和预测

### 4. 独立模块 (`src/independent_modules/`)
- 传统统计分析
- 生肖维度分析
- 特码专项分析

## 🎯 开发指南

### 添加新的预测算法
1. 在 `src/algorithm_layer/` 创建新模块
2. 实现标准预测接口
3. 在融合管理器中注册
4. 更新GUI界面

### 扩展分析功能
1. 在 `src/independent_modules/` 创建模块
2. 实现 `predict()` 方法
3. 返回标准格式结果
4. 集成到完美预测系统

### 自定义GUI界面
1. 修改 `lottery_prediction_gui.py`
2. 添加新的页面或控件
3. 连接后端预测逻辑
4. 更新样式和布局

## 🧪 测试说明
- 运行单元测试: `python -m pytest tests/`
- 性能测试: `python test_performance.py`
- 集成测试: `python test_integration.py`

## 📝 贡献指南
1. Fork项目
2. 创建功能分支
3. 提交代码
4. 发起Pull Request

## 📞 技术支持
- 问题反馈: 创建Issue
- 功能建议: 创建Feature Request
- 技术讨论: 参与Discussions

---
© 2025 六合彩预测系统 {self.version} - 源码版
'''

        with open(target_dir / "README.md", "w", encoding="utf-8") as f:
            f.write(dev_readme)

        print("  ✅ 开发文档已创建")

    def _create_executable(self, target_dir):
        """创建可执行文件"""
        print("🔨 创建可执行文件...")

        try:
            # 检查PyInstaller
            try:
                import PyInstaller
                print("  ✅ PyInstaller已安装")
            except ImportError:
                print("  📦 安装PyInstaller...")
                subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)

            # PyInstaller命令
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--onefile",
                "--windowed",
                "--name", f"六合彩预测系统_{self.version}",
                "--icon", "icon.ico" if os.path.exists("icon.ico") else None,
                "--add-data", "src;src",
                "--add-data", "data;data",
                "--hidden-import", "PyQt5",
                "--hidden-import", "numpy",
                "--hidden-import", "pandas",
                "--hidden-import", "sklearn",
                "lottery_prediction_gui.py"
            ]

            # 过滤None值
            cmd = [arg for arg in cmd if arg is not None]

            print(f"  🔨 执行PyInstaller命令...")
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                # 移动生成的exe文件
                exe_name = f"六合彩预测系统_{self.version}.exe"
                if os.path.exists(f"dist/{exe_name}"):
                    shutil.move(f"dist/{exe_name}", target_dir / exe_name)
                    print(f"  ✅ 可执行文件已创建: {exe_name}")

                    # 清理临时文件
                    if os.path.exists("build"):
                        shutil.rmtree("build")
                    if os.path.exists("dist"):
                        shutil.rmtree("dist")
                    if os.path.exists(f"六合彩预测系统_{self.version}.spec"):
                        os.remove(f"六合彩预测系统_{self.version}.spec")

                    return True
                else:
                    print(f"  ❌ 未找到生成的exe文件")
                    return False
            else:
                print(f"  ❌ PyInstaller执行失败: {result.stderr}")
                return False

        except Exception as e:
            print(f"  ❌ 创建可执行文件失败: {e}")
            return False

    def _copy_resources(self, target_dir):
        """复制资源文件"""
        print("📁 复制资源文件...")

        # 复制数据目录
        if os.path.exists("data"):
            shutil.copytree("data", target_dir / "data", dirs_exist_ok=True)
            print("  ✅ data/")

        # 复制配置文件
        config_files = ["config.json", "*.ini", "*.cfg"]
        for pattern in config_files:
            for file_path in Path(".").glob(pattern):
                shutil.copy2(file_path, target_dir)
                print(f"  ✅ {file_path.name}")

    def _create_windows_launcher(self, target_dir):
        """创建Windows启动器"""
        print("🚀 创建Windows启动器...")

        launcher_content = f'''@echo off
chcp 65001 > nul
title 六合彩预测系统 {self.version}

echo 🎊 六合彩预测系统 {self.version} - Windows版
echo ==========================================

REM 检查exe文件
if not exist "六合彩预测系统_{self.version}.exe" (
    echo ❌ 错误: 未找到主程序文件
    echo 请确保所有文件完整
    pause
    exit /b 1
)

REM 启动程序
echo 🚀 启动系统...
start "" "六合彩预测系统_{self.version}.exe"

REM 等待程序启动
timeout /t 2 > nul
echo ✅ 系统已启动
'''

        with open(target_dir / "启动系统.bat", "w", encoding="gbk") as f:
            f.write(launcher_content)

        print("  ✅ Windows启动器已创建")

    def _create_uninstaller(self, target_dir):
        """创建卸载程序"""
        print("🗑️ 创建卸载程序...")

        uninstall_content = f'''@echo off
chcp 65001 > nul
title 六合彩预测系统 {self.version} - 卸载程序

echo 🗑️ 六合彩预测系统 {self.version} - 卸载程序
echo ==========================================

set /p confirm="确定要卸载系统吗？(y/N): "
if /i not "%confirm%"=="y" (
    echo 取消卸载
    pause
    exit /b 0
)

echo 正在卸载...

REM 关闭可能运行的程序
taskkill /f /im "六合彩预测系统_{self.version}.exe" > nul 2>&1

REM 备份数据
if exist "data" (
    set /p backup="是否备份数据？(Y/n): "
    if /i not "%backup%"=="n" (
        echo 备份数据到桌面...
        xcopy "data" "%USERPROFILE%\\Desktop\\六合彩预测系统_数据备份_%date:~0,10%" /e /i /y > nul
        echo 数据已备份到桌面
    )
)

REM 删除文件
echo 删除程序文件...
del /q "六合彩预测系统_{self.version}.exe" > nul 2>&1
del /q "启动系统.bat" > nul 2>&1
del /q "README.md" > nul 2>&1
rmdir /s /q "data" > nul 2>&1

echo ✅ 卸载完成
pause
del "%~f0"
'''

        with open(target_dir / "卸载系统.bat", "w", encoding="gbk") as f:
            f.write(uninstall_content)

        print("  ✅ 卸载程序已创建")

    def _create_windows_readme(self, target_dir):
        """创建Windows版说明"""
        print("📖 创建Windows版说明...")

        readme_content = f'''# 六合彩预测系统 {self.version} - Windows版

## 📋 版本信息
- 版本号: {self.version}
- 发布日期: {self.release_date}
- 包类型: Windows版 (可执行文件)

## 🚀 快速开始
1. 解压到任意目录
2. 双击 `启动系统.bat` 或直接运行 `六合彩预测系统_{self.version}.exe`
3. 系统将自动启动GUI界面

## 📦 系统要求
- Windows 7/8/10/11 (64位)
- 至少 2GB 可用内存
- 至少 500MB 可用磁盘空间
- 无需安装Python环境

## 🔧 主要功能
- ✅ 特码预测 (16个推荐号码)
- ✅ 一致性预测 (确定性算法)
- ✅ 完美预测系统 (4模组融合)
- ✅ 历史回测分析
- ✅ 增强回测优化
- ✅ 综合评估系统
- ✅ 数据管理功能

## 📁 文件说明
- `六合彩预测系统_{self.version}.exe` - 主程序
- `启动系统.bat` - 启动脚本
- `卸载系统.bat` - 卸载程序
- `data/` - 数据目录
- `README.md` - 说明文档

## 🎯 使用说明
1. **首次运行**: 系统会自动创建数据库
2. **数据导入**: 在"数据管理"页面导入历史开奖数据
3. **开始预测**: 选择预测功能开始使用
4. **结果查看**: 预测结果会自动保存

## ⚠️ 注意事项
- 建议定期备份data目录
- 预测结果仅供参考
- 如遇问题请重启程序

## 🗑️ 卸载说明
运行 `卸载系统.bat` 可完全卸载系统，支持数据备份选项。

---
© 2025 六合彩预测系统 {self.version} - Windows版
'''

        with open(target_dir / "README.md", "w", encoding="utf-8") as f:
            f.write(readme_content)

        print("  ✅ Windows版说明已创建")

    def _create_zip(self, source_dir, zip_path):
        """创建ZIP压缩包"""
        print(f"📦 创建压缩包: {zip_path.name}")

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in source_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(source_dir.parent)
                    zipf.write(file_path, arcname)

        # 获取文件大小
        size_mb = zip_path.stat().st_size / (1024 * 1024)
        print(f"  ✅ 压缩包已创建 ({size_mb:.1f}MB)")

    def print_summary(self, portable_success, source_success, windows_success):
        """打印总结信息"""
        print("\n" + "="*60)
        print("🎊 多版本封装完成总结")
        print("="*60)

        print(f"📦 版本: {self.version}")
        print(f"📅 发布日期: {self.release_date}")
        print(f"📁 发布目录: {self.release_dir}")

        print(f"\n📋 封装结果:")
        print(f"  便携版: {'✅ 成功' if portable_success else '❌ 失败'}")
        print(f"  源码版: {'✅ 成功' if source_success else '❌ 失败'}")
        print(f"  Windows版: {'✅ 成功' if windows_success else '❌ 失败'}")

        success_count = sum([portable_success, source_success, windows_success])
        print(f"\n🎯 成功率: {success_count}/3 ({success_count/3*100:.0f}%)")

        if success_count > 0:
            print(f"\n📁 发布文件:")
            for zip_file in self.release_dir.glob("*.zip"):
                size_mb = zip_file.stat().st_size / (1024 * 1024)
                print(f"  📦 {zip_file.name} ({size_mb:.1f}MB)")

        print(f"\n💡 使用说明:")
        if portable_success:
            print(f"  便携版: 解压后运行启动脚本")
        if source_success:
            print(f"  源码版: 解压后运行install脚本")
        if windows_success:
            print(f"  Windows版: 解压后运行exe文件")

        print("="*60)

def create_version_info():
    """创建版本信息文件"""
    version_info = {
        "version": "v2.0.0",
        "release_date": datetime.now().strftime("%Y-%m-%d"),
        "build_time": datetime.now().isoformat(),
        "packages": {
            "portable": {
                "name": "便携版",
                "description": "免安装版本，包含所有依赖",
                "requirements": "Python 3.8+",
                "size_estimate": "~50MB"
            },
            "source": {
                "name": "源码版",
                "description": "完整源码，适合开发者",
                "requirements": "Python 3.8+, pip install",
                "size_estimate": "~10MB"
            },
            "windows": {
                "name": "Windows版",
                "description": "Windows可执行文件",
                "requirements": "Windows 7+",
                "size_estimate": "~100MB"
            }
        },
        "features": [
            "特码预测 (16个推荐号码)",
            "一致性预测 (确定性算法)",
            "完美预测系统 (4模组融合)",
            "历史回测分析",
            "增强回测优化",
            "综合评估系统",
            "数据管理功能",
            "机器学习集成",
            "多维生肖分析",
            "动态融合管理"
        ],
        "changelog": {
            "v2.0.0": [
                "新增完美预测系统",
                "集成机器学习模组",
                "优化融合策略",
                "增强回测功能",
                "改进GUI界面",
                "修复已知问题"
            ]
        }
    }

    with open("version_info.json", "w", encoding="utf-8") as f:
        json.dump(version_info, f, ensure_ascii=False, indent=2)

    print("✅ 版本信息文件已创建")

def main():
    """主函数"""
    print("🎊 六合彩预测系统多版本封装工具")
    print("="*60)

    # 创建版本信息
    create_version_info()

    # 创建打包器
    packager = LotterySystemPackager()

    # 创建所有版本
    packager.create_all_packages()

    print(f"\n🎉 封装完成！")
    print(f"📁 所有发布包已保存到: {packager.release_dir}")

if __name__ == "__main__":
    main()