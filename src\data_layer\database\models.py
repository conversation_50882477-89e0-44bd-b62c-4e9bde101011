"""
数据库模型定义
"""
from sqlalchemy import create_engine, Column, Integer, String, Date, Boolean, Float, Text, DateTime, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime
import json

Base = declarative_base()

class LunarYearMapping(Base):
    """农历年份映射表"""
    __tablename__ = 'lunar_year_mapping'
    
    id = Column(Integer, primary_key=True)
    lunar_year = Column(Integer, nullable=False, unique=True)
    zodiac_name = Column(String(10), nullable=False)
    zodiac_order = Column(Integer, nullable=False)
    wuxing_element = Column(String(10), nullable=False)
    spring_festival_date = Column(Date, nullable=False)
    year_start_date = Column(Date, nullable=False)
    year_end_date = Column(Date, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

class NumberZodiacMapping(Base):
    """号码生肖五行映射表"""
    __tablename__ = 'number_zodiac_mapping'
    
    id = Column(Integer, primary_key=True)
    number = Column(Integer, nullable=False)
    lunar_year = Column(Integer, ForeignKey('lunar_year_mapping.lunar_year'), nullable=False)
    zodiac_name = Column(String(10), nullable=False)
    wuxing_element = Column(String(10), nullable=False)
    is_active = Column(Boolean, default=True)
    effective_date = Column(Date, nullable=False)
    expiry_date = Column(Date)
    
    # 关系
    lunar_year_info = relationship("LunarYearMapping", backref="number_mappings")

class LotteryData(Base):
    """开奖数据表"""
    __tablename__ = 'lottery_data'
    
    id = Column(Integer, primary_key=True)
    period_number = Column(String(20), nullable=False, unique=True)
    draw_date = Column(Date, nullable=False)
    regular_numbers = Column(Text, nullable=False)  # JSON格式存储
    special_number = Column(Integer, nullable=False)
    
    # 年份信息
    natural_year = Column(Integer, nullable=False)
    lunar_year = Column(Integer, ForeignKey('lunar_year_mapping.lunar_year'), nullable=False)
    
    # 特码属性
    special_zodiac = Column(String(10))
    special_wuxing = Column(String(10))
    
    # 扩展生肖属性
    zodiac_color_wave = Column(String(10))  # 红绿蓝肖
    zodiac_season = Column(String(10))      # 春夏秋冬
    zodiac_day_night = Column(String(10))   # 日夜肖
    zodiac_left_right = Column(String(10))  # 左右肖
    zodiac_yin_yang = Column(String(10))    # 阴阳肖
    zodiac_courage = Column(String(10))     # 胆大胆小
    zodiac_fortune = Column(String(10))     # 吉凶肖
    zodiac_group = Column(String(10))       # 琴棋书画
    zodiac_origin = Column(String(10))      # 家野肖
    zodiac_heaven_earth = Column(String(10)) # 天地肖
    zodiac_gender = Column(String(10))      # 男女肖
    zodiac_position = Column(String(10))    # 前后肖
    zodiac_strokes = Column(String(10))     # 单双笔
    zodiac_combine = Column(String(10))     # 合独肖
    
    # 号码基础属性
    special_size = Column(String(10))       # 大小
    special_parity = Column(String(10))     # 单双
    special_prime = Column(String(10))      # 质合
    special_tail = Column(Integer)          # 尾数
    special_wave_color = Column(String(10)) # 波色
    special_positive_negative = Column(String(10)) # 正反码
    special_segment = Column(Integer)       # 段位
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    lunar_year_info = relationship("LunarYearMapping", backref="lottery_records")

class PredictionResult(Base):
    """预测结果表"""
    __tablename__ = 'prediction_results'
    
    id = Column(Integer, primary_key=True)
    target_period = Column(String(20), nullable=False)
    prediction_time = Column(DateTime, nullable=False)
    model_name = Column(String(50), nullable=False)
    model_type = Column(String(20), nullable=False)  # traditional, ml, fusion
    
    # 预测结果
    predicted_numbers = Column(Text, nullable=False)  # JSON格式
    predicted_zodiac = Column(Text)  # JSON格式，生肖预测
    confidence_score = Column(Float)
    
    # 验证结果
    actual_result = Column(Integer)  # 实际开奖特码
    is_hit = Column(Boolean)
    hit_rank = Column(Integer)  # 命中排名
    
    # 用户反馈
    user_rating = Column(Integer)  # 1-5分
    user_comment = Column(Text)
    
    created_at = Column(DateTime, default=datetime.utcnow)

class ModelPerformance(Base):
    """模型性能表"""
    __tablename__ = 'model_performance'
    
    id = Column(Integer, primary_key=True)
    model_name = Column(String(50), nullable=False)
    model_type = Column(String(20), nullable=False)
    
    # 性能指标
    accuracy = Column(Float)
    precision = Column(Float)
    recall = Column(Float)
    f1_score = Column(Float)
    
    # 最近表现
    recent_10_hit_rate = Column(Float)
    recent_20_hit_rate = Column(Float)
    recent_50_hit_rate = Column(Float)
    
    # 模型信息
    training_data_count = Column(Integer)
    last_training_time = Column(DateTime)
    model_parameters = Column(Text)  # JSON格式
    model_version = Column(String(20))
    
    # 权重信息
    fusion_weight = Column(Float, default=1.0)
    is_active = Column(Boolean, default=True)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# 数据库引擎和会话
def create_database_engine(database_url="sqlite:///data/lottery.db"):
    """创建数据库引擎"""
    engine = create_engine(database_url, echo=False)
    return engine

def create_tables(engine):
    """创建所有表"""
    Base.metadata.create_all(engine)

def get_session(engine):
    """获取数据库会话"""
    Session = sessionmaker(bind=engine)
    return Session()
