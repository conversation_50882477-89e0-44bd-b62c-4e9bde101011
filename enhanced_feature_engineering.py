#!/usr/bin/env python3
"""
增强特征工程 - 专门为六合彩预测优化的特征工程系统
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any
from collections import Counter, defaultdict
from sklearn.feature_selection import SelectKBest, RFE, mutual_info_classif
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier
import warnings
warnings.filterwarnings('ignore')

class EnhancedFeatureEngineering:
    """增强特征工程系统"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        
        # 生肖映射
        self.zodiac_mapping = {
            1: '鼠', 13: '鼠', 25: '鼠', 37: '鼠', 49: '鼠',
            2: '牛', 14: '牛', 26: '牛', 38: '牛',
            3: '虎', 15: '虎', 27: '虎', 39: '虎',
            4: '兔', 16: '兔', 28: '兔', 40: '兔',
            5: '龙', 17: '龙', 29: '龙', 41: '龙',
            6: '蛇', 18: '蛇', 30: '蛇', 42: '蛇',
            7: '马', 19: '马', 31: '马', 43: '马',
            8: '羊', 20: '羊', 32: '羊', 44: '羊',
            9: '猴', 21: '猴', 33: '猴', 45: '猴',
            10: '鸡', 22: '鸡', 34: '鸡', 46: '鸡',
            11: '狗', 23: '狗', 35: '狗', 47: '狗',
            12: '猪', 24: '猪', 36: '猪', 48: '猪'
        }
        
        # 五行映射
        self.wuxing_mapping = {
            '金': [4, 5, 12, 13, 20, 21, 28, 29, 36, 37, 44, 45],
            '木': [1, 8, 9, 16, 17, 24, 25, 32, 33, 40, 41, 48, 49],
            '水': [6, 7, 14, 15, 22, 23, 30, 31, 38, 39, 46, 47],
            '火': [2, 3, 10, 11, 18, 19, 26, 27, 34, 35, 42, 43],
            '土': []  # 土在六合彩中通常不单独分类
        }
        
    def extract_comprehensive_features(self, historical_data: List[Dict], window_size: int = 20) -> np.ndarray:
        """提取综合特征"""
        print(f"🔧 开始提取综合特征，窗口大小: {window_size}")
        
        all_features = []
        
        for i in range(window_size, len(historical_data)):
            window_data = historical_data[i-window_size:i]
            current_record = historical_data[i]
            
            # 提取各类特征
            features = []
            
            # 1. 基础统计特征
            basic_features = self._extract_basic_statistical_features(window_data)
            features.extend(basic_features)
            
            # 2. 六合彩专用特征
            lottery_features = self._extract_lottery_specific_features(window_data)
            features.extend(lottery_features)
            
            # 3. 时序特征
            temporal_features = self._extract_temporal_features(window_data)
            features.extend(temporal_features)
            
            # 4. 模式特征
            pattern_features = self._extract_pattern_features(window_data)
            features.extend(pattern_features)
            
            # 5. 交互特征
            interaction_features = self._extract_interaction_features(window_data)
            features.extend(interaction_features)
            
            all_features.append(features)
        
        feature_matrix = np.array(all_features)
        print(f"✅ 特征提取完成，特征维度: {feature_matrix.shape}")
        
        return feature_matrix
    
    def _extract_basic_statistical_features(self, window_data: List[Dict]) -> List[float]:
        """提取基础统计特征"""
        features = []
        
        # 获取特码序列
        special_numbers = [record.get('special_number', 0) for record in window_data]
        special_numbers = [n for n in special_numbers if n > 0]
        
        if not special_numbers:
            return [0] * 15  # 返回15个零特征
        
        # 基础统计量
        features.append(np.mean(special_numbers))      # 均值
        features.append(np.std(special_numbers))       # 标准差
        features.append(np.min(special_numbers))       # 最小值
        features.append(np.max(special_numbers))       # 最大值
        features.append(np.median(special_numbers))    # 中位数
        
        # 分位数
        features.append(np.percentile(special_numbers, 25))  # 25%分位数
        features.append(np.percentile(special_numbers, 75))  # 75%分位数
        
        # 偏度和峰度
        features.append(self._calculate_skewness(special_numbers))
        features.append(self._calculate_kurtosis(special_numbers))
        
        # 变异系数
        cv = np.std(special_numbers) / np.mean(special_numbers) if np.mean(special_numbers) != 0 else 0
        features.append(cv)
        
        # 极差
        features.append(np.max(special_numbers) - np.min(special_numbers))
        
        # 众数相关
        counter = Counter(special_numbers)
        most_common = counter.most_common(1)[0] if counter else (0, 0)
        features.append(most_common[0])  # 众数值
        features.append(most_common[1])  # 众数频次
        
        # 唯一值比例
        unique_ratio = len(set(special_numbers)) / len(special_numbers)
        features.append(unique_ratio)
        
        # 连续性特征
        consecutive_count = self._count_consecutive_numbers(special_numbers)
        features.append(consecutive_count)
        
        return features
    
    def _extract_lottery_specific_features(self, window_data: List[Dict]) -> List[float]:
        """提取六合彩专用特征"""
        features = []
        
        special_numbers = [record.get('special_number', 0) for record in window_data if record.get('special_number', 0) > 0]
        
        if not special_numbers:
            return [0] * 20  # 返回20个零特征
        
        # 大小分布
        big_count = sum(1 for n in special_numbers if n >= 25)
        small_count = len(special_numbers) - big_count
        features.append(big_count / len(special_numbers))
        features.append(small_count / len(special_numbers))
        
        # 单双分布
        odd_count = sum(1 for n in special_numbers if n % 2 == 1)
        even_count = len(special_numbers) - odd_count
        features.append(odd_count / len(special_numbers))
        features.append(even_count / len(special_numbers))
        
        # 尾数分布
        tail_counts = Counter([n % 10 for n in special_numbers])
        for i in range(10):
            features.append(tail_counts.get(i, 0) / len(special_numbers))
        
        # 生肖分布
        zodiac_counts = Counter([self.zodiac_mapping.get(n, '未知') for n in special_numbers])
        zodiac_entropy = self._calculate_entropy(list(zodiac_counts.values()))
        features.append(zodiac_entropy)
        
        # 五行分布
        wuxing_counts = defaultdict(int)
        for number in special_numbers:
            for element, numbers in self.wuxing_mapping.items():
                if number in numbers:
                    wuxing_counts[element] += 1
                    break
        
        total_wuxing = sum(wuxing_counts.values())
        if total_wuxing > 0:
            for element in ['金', '木', '水', '火']:
                features.append(wuxing_counts[element] / total_wuxing)
        else:
            features.extend([0, 0, 0, 0])
        
        # 段位分布 (1-12, 13-24, 25-36, 37-49)
        segment_counts = [0, 0, 0, 0]
        for n in special_numbers:
            if 1 <= n <= 12:
                segment_counts[0] += 1
            elif 13 <= n <= 24:
                segment_counts[1] += 1
            elif 25 <= n <= 36:
                segment_counts[2] += 1
            elif 37 <= n <= 49:
                segment_counts[3] += 1
        
        total_segments = sum(segment_counts)
        if total_segments > 0:
            features.extend([count / total_segments for count in segment_counts])
        else:
            features.extend([0, 0, 0, 0])
        
        return features
    
    def _extract_temporal_features(self, window_data: List[Dict]) -> List[float]:
        """提取时序特征"""
        features = []
        
        special_numbers = [record.get('special_number', 0) for record in window_data if record.get('special_number', 0) > 0]
        
        if len(special_numbers) < 3:
            return [0] * 10
        
        # 趋势特征
        x = np.arange(len(special_numbers))
        trend_coef = np.polyfit(x, special_numbers, 1)[0]
        features.append(trend_coef)
        
        # 差分特征
        diffs = np.diff(special_numbers)
        features.append(np.mean(diffs))
        features.append(np.std(diffs))
        features.append(np.max(diffs))
        features.append(np.min(diffs))
        
        # 二阶差分
        if len(diffs) > 1:
            second_diffs = np.diff(diffs)
            features.append(np.mean(second_diffs))
            features.append(np.std(second_diffs))
        else:
            features.extend([0, 0])
        
        # 周期性特征
        if len(special_numbers) >= 7:
            # 7期周期
            period_7_corr = self._calculate_autocorrelation(special_numbers, 7)
            features.append(period_7_corr)
        else:
            features.append(0)
        
        # 波动性
        volatility = np.std(diffs) / np.mean(special_numbers) if np.mean(special_numbers) != 0 else 0
        features.append(volatility)
        
        # 动量指标
        if len(special_numbers) >= 5:
            momentum = special_numbers[-1] - special_numbers[-5]
            features.append(momentum)
        else:
            features.append(0)
        
        return features
    
    def _extract_pattern_features(self, window_data: List[Dict]) -> List[float]:
        """提取模式特征"""
        features = []
        
        special_numbers = [record.get('special_number', 0) for record in window_data if record.get('special_number', 0) > 0]
        
        if len(special_numbers) < 3:
            return [0] * 8
        
        # 连号模式
        consecutive_pairs = 0
        for i in range(len(special_numbers) - 1):
            if abs(special_numbers[i] - special_numbers[i+1]) == 1:
                consecutive_pairs += 1
        features.append(consecutive_pairs / max(len(special_numbers) - 1, 1))
        
        # 重复模式
        repeat_count = 0
        for i in range(1, len(special_numbers)):
            if special_numbers[i] in special_numbers[:i]:
                repeat_count += 1
        features.append(repeat_count / len(special_numbers))
        
        # 间隔模式
        gap_patterns = defaultdict(int)
        for i in range(len(special_numbers) - 1):
            gap = abs(special_numbers[i+1] - special_numbers[i])
            gap_patterns[gap] += 1
        
        # 常见间隔
        common_gaps = [1, 2, 3, 5, 7, 10, 12]
        for gap in common_gaps:
            features.append(gap_patterns.get(gap, 0) / max(len(special_numbers) - 1, 1))
        
        return features
    
    def _extract_interaction_features(self, window_data: List[Dict]) -> List[float]:
        """提取交互特征"""
        features = []
        
        special_numbers = [record.get('special_number', 0) for record in window_data if record.get('special_number', 0) > 0]
        
        if not special_numbers:
            return [0] * 5
        
        # 大小与单双交互
        big_odd = sum(1 for n in special_numbers if n >= 25 and n % 2 == 1)
        big_even = sum(1 for n in special_numbers if n >= 25 and n % 2 == 0)
        small_odd = sum(1 for n in special_numbers if n < 25 and n % 2 == 1)
        small_even = sum(1 for n in special_numbers if n < 25 and n % 2 == 0)
        
        total = len(special_numbers)
        features.append(big_odd / total)
        features.append(big_even / total)
        features.append(small_odd / total)
        features.append(small_even / total)
        
        # 生肖与五行交互（简化）
        zodiac_wuxing_score = 0
        for number in special_numbers:
            zodiac = self.zodiac_mapping.get(number, '未知')
            # 简化的生肖五行关联评分
            if zodiac in ['龙', '狗', '牛', '羊']:  # 土属性生肖
                zodiac_wuxing_score += 0.2
            elif zodiac in ['虎', '兔']:  # 木属性生肖
                zodiac_wuxing_score += 0.3
        
        features.append(zodiac_wuxing_score / total if total > 0 else 0)
        
        return features
    
    def _calculate_skewness(self, data: List[float]) -> float:
        """计算偏度"""
        if len(data) < 3:
            return 0
        
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0
        
        skew = np.mean([((x - mean) / std) ** 3 for x in data])
        return skew
    
    def _calculate_kurtosis(self, data: List[float]) -> float:
        """计算峰度"""
        if len(data) < 4:
            return 0
        
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0
        
        kurt = np.mean([((x - mean) / std) ** 4 for x in data]) - 3
        return kurt
    
    def _calculate_entropy(self, counts: List[int]) -> float:
        """计算熵"""
        if not counts or sum(counts) == 0:
            return 0
        
        total = sum(counts)
        probs = [count / total for count in counts if count > 0]
        entropy = -sum(p * np.log2(p) for p in probs)
        return entropy
    
    def _count_consecutive_numbers(self, numbers: List[int]) -> int:
        """统计连续数字的数量"""
        if len(numbers) < 2:
            return 0
        
        consecutive_count = 0
        for i in range(len(numbers) - 1):
            if abs(numbers[i+1] - numbers[i]) == 1:
                consecutive_count += 1
        
        return consecutive_count
    
    def _calculate_autocorrelation(self, data: List[float], lag: int) -> float:
        """计算自相关系数"""
        if len(data) <= lag:
            return 0
        
        n = len(data)
        mean = np.mean(data)
        
        numerator = sum((data[i] - mean) * (data[i + lag] - mean) for i in range(n - lag))
        denominator = sum((x - mean) ** 2 for x in data)
        
        if denominator == 0:
            return 0
        
        return numerator / denominator
    
    def select_best_features(self, X: np.ndarray, y: np.ndarray, n_features: int = 50) -> Tuple[np.ndarray, np.ndarray]:
        """选择最佳特征"""
        print(f"🔍 开始特征选择，从 {X.shape[1]} 个特征中选择 {n_features} 个")
        
        # 使用多种特征选择方法
        methods = {
            'mutual_info': SelectKBest(score_func=mutual_info_classif, k=min(n_features, X.shape[1])),
            'rfe': RFE(RandomForestClassifier(n_estimators=50, random_state=42), 
                      n_features_to_select=min(n_features, X.shape[1]))
        }
        
        selected_features = {}
        
        for method_name, selector in methods.items():
            try:
                X_selected = selector.fit_transform(X, y)
                feature_mask = selector.get_support() if hasattr(selector, 'get_support') else selector.support_
                selected_features[method_name] = feature_mask
                print(f"  ✅ {method_name}: 选择了 {np.sum(feature_mask)} 个特征")
            except Exception as e:
                print(f"  ❌ {method_name} 失败: {e}")
        
        # 融合特征选择结果
        if selected_features:
            # 投票机制：被多个方法选中的特征
            feature_votes = np.zeros(X.shape[1])
            for mask in selected_features.values():
                feature_votes += mask.astype(int)
            
            # 选择得票最多的特征
            top_indices = np.argsort(feature_votes)[::-1][:n_features]
            final_mask = np.zeros(X.shape[1], dtype=bool)
            final_mask[top_indices] = True
            
            X_final = X[:, final_mask]
            print(f"✅ 特征选择完成，最终选择 {X_final.shape[1]} 个特征")
            
            return X_final, final_mask
        else:
            print("⚠️ 特征选择失败，返回原始特征")
            return X, np.ones(X.shape[1], dtype=bool)

if __name__ == "__main__":
    # 测试增强特征工程
    fe = EnhancedFeatureEngineering()
    
    # 模拟历史数据
    import random
    historical_data = []
    for i in range(50):
        historical_data.append({
            'special_number': random.randint(1, 49),
            'draw_date': f'2025-06-{i+1:02d}'
        })
    
    # 提取特征
    features = fe.extract_comprehensive_features(historical_data, window_size=20)
    print(f"🎊 特征工程测试完成，特征矩阵形状: {features.shape}")
