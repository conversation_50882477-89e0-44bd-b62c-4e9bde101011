"""
澳门六合彩智能预测系统 - 发行版打包脚本
支持三种格式：发行版、Windows版、便携版
"""
import os
import shutil
import zipfile
import json
from datetime import datetime
import subprocess
import sys

class LotteryPackager:
    """彩票预测系统打包器"""
    
    def __init__(self):
        self.version = "1.0.0"
        self.build_date = datetime.now().strftime("%Y%m%d")
        self.project_name = "澳门六合彩智能预测系统"
        self.base_dir = os.getcwd()
        self.dist_dir = os.path.join(self.base_dir, "dist")
        
        # 核心文件列表
        self.core_files = [
            "lottery_prediction_gui.py",
            "main.py",
            "requirements.txt",
            "README.md"
        ]
        
        # 源码目录
        self.source_dirs = [
            "src",
            "data",
            "docs"
        ]
        
        # 可选文件
        self.optional_files = [
            "config.json",
            "settings.ini",
            "LICENSE"
        ]
    
    def create_dist_directory(self):
        """创建发行目录"""
        if os.path.exists(self.dist_dir):
            shutil.rmtree(self.dist_dir)
        os.makedirs(self.dist_dir)
        print(f"✅ 创建发行目录: {self.dist_dir}")
    
    def copy_core_files(self, target_dir):
        """复制核心文件"""
        print(f"📁 复制核心文件到: {target_dir}")
        
        for file in self.core_files:
            if os.path.exists(file):
                shutil.copy2(file, target_dir)
                print(f"   ✅ {file}")
            else:
                print(f"   ⚠️ {file} (不存在)")
        
        # 复制可选文件
        for file in self.optional_files:
            if os.path.exists(file):
                shutil.copy2(file, target_dir)
                print(f"   ✅ {file}")
    
    def copy_source_dirs(self, target_dir):
        """复制源码目录"""
        print(f"📂 复制源码目录到: {target_dir}")
        
        for dir_name in self.source_dirs:
            if os.path.exists(dir_name):
                target_path = os.path.join(target_dir, dir_name)
                shutil.copytree(dir_name, target_path, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
                print(f"   ✅ {dir_name}/")
            else:
                print(f"   ⚠️ {dir_name}/ (不存在)")
    
    def create_batch_files(self, target_dir, package_type):
        """创建批处理文件"""
        print(f"📝 创建批处理文件...")
        
        # 启动脚本
        if package_type == "portable":
            start_script = """@echo off
chcp 65001 > nul
title 澳门六合彩智能预测系统 - 便携版
echo 🎯 澳门六合彩智能预测系统 - 便携版
echo ==========================================
echo 正在启动系统...
python lottery_prediction_gui.py
if errorlevel 1 (
    echo.
    echo ❌ 启动失败，请检查Python环境
    echo 💡 确保已安装Python 3.7+和所需依赖
    pause
)
"""
        elif package_type == "windows":
            start_script = """@echo off
chcp 65001 > nul
title 澳门六合彩智能预测系统 - Windows版
echo 🎯 澳门六合彩智能预测系统 - Windows版
echo ==========================================
echo 正在检查环境...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python环境
    echo 💡 请先安装Python 3.7+
    echo 📥 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python环境检查通过
echo 正在安装依赖...
pip install -r requirements.txt
echo 正在启动系统...
python lottery_prediction_gui.py
pause
"""
        else:  # release
            start_script = """@echo off
chcp 65001 > nul
title 澳门六合彩智能预测系统 - 发行版
echo 🎯 澳门六合彩智能预测系统 - 发行版
echo ==========================================
echo 正在检查环境...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python环境
    echo 💡 请先安装Python 3.7+
    pause
    exit /b 1
)
echo ✅ Python环境检查通过
echo 正在检查依赖...
pip install -r requirements.txt --quiet
echo 正在启动系统...
python lottery_prediction_gui.py
if errorlevel 1 (
    echo.
    echo ❌ 启动失败，请检查错误信息
    pause
)
"""
        
        with open(os.path.join(target_dir, "启动系统.bat"), "w", encoding="utf-8") as f:
            f.write(start_script)
        
        # 安装依赖脚本
        install_script = """@echo off
chcp 65001 > nul
title 安装依赖 - 澳门六合彩智能预测系统
echo 📦 安装系统依赖
echo ==========================================
echo 正在检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ 未检测到Python环境
    echo 💡 请先安装Python 3.7+
    pause
    exit /b 1
)
echo.
echo 正在安装依赖包...
pip install -r requirements.txt
echo.
echo ✅ 依赖安装完成
echo 💡 现在可以运行"启动系统.bat"
pause
"""
        
        with open(os.path.join(target_dir, "安装依赖.bat"), "w", encoding="utf-8") as f:
            f.write(install_script)
        
        print(f"   ✅ 启动系统.bat")
        print(f"   ✅ 安装依赖.bat")
    
    def create_readme(self, target_dir, package_type):
        """创建说明文件"""
        print(f"📄 创建说明文件...")
        
        if package_type == "portable":
            readme_content = f"""# 🎯 澳门六合彩智能预测系统 - 便携版

## 📦 版本信息
- 版本: {self.version}
- 构建日期: {self.build_date}
- 类型: 便携版 (Portable)

## 🚀 快速启动
1. 确保已安装Python 3.7+
2. 双击运行 `启动系统.bat`
3. 系统将自动启动GUI界面

## 📋 系统要求
- Windows 10/11
- Python 3.7 或更高版本
- 2GB 可用内存
- 100MB 磁盘空间

## 🔧 手动启动
如果批处理文件无法运行，可以手动执行：
```bash
python lottery_prediction_gui.py
```

## 📚 功能特性
- ✅ 特码预测 (16个号码推荐)
- ✅ 生肖预测 (4个生肖推荐)
- ✅ 一致性验证
- ✅ 历史回测
- ✅ 增强回测
- ✅ 数据管理
- ✅ 系统设置

## 📞 技术支持
如遇问题，请检查：
1. Python环境是否正确安装
2. 依赖包是否完整安装
3. 系统权限是否充足

## ⚠️ 免责声明
本软件仅供学习和研究使用，预测结果仅供参考，不构成投注建议。
"""
        elif package_type == "windows":
            readme_content = f"""# 🎯 澳门六合彩智能预测系统 - Windows版

## 📦 版本信息
- 版本: {self.version}
- 构建日期: {self.build_date}
- 类型: Windows版

## 🚀 安装步骤
1. 确保已安装Python 3.7+
2. 双击运行 `安装依赖.bat` 安装所需依赖
3. 双击运行 `启动系统.bat` 启动系统

## 📋 系统要求
- Windows 10/11 (推荐)
- Python 3.7 或更高版本
- pip 包管理器
- 2GB 可用内存
- 200MB 磁盘空间

## 🔧 依赖安装
系统需要以下Python包：
- PyQt5 (GUI界面)
- numpy (数值计算)
- pandas (数据处理)
- scikit-learn (机器学习)
- matplotlib (图表显示)

## 📚 完整功能
- 🎯 特码预测系统
- 🐲 生肖预测系统
- 🔄 一致性验证系统
- 📊 历史回测系统
- 🚀 增强回测系统
- 📁 数据管理系统
- ⚙️ 系统设置

## 🛠️ 故障排除
1. **Python未安装**: 下载安装Python 3.7+
2. **依赖安装失败**: 检查网络连接，使用管理员权限
3. **启动失败**: 检查Python路径，确认依赖完整

## ⚠️ 免责声明
本软件仅供学习和研究使用，预测结果仅供参考，不构成投注建议。
"""
        else:  # release
            readme_content = f"""# 🎯 澳门六合彩智能预测系统 - 发行版

## 📦 版本信息
- 版本: {self.version}
- 构建日期: {self.build_date}
- 类型: 正式发行版

## 🚀 安装指南
1. 解压到任意目录
2. 确保Python 3.7+环境
3. 运行 `安装依赖.bat` 安装依赖
4. 运行 `启动系统.bat` 启动系统

## 📋 系统要求
- 操作系统: Windows 10/11 (推荐)
- Python: 3.7 或更高版本
- 内存: 2GB 可用内存
- 磁盘: 500MB 可用空间
- 网络: 用于依赖下载

## 🎯 核心功能
### 预测系统
- 特码预测 (16个号码推荐)
- 生肖预测 (4个生肖推荐)
- 多模组融合预测
- 智能筛选优化

### 验证系统
- 一致性验证
- 历史回测验证
- 增强回测分析
- 性能评估

### 管理系统
- 数据导入管理
- 历史数据查看
- 系统配置设置
- 报告生成导出

## 🔧 技术架构
- 前端: PyQt5 GUI界面
- 后端: Python核心算法
- 数据: SQLite数据库
- 算法: 机器学习 + 统计分析

## 📚 使用说明
1. **数据管理**: 导入历史开奖数据
2. **特码预测**: 选择日期进行预测
3. **一致性验证**: 验证预测一致性
4. **历史回测**: 评估算法性能
5. **系统设置**: 调整算法参数

## 🛠️ 故障排除
### 常见问题
1. **启动失败**: 检查Python环境和依赖
2. **预测错误**: 确认数据完整性
3. **界面异常**: 重启系统或重装依赖

### 技术支持
- 检查日志文件: logs/目录
- 查看错误信息
- 确认系统权限

## ⚠️ 重要声明
- 本软件仅供学习研究使用
- 预测结果仅供参考，不构成投注建议
- 使用者需自行承担使用风险
- 严禁用于非法用途

## 📄 许可证
本软件遵循开源许可证，详见LICENSE文件。
"""
        
        with open(os.path.join(target_dir, "README.md"), "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        print(f"   ✅ README.md")
    
    def create_version_info(self, target_dir, package_type):
        """创建版本信息文件"""
        version_info = {
            "name": self.project_name,
            "version": self.version,
            "build_date": self.build_date,
            "package_type": package_type,
            "python_version": "3.7+",
            "platform": "Windows",
            "features": [
                "特码预测",
                "生肖预测", 
                "一致性验证",
                "历史回测",
                "增强回测",
                "数据管理",
                "系统设置"
            ]
        }
        
        with open(os.path.join(target_dir, "version.json"), "w", encoding="utf-8") as f:
            json.dump(version_info, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ version.json")
    
    def create_release_package(self):
        """创建发行版"""
        print(f"\n📦 创建发行版...")
        package_name = f"澳门六合彩预测系统_发行版_v{self.version}_{self.build_date}"
        package_dir = os.path.join(self.dist_dir, package_name)
        
        os.makedirs(package_dir)
        
        # 复制文件
        self.copy_core_files(package_dir)
        self.copy_source_dirs(package_dir)
        
        # 创建脚本和文档
        self.create_batch_files(package_dir, "release")
        self.create_readme(package_dir, "release")
        self.create_version_info(package_dir, "release")
        
        # 创建ZIP包
        zip_path = f"{package_dir}.zip"
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(package_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, self.dist_dir)
                    zipf.write(file_path, arc_path)
        
        print(f"✅ 发行版创建完成: {zip_path}")
        return zip_path
    
    def create_windows_package(self):
        """创建Windows版"""
        print(f"\n🪟 创建Windows版...")
        package_name = f"澳门六合彩预测系统_Windows版_v{self.version}_{self.build_date}"
        package_dir = os.path.join(self.dist_dir, package_name)
        
        os.makedirs(package_dir)
        
        # 复制文件
        self.copy_core_files(package_dir)
        self.copy_source_dirs(package_dir)
        
        # 创建脚本和文档
        self.create_batch_files(package_dir, "windows")
        self.create_readme(package_dir, "windows")
        self.create_version_info(package_dir, "windows")
        
        # 创建ZIP包
        zip_path = f"{package_dir}.zip"
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(package_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, self.dist_dir)
                    zipf.write(file_path, arc_path)
        
        print(f"✅ Windows版创建完成: {zip_path}")
        return zip_path
    
    def create_portable_package(self):
        """创建便携版"""
        print(f"\n💼 创建便携版...")
        package_name = f"澳门六合彩预测系统_便携版_v{self.version}_{self.build_date}"
        package_dir = os.path.join(self.dist_dir, package_name)
        
        os.makedirs(package_dir)
        
        # 复制文件
        self.copy_core_files(package_dir)
        self.copy_source_dirs(package_dir)
        
        # 创建脚本和文档
        self.create_batch_files(package_dir, "portable")
        self.create_readme(package_dir, "portable")
        self.create_version_info(package_dir, "portable")
        
        # 创建ZIP包
        zip_path = f"{package_dir}.zip"
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(package_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, self.dist_dir)
                    zipf.write(file_path, arc_path)
        
        print(f"✅ 便携版创建完成: {zip_path}")
        return zip_path
    
    def package_all(self):
        """打包所有版本"""
        print(f"🎯 {self.project_name} - 发行版打包")
        print(f"版本: {self.version}")
        print(f"构建日期: {self.build_date}")
        print("=" * 60)
        
        # 创建发行目录
        self.create_dist_directory()
        
        # 创建三种版本
        packages = []
        packages.append(self.create_release_package())
        packages.append(self.create_windows_package())
        packages.append(self.create_portable_package())
        
        # 显示总结
        print(f"\n🎊 打包完成!")
        print(f"📦 共创建 {len(packages)} 个发行包:")
        for i, package in enumerate(packages, 1):
            size = os.path.getsize(package) / (1024 * 1024)
            print(f"   {i}. {os.path.basename(package)} ({size:.1f} MB)")
        
        print(f"\n📁 发行目录: {self.dist_dir}")
        return packages

def main():
    """主函数"""
    try:
        packager = LotteryPackager()
        packages = packager.package_all()
        
        print(f"\n✅ 所有发行版创建成功!")
        print(f"💡 可以分发这些ZIP文件给用户使用")
        
    except Exception as e:
        print(f"❌ 打包失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
