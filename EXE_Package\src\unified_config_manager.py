"""
统一配置管理器 - 解决多预测系统配置冲突问题
防止特码预测、完美预测、历史回测、增强回测之间的配置混乱
"""

import json
import os
import threading
import time
from typing import Dict, Any, Optional
from datetime import datetime
import logging

class UnifiedConfigManager:
    """统一配置管理器 - 防止预测系统间配置冲突"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式确保全局唯一配置管理器"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self.logger = logging.getLogger(__name__)
        self._initialized = True
        self._config_lock = threading.RLock()
        
        # 配置文件路径映射
        self.config_paths = {
            'master': 'config/master_config.json',
            'special_predictor': 'config/special_predictor_config.json',
            'perfect_prediction': 'config/perfect_prediction_config.json',
            'historical_backtest': 'config/historical_backtest_config.json',
            'enhanced_backtest': 'config/enhanced_backtest_config.json',
            'ml_module': 'config/ml_module_config.json',
            'traditional_analysis': 'config/traditional_analysis_config.json',
            'zodiac_extended': 'config/zodiac_extended_config.json'
        }
        
        # 内存配置缓存
        self._config_cache = {}
        self._cache_timestamps = {}
        
        # 配置访问状态跟踪
        self._access_log = {}
        self._conflict_detection = True
        
        # 确保配置目录存在
        os.makedirs('config', exist_ok=True)
        
        # 初始化默认配置
        self._initialize_default_configs()
        
        self.logger.info("统一配置管理器初始化完成")
    
    def _initialize_default_configs(self):
        """初始化默认配置"""
        
        # 主配置
        master_config = {
            "system_version": "1.0.0",
            "config_manager_version": "1.0.0",
            "last_updated": datetime.now().isoformat(),
            "active_prediction_mode": "none",  # none, special, perfect, backtest
            "conflict_detection_enabled": True,
            "auto_backup_enabled": True,
            "max_backup_files": 5
        }
        
        # 特码预测器配置
        special_config = {
            "prediction_count": 16,
            "hot_threshold": 5,
            "cold_threshold": 10,
            "confidence_threshold": 0.6,
            "analysis_methods": ["frequency", "pattern", "trend"],
            "db_path": "data/lottery.db"
        }
        
        # 完美预测系统配置
        perfect_config = {
            "modules": {
                "traditional_analysis": {"enabled": True, "weight": 0.35},
                "machine_learning": {"enabled": True, "weight": 0.4},
                "zodiac_extended": {"enabled": True, "weight": 0.15},
                "special_zodiac": {"enabled": True, "weight": 0.1}
            },
            "fusion_strategy": "enhanced_fusion",
            "confidence_threshold": 0.82,
            "stability_target": 0.75,
            "optimization_enabled": True
        }
        
        # 历史回测配置
        backtest_config = {
            "prediction_window": 30,
            "evaluation_metrics": ["hit_rate", "precision", "recall"],
            "batch_size": 10,
            "parallel_processing": False,
            "cache_results": True
        }
        
        # 增强回测配置
        enhanced_config = {
            "optimization_rounds": 10,
            "parameter_search_space": {
                "confidence_threshold": [0.7, 0.75, 0.8, 0.85],
                "fusion_weights": "auto_optimize",
                "stability_target": [0.65, 0.7, 0.75, 0.8]
            },
            "selection_criteria": "hit_rate",
            "auto_apply_optimal": True
        }
        
        # 保存默认配置
        configs = {
            'master': master_config,
            'special_predictor': special_config,
            'perfect_prediction': perfect_config,
            'historical_backtest': backtest_config,
            'enhanced_backtest': enhanced_config
        }
        
        for config_type, config_data in configs.items():
            if not os.path.exists(self.config_paths[config_type]):
                self._save_config_file(config_type, config_data)
    
    def get_config(self, config_type: str, section: Optional[str] = None) -> Dict[str, Any]:
        """获取配置（线程安全）"""
        with self._config_lock:
            # 记录访问
            self._log_access(config_type, 'read')
            
            # 检查冲突
            if self._conflict_detection:
                self._check_access_conflict(config_type)
            
            # 从缓存获取或加载
            config = self._get_cached_config(config_type)
            
            if section:
                return config.get(section, {})
            return config
    
    def set_config(self, config_type: str, config_data: Dict[str, Any], 
                   section: Optional[str] = None, backup: bool = True) -> bool:
        """设置配置（线程安全）"""
        with self._config_lock:
            try:
                # 记录访问
                self._log_access(config_type, 'write')
                
                # 检查冲突
                if self._conflict_detection:
                    self._check_access_conflict(config_type)
                
                # 备份当前配置
                if backup:
                    self._backup_config(config_type)
                
                # 更新配置
                if section:
                    current_config = self._get_cached_config(config_type)
                    current_config[section] = config_data
                    config_data = current_config
                
                # 保存配置
                self._save_config_file(config_type, config_data)
                
                # 更新缓存
                self._config_cache[config_type] = config_data
                self._cache_timestamps[config_type] = time.time()
                
                self.logger.info(f"配置已更新: {config_type}")
                return True
                
            except Exception as e:
                self.logger.error(f"配置更新失败 {config_type}: {e}")
                return False
    
    def _get_cached_config(self, config_type: str) -> Dict[str, Any]:
        """获取缓存配置"""
        # 检查缓存是否有效
        if config_type in self._config_cache:
            cache_time = self._cache_timestamps.get(config_type, 0)
            file_path = self.config_paths[config_type]
            
            if os.path.exists(file_path):
                file_time = os.path.getmtime(file_path)
                if cache_time >= file_time:
                    return self._config_cache[config_type]
        
        # 加载配置文件
        config = self._load_config_file(config_type)
        self._config_cache[config_type] = config
        self._cache_timestamps[config_type] = time.time()
        return config
    
    def _load_config_file(self, config_type: str) -> Dict[str, Any]:
        """加载配置文件"""
        file_path = self.config_paths[config_type]
        
        if not os.path.exists(file_path):
            self.logger.warning(f"配置文件不存在: {file_path}")
            return {}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"配置文件加载失败 {file_path}: {e}")
            return {}
    
    def _save_config_file(self, config_type: str, config_data: Dict[str, Any]):
        """保存配置文件"""
        file_path = self.config_paths[config_type]
        
        # 添加元数据
        config_data['_metadata'] = {
            'last_updated': datetime.now().isoformat(),
            'config_type': config_type,
            'manager_version': '1.0.0'
        }
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"配置文件保存失败 {file_path}: {e}")
            raise
    
    def _log_access(self, config_type: str, operation: str):
        """记录配置访问"""
        timestamp = time.time()
        if config_type not in self._access_log:
            self._access_log[config_type] = []
        
        self._access_log[config_type].append({
            'timestamp': timestamp,
            'operation': operation,
            'thread_id': threading.current_thread().ident
        })
        
        # 保持最近100条记录
        if len(self._access_log[config_type]) > 100:
            self._access_log[config_type] = self._access_log[config_type][-100:]
    
    def _check_access_conflict(self, config_type: str):
        """检查访问冲突"""
        if config_type not in self._access_log:
            return
        
        current_time = time.time()
        recent_accesses = [
            access for access in self._access_log[config_type]
            if current_time - access['timestamp'] < 5.0  # 5秒内的访问
        ]
        
        # 检查是否有多个线程同时写入
        write_threads = set()
        for access in recent_accesses:
            if access['operation'] == 'write':
                write_threads.add(access['thread_id'])
        
        if len(write_threads) > 1:
            self.logger.warning(f"检测到配置冲突: {config_type} 被多个线程同时写入")
    
    def _backup_config(self, config_type: str):
        """备份配置"""
        try:
            source_path = self.config_paths[config_type]
            if not os.path.exists(source_path):
                return
            
            backup_dir = 'config/backups'
            os.makedirs(backup_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = f"{backup_dir}/{config_type}_backup_{timestamp}.json"
            
            with open(source_path, 'r', encoding='utf-8') as src:
                with open(backup_path, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
            
            # 清理旧备份
            self._cleanup_old_backups(config_type, backup_dir)
            
        except Exception as e:
            self.logger.error(f"配置备份失败 {config_type}: {e}")
    
    def _cleanup_old_backups(self, config_type: str, backup_dir: str):
        """清理旧备份文件"""
        try:
            backup_files = [
                f for f in os.listdir(backup_dir)
                if f.startswith(f"{config_type}_backup_") and f.endswith('.json')
            ]
            
            backup_files.sort(reverse=True)  # 最新的在前
            
            # 保留最近5个备份
            for old_backup in backup_files[5:]:
                os.remove(os.path.join(backup_dir, old_backup))
                
        except Exception as e:
            self.logger.error(f"清理备份文件失败: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        with self._config_lock:
            master_config = self.get_config('master')
            
            status = {
                'active_mode': master_config.get('active_prediction_mode', 'none'),
                'config_files': len(self.config_paths),
                'cached_configs': len(self._config_cache),
                'total_accesses': sum(len(log) for log in self._access_log.values()),
                'conflict_detection': self._conflict_detection,
                'last_updated': master_config.get('last_updated', 'unknown')
            }
            
            return status
    
    def set_active_mode(self, mode: str):
        """设置活动预测模式"""
        valid_modes = ['none', 'special', 'perfect', 'historical_backtest', 'enhanced_backtest']
        if mode not in valid_modes:
            raise ValueError(f"无效模式: {mode}. 有效模式: {valid_modes}")
        
        master_config = self.get_config('master')
        master_config['active_prediction_mode'] = mode
        master_config['mode_changed_at'] = datetime.now().isoformat()
        
        self.set_config('master', master_config)
        self.logger.info(f"活动预测模式已设置为: {mode}")
    
    def clear_cache(self):
        """清理配置缓存"""
        with self._config_lock:
            self._config_cache.clear()
            self._cache_timestamps.clear()
            self.logger.info("配置缓存已清理")

# 全局配置管理器实例
config_manager = UnifiedConfigManager()
