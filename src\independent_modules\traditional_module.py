"""
传统分析模组 - 独立预测和回测
支持冷热分析、遗漏分析、趋势分析等传统方法
"""
import sqlite3
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import Counter
import json

class TraditionalAnalysisModule:
    """传统分析模组"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.module_name = "传统分析模组"
        
        # 分析参数
        self.analysis_config = {
            "hot_cold_window": 50,      # 冷热分析窗口
            "trend_window": 30,         # 趋势分析窗口
            "omission_threshold": 10,   # 遗漏阈值
            "frequency_weight": 0.4,    # 频率权重
            "trend_weight": 0.3,        # 趋势权重
            "omission_weight": 0.3      # 遗漏权重
        }
    
    def predict(self, target_date: str, analysis_days: int = 100) -> Dict[str, Any]:
        """独立预测方法"""
        print(f"🔍 {self.module_name}开始预测 - 目标日期: {target_date}")
        
        # 获取历史数据
        historical_data = self._get_historical_data(target_date, analysis_days)
        
        if len(historical_data) < 30:
            return self._get_default_result()
        
        # 执行各种传统分析
        hot_cold_analysis = self._analyze_hot_cold_numbers(historical_data)
        trend_analysis = self._analyze_trend_patterns(historical_data)
        omission_analysis = self._analyze_omission_patterns(historical_data)
        frequency_analysis = self._analyze_frequency_patterns(historical_data)
        
        # 综合评分
        number_scores = self._calculate_comprehensive_scores(
            hot_cold_analysis, trend_analysis, omission_analysis, frequency_analysis
        )
        
        # 选择推荐号码
        recommended_numbers = self._select_recommended_numbers(number_scores)
        
        # 计算置信度
        confidence = self._calculate_confidence(number_scores, recommended_numbers)
        
        result = {
            "predicted_numbers": recommended_numbers,
            "confidence": confidence,
            "analysis_details": {
                "hot_cold": hot_cold_analysis,
                "trend": trend_analysis,
                "omission": omission_analysis,
                "frequency": frequency_analysis
            },
            "number_scores": number_scores,
            "metadata": {
                "analysis_days": analysis_days,
                "data_count": len(historical_data),
                "prediction_time": datetime.now().isoformat()
            }
        }
        
        print(f"✅ {self.module_name}预测完成 - 推荐{len(recommended_numbers)}个号码")
        return result
    
    def predict_historical(self, target_date: str, window_size: int = 30) -> Dict[str, Any]:
        """历史预测方法（用于回测）"""
        # 获取目标日期之前的历史数据
        end_date = datetime.strptime(target_date, "%Y-%m-%d") - timedelta(days=1)
        end_date_str = end_date.strftime("%Y-%m-%d")
        
        return self.predict(end_date_str, window_size)
    
    def run_backtest(self, start_date: str, end_date: str, window_size: int = 30) -> List[Dict[str, Any]]:
        """运行回测"""
        print(f"🔄 {self.module_name}开始回测: {start_date} 到 {end_date}")
        
        test_dates = self._get_test_dates(start_date, end_date)
        results = []
        
        for test_date in test_dates:
            try:
                # 获取真实开奖号码
                actual_number = self._get_actual_number(test_date)
                if actual_number is None:
                    continue
                
                # 运行历史预测
                prediction = self.predict_historical(test_date, window_size)
                predicted_numbers = prediction["predicted_numbers"]
                
                # 计算命中情况
                hit = actual_number in predicted_numbers
                hit_rank = predicted_numbers.index(actual_number) + 1 if hit else None
                
                result = {
                    "test_date": test_date,
                    "actual_number": actual_number,
                    "predicted_numbers": predicted_numbers,
                    "hit": hit,
                    "hit_rank": hit_rank,
                    "confidence": prediction["confidence"],
                    "analysis_details": prediction["analysis_details"]
                }
                
                results.append(result)
                print(f"  {test_date}: 实际={actual_number}, 命中={'✅' if hit else '❌'}")
                
            except Exception as e:
                print(f"  {test_date}: 回测失败 - {e}")
        
        # 计算回测统计
        hit_count = sum(1 for r in results if r["hit"])
        hit_rate = hit_count / len(results) if results else 0
        
        print(f"✅ {self.module_name}回测完成: {hit_count}/{len(results)} = {hit_rate:.1%}")
        
        return {
            "results": results,
            "statistics": {
                "total_tests": len(results),
                "hit_count": hit_count,
                "hit_rate": hit_rate,
                "avg_confidence": np.mean([r["confidence"] for r in results]) if results else 0
            }
        }
    
    def _get_historical_data(self, before_date: str, days: int) -> List[Dict[str, Any]]:
        """获取历史数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT draw_date, special_number, period_number
            FROM lottery_results 
            WHERE draw_date < ? 
            ORDER BY draw_date DESC 
            LIMIT ?
        ''', (before_date, days))
        
        data = []
        for row in cursor.fetchall():
            data.append({
                "draw_date": row[0],
                "special_number": row[1],
                "period_number": row[2]
            })
        
        conn.close()
        return data
    
    def _analyze_hot_cold_numbers(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """冷热号码分析"""
        window = self.analysis_config["hot_cold_window"]
        recent_data = historical_data[:window]
        
        # 统计频率
        number_counts = Counter(record["special_number"] for record in recent_data)
        
        # 计算平均频率
        avg_frequency = len(recent_data) / 49 if recent_data else 0
        
        hot_numbers = []
        cold_numbers = []
        
        for number in range(1, 50):
            count = number_counts.get(number, 0)
            if count > avg_frequency * 1.2:
                hot_numbers.append(number)
            elif count < avg_frequency * 0.8:
                cold_numbers.append(number)
        
        return {
            "hot_numbers": sorted(hot_numbers),
            "cold_numbers": sorted(cold_numbers),
            "avg_frequency": avg_frequency,
            "analysis_window": window
        }
    
    def _analyze_trend_patterns(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """趋势模式分析"""
        window = self.analysis_config["trend_window"]
        recent_data = historical_data[:window]
        
        if len(recent_data) < 10:
            return {"rising_numbers": [], "falling_numbers": [], "stable_numbers": []}
        
        # 分析最近趋势
        first_half = recent_data[window//2:]
        second_half = recent_data[:window//2]
        
        first_counts = Counter(record["special_number"] for record in first_half)
        second_counts = Counter(record["special_number"] for record in second_half)
        
        rising_numbers = []
        falling_numbers = []
        stable_numbers = []
        
        for number in range(1, 50):
            first_freq = first_counts.get(number, 0)
            second_freq = second_counts.get(number, 0)
            
            if second_freq > first_freq * 1.5:
                rising_numbers.append(number)
            elif second_freq < first_freq * 0.5:
                falling_numbers.append(number)
            else:
                stable_numbers.append(number)
        
        return {
            "rising_numbers": sorted(rising_numbers),
            "falling_numbers": sorted(falling_numbers),
            "stable_numbers": sorted(stable_numbers),
            "analysis_window": window
        }
    
    def _analyze_omission_patterns(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """遗漏模式分析"""
        # 计算每个号码的遗漏期数
        omission_counts = {}
        
        for number in range(1, 50):
            omission_counts[number] = 0
            
            for record in historical_data:
                if record["special_number"] == number:
                    break
                omission_counts[number] += 1
        
        # 找出高遗漏号码（可能反弹）
        threshold = self.analysis_config["omission_threshold"]
        high_omission = [num for num, count in omission_counts.items() if count >= threshold]
        
        return {
            "omission_counts": omission_counts,
            "high_omission_numbers": sorted(high_omission),
            "omission_threshold": threshold
        }
    
    def _analyze_frequency_patterns(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """频率模式分析"""
        total_count = len(historical_data)
        number_counts = Counter(record["special_number"] for record in historical_data)
        
        # 计算频率
        frequencies = {}
        for number in range(1, 50):
            frequencies[number] = number_counts.get(number, 0) / total_count if total_count > 0 else 0
        
        # 按频率排序
        sorted_by_freq = sorted(frequencies.items(), key=lambda x: x[1], reverse=True)
        
        return {
            "frequencies": frequencies,
            "top_frequent": [num for num, freq in sorted_by_freq[:10]],
            "low_frequent": [num for num, freq in sorted_by_freq[-10:]],
            "total_periods": total_count
        }
    
    def _calculate_comprehensive_scores(self, hot_cold: Dict, trend: Dict, 
                                      omission: Dict, frequency: Dict) -> Dict[int, float]:
        """计算综合评分"""
        scores = {}
        
        for number in range(1, 50):
            score = 0.0
            
            # 频率得分
            freq_score = frequency["frequencies"].get(number, 0) * 100
            score += freq_score * self.analysis_config["frequency_weight"]
            
            # 趋势得分
            if number in trend["rising_numbers"]:
                trend_score = 80
            elif number in trend["stable_numbers"]:
                trend_score = 50
            else:
                trend_score = 20
            score += trend_score * self.analysis_config["trend_weight"]
            
            # 遗漏得分（高遗漏号码得分较高）
            omission_count = omission["omission_counts"].get(number, 0)
            omission_score = min(100, omission_count * 5)  # 遗漏越久得分越高
            score += omission_score * self.analysis_config["omission_weight"]
            
            scores[number] = score
        
        return scores
    
    def _select_recommended_numbers(self, number_scores: Dict[int, float]) -> List[int]:
        """选择推荐号码"""
        # 按得分排序
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 选择前16个号码
        recommended = [number for number, score in sorted_numbers[:16]]
        
        return sorted(recommended)
    
    def _calculate_confidence(self, number_scores: Dict[int, float], 
                            recommended_numbers: List[int]) -> float:
        """计算置信度"""
        if not recommended_numbers:
            return 0.0
        
        # 推荐号码的平均得分
        recommended_scores = [number_scores[num] for num in recommended_numbers]
        avg_recommended_score = np.mean(recommended_scores)
        
        # 所有号码的平均得分
        all_scores = list(number_scores.values())
        avg_all_score = np.mean(all_scores)
        
        # 置信度基于得分差异
        if avg_all_score > 0:
            confidence = min(0.95, max(0.3, avg_recommended_score / avg_all_score / 2))
        else:
            confidence = 0.5
        
        return confidence
    
    def _get_test_dates(self, start_date: str, end_date: str) -> List[str]:
        """获取测试日期"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT draw_date FROM lottery_results 
            WHERE draw_date BETWEEN ? AND ? 
            ORDER BY draw_date
        ''', (start_date, end_date))
        
        dates = [row[0] for row in cursor.fetchall()]
        conn.close()
        return dates
    
    def _get_actual_number(self, test_date: str) -> Optional[int]:
        """获取真实开奖号码"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT special_number FROM lottery_results 
            WHERE draw_date = ?
        ''', (test_date,))
        
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None
    
    def _get_default_result(self) -> Dict[str, Any]:
        """获取默认结果"""
        return {
            "predicted_numbers": list(range(1, 17)),
            "confidence": 0.3,
            "analysis_details": {},
            "number_scores": {i: 50.0 for i in range(1, 50)},
            "metadata": {"note": "数据不足，使用默认预测"}
        }
