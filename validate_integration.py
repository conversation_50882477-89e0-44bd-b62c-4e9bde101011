"""
验证增强回测配置集成效果
"""
import sys
import os
sys.path.append('src')

def validate_enhanced_integration():
    """验证增强回测配置集成效果"""
    print("🧪 验证增强回测配置集成效果")
    print("=" * 50)
    
    try:
        from perfect_prediction_system import PerfectPredictionSystem
        
        # 创建系统实例
        system = PerfectPredictionSystem()
        print("✅ 完美预测系统创建成功")
        
        # 验证配置加载
        print(f"\n📋 验证配置加载功能:")
        config_loaded = system.load_optimal_config()
        if config_loaded:
            print(f"   ✅ 最优配置加载成功")
            print(f"   📊 配置源: {system.config_source}")
            print(f"   🔧 配置状态: {system.enhanced_config_loaded}")
        else:
            print(f"   ❌ 最优配置加载失败")
        
        # 验证配置应用
        print(f"\n🔧 验证配置应用功能:")
        if system.optimal_config:
            config_applied = system.apply_enhanced_config()
            if config_applied:
                print(f"   ✅ 增强配置应用成功")
                
                # 显示应用的配置
                if 'fusion_weights' in system.optimal_config:
                    weights = system.optimal_config['fusion_weights']
                    print(f"   📊 融合权重: {weights}")
                
                if 'filter_thresholds' in system.optimal_config:
                    thresholds = system.optimal_config['filter_thresholds']
                    print(f"   🔍 筛选阈值: {thresholds}")
                
                if 'confidence_threshold' in system.optimal_config:
                    threshold = system.optimal_config['confidence_threshold']
                    print(f"   📈 置信度阈值: {threshold}")
            else:
                print(f"   ❌ 增强配置应用失败")
        else:
            print(f"   ⚠️ 无最优配置可应用")
        
        # 验证配置信息获取
        print(f"\n📊 验证配置信息获取:")
        config_info = system.get_config_info()
        for key, value in config_info.items():
            print(f"   {key}: {value}")
        
        # 初始化系统
        print(f"\n🚀 验证系统初始化:")
        system.initialize_modules()
        print(f"   ✅ 系统初始化完成")
        print(f"   🔧 系统就绪状态: {system.system_ready}")
        
        # 运行预测并验证配置集成
        print(f"\n🎯 验证预测功能集成:")
        target_date = "2025-06-23"
        result = system.run_complete_prediction(target_date)
        
        print(f"   ✅ 预测执行成功")
        print(f"   🆔 预测ID: {result['prediction_id']}")
        
        # 检查结果中的配置信息
        if 'config_info' in result:
            config_info = result['config_info']
            print(f"   📊 配置信息已包含在结果中:")
            print(f"      配置源: {config_info.get('config_source', 'unknown')}")
            print(f"      增强配置: {config_info.get('enhanced_config_loaded', False)}")
            print(f"      最优配置可用: {config_info.get('optimal_config_available', False)}")
        
        if 'enhanced_backtest_applied' in result:
            print(f"   🔧 增强回测应用状态: {result['enhanced_backtest_applied']}")
        
        if 'optimization_source' in result:
            print(f"   🎯 优化源: {result['optimization_source']}")
        
        # 分析预测质量改进
        print(f"\n📈 分析预测质量:")
        final_results = result.get('final_results', {})
        if final_results:
            confidence = final_results.get('overall_confidence', 0)
            stability = final_results.get('prediction_stability', 0)
            diversity = final_results.get('diversity_score', 0)
            
            print(f"   📊 整体置信度: {confidence}")
            print(f"   🔄 预测稳定性: {stability}")
            print(f"   🎲 多样性得分: {diversity}")
            
            # 评估改进效果
            if system.enhanced_config_loaded:
                expected_confidence = system.optimal_config.get('confidence_threshold', 0.7)
                if confidence >= expected_confidence * 0.8:  # 允许20%的偏差
                    print(f"   ✅ 置信度达到预期水平 (期望: {expected_confidence})")
                else:
                    print(f"   ⚠️ 置信度低于预期 (期望: {expected_confidence}, 实际: {confidence})")
            
            # 性能对比
            if system.backtest_performance:
                backtest_hit_rate = system.backtest_performance.get('hit_rate', 0)
                print(f"   📊 回测命中率: {backtest_hit_rate}")
                print(f"   📈 预期性能提升: {(backtest_hit_rate - 0.5) * 100:.1f}%")
        
        return result
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def performance_comparison_test():
    """性能对比测试"""
    print(f"\n🔬 性能对比测试")
    print("=" * 30)
    
    try:
        from perfect_prediction_system import PerfectPredictionSystem
        
        # 测试1: 使用默认配置
        print(f"📊 测试1: 默认配置预测")
        system_default = PerfectPredictionSystem()
        
        # 删除配置文件进行默认配置测试
        import os
        config_files = ['optimal_config.json', 'enhanced_backtest_config.json']
        backup_configs = {}
        
        for config_file in config_files:
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    backup_configs[config_file] = f.read()
                os.rename(config_file, f"{config_file}.backup")
        
        system_default.initialize_modules()
        result_default = system_default.run_complete_prediction("2025-06-23")
        
        confidence_default = result_default['final_results']['overall_confidence']
        print(f"   默认配置置信度: {confidence_default}")
        
        # 恢复配置文件
        for config_file, content in backup_configs.items():
            with open(config_file, 'w') as f:
                f.write(content)
            if os.path.exists(f"{config_file}.backup"):
                os.remove(f"{config_file}.backup")
        
        # 测试2: 使用增强配置
        print(f"\n📊 测试2: 增强配置预测")
        system_enhanced = PerfectPredictionSystem()
        system_enhanced.initialize_modules()
        result_enhanced = system_enhanced.run_complete_prediction("2025-06-23")
        
        confidence_enhanced = result_enhanced['final_results']['overall_confidence']
        print(f"   增强配置置信度: {confidence_enhanced}")
        
        # 性能对比
        print(f"\n📈 性能对比结果:")
        improvement = confidence_enhanced - confidence_default
        improvement_percent = (improvement / confidence_default) * 100 if confidence_default > 0 else 0
        
        print(f"   置信度提升: {improvement:.3f} ({improvement_percent:+.1f}%)")
        
        if improvement > 0:
            print(f"   ✅ 增强配置显示性能改进")
        elif improvement == 0:
            print(f"   ⚠️ 增强配置无明显改进")
        else:
            print(f"   ❌ 增强配置性能下降")
        
        return {
            'default_confidence': confidence_default,
            'enhanced_confidence': confidence_enhanced,
            'improvement': improvement,
            'improvement_percent': improvement_percent
        }
        
    except Exception as e:
        print(f"❌ 性能对比测试失败: {e}")
        return None

def generate_integration_report(validation_result, performance_comparison):
    """生成集成报告"""
    print(f"\n📋 生成集成效果报告")
    print("=" * 30)
    
    report = {
        "integration_report": {
            "timestamp": __import__('datetime').datetime.now().isoformat(),
            "validation_status": "success" if validation_result else "failed",
            "configuration_integration": {
                "config_loading": validation_result is not None,
                "config_application": validation_result is not None,
                "system_initialization": validation_result is not None
            }
        }
    }
    
    if validation_result:
        config_info = validation_result.get('config_info', {})
        report["integration_report"]["configuration_details"] = {
            "config_source": config_info.get('config_source', 'unknown'),
            "enhanced_config_loaded": config_info.get('enhanced_config_loaded', False),
            "optimal_config_available": config_info.get('optimal_config_available', False)
        }
        
        final_results = validation_result.get('final_results', {})
        report["integration_report"]["prediction_quality"] = {
            "overall_confidence": final_results.get('overall_confidence', 0),
            "prediction_stability": final_results.get('prediction_stability', 0),
            "diversity_score": final_results.get('diversity_score', 0)
        }
    
    if performance_comparison:
        report["integration_report"]["performance_comparison"] = performance_comparison
    
    # 保存报告
    import json
    with open("integration_validation_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 集成报告已保存: integration_validation_report.json")
    return report

if __name__ == "__main__":
    # 执行验证
    validation_result = validate_enhanced_integration()
    
    # 执行性能对比
    performance_comparison = performance_comparison_test()
    
    # 生成报告
    report = generate_integration_report(validation_result, performance_comparison)
    
    print(f"\n🎊 增强回测配置集成验证完成!")
    print(f"📁 详细报告: integration_validation_report.json")
