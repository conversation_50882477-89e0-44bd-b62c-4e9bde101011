#!/usr/bin/env python3
"""
后台功能模块全面互动测试
系统性测试所有功能模块的交互和性能
"""

import sys
import time
import json
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

class BackendModuleInteractionTester:
    """后台模块互动测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.interaction_log = []
        self.performance_metrics = {}
        
    def log_interaction(self, module: str, action: str, result: Any, duration: float):
        """记录交互日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "module": module,
            "action": action,
            "success": result is not None and result != False,
            "duration": duration,
            "result_summary": str(result)[:100] if result else "None"
        }
        self.interaction_log.append(log_entry)
        
    def test_data_management_interaction(self):
        """测试数据管理模块交互"""
        print("📊 测试数据管理模块交互")
        print("-" * 50)
        
        results = {}
        
        try:
            start_time = time.time()
            
            # 测试数据库连接和查询
            import sqlite3
            conn = sqlite3.connect("data/lottery.db")
            cursor = conn.cursor()
            
            # 测试1: 数据统计查询
            cursor.execute("SELECT COUNT(*) FROM lottery_results")
            total_records = cursor.fetchone()[0]
            results["total_records"] = total_records
            
            # 测试2: 最新数据查询
            cursor.execute("SELECT * FROM lottery_results ORDER BY draw_date DESC LIMIT 5")
            latest_records = cursor.fetchall()
            results["latest_records_count"] = len(latest_records)
            
            # 测试3: 日期范围查询
            cursor.execute("SELECT COUNT(*) FROM lottery_results WHERE draw_date >= '2025-01-01'")
            recent_records = cursor.fetchone()[0]
            results["recent_records"] = recent_records
            
            # 测试4: 特码统计
            cursor.execute("SELECT special_number, COUNT(*) as count FROM lottery_results GROUP BY special_number ORDER BY count DESC LIMIT 10")
            hot_numbers = cursor.fetchall()
            results["hot_numbers"] = hot_numbers
            
            conn.close()
            
            duration = time.time() - start_time
            self.log_interaction("data_management", "database_queries", results, duration)
            
            print(f"✅ 数据管理交互测试成功")
            print(f"  总记录数: {total_records}")
            print(f"  最新记录: {len(latest_records)}条")
            print(f"  近期记录: {recent_records}条")
            print(f"  热门号码: {len(hot_numbers)}个")
            print(f"  执行时间: {duration:.3f}秒")
            
            return True, results
            
        except Exception as e:
            print(f"❌ 数据管理交互测试失败: {e}")
            return False, {"error": str(e)}
    
    def test_prediction_modules_interaction(self):
        """测试预测模块交互"""
        print("\n🎯 测试预测模块交互")
        print("-" * 50)
        
        results = {}
        target_date = "2025-06-25"
        
        # 测试特码预测系统
        try:
            start_time = time.time()
            from src.special_number_predictor import SpecialNumberPredictor
            
            predictor = SpecialNumberPredictor()
            result = predictor.predict(target_date)
            
            duration = time.time() - start_time
            self.log_interaction("special_number_predictor", "predict", result, duration)
            
            if result and "predicted_numbers" in result:
                results["special_number"] = {
                    "success": True,
                    "numbers_count": len(result["predicted_numbers"]),
                    "confidence": result.get("confidence", 0),
                    "duration": duration
                }
                print(f"✅ 特码预测系统: {len(result['predicted_numbers'])}个号码 ({duration:.3f}秒)")
            else:
                results["special_number"] = {"success": False, "error": "结果格式异常"}
                print(f"❌ 特码预测系统: 结果格式异常")
                
        except Exception as e:
            results["special_number"] = {"success": False, "error": str(e)}
            print(f"❌ 特码预测系统: {e}")
        
        # 测试一致性预测系统
        try:
            start_time = time.time()
            from src.consistency_predictor import ConsistencyPredictor
            
            predictor = ConsistencyPredictor()
            result = predictor.predict_with_consistency(target_date)
            
            duration = time.time() - start_time
            self.log_interaction("consistency_predictor", "predict_with_consistency", result, duration)
            
            if result and "recommended_numbers" in result:
                results["consistency"] = {
                    "success": True,
                    "numbers_count": len(result["recommended_numbers"]),
                    "confidence": result.get("confidence", 0),
                    "duration": duration
                }
                print(f"✅ 一致性预测系统: {len(result['recommended_numbers'])}个号码 ({duration:.3f}秒)")
            else:
                results["consistency"] = {"success": False, "error": "结果格式异常"}
                print(f"❌ 一致性预测系统: 结果格式异常")
                
        except Exception as e:
            results["consistency"] = {"success": False, "error": str(e)}
            print(f"❌ 一致性预测系统: {e}")
        
        # 测试完美预测系统
        try:
            start_time = time.time()
            from src.perfect_prediction_system import PerfectPredictionSystem
            
            system = PerfectPredictionSystem()
            system.initialize_modules()
            result = system.run_complete_prediction(target_date)
            
            duration = time.time() - start_time
            self.log_interaction("perfect_prediction_system", "run_complete_prediction", result, duration)
            
            if result and "final_results" in result:
                final_results = result["final_results"]
                numbers = final_results.get("recommended_16_numbers", [])
                zodiacs = final_results.get("recommended_4_zodiacs", [])
                
                results["perfect_prediction"] = {
                    "success": True,
                    "numbers_count": len(numbers),
                    "zodiacs_count": len(zodiacs),
                    "confidence": final_results.get("overall_confidence", 0),
                    "stability": final_results.get("prediction_stability", 0),
                    "duration": duration
                }
                print(f"✅ 完美预测系统: {len(numbers)}个号码, {len(zodiacs)}个生肖 ({duration:.3f}秒)")
            else:
                results["perfect_prediction"] = {"success": False, "error": "结果格式异常"}
                print(f"❌ 完美预测系统: 结果格式异常")
                
        except Exception as e:
            results["perfect_prediction"] = {"success": False, "error": str(e)}
            print(f"❌ 完美预测系统: {e}")
        
        return results
    
    def test_analysis_modules_interaction(self):
        """测试分析模块交互"""
        print("\n📈 测试分析模块交互")
        print("-" * 50)
        
        results = {}
        target_date = "2025-06-25"
        
        # 测试机器学习模块
        try:
            start_time = time.time()
            from src.independent_modules.ml_module import MachineLearningModule
            
            module = MachineLearningModule()
            result = module.predict(target_date)
            
            duration = time.time() - start_time
            self.log_interaction("ml_module", "predict", result, duration)
            
            if result and "predicted_numbers" in result:
                algorithm_info = result.get("algorithm_info", {})
                results["machine_learning"] = {
                    "success": True,
                    "numbers_count": len(result["predicted_numbers"]),
                    "confidence": result.get("confidence", 0),
                    "version": algorithm_info.get("version", "unknown"),
                    "xgboost_enabled": algorithm_info.get("xgboost_enabled", False),
                    "models_count": algorithm_info.get("total_models", 0),
                    "duration": duration
                }
                print(f"✅ 机器学习模块: {len(result['predicted_numbers'])}个号码, v{algorithm_info.get('version', '1.0')} ({duration:.3f}秒)")
            else:
                results["machine_learning"] = {"success": False, "error": "结果格式异常"}
                print(f"❌ 机器学习模块: 结果格式异常")
                
        except Exception as e:
            results["machine_learning"] = {"success": False, "error": str(e)}
            print(f"❌ 机器学习模块: {e}")
        
        # 测试生肖扩展模块
        try:
            start_time = time.time()
            from src.independent_modules.zodiac_extended_module import ZodiacExtendedModule
            
            module = ZodiacExtendedModule()
            result = module.predict(target_date)
            
            duration = time.time() - start_time
            self.log_interaction("zodiac_extended_module", "predict", result, duration)
            
            if result and "predicted_numbers" in result:
                results["zodiac_extended"] = {
                    "success": True,
                    "numbers_count": len(result["predicted_numbers"]),
                    "confidence": result.get("confidence", 0),
                    "duration": duration
                }
                print(f"✅ 生肖扩展模块: {len(result['predicted_numbers'])}个号码 ({duration:.3f}秒)")
            else:
                results["zodiac_extended"] = {"success": False, "error": "结果格式异常"}
                print(f"❌ 生肖扩展模块: 结果格式异常")
                
        except Exception as e:
            results["zodiac_extended"] = {"success": False, "error": str(e)}
            print(f"❌ 生肖扩展模块: {e}")
        
        # 测试特殊生肖模块
        try:
            start_time = time.time()
            from src.independent_modules.special_zodiac_module import SpecialZodiacModule
            
            module = SpecialZodiacModule()
            result = module.predict(target_date)
            
            duration = time.time() - start_time
            self.log_interaction("special_zodiac_module", "predict", result, duration)
            
            if result and "predicted_numbers" in result:
                results["special_zodiac"] = {
                    "success": True,
                    "numbers_count": len(result["predicted_numbers"]),
                    "confidence": result.get("confidence", 0),
                    "duration": duration
                }
                print(f"✅ 特殊生肖模块: {len(result['predicted_numbers'])}个号码 ({duration:.3f}秒)")
            else:
                results["special_zodiac"] = {"success": False, "error": "结果格式异常"}
                print(f"❌ 特殊生肖模块: 结果格式异常")
                
        except Exception as e:
            results["special_zodiac"] = {"success": False, "error": str(e)}
            print(f"❌ 特殊生肖模块: {e}")
        
        return results
    
    def test_fusion_optimization_interaction(self):
        """测试融合优化模块交互"""
        print("\n🔀 测试融合优化模块交互")
        print("-" * 50)
        
        results = {}
        target_date = "2025-06-25"
        
        # 测试动态融合管理器
        try:
            start_time = time.time()
            from src.dynamic_fusion_manager_v3 import DynamicFusionManager
            
            manager = DynamicFusionManager()
            
            # 模拟模块预测数据
            module_predictions = {
                "traditional_analysis": {
                    "numbers": [1, 5, 12, 18, 23, 28, 33, 38, 42, 47, 2, 8, 15, 22, 29, 35],
                    "confidence": 0.75
                },
                "machine_learning": {
                    "numbers": [3, 7, 14, 19, 25, 31, 36, 41, 45, 49, 6, 11, 17, 24, 30, 37],
                    "confidence": 0.85
                },
                "zodiac_extended": {
                    "numbers": [2, 9, 16, 21, 26, 32, 39, 44, 48, 4, 10, 13, 20, 27, 34, 40],
                    "confidence": 0.70
                }
            }
            
            result = manager.fuse_predictions(module_predictions, target_date)
            
            duration = time.time() - start_time
            self.log_interaction("dynamic_fusion_manager", "fuse_predictions", result, duration)
            
            if result and "numbers" in result:
                results["dynamic_fusion"] = {
                    "success": True,
                    "numbers_count": len(result["numbers"]),
                    "confidence": result.get("confidence", 0),
                    "stability_score": result.get("stability_score", 0),
                    "method": result.get("method", "unknown"),
                    "duration": duration
                }
                print(f"✅ 动态融合管理器: {len(result['numbers'])}个号码, 稳定性{result.get('stability_score', 0):.1%} ({duration:.3f}秒)")
            else:
                results["dynamic_fusion"] = {"success": False, "error": "结果格式异常"}
                print(f"❌ 动态融合管理器: 结果格式异常")
                
        except Exception as e:
            results["dynamic_fusion"] = {"success": False, "error": str(e)}
            print(f"❌ 动态融合管理器: {e}")
        
        # 测试稳定性优化器
        try:
            start_time = time.time()
            from src.stability_optimizer_v3 import StabilityOptimizer
            
            optimizer = StabilityOptimizer()
            
            prediction_result = {
                "numbers": [5, 12, 18, 23, 28, 33, 38, 42, 47, 3, 9, 15, 21, 29, 35, 41],
                "confidence": 0.75
            }
            
            result = optimizer.optimize_stability(prediction_result, target_date)
            
            duration = time.time() - start_time
            self.log_interaction("stability_optimizer", "optimize_stability", result, duration)
            
            if result and "numbers" in result:
                results["stability_optimizer"] = {
                    "success": True,
                    "numbers_count": len(result["numbers"]),
                    "stability_score": result.get("stability_score", 0),
                    "final_stability_score": result.get("final_stability_score", 0),
                    "method": result.get("optimization_method", "unknown"),
                    "duration": duration
                }
                print(f"✅ 稳定性优化器: {len(result['numbers'])}个号码, 稳定性{result.get('stability_score', 0):.1%} ({duration:.3f}秒)")
            else:
                results["stability_optimizer"] = {"success": False, "error": "结果格式异常"}
                print(f"❌ 稳定性优化器: 结果格式异常")
                
        except Exception as e:
            results["stability_optimizer"] = {"success": False, "error": str(e)}
            print(f"❌ 稳定性优化器: {e}")
        
        # 测试增强特征工程
        try:
            start_time = time.time()
            from src.enhanced_feature_engineering_v2 import EnhancedFeatureEngineering
            
            engine = EnhancedFeatureEngineering()
            
            # 模拟历史数据
            historical_data = []
            import numpy as np
            for i in range(100):
                historical_data.append({
                    'draw_date': (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d'),
                    'special_number': np.random.randint(1, 50),
                    'period_number': f'2024{100-i:03d}'
                })
            
            result = engine.generate_enhanced_features(historical_data, target_date)
            
            duration = time.time() - start_time
            self.log_interaction("enhanced_feature_engineering", "generate_enhanced_features", result, duration)
            
            if result and "features" in result:
                results["enhanced_features"] = {
                    "success": True,
                    "feature_count": result.get("feature_count", 0),
                    "version": result.get("generation_info", {}).get("version", "unknown"),
                    "data_samples": result.get("generation_info", {}).get("data_samples", 0),
                    "duration": duration
                }
                print(f"✅ 增强特征工程: {result.get('feature_count', 0)}个特征 ({duration:.3f}秒)")
            else:
                results["enhanced_features"] = {"success": False, "error": "结果格式异常"}
                print(f"❌ 增强特征工程: 结果格式异常")
                
        except Exception as e:
            results["enhanced_features"] = {"success": False, "error": str(e)}
            print(f"❌ 增强特征工程: {e}")
        
        return results
    
    def test_cross_module_interaction(self):
        """测试跨模块交互"""
        print("\n🔗 测试跨模块交互")
        print("-" * 50)
        
        results = {}
        target_date = "2025-06-25"
        
        try:
            start_time = time.time()
            
            # 步骤1: 获取各模块预测
            print("  步骤1: 获取各模块预测...")
            
            # ML模块预测
            from src.independent_modules.ml_module import MachineLearningModule
            ml_module = MachineLearningModule()
            ml_result = ml_module.predict(target_date)
            
            # 生肖模块预测
            from src.independent_modules.zodiac_extended_module import ZodiacExtendedModule
            zodiac_module = ZodiacExtendedModule()
            zodiac_result = zodiac_module.predict(target_date)
            
            # 步骤2: 融合预测结果
            print("  步骤2: 融合预测结果...")
            
            from src.dynamic_fusion_manager_v3 import DynamicFusionManager
            fusion_manager = DynamicFusionManager()
            
            module_predictions = {}
            if ml_result and "predicted_numbers" in ml_result:
                module_predictions["machine_learning"] = {
                    "numbers": ml_result["predicted_numbers"],
                    "confidence": ml_result.get("confidence", 0.8)
                }
            
            if zodiac_result and "predicted_numbers" in zodiac_result:
                module_predictions["zodiac_extended"] = {
                    "numbers": zodiac_result["predicted_numbers"],
                    "confidence": zodiac_result.get("confidence", 0.7)
                }
            
            fusion_result = fusion_manager.fuse_predictions(module_predictions, target_date)
            
            # 步骤3: 稳定性优化
            print("  步骤3: 稳定性优化...")
            
            from src.stability_optimizer_v3 import StabilityOptimizer
            optimizer = StabilityOptimizer()
            
            final_result = optimizer.optimize_stability(fusion_result, target_date)
            
            duration = time.time() - start_time
            self.log_interaction("cross_module", "full_pipeline", final_result, duration)
            
            results["cross_module_interaction"] = {
                "success": True,
                "pipeline_steps": 3,
                "modules_involved": len(module_predictions),
                "final_numbers": len(final_result.get("numbers", [])),
                "final_stability": final_result.get("stability_score", 0),
                "total_duration": duration
            }
            
            print(f"✅ 跨模块交互测试成功")
            print(f"  涉及模块: {len(module_predictions)}个")
            print(f"  最终号码: {len(final_result.get('numbers', []))}个")
            print(f"  最终稳定性: {final_result.get('stability_score', 0):.1%}")
            print(f"  总执行时间: {duration:.3f}秒")
            
        except Exception as e:
            results["cross_module_interaction"] = {"success": False, "error": str(e)}
            print(f"❌ 跨模块交互测试失败: {e}")
        
        return results
    
    def generate_interaction_report(self):
        """生成交互测试报告"""
        print("\n" + "=" * 60)
        print("📊 后台功能模块交互测试报告")
        print("=" * 60)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 统计交互日志
        total_interactions = len(self.interaction_log)
        successful_interactions = sum(1 for log in self.interaction_log if log["success"])
        total_duration = sum(log["duration"] for log in self.interaction_log)
        
        print(f"📊 交互统计:")
        print(f"  总交互次数: {total_interactions}")
        print(f"  成功交互: {successful_interactions}")
        print(f"  成功率: {successful_interactions/total_interactions:.1%}" if total_interactions > 0 else "  成功率: 0%")
        print(f"  总执行时间: {total_duration:.3f}秒")
        print(f"  平均响应时间: {total_duration/total_interactions:.3f}秒" if total_interactions > 0 else "  平均响应时间: 0秒")
        print()
        
        # 模块性能分析
        module_stats = {}
        for log in self.interaction_log:
            module = log["module"]
            if module not in module_stats:
                module_stats[module] = {"count": 0, "success": 0, "total_duration": 0}
            
            module_stats[module]["count"] += 1
            if log["success"]:
                module_stats[module]["success"] += 1
            module_stats[module]["total_duration"] += log["duration"]
        
        print("📈 模块性能分析:")
        for module, stats in module_stats.items():
            success_rate = stats["success"] / stats["count"] if stats["count"] > 0 else 0
            avg_duration = stats["total_duration"] / stats["count"] if stats["count"] > 0 else 0
            print(f"  {module}:")
            print(f"    交互次数: {stats['count']}")
            print(f"    成功率: {success_rate:.1%}")
            print(f"    平均响应时间: {avg_duration:.3f}秒")
        
        print()
        
        # 系统健康度评估
        overall_health = successful_interactions / total_interactions if total_interactions > 0 else 0
        
        if overall_health >= 0.9:
            health_status = "🎊 优秀"
            health_desc = "所有模块运行正常，系统健康度极佳"
        elif overall_health >= 0.8:
            health_status = "👍 良好"
            health_desc = "大部分模块运行正常，系统健康度良好"
        elif overall_health >= 0.6:
            health_status = "⚠️ 一般"
            health_desc = "部分模块存在问题，需要关注"
        else:
            health_status = "❌ 较差"
            health_desc = "多个模块存在问题，需要修复"
        
        print(f"🏥 系统健康度: {health_status}")
        print(f"   {health_desc}")
        print(f"   健康度评分: {overall_health:.1%}")
        print()
        
        # 性能建议
        print("💡 性能建议:")
        
        slow_modules = [module for module, stats in module_stats.items() 
                       if stats["total_duration"] / stats["count"] > 2.0]
        
        if slow_modules:
            print(f"  • 以下模块响应较慢，建议优化: {', '.join(slow_modules)}")
        
        failed_modules = [module for module, stats in module_stats.items() 
                         if stats["success"] / stats["count"] < 0.8]
        
        if failed_modules:
            print(f"  • 以下模块成功率较低，建议检查: {', '.join(failed_modules)}")
        
        if not slow_modules and not failed_modules:
            print("  • 所有模块性能良好，无需特别优化")
        
        print()
        print("🎯 总结:")
        print("  后台功能模块交互测试完成")
        print(f"  系统整体运行{health_status.split()[1]}")
        print("  所有核心功能模块已验证")

def main():
    """主测试函数"""
    print("🎊 后台功能模块全面互动测试开始")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tester = BackendModuleInteractionTester()
    
    try:
        # 执行各项交互测试
        
        # 数据管理模块交互
        data_success, data_results = tester.test_data_management_interaction()
        tester.test_results["data_management"] = data_results
        
        # 预测模块交互
        prediction_results = tester.test_prediction_modules_interaction()
        tester.test_results["prediction_modules"] = prediction_results
        
        # 分析模块交互
        analysis_results = tester.test_analysis_modules_interaction()
        tester.test_results["analysis_modules"] = analysis_results
        
        # 融合优化模块交互
        fusion_results = tester.test_fusion_optimization_interaction()
        tester.test_results["fusion_optimization"] = fusion_results
        
        # 跨模块交互
        cross_results = tester.test_cross_module_interaction()
        tester.test_results["cross_module"] = cross_results
        
        # 生成交互报告
        tester.generate_interaction_report()
        
        # 保存测试结果
        with open("backend_interaction_test_results.json", "w", encoding="utf-8") as f:
            json.dump({
                "test_results": tester.test_results,
                "interaction_log": tester.interaction_log,
                "test_time": datetime.now().isoformat()
            }, f, ensure_ascii=False, indent=2)
        
        print("📄 测试结果已保存到: backend_interaction_test_results.json")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
