import sys
import os
sys.path.append('src')

def check_enhanced_backtest_integration():
    """检查完美预测是否集成了增强回测的最优配置"""
    print("🔍 检查完美预测与增强回测的集成")
    print("=" * 50)
    
    # 1. 检查完美预测系统源码中的增强回测相关内容
    try:
        with open("src/perfect_prediction_system.py", "r", encoding="utf-8") as f:
            perfect_content = f.read()
        
        print("📋 检查完美预测系统中的增强回测集成:")
        
        # 搜索增强回测相关关键词
        enhanced_keywords = [
            "enhanced_backtest", "增强回测", "optimal_config", "最优配置",
            "backtest_result", "optimal_pattern", "adaptive_config",
            "enhanced_config", "optimal_parameters"
        ]
        
        found_keywords = []
        for keyword in enhanced_keywords:
            if keyword in perfect_content:
                found_keywords.append(keyword)
                print(f"   ✅ 找到关键词: {keyword}")
        
        if not found_keywords:
            print(f"   ❌ 未找到增强回测相关关键词")
        
        # 2. 检查是否有配置加载机制
        print(f"\n🔧 检查配置加载机制:")
        config_patterns = [
            "load_optimal_config",
            "apply_enhanced_config", 
            "load_backtest_config",
            "get_optimal_parameters",
            "apply_optimal_pattern"
        ]
        
        found_config_methods = []
        for pattern in config_patterns:
            if pattern in perfect_content:
                found_config_methods.append(pattern)
                print(f"   ✅ 找到配置方法: {pattern}")
        
        if not found_config_methods:
            print(f"   ❌ 未找到配置加载方法")
        
        # 3. 检查初始化方法中是否加载了最优配置
        print(f"\n📊 检查初始化方法中的配置加载:")
        init_start = perfect_content.find("def initialize_modules")
        if init_start != -1:
            init_section = perfect_content[init_start:init_start+2000]
            
            if "optimal" in init_section or "enhanced" in init_section or "backtest" in init_section:
                print(f"   ✅ 初始化方法中包含优化配置相关代码")
                
                # 提取相关行
                lines = init_section.split('\n')
                for i, line in enumerate(lines):
                    if any(keyword in line.lower() for keyword in ['optimal', 'enhanced', 'backtest']):
                        print(f"      L{i+1}: {line.strip()}")
            else:
                print(f"   ❌ 初始化方法中未找到优化配置加载")
        
    except Exception as e:
        print(f"❌ 检查完美预测系统失败: {e}")
    
    # 4. 检查是否存在最优配置文件
    print(f"\n📁 检查最优配置文件:")
    config_files = [
        "optimal_config.json",
        "enhanced_backtest_config.json",
        "best_parameters.json",
        "optimal_pattern.json",
        "adaptive_config.json"
    ]
    
    found_config_files = []
    for config_file in config_files:
        if os.path.exists(config_file):
            found_config_files.append(config_file)
            print(f"   ✅ 找到配置文件: {config_file}")
            
            # 读取配置文件内容
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    import json
                    config_data = json.load(f)
                    print(f"      �� 配置项数量: {len(config_data)}")
                    print(f"      🔑 主要配置: {list(config_data.keys())[:5]}")
            except Exception as e:
                print(f"      ❌ 读取配置文件失败: {e}")
    
    if not found_config_files:
        print(f"   ❌ 未找到最优配置文件")
    
    # 5. 检查增强回测系统是否生成了最优配置
    print(f"\n🚀 检查增强回测系统的最优配置生成:")
    
    enhanced_files = [
        "optimal_pattern_selector.py",
        "adaptive_optimizer.py",
        "enhanced_backtest_results.json"
    ]
    
    for file in enhanced_files:
        if os.path.exists(file):
            print(f"   ✅ 找到增强回测文件: {file}")
            
            if file.endswith('.py'):
                # 检查Python文件中的配置生成方法
                with open(file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if "save_optimal_config" in content or "export_config" in content:
                    print(f"      ✅ 包含配置导出功能")
                else:
                    print(f"      ⚠️ 未找到配置导出功能")
        else:
            print(f"   ❌ 未找到文件: {file}")
    
    # 6. 测试完美预测系统是否能加载最优配置
    print(f"\n🧪 测试完美预测系统的配置加载:")
    
    try:
        from perfect_prediction_system import PerfectPredictionSystem
        
        system = PerfectPredictionSystem()
        
        # 检查系统是否有配置属性
        if hasattr(system, 'config'):
            print(f"   ✅ 系统有config属性")
            config = system.config
            
            # 检查配置中是否有优化相关项
            if isinstance(config, dict):
                optimal_keys = [key for key in config.keys() if 'optimal' in key.lower() or 'enhanced' in key.lower()]
                if optimal_keys:
                    print(f"   ✅ 配置中包含优化项: {optimal_keys}")
                else:
                    print(f"   ❌ 配置中未包含优化项")
            else:
                print(f"   ⚠️ 配置不是字典格式")
        else:
            print(f"   ❌ 系统没有config属性")
        
        # 检查是否有加载最优配置的方法
        config_methods = [
            'load_optimal_config',
            'apply_enhanced_config',
            'get_optimal_parameters'
        ]
        
        for method in config_methods:
            if hasattr(system, method):
                print(f"   ✅ 系统有方法: {method}")
            else:
                print(f"   ❌ 系统没有方法: {method}")
    
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")

if __name__ == "__main__":
    check_enhanced_backtest_integration()
