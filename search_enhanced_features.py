import os
import re

def search_statistical_features():
    """搜索统计学支持功能"""
    print("📈 搜索统计学支持和多次采样功能")
    print("=" * 50)
    
    # 搜索关键词
    keywords = [
        "多次采样", "sampling", "bootstrap", "cross_validation",
        "统计学", "statistical", "参数组合", "parameter", 
        "最优", "optimal", "训练", "train", "验证", "valid",
        "应用", "apply", "流程", "workflow"
    ]
    
    # 要搜索的文件
    files_to_search = [
        "historical_backtest.py",
        "test_enhanced_backtest.py", 
        "diagnose_enhanced_backtest.py",
        "test_enhanced_backtest_fix.py"
    ]
    
    for filename in files_to_search:
        if os.path.exists(filename):
            print(f"\n🔍 搜索文件: {filename}")
            
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            found_features = []
            
            for i, line in enumerate(lines):
                for keyword in keywords:
                    if keyword in line.lower():
                        found_features.append({
                            'line_num': i + 1,
                            'keyword': keyword,
                            'content': line.strip()
                        })
            
            if found_features:
                print(f"   ✅ 找到 {len(found_features)} 个相关特性:")
                
                # 按关键词分组显示
                keyword_groups = {}
                for feature in found_features:
                    keyword = feature['keyword']
                    if keyword not in keyword_groups:
                        keyword_groups[keyword] = []
                    keyword_groups[keyword].append(feature)
                
                for keyword, features in keyword_groups.items():
                    print(f"      📌 {keyword}: {len(features)} 处")
                    for feature in features[:2]:  # 只显示前2个
                        line_num = feature['line_num']
                        content = feature['content'][:60]
                        print(f"         L{line_num}: {content}...")
            else:
                print(f"   ❌ 未找到相关特性")
        else:
            print(f"\n❌ 文件不存在: {filename}")

def search_training_validation_workflow():
    """搜索训练-验证-应用流程"""
    print(f"\n🔄 搜索训练-验证-应用流程")
    print("=" * 40)
    
    # 搜索流程相关的模式
    workflow_patterns = [
        r"训练.*验证.*应用",
        r"train.*valid.*apply",
        r"training.*validation.*application",
        r"def.*train.*\(",
        r"def.*valid.*\(",
        r"def.*apply.*\("
    ]
    
    files_to_search = [
        "historical_backtest.py",
        "test_enhanced_backtest.py",
        "diagnose_enhanced_backtest.py"
    ]
    
    for filename in files_to_search:
        if os.path.exists(filename):
            print(f"\n🔍 检查文件: {filename}")
            
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            found_workflows = []
            
            for pattern in workflow_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    # 找到匹配的行号
                    line_num = content[:match.start()].count('\n') + 1
                    line_content = content.split('\n')[line_num - 1].strip()
                    
                    found_workflows.append({
                        'pattern': pattern,
                        'line_num': line_num,
                        'content': line_content
                    })
            
            if found_workflows:
                print(f"   ✅ 找到 {len(found_workflows)} 个流程模式:")
                for workflow in found_workflows[:5]:  # 显示前5个
                    line_num = workflow['line_num']
                    content = workflow['content'][:80]
                    print(f"      L{line_num}: {content}...")
            else:
                print(f"   ❌ 未找到训练-验证-应用流程")

def search_parameter_optimization():
    """搜索参数优化功能"""
    print(f"\n⚙️ 搜索参数优化功能")
    print("=" * 30)
    
    optimization_keywords = [
        "参数优化", "parameter optimization", "hyperparameter",
        "最优参数", "optimal parameter", "参数组合", "parameter combination",
        "网格搜索", "grid search", "随机搜索", "random search",
        "贝叶斯优化", "bayesian optimization"
    ]
    
    files_to_search = [
        "historical_backtest.py",
        "optimal_pattern_selector.py",
        "adaptive_optimizer.py"
    ]
    
    for filename in files_to_search:
        if os.path.exists(filename):
            print(f"\n🔍 检查文件: {filename}")
            
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            found_optimizations = []
            
            for keyword in optimization_keywords:
                if keyword in content.lower():
                    # 找到包含关键词的行
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if keyword in line.lower():
                            found_optimizations.append({
                                'keyword': keyword,
                                'line_num': i + 1,
                                'content': line.strip()
                            })
            
            if found_optimizations:
                print(f"   ✅ 找到 {len(found_optimizations)} 个优化特性:")
                for opt in found_optimizations[:3]:  # 显示前3个
                    line_num = opt['line_num']
                    content = opt['content'][:60]
                    print(f"      L{line_num}: {content}...")
            else:
                print(f"   ❌ 未找到参数优化功能")

if __name__ == "__main__":
    search_statistical_features()
    search_training_validation_workflow()
    search_parameter_optimization()
