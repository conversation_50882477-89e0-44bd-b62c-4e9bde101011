"""
历史回测与模型评估引擎主入口 - 统一管理所有评估功能
"""
import sys
sys.path.insert(0, 'src')

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import json
import os

# 导入各个子系统
from src.evaluation_layer.backtesting_framework.backtesting_engine import BacktestingEngine, BacktestingStrategy
from src.evaluation_layer.performance_metrics.performance_metrics import PerformanceMetrics
from src.evaluation_layer.fusion_validation.fusion_validator import FusionValidator
from src.evaluation_layer.visualization.backtest_visualizer import BacktestVisualizer
from src.evaluation_layer.statistical_tests.statistical_tester import StatisticalTester

class EvaluationEngine:
    """历史回测与模型评估引擎主控制器"""
    
    def __init__(self, 
                 output_dir: str = "evaluation_results",
                 alpha: float = 0.05):
        """
        初始化评估引擎
        
        Args:
            output_dir: 输出目录
            alpha: 统计检验显著性水平
        """
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        self.output_dir = output_dir
        
        # 初始化各个子系统
        self.backtesting_engine = BacktestingEngine()
        self.performance_metrics = PerformanceMetrics()
        self.fusion_validator = FusionValidator()
        self.visualizer = BacktestVisualizer()
        self.statistical_tester = StatisticalTester(alpha=alpha)
        
        # 系统状态
        self.system_status = {
            'initialized_at': datetime.now(),
            'backtest_runs': 0,
            'fusion_validations': 0,
            'statistical_tests': 0,
            'reports_generated': 0
        }
        
        # 评估历史
        self.evaluation_history = []
    
    def run_comprehensive_evaluation(self, 
                                   strategies: List[BacktestingStrategy],
                                   historical_data: pd.DataFrame,
                                   fusion_methods: List[str] = None,
                                   generate_report: bool = True) -> Dict[str, Any]:
        """
        运行综合评估
        
        Args:
            strategies: 回测策略列表
            historical_data: 历史数据
            fusion_methods: 融合方法列表
            generate_report: 是否生成报告
        """
        
        print("🚀 开始综合评估...")
        
        evaluation_id = f"eval_{int(datetime.now().timestamp())}"
        
        # 1. 执行回测
        print("📊 步骤1: 执行历史回测...")
        backtest_results = self.backtesting_engine.compare_strategies(strategies, historical_data)
        self.system_status['backtest_runs'] += len(strategies)
        
        # 2. 性能指标计算
        print("📊 步骤2: 计算详细性能指标...")
        detailed_performance = {}
        for strategy_name, result in backtest_results['individual_results'].items():
            predictions = result.get('predictions', [])
            actual_results = result.get('actual_results', [])
            
            if predictions and actual_results:
                performance_report = self.performance_metrics.generate_performance_report(
                    predictions, actual_results, strategy_name
                )
                detailed_performance[strategy_name] = performance_report
        
        # 3. 融合策略验证
        fusion_results = {}
        if len(strategies) > 1:
            print("📊 步骤3: 验证融合策略...")
            
            # 准备融合输入数据
            individual_predictions = {}
            for strategy_name, result in backtest_results['individual_results'].items():
                individual_predictions[strategy_name] = result.get('predictions', [])
            
            # 获取实际结果（使用第一个策略的实际结果）
            first_strategy = list(backtest_results['individual_results'].keys())[0]
            actual_results = backtest_results['individual_results'][first_strategy].get('actual_results', [])
            
            if fusion_methods is None:
                fusion_methods = ['simple_average', 'weighted_average', 'voting']
            
            fusion_comparison = self.fusion_validator.compare_fusion_methods(
                individual_predictions, actual_results, fusion_methods
            )
            fusion_results = fusion_comparison
            self.system_status['fusion_validations'] += len(fusion_methods)
        
        # 4. 统计显著性检验
        print("📊 步骤4: 执行统计显著性检验...")
        statistical_results = {}
        
        if len(strategies) >= 2:
            strategy_names = list(backtest_results['individual_results'].keys())
            
            # 两两比较前两个策略
            strategy_a = strategy_names[0]
            strategy_b = strategy_names[1]
            
            predictions_a = backtest_results['individual_results'][strategy_a].get('predictions', [])
            predictions_b = backtest_results['individual_results'][strategy_b].get('predictions', [])
            actual_results = backtest_results['individual_results'][strategy_a].get('actual_results', [])
            
            if predictions_a and predictions_b and actual_results:
                comparison = self.statistical_tester.comprehensive_comparison(
                    predictions_a, predictions_b, actual_results, strategy_a, strategy_b
                )
                statistical_results[f"{strategy_a}_vs_{strategy_b}"] = comparison
                self.system_status['statistical_tests'] += 1
            
            # 多模型比较
            if len(strategies) >= 3:
                model_predictions = {}
                for strategy_name, result in backtest_results['individual_results'].items():
                    model_predictions[strategy_name] = result.get('predictions', [])
                
                multi_comparison = self.statistical_tester.multiple_model_comparison(
                    model_predictions, actual_results
                )
                statistical_results['multi_model_comparison'] = multi_comparison
        
        # 5. 生成可视化报告
        visualization_paths = {}
        if generate_report:
            print("📊 步骤5: 生成可视化报告...")
            
            report_dir = os.path.join(self.output_dir, evaluation_id)
            os.makedirs(report_dir, exist_ok=True)
            
            # 生成综合报告
            report_path = self.visualizer.generate_comprehensive_report(
                backtest_results['individual_results'],
                fusion_results.get('individual_results', {}),
                report_dir
            )
            
            visualization_paths['comprehensive_report'] = report_path
            self.system_status['reports_generated'] += 1
        
        # 6. 整合结果
        comprehensive_result = {
            'evaluation_id': evaluation_id,
            'timestamp': datetime.now().isoformat(),
            'backtest_results': backtest_results,
            'detailed_performance': detailed_performance,
            'fusion_results': fusion_results,
            'statistical_results': statistical_results,
            'visualization_paths': visualization_paths,
            'summary': self._generate_evaluation_summary(
                backtest_results, fusion_results, statistical_results
            )
        }
        
        # 保存评估结果
        self._save_evaluation_results(comprehensive_result, evaluation_id)
        
        # 记录评估历史
        self.evaluation_history.append({
            'evaluation_id': evaluation_id,
            'timestamp': datetime.now().isoformat(),
            'strategy_count': len(strategies),
            'fusion_method_count': len(fusion_methods) if fusion_methods else 0,
            'has_statistical_tests': bool(statistical_results),
            'report_generated': generate_report
        })
        
        print(f"✅ 综合评估完成！评估ID: {evaluation_id}")
        
        return comprehensive_result
    
    def quick_backtest(self, 
                      strategy: BacktestingStrategy,
                      historical_data: pd.DataFrame,
                      start_date: str = None,
                      end_date: str = None) -> Dict[str, Any]:
        """
        快速回测单个策略
        """
        
        print(f"⚡ 快速回测策略: {strategy.get_strategy_name()}")
        
        # 设置回测参数
        if start_date:
            self.backtesting_engine.start_date = pd.to_datetime(start_date)
        if end_date:
            self.backtesting_engine.end_date = pd.to_datetime(end_date)
        
        # 执行回测
        result = self.backtesting_engine.run_backtest(strategy, historical_data)
        
        # 计算性能指标
        predictions = result.get('predictions', [])
        actual_results = result.get('actual_results', [])
        
        if predictions and actual_results:
            performance_report = self.performance_metrics.generate_performance_report(
                predictions, actual_results, strategy.get_strategy_name()
            )
            result['detailed_performance'] = performance_report
        
        self.system_status['backtest_runs'] += 1
        
        return result
    
    def validate_fusion_strategy(self, 
                                individual_predictions: Dict[str, List[Dict]],
                                actual_results: List[Dict],
                                fusion_method: str = 'simple_average',
                                fusion_params: Dict = None) -> Dict[str, Any]:
        """
        验证单个融合策略
        """
        
        print(f"🔀 验证融合策略: {fusion_method}")
        
        result = self.fusion_validator.validate_fusion_strategy(
            individual_predictions, actual_results, fusion_method, fusion_params
        )
        
        self.system_status['fusion_validations'] += 1
        
        return result
    
    def compare_two_models(self, 
                          predictions_a: List[Dict],
                          predictions_b: List[Dict],
                          actual_results: List[Dict],
                          model_name_a: str = "Model A",
                          model_name_b: str = "Model B") -> Dict[str, Any]:
        """
        比较两个模型的性能
        """
        
        print(f"⚖️ 比较模型: {model_name_a} vs {model_name_b}")
        
        # 统计检验
        statistical_comparison = self.statistical_tester.comprehensive_comparison(
            predictions_a, predictions_b, actual_results, model_name_a, model_name_b
        )
        
        # 性能指标比较
        performance_a = self.performance_metrics.generate_performance_report(
            predictions_a, actual_results, model_name_a
        )
        performance_b = self.performance_metrics.generate_performance_report(
            predictions_b, actual_results, model_name_b
        )
        
        self.system_status['statistical_tests'] += 1
        
        return {
            'statistical_comparison': statistical_comparison,
            'performance_comparison': {
                model_name_a: performance_a,
                model_name_b: performance_b
            },
            'recommendation': statistical_comparison['summary']['recommendation']
        }
    
    def _generate_evaluation_summary(self, 
                                   backtest_results: Dict,
                                   fusion_results: Dict,
                                   statistical_results: Dict) -> Dict[str, Any]:
        """生成评估总结"""
        
        summary = {
            'best_individual_strategy': None,
            'best_fusion_method': None,
            'statistical_significance': {},
            'key_findings': [],
            'recommendations': []
        }
        
        # 最佳个体策略
        if 'individual_results' in backtest_results:
            best_strategy = None
            best_hit_rate = 0
            
            for strategy_name, result in backtest_results['individual_results'].items():
                hit_rate = result.get('performance', {}).get('hit_rate', 0)
                if hit_rate > best_hit_rate:
                    best_hit_rate = hit_rate
                    best_strategy = strategy_name
            
            summary['best_individual_strategy'] = {
                'name': best_strategy,
                'hit_rate': best_hit_rate
            }
        
        # 最佳融合方法
        if fusion_results and 'individual_results' in fusion_results:
            best_fusion = None
            best_fusion_hit_rate = 0
            
            for method, result in fusion_results['individual_results'].items():
                hit_rate = result.get('fusion_performance', {}).get('hit_rate', 0)
                if hit_rate > best_fusion_hit_rate:
                    best_fusion_hit_rate = hit_rate
                    best_fusion = method
            
            summary['best_fusion_method'] = {
                'name': best_fusion,
                'hit_rate': best_fusion_hit_rate
            }
        
        # 统计显著性总结
        significant_comparisons = 0
        total_comparisons = 0
        
        for comparison_name, result in statistical_results.items():
            if isinstance(result, dict) and 'summary' in result:
                total_comparisons += 1
                if result['summary'].get('significant_count', 0) > 0:
                    significant_comparisons += 1
        
        summary['statistical_significance'] = {
            'significant_comparisons': significant_comparisons,
            'total_comparisons': total_comparisons,
            'significance_ratio': significant_comparisons / total_comparisons if total_comparisons > 0 else 0
        }
        
        # 关键发现
        if summary['best_individual_strategy']:
            summary['key_findings'].append(
                f"最佳个体策略: {summary['best_individual_strategy']['name']} "
                f"(命中率: {summary['best_individual_strategy']['hit_rate']:.3f})"
            )
        
        if summary['best_fusion_method']:
            summary['key_findings'].append(
                f"最佳融合方法: {summary['best_fusion_method']['name']} "
                f"(命中率: {summary['best_fusion_method']['hit_rate']:.3f})"
            )
        
        # 推荐
        if summary['best_fusion_method'] and summary['best_individual_strategy']:
            if summary['best_fusion_method']['hit_rate'] > summary['best_individual_strategy']['hit_rate']:
                summary['recommendations'].append("推荐使用融合策略以获得更好的性能")
            else:
                summary['recommendations'].append("个体策略已经表现良好，融合可能不会带来显著提升")
        
        return summary
    
    def _save_evaluation_results(self, results: Dict[str, Any], evaluation_id: str):
        """保存评估结果"""
        
        # 保存JSON格式的详细结果
        json_path = os.path.join(self.output_dir, f"{evaluation_id}_results.json")
        
        # 处理不能序列化的对象
        serializable_results = self._make_serializable(results)
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 评估结果已保存到: {json_path}")
    
    def _make_serializable(self, obj):
        """使对象可序列化"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, pd.Timestamp):
            return obj.isoformat()
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            return obj
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return self.system_status
    
    def get_evaluation_history(self) -> List[Dict[str, Any]]:
        """获取评估历史"""
        return self.evaluation_history
    
    def export_results_summary(self, filepath: str = None) -> str:
        """导出结果总结"""
        
        if filepath is None:
            filepath = os.path.join(self.output_dir, "evaluation_summary.json")
        
        summary = {
            'system_status': self.get_system_status(),
            'evaluation_history': self.get_evaluation_history(),
            'total_evaluations': len(self.evaluation_history)
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 评估总结已导出到: {filepath}")
        return filepath


def main():
    """主函数 - 演示评估引擎"""
    print("📊 澳门六合彩预测系统 - 历史回测与模型评估引擎")
    print("=" * 60)
    
    # 初始化系统
    evaluation_engine = EvaluationEngine()
    
    # 显示系统状态
    status = evaluation_engine.get_system_status()
    print(f"📊 系统状态:")
    print(f"   初始化时间: {status['initialized_at']}")
    print(f"   回测运行次数: {status['backtest_runs']}")
    print(f"   融合验证次数: {status['fusion_validations']}")
    print(f"   统计检验次数: {status['statistical_tests']}")
    print(f"   生成报告数: {status['reports_generated']}")
    
    print("\n🎊 历史回测与模型评估引擎初始化完成！")
    print("系统包含以下核心功能:")
    print("   • 历史数据回测框架 - 完整的回测流程")
    print("   • 模型性能评估指标 - 全面的性能评估")
    print("   • 融合策略验证机制 - 多模型融合验证")
    print("   • 回测结果可视化 - 丰富的图表和报告")
    print("   • 统计显著性检验 - 科学的统计验证")

if __name__ == "__main__":
    main()
