"""
染色体编码 - 遗传算法的参数和结构编码
"""
import numpy as np
from typing import List, Dict, Any, Tuple, Union
import json
import random
from abc import ABC, abstractmethod

class ChromosomeEncoder(ABC):
    """染色体编码器基类"""
    
    @abstractmethod
    def encode(self, parameters: Dict[str, Any]) -> np.ndarray:
        """编码参数为染色体"""
        pass
    
    @abstractmethod
    def decode(self, chromosome: np.ndarray) -> Dict[str, Any]:
        """解码染色体为参数"""
        pass
    
    @abstractmethod
    def get_chromosome_length(self) -> int:
        """获取染色体长度"""
        pass

class BinaryEncoder(ChromosomeEncoder):
    """二进制编码器"""
    
    def __init__(self, parameter_ranges: Dict[str, Dict]):
        """
        初始化二进制编码器
        
        Args:
            parameter_ranges: 参数范围定义
                例如: {
                    'param1': {'min': 0, 'max': 100, 'bits': 8},
                    'param2': {'min': 0.0, 'max': 1.0, 'bits': 10}
                }
        """
        self.parameter_ranges = parameter_ranges
        self.parameter_names = list(parameter_ranges.keys())
        self.chromosome_length = sum(param['bits'] for param in parameter_ranges.values())
    
    def encode(self, parameters: Dict[str, Any]) -> np.ndarray:
        """编码参数为二进制染色体"""
        chromosome = []
        
        for param_name in self.parameter_names:
            if param_name not in parameters:
                raise ValueError(f"参数 {param_name} 未找到")
            
            param_config = self.parameter_ranges[param_name]
            value = parameters[param_name]
            
            # 归一化到[0, 1]
            normalized = (value - param_config['min']) / (param_config['max'] - param_config['min'])
            normalized = max(0, min(1, normalized))  # 确保在范围内
            
            # 转换为二进制
            max_int = (1 << param_config['bits']) - 1
            int_value = int(normalized * max_int)
            
            # 转换为二进制数组
            binary = [(int_value >> i) & 1 for i in range(param_config['bits'])]
            chromosome.extend(binary)
        
        return np.array(chromosome, dtype=np.int8)
    
    def decode(self, chromosome: np.ndarray) -> Dict[str, Any]:
        """解码二进制染色体为参数"""
        parameters = {}
        start_idx = 0
        
        for param_name in self.parameter_names:
            param_config = self.parameter_ranges[param_name]
            bits = param_config['bits']
            
            # 提取对应的二进制位
            binary_segment = chromosome[start_idx:start_idx + bits]
            
            # 转换为整数
            int_value = sum(bit * (1 << i) for i, bit in enumerate(binary_segment))
            
            # 归一化
            max_int = (1 << bits) - 1
            normalized = int_value / max_int if max_int > 0 else 0
            
            # 反归一化到实际范围
            value = param_config['min'] + normalized * (param_config['max'] - param_config['min'])
            
            # 根据参数类型处理
            if isinstance(param_config.get('default', 0), int):
                value = int(round(value))
            
            parameters[param_name] = value
            start_idx += bits
        
        return parameters
    
    def get_chromosome_length(self) -> int:
        return self.chromosome_length

class RealValueEncoder(ChromosomeEncoder):
    """实数编码器"""
    
    def __init__(self, parameter_ranges: Dict[str, Dict]):
        """
        初始化实数编码器
        
        Args:
            parameter_ranges: 参数范围定义
                例如: {
                    'param1': {'min': 0, 'max': 100},
                    'param2': {'min': 0.0, 'max': 1.0}
                }
        """
        self.parameter_ranges = parameter_ranges
        self.parameter_names = list(parameter_ranges.keys())
        self.chromosome_length = len(parameter_ranges)
    
    def encode(self, parameters: Dict[str, Any]) -> np.ndarray:
        """编码参数为实数染色体"""
        chromosome = []
        
        for param_name in self.parameter_names:
            if param_name not in parameters:
                raise ValueError(f"参数 {param_name} 未找到")
            
            param_config = self.parameter_ranges[param_name]
            value = parameters[param_name]
            
            # 归一化到[0, 1]
            normalized = (value - param_config['min']) / (param_config['max'] - param_config['min'])
            normalized = max(0, min(1, normalized))  # 确保在范围内
            
            chromosome.append(normalized)
        
        return np.array(chromosome, dtype=np.float32)
    
    def decode(self, chromosome: np.ndarray) -> Dict[str, Any]:
        """解码实数染色体为参数"""
        parameters = {}
        
        for i, param_name in enumerate(self.parameter_names):
            param_config = self.parameter_ranges[param_name]
            normalized = chromosome[i]
            
            # 反归一化到实际范围
            value = param_config['min'] + normalized * (param_config['max'] - param_config['min'])
            
            # 根据参数类型处理
            if isinstance(param_config.get('default', 0), int):
                value = int(round(value))
            
            parameters[param_name] = value
        
        return parameters
    
    def get_chromosome_length(self) -> int:
        return self.chromosome_length

class StructureEncoder(ChromosomeEncoder):
    """结构编码器 - 用于编码神经网络结构等"""
    
    def __init__(self, 
                 max_layers: int = 10,
                 layer_types: List[str] = ['dense', 'dropout', 'batch_norm'],
                 neuron_range: Tuple[int, int] = (10, 500)):
        """
        初始化结构编码器
        
        Args:
            max_layers: 最大层数
            layer_types: 层类型列表
            neuron_range: 神经元数量范围
        """
        self.max_layers = max_layers
        self.layer_types = layer_types
        self.neuron_range = neuron_range
        
        # 每层需要的基因数：层类型(1) + 神经元数(1) + 激活函数(1) + 其他参数(2)
        self.genes_per_layer = 5
        self.chromosome_length = max_layers * self.genes_per_layer
    
    def encode(self, structure: Dict[str, Any]) -> np.ndarray:
        """编码网络结构为染色体"""
        chromosome = np.zeros(self.chromosome_length)
        
        layers = structure.get('layers', [])
        
        for i, layer in enumerate(layers[:self.max_layers]):
            base_idx = i * self.genes_per_layer
            
            # 层类型
            layer_type = layer.get('type', 'dense')
            if layer_type in self.layer_types:
                chromosome[base_idx] = self.layer_types.index(layer_type) / len(self.layer_types)
            
            # 神经元数量
            neurons = layer.get('neurons', 64)
            normalized_neurons = (neurons - self.neuron_range[0]) / (self.neuron_range[1] - self.neuron_range[0])
            chromosome[base_idx + 1] = max(0, min(1, normalized_neurons))
            
            # 激活函数
            activation = layer.get('activation', 'relu')
            activations = ['relu', 'tanh', 'sigmoid', 'linear']
            if activation in activations:
                chromosome[base_idx + 2] = activations.index(activation) / len(activations)
            
            # Dropout率
            dropout_rate = layer.get('dropout_rate', 0.0)
            chromosome[base_idx + 3] = dropout_rate
            
            # 其他参数
            chromosome[base_idx + 4] = layer.get('other_param', 0.5)
        
        return chromosome
    
    def decode(self, chromosome: np.ndarray) -> Dict[str, Any]:
        """解码染色体为网络结构"""
        structure = {'layers': []}
        
        for i in range(self.max_layers):
            base_idx = i * self.genes_per_layer
            
            # 检查是否有有效层
            if chromosome[base_idx] == 0 and chromosome[base_idx + 1] == 0:
                break
            
            # 层类型
            layer_type_idx = int(chromosome[base_idx] * len(self.layer_types))
            layer_type_idx = min(layer_type_idx, len(self.layer_types) - 1)
            layer_type = self.layer_types[layer_type_idx]
            
            # 神经元数量
            normalized_neurons = chromosome[base_idx + 1]
            neurons = int(self.neuron_range[0] + normalized_neurons * (self.neuron_range[1] - self.neuron_range[0]))
            
            # 激活函数
            activations = ['relu', 'tanh', 'sigmoid', 'linear']
            activation_idx = int(chromosome[base_idx + 2] * len(activations))
            activation_idx = min(activation_idx, len(activations) - 1)
            activation = activations[activation_idx]
            
            # Dropout率
            dropout_rate = chromosome[base_idx + 3]
            
            # 其他参数
            other_param = chromosome[base_idx + 4]
            
            layer = {
                'type': layer_type,
                'neurons': neurons,
                'activation': activation,
                'dropout_rate': dropout_rate,
                'other_param': other_param
            }
            
            structure['layers'].append(layer)
        
        return structure
    
    def get_chromosome_length(self) -> int:
        return self.chromosome_length

class HybridEncoder(ChromosomeEncoder):
    """混合编码器 - 结合多种编码方式"""
    
    def __init__(self, encoders: Dict[str, ChromosomeEncoder]):
        """
        初始化混合编码器
        
        Args:
            encoders: 编码器字典，键为编码器名称，值为编码器实例
        """
        self.encoders = encoders
        self.encoder_names = list(encoders.keys())
        self.chromosome_length = sum(encoder.get_chromosome_length() for encoder in encoders.values())
        
        # 计算每个编码器的起始位置
        self.encoder_positions = {}
        start_pos = 0
        for name, encoder in encoders.items():
            self.encoder_positions[name] = (start_pos, start_pos + encoder.get_chromosome_length())
            start_pos += encoder.get_chromosome_length()
    
    def encode(self, parameters: Dict[str, Any]) -> np.ndarray:
        """编码参数为混合染色体"""
        chromosome = np.zeros(self.chromosome_length)
        
        for encoder_name, encoder in self.encoders.items():
            # 提取对应编码器的参数
            encoder_params = parameters.get(encoder_name, {})
            
            # 编码
            encoded_segment = encoder.encode(encoder_params)
            
            # 放入染色体对应位置
            start_pos, end_pos = self.encoder_positions[encoder_name]
            chromosome[start_pos:end_pos] = encoded_segment
        
        return chromosome
    
    def decode(self, chromosome: np.ndarray) -> Dict[str, Any]:
        """解码混合染色体为参数"""
        parameters = {}
        
        for encoder_name, encoder in self.encoders.items():
            # 提取对应的染色体段
            start_pos, end_pos = self.encoder_positions[encoder_name]
            chromosome_segment = chromosome[start_pos:end_pos]
            
            # 解码
            decoded_params = encoder.decode(chromosome_segment)
            parameters[encoder_name] = decoded_params
        
        return parameters
    
    def get_chromosome_length(self) -> int:
        return self.chromosome_length

class LotteryPredictionEncoder:
    """澳门六合彩预测专用编码器"""
    
    def __init__(self):
        """初始化彩票预测编码器"""
        
        # 定义预测模型参数范围
        model_params = {
            'n_estimators': {'min': 50, 'max': 500, 'bits': 8},
            'max_depth': {'min': 3, 'max': 20, 'bits': 5},
            'learning_rate': {'min': 0.01, 'max': 0.3, 'bits': 8},
            'min_samples_split': {'min': 2, 'max': 20, 'bits': 5},
            'min_samples_leaf': {'min': 1, 'max': 10, 'bits': 4}
        }
        
        # 定义特征选择参数
        feature_params = {
            'n_features': {'min': 10, 'max': 100},
            'selection_method': {'min': 0, 'max': 3},  # 0-3对应不同方法
            'threshold': {'min': 0.0, 'max': 1.0}
        }
        
        # 定义预测策略参数
        strategy_params = {
            'prediction_method': {'min': 0, 'max': 4},  # 不同预测方法
            'ensemble_weight': {'min': 0.0, 'max': 1.0},
            'confidence_threshold': {'min': 0.5, 'max': 0.95}
        }
        
        # 创建混合编码器
        self.encoder = HybridEncoder({
            'model': BinaryEncoder(model_params),
            'features': RealValueEncoder(feature_params),
            'strategy': RealValueEncoder(strategy_params)
        })
    
    def encode_prediction_config(self, config: Dict[str, Any]) -> np.ndarray:
        """编码预测配置"""
        return self.encoder.encode(config)
    
    def decode_prediction_config(self, chromosome: np.ndarray) -> Dict[str, Any]:
        """解码预测配置"""
        return self.encoder.decode(chromosome)
    
    def get_chromosome_length(self) -> int:
        """获取染色体长度"""
        return self.encoder.get_chromosome_length()
    
    def create_random_chromosome(self) -> np.ndarray:
        """创建随机染色体"""
        return np.random.random(self.get_chromosome_length())
    
    def validate_chromosome(self, chromosome: np.ndarray) -> bool:
        """验证染色体有效性"""
        if len(chromosome) != self.get_chromosome_length():
            return False
        
        # 检查数值范围
        if np.any(chromosome < 0) or np.any(chromosome > 1):
            return False
        
        return True
    
    def repair_chromosome(self, chromosome: np.ndarray) -> np.ndarray:
        """修复无效染色体"""
        # 确保长度正确
        if len(chromosome) != self.get_chromosome_length():
            if len(chromosome) < self.get_chromosome_length():
                # 补零
                padding = np.zeros(self.get_chromosome_length() - len(chromosome))
                chromosome = np.concatenate([chromosome, padding])
            else:
                # 截断
                chromosome = chromosome[:self.get_chromosome_length()]
        
        # 确保数值在[0,1]范围内
        chromosome = np.clip(chromosome, 0, 1)
        
        return chromosome
