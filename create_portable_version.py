"""
创建便携版封包
"""
import os
import shutil
import zipfile
import json
from datetime import datetime

def create_portable_version():
    """创建便携版"""
    print("📦 创建便携版封包")
    print("=" * 50)
    
    # 1. 创建便携版目录结构
    portable_dir = "LotteryPrediction_Portable_v1.0.0"
    
    if os.path.exists(portable_dir):
        shutil.rmtree(portable_dir)
    
    print(f"📁 创建便携版目录: {portable_dir}")
    
    # 创建目录结构
    dirs_to_create = [
        f"{portable_dir}",
        f"{portable_dir}/src",
        f"{portable_dir}/data", 
        f"{portable_dir}/config",
        f"{portable_dir}/docs",
        f"{portable_dir}/logs",
        f"{portable_dir}/output",
        f"{portable_dir}/backup"
    ]
    
    for dir_path in dirs_to_create:
        os.makedirs(dir_path, exist_ok=True)
        print(f"   ✅ {dir_path}")
    
    # 2. 复制核心文件
    print(f"\n📄 复制核心文件:")
    
    # 核心Python文件
    core_files = [
        ('src/perfect_prediction_system.py', f'{portable_dir}/src/'),
        ('src/fusion_manager.py', f'{portable_dir}/src/'),
        ('src/ml_module_adapter.py', f'{portable_dir}/src/'),
        ('src/enhanced_ml_integration.py', f'{portable_dir}/src/'),
        ('optimal_config.json', f'{portable_dir}/config/'),
        ('enhanced_backtest_config.json', f'{portable_dir}/config/'),
        ('config_summary.json', f'{portable_dir}/config/'),
        ('requirements.txt', f'{portable_dir}/'),
        ('package_info.json', f'{portable_dir}/')
    ]
    
    copied_files = []
    for src_file, dst_dir in core_files:
        if os.path.exists(src_file):
            if not os.path.exists(dst_dir):
                os.makedirs(dst_dir, exist_ok=True)
            
            dst_file = os.path.join(dst_dir, os.path.basename(src_file))
            shutil.copy2(src_file, dst_file)
            copied_files.append(dst_file)
            print(f"   ✅ {src_file} → {dst_file}")
        else:
            print(f"   ⚠️ {src_file}: 文件不存在")
    
    # 复制src目录下的所有Python文件
    if os.path.exists('src'):
        for root, dirs, files in os.walk('src'):
            for file in files:
                if file.endswith('.py'):
                    src_path = os.path.join(root, file)
                    rel_path = os.path.relpath(src_path, 'src')
                    dst_path = os.path.join(f'{portable_dir}/src', rel_path)
                    
                    # 确保目标目录存在
                    dst_dir = os.path.dirname(dst_path)
                    if dst_dir and not os.path.exists(dst_dir):
                        os.makedirs(dst_dir, exist_ok=True)
                    
                    if not os.path.exists(dst_path):  # 避免重复复制
                        shutil.copy2(src_path, dst_path)
                        copied_files.append(dst_path)
                        print(f"   ✅ {src_path} → {dst_path}")
    
    # 3. 创建启动脚本
    print(f"\n🚀 创建启动脚本:")
    
    # Windows批处理启动脚本
    batch_script = f'''@echo off
chcp 65001 > nul
title 澳门六合彩智能预测系统 - 便携版 v1.0.0

echo.
echo ========================================
echo   澳门六合彩智能预测系统 - 便携版
echo   版本: v1.0.0
echo   发布日期: {datetime.now().strftime("%Y-%m-%d")}
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请确保已安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 📦 检查依赖包...
python -c "import sys; sys.path.append('src'); from perfect_prediction_system import PerfectPredictionSystem; print('✅ 核心模块导入成功')" 2>nul
if errorlevel 1 (
    echo ⚠️ 检测到缺少依赖包，正在安装...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖包检查通过

echo.
echo 🚀 启动预测系统...
cd /d "%~dp0"
python -c "
import sys
sys.path.append('src')
from perfect_prediction_system import PerfectPredictionSystem

print('🎯 澳门六合彩智能预测系统')
print('=' * 40)

try:
    system = PerfectPredictionSystem()
    system.initialize_modules()
    
    print('✅ 系统初始化成功')
    print(f'📊 配置源: {{system.config_source}}')
    print(f'🔧 增强配置: {{system.enhanced_config_loaded}}')
    
    # 运行预测
    result = system.run_complete_prediction('2025-06-23')
    
    print('\\n🎯 预测结果:')
    final_results = result.get('final_results', {{}})
    if final_results:
        numbers = final_results.get('recommended_16_numbers', [])
        zodiacs = final_results.get('recommended_4_zodiacs', [])
        confidence = final_results.get('overall_confidence', 0)
        
        print(f'📊 推荐号码: {{numbers}}')
        print(f'🐲 推荐生肖: {{zodiacs}}')
        print(f'�� 置信度: {{confidence}}')
        
        # 保存结果
        import json
        with open('output/latest_prediction.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print('💾 预测结果已保存到 output/latest_prediction.json')
    
    print('\\n🎊 预测完成!')
    
except Exception as e:
    print(f'❌ 系统运行失败: {{e}}')
    import traceback
    traceback.print_exc()
"

echo.
echo 按任意键退出...
pause > nul
'''
    
    with open(f'{portable_dir}/启动预测系统.bat', 'w', encoding='utf-8') as f:
        f.write(batch_script)
    
    print(f"   ✅ 启动预测系统.bat")
    
    # PowerShell启动脚本
    ps_script = f'''# 澳门六合彩智能预测系统 - 便携版启动脚本
# 版本: v1.0.0

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  澳门六合彩智能预测系统 - 便携版" -ForegroundColor Green
Write-Host "  版本: v1.0.0" -ForegroundColor Yellow
Write-Host "  发布日期: {datetime.now().strftime("%Y-%m-%d")}" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Python环境
Write-Host "🔍 检查Python环境..." -ForegroundColor Blue
try {{
    $pythonVersion = python --version 2>&1
    Write-Host "✅ $pythonVersion" -ForegroundColor Green
}} catch {{
    Write-Host "❌ 错误: 未找到Python环境" -ForegroundColor Red
    Write-Host "请确保已安装Python 3.8或更高版本" -ForegroundColor Yellow
    Read-Host "按Enter键退出"
    exit 1
}}

# 运行预测系统
Write-Host ""
Write-Host "🚀 启动预测系统..." -ForegroundColor Blue

Set-Location $PSScriptRoot
python -c @"
import sys
sys.path.append('src')
from perfect_prediction_system import PerfectPredictionSystem

print('🎯 澳门六合彩智能预测系统')
print('=' * 40)

try:
    system = PerfectPredictionSystem()
    system.initialize_modules()
    
    print('✅ 系统初始化成功')
    
    # 运行预测
    result = system.run_complete_prediction('2025-06-23')
    
    print('\\n🎯 预测结果:')
    final_results = result.get('final_results', {{}})
    if final_results:
        numbers = final_results.get('recommended_16_numbers', [])
        zodiacs = final_results.get('recommended_4_zodiacs', [])
        confidence = final_results.get('overall_confidence', 0)
        
        print(f'📊 推荐号码: {{numbers}}')
        print(f'🐲 推荐生肖: {{zodiacs}}')
        print(f'📈 置信度: {{confidence}}')
    
    print('\\n🎊 预测完成!')
    
except Exception as e:
    print(f'❌ 系统运行失败: {{e}}')
"@

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Yellow
Read-Host
'''
    
    with open(f'{portable_dir}/启动预测系统.ps1', 'w', encoding='utf-8') as f:
        f.write(ps_script)
    
    print(f"   ✅ 启动预测系统.ps1")
    
    # 4. 创建说明文档
    print(f"\n📚 创建说明文档:")
    
    readme_content = f'''# 澳门六合彩智能预测系统 - 便携版

## 📋 版本信息
- **版本**: v1.0.0
- **发布日期**: {datetime.now().strftime("%Y-%m-%d")}
- **平台**: Windows 11
- **Python版本**: 3.8+

## 🚀 快速开始

### 方法1: 使用批处理脚本（推荐）
1. 双击 `启动预测系统.bat`
2. 系统会自动检查环境并运行预测

### 方法2: 使用PowerShell脚本
1. 右键点击 `启动预测系统.ps1`
2. 选择"使用PowerShell运行"

### 方法3: 手动运行
1. 打开命令提示符或PowerShell
2. 切换到当前目录
3. 运行: `python -c "import sys; sys.path.append('src'); from perfect_prediction_system import PerfectPredictionSystem; system = PerfectPredictionSystem(); system.initialize_modules(); result = system.run_complete_prediction('2025-06-23'); print(result)"`

## 📁 目录结构
```
LotteryPrediction_Portable_v1.0.0/
├── src/                    # 源代码目录
│   ├── perfect_prediction_system.py
│   ├── fusion_manager.py
│   └── ...
├── config/                 # 配置文件目录
│   ├── optimal_config.json
│   └── ...
├── data/                   # 数据目录
├── docs/                   # 文档目录
├── logs/                   # 日志目录
├── output/                 # 输出目录
├── backup/                 # 备份目录
├── requirements.txt        # 依赖包列表
├── package_info.json       # 包信息
├── 启动预测系统.bat        # Windows启动脚本
├── 启动预测系统.ps1        # PowerShell启动脚本
└── README.md              # 说明文档
```

## 🔧 系统要求
- Windows 10/11
- Python 3.8 或更高版本
- 至少 2GB 可用内存
- 至少 500MB 可用磁盘空间

## 📦 依赖包
系统会自动检查并安装以下依赖包：
- numpy>=1.21.0
- pandas>=1.3.0
- scikit-learn>=1.0.0
- matplotlib>=3.4.0
- 其他必要依赖...

## 🎯 功能特性
- ✅ 完美预测系统
- ✅ 增强回测功能
- ✅ 特码预测
- ✅ 生肖预测
- ✅ 多模组协作
- ✅ 智能融合算法

## 📊 使用说明
1. **首次运行**: 系统会自动安装依赖包
2. **预测结果**: 保存在 `output/` 目录
3. **日志文件**: 保存在 `logs/` 目录
4. **配置修改**: 编辑 `config/` 目录下的配置文件

## ⚠️ 注意事项
- 确保网络连接正常（用于安装依赖包）
- 首次运行可能需要较长时间安装依赖
- 建议定期备份配置文件和预测结果

## 🆘 故障排除
1. **Python未找到**: 请安装Python 3.8+
2. **依赖包安装失败**: 检查网络连接，手动运行 `pip install -r requirements.txt`
3. **预测失败**: 检查 `logs/` 目录下的日志文件

## 📞 技术支持
如遇问题，请检查日志文件或联系技术支持。

---
© 2025 澳门六合彩智能预测系统 - 便携版
'''
    
    with open(f'{portable_dir}/README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"   ✅ README.md")
    
    # 5. 创建版本信息文件
    version_info = {
        "version": "1.0.0",
        "build_date": datetime.now().isoformat(),
        "build_type": "portable",
        "platform": "Windows 11",
        "python_version": "3.8+",
        "total_files": len(copied_files) + 3,  # +3 for scripts and readme
        "package_size": "估算中...",
        "features": [
            "完美预测系统",
            "增强回测功能",
            "特码预测",
            "生肖预测",
            "便携式运行"
        ]
    }
    
    with open(f'{portable_dir}/version_info.json', 'w', encoding='utf-8') as f:
        json.dump(version_info, f, ensure_ascii=False, indent=2)
    
    print(f"   ✅ version_info.json")
    
    # 6. 创建ZIP压缩包
    print(f"\n📦 创建ZIP压缩包:")
    
    zip_filename = f"{portable_dir}.zip"
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(portable_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_path = os.path.relpath(file_path, '.')
                zipf.write(file_path, arc_path)
    
    # 获取压缩包大小
    zip_size = os.path.getsize(zip_filename)
    zip_size_mb = zip_size / (1024 * 1024)
    
    print(f"   ✅ {zip_filename}")
    print(f"   📊 压缩包大小: {zip_size_mb:.2f} MB")
    
    # 更新版本信息中的包大小
    version_info["package_size"] = f"{zip_size_mb:.2f} MB"
    with open(f'{portable_dir}/version_info.json', 'w', encoding='utf-8') as f:
        json.dump(version_info, f, ensure_ascii=False, indent=2)
    
    return {
        'portable_dir': portable_dir,
        'zip_file': zip_filename,
        'size_mb': zip_size_mb,
        'total_files': len(copied_files) + 4
    }

if __name__ == "__main__":
    result = create_portable_version()
    
    print(f"\n🎊 便携版创建完成!")
    print(f"📁 目录: {result['portable_dir']}")
    print(f"📦 压缩包: {result['zip_file']}")
    print(f"📊 大小: {result['size_mb']:.2f} MB")
    print(f"📄 文件数: {result['total_files']}")
