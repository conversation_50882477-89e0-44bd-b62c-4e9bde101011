"""
预测系统识别工具
帮助识别GUI和终端使用的不同预测系统
"""

def identify_prediction_system():
    """识别当前可用的预测系统"""
    print("🔍 澳门六合彩预测系统识别工具")
    print("=" * 60)
    
    systems = {}
    
    # 1. 测试完美预测系统
    print("🎊 测试完美预测系统...")
    try:
        from src.perfect_prediction_system import PerfectPredictionSystem
        system = PerfectPredictionSystem()
        system.initialize_modules()
        result = system.run_complete_prediction('2025-06-25')
        
        if result and 'final_results' in result:
            numbers = result['final_results']['recommended_16_numbers']
            systems['perfect_prediction'] = {
                'name': '完美预测系统',
                'numbers': numbers,
                'count': len(numbers),
                'confidence': result['final_results'].get('overall_confidence', 0),
                'status': '✅ 可用'
            }
        else:
            systems['perfect_prediction'] = {
                'name': '完美预测系统',
                'status': '❌ 结果格式异常'
            }
    except Exception as e:
        systems['perfect_prediction'] = {
            'name': '完美预测系统',
            'status': f'❌ 失败: {str(e)[:50]}...'
        }
    
    # 2. 测试特码预测器
    print("🎯 测试特码预测器...")
    try:
        from src.special_number_predictor import SpecialNumberPredictor
        predictor = SpecialNumberPredictor()
        result = predictor.predict('2025-06-25')
        
        if result and 'predicted_numbers' in result:
            numbers = result['predicted_numbers']
            systems['special_predictor'] = {
                'name': '特码预测器',
                'numbers': numbers,
                'count': len(numbers),
                'confidence': result.get('confidence', 0),
                'status': '✅ 可用'
            }
        else:
            systems['special_predictor'] = {
                'name': '特码预测器',
                'status': '❌ 结果格式异常'
            }
    except Exception as e:
        systems['special_predictor'] = {
            'name': '特码预测器',
            'status': f'❌ 失败: {str(e)[:50]}...'
        }
    
    # 3. 测试一致性预测器
    print("🔒 测试一致性预测器...")
    try:
        from consistent_predictor import ConsistentSpecialNumberPredictor
        predictor = ConsistentSpecialNumberPredictor()
        result = predictor.run_consistent_prediction('2025-06-25')
        
        if result and 'recommended_numbers' in result:
            numbers = result['recommended_numbers']
            systems['consistent_predictor'] = {
                'name': '一致性预测器',
                'numbers': numbers,
                'count': len(numbers),
                'confidence': result.get('overall_confidence', 0),
                'status': '✅ 可用'
            }
        else:
            systems['consistent_predictor'] = {
                'name': '一致性预测器',
                'status': '❌ 结果格式异常'
            }
    except Exception as e:
        systems['consistent_predictor'] = {
            'name': '一致性预测器',
            'status': f'❌ 失败: {str(e)[:50]}...'
        }
    
    # 4. 测试历史回测系统
    print("📊 测试历史回测系统...")
    try:
        from historical_backtest import HistoricalBacktestSystem
        backtest = HistoricalBacktestSystem()
        # 尝试获取预测功能
        systems['historical_backtest'] = {
            'name': '历史回测系统',
            'status': '✅ 可用 (仅回测功能)',
            'note': '主要用于历史数据回测，不直接预测'
        }
    except Exception as e:
        systems['historical_backtest'] = {
            'name': '历史回测系统',
            'status': f'❌ 失败: {str(e)[:50]}...'
        }
    
    # 显示结果
    print("\n📋 预测系统识别结果")
    print("=" * 60)
    
    for system_id, info in systems.items():
        print(f"\n🔧 {info['name']}:")
        print(f"   状态: {info['status']}")
        
        if 'numbers' in info:
            print(f"   预测号码: {info['numbers']}")
            print(f"   号码数量: {info['count']}个")
            print(f"   置信度: {info['confidence']:.1%}")
        
        if 'note' in info:
            print(f"   说明: {info['note']}")
    
    # 分析差异
    print("\n🔍 预测结果差异分析")
    print("=" * 60)
    
    available_systems = {k: v for k, v in systems.items() if 'numbers' in v}
    
    if len(available_systems) > 1:
        print("发现多个可用预测系统，结果可能不同：")
        
        for system_id, info in available_systems.items():
            print(f"\n📊 {info['name']}:")
            print(f"   号码: {info['numbers']}")
            print(f"   特征: {len(info['numbers'])}个号码, 置信度{info['confidence']:.1%}")
    
    # 提供建议
    print("\n💡 使用建议")
    print("=" * 60)
    print("1. GUI界面使用: 完美预测系统 (推荐)")
    print("2. 终端使用: 请明确指定要使用的预测系统")
    print("3. 一致性要求: 使用一致性预测器")
    print("4. 性能评估: 使用历史回测系统")
    
    return systems

def compare_with_user_results(gui_numbers, terminal_numbers):
    """比较用户提供的结果"""
    print("\n🔍 用户结果比较分析")
    print("=" * 60)
    
    print(f"GUI界面结果: {gui_numbers} ({len(gui_numbers)}个)")
    print(f"终端界面结果: {terminal_numbers} ({len(terminal_numbers)}个)")
    
    # 获取系统识别结果
    systems = identify_prediction_system()
    
    print("\n📊 匹配分析:")
    
    # 检查GUI结果匹配
    gui_matched = False
    for system_id, info in systems.items():
        if 'numbers' in info and info['numbers'] == gui_numbers:
            print(f"✅ GUI结果匹配: {info['name']}")
            gui_matched = True
            break
    
    if not gui_matched:
        print("⚠️ GUI结果未匹配任何已知系统")
    
    # 检查终端结果匹配
    terminal_matched = False
    for system_id, info in systems.items():
        if 'numbers' in info:
            # 检查是否是子集
            if set(terminal_numbers).issubset(set(info['numbers'])):
                print(f"🔍 终端结果可能来自: {info['name']} (部分匹配)")
                terminal_matched = True
            elif info['numbers'] == terminal_numbers:
                print(f"✅ 终端结果匹配: {info['name']}")
                terminal_matched = True
    
    if not terminal_matched:
        print("⚠️ 终端结果未匹配任何已知系统")
    
    # 分析可能原因
    print("\n🔍 可能原因分析:")
    if len(gui_numbers) != len(terminal_numbers):
        print(f"1. 号码数量不同: GUI({len(gui_numbers)}) vs 终端({len(terminal_numbers)})")
    
    if len(terminal_numbers) == 12:
        print("2. 终端结果12个号码，可能是筛选后的结果")
    
    if set(gui_numbers) != set(terminal_numbers):
        overlap = set(gui_numbers) & set(terminal_numbers)
        print(f"3. 号码重叠: {len(overlap)}个相同号码")
        if overlap:
            print(f"   相同号码: {sorted(list(overlap))}")

if __name__ == "__main__":
    # 运行识别
    systems = identify_prediction_system()
    
    # 用户提供的结果
    gui_numbers = [1, 8, 7, 6, 4, 13, 3, 2, 14, 37, 28, 9, 5, 29, 12, 10]
    terminal_numbers = [6, 7, 8, 18, 19, 20, 30, 31, 32, 42, 43, 44]
    
    # 比较分析
    compare_with_user_results(gui_numbers, terminal_numbers)
