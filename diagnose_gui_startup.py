"""
GUI启动问题诊断工具
检查GUI无法启动的原因并提供解决方案
"""

import sys
import os
import subprocess
import importlib.util

def check_python_environment():
    """检查Python环境"""
    print("🔍 检查Python环境...")
    print("-" * 40)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查Python版本
    version_info = sys.version_info
    if version_info.major < 3 or (version_info.major == 3 and version_info.minor < 8):
        print("❌ Python版本过低，需要3.8或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_required_packages():
    """检查必需的包"""
    print("\n📦 检查必需的包...")
    print("-" * 40)
    
    required_packages = {
        'PyQt5': 'PyQt5',
        'numpy': 'numpy', 
        'pandas': 'pandas',
        'sklearn': 'scikit-learn'
    }
    
    missing_packages = []
    
    for package_name, pip_name in required_packages.items():
        try:
            spec = importlib.util.find_spec(package_name)
            if spec is not None:
                print(f"✅ {package_name}: 已安装")
            else:
                print(f"❌ {package_name}: 未安装")
                missing_packages.append(pip_name)
        except ImportError:
            print(f"❌ {package_name}: 未安装")
            missing_packages.append(pip_name)
    
    return missing_packages

def install_missing_packages(missing_packages):
    """安装缺失的包"""
    if not missing_packages:
        print("✅ 所有必需包都已安装")
        return True
    
    print(f"\n📦 安装缺失的包: {missing_packages}")
    print("-" * 40)
    
    try:
        for package in missing_packages:
            print(f"正在安装 {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {package} 安装成功")
            else:
                print(f"❌ {package} 安装失败: {result.stderr}")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def check_gui_file():
    """检查GUI文件"""
    print("\n📁 检查GUI文件...")
    print("-" * 40)
    
    gui_files = [
        "lottery_prediction_gui.py",
        "src/perfect_prediction_system.py",
        "src/dynamic_fusion_manager_v3.py"
    ]
    
    all_exist = True
    for file_path in gui_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}: 存在")
        else:
            print(f"❌ {file_path}: 不存在")
            all_exist = False
    
    return all_exist

def test_pyqt5_import():
    """测试PyQt5导入"""
    print("\n🧪 测试PyQt5导入...")
    print("-" * 40)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        print("✅ PyQt5.QtWidgets 导入成功")
        
        # 测试创建QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("✅ QApplication 创建成功")
        
        return True
    except ImportError as e:
        print(f"❌ PyQt5导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ PyQt5测试失败: {e}")
        return False

def test_gui_startup():
    """测试GUI启动"""
    print("\n🚀 测试GUI启动...")
    print("-" * 40)
    
    if not os.path.exists("lottery_prediction_gui.py"):
        print("❌ lottery_prediction_gui.py 文件不存在")
        return False
    
    try:
        # 尝试导入GUI模块
        import lottery_prediction_gui
        print("✅ lottery_prediction_gui 模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ GUI模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        return False

def create_simple_gui_test():
    """创建简单的GUI测试"""
    print("\n🧪 创建简单GUI测试...")
    print("-" * 40)
    
    test_code = '''
import sys
from PyQt5.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout
from PyQt5.QtCore import Qt

def test_simple_gui():
    app = QApplication(sys.argv)
    
    window = QWidget()
    window.setWindowTitle("GUI测试")
    window.setGeometry(300, 300, 300, 200)
    
    layout = QVBoxLayout()
    label = QLabel("GUI测试成功！\\n如果你看到这个窗口，说明PyQt5工作正常。")
    label.setAlignment(Qt.AlignCenter)
    layout.addWidget(label)
    
    window.setLayout(layout)
    window.show()
    
    print("✅ 简单GUI窗口已显示")
    print("请检查是否有窗口弹出")
    
    # 运行3秒后自动关闭
    from PyQt5.QtCore import QTimer
    timer = QTimer()
    timer.timeout.connect(app.quit)
    timer.start(3000)  # 3秒
    
    app.exec_()

if __name__ == "__main__":
    test_simple_gui()
'''
    
    with open("test_simple_gui.py", "w", encoding="utf-8") as f:
        f.write(test_code)
    
    print("✅ 简单GUI测试文件已创建: test_simple_gui.py")
    print("请运行: python test_simple_gui.py")

def create_fixed_startup_script():
    """创建修复的启动脚本"""
    print("\n🔧 创建修复的启动脚本...")
    print("-" * 40)
    
    fixed_script = '''@echo off
chcp 65001 > nul
title Lottery Prediction System v2.0.0 - Fixed

echo 🎊 六合彩预测系统 v2.0.0
echo ==========================================
echo 正在启动系统...

REM 检查Python环境
echo 📋 检查Python环境...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

python --version
echo ✅ Python环境检查通过

REM 升级pip
echo 📦 升级pip...
python -m pip install --upgrade pip --quiet

REM 安装依赖
echo 📦 安装/检查依赖包...
python -m pip install PyQt5 numpy pandas scikit-learn matplotlib --quiet
if errorlevel 1 (
    echo ❌ 依赖包安装失败
    echo 请检查网络连接或手动安装
    pause
    exit /b 1
)
echo ✅ 依赖包检查完成

REM 检查GUI文件
echo 📁 检查程序文件...
if not exist "lottery_prediction_gui.py" (
    echo ❌ 错误: 未找到主程序文件 lottery_prediction_gui.py
    echo 请确保所有文件完整
    pause
    exit /b 1
)
echo ✅ 程序文件检查通过

REM 测试PyQt5
echo 🧪 测试GUI环境...
python -c "from PyQt5.QtWidgets import QApplication; print('PyQt5测试通过')" > nul 2>&1
if errorlevel 1 (
    echo ❌ PyQt5测试失败
    echo 正在重新安装PyQt5...
    python -m pip uninstall PyQt5 -y
    python -m pip install PyQt5
)

REM 启动程序
echo 🚀 启动GUI界面...
echo 如果没有窗口弹出，请检查任务栏或按Alt+Tab切换窗口
python lottery_prediction_gui.py

if errorlevel 1 (
    echo ❌ 程序启动失败
    echo 正在运行诊断...
    python diagnose_gui_startup.py
)

pause
'''
    
    with open("start_system_fixed.bat", "w", encoding="gbk", errors="ignore") as f:
        f.write(fixed_script)
    
    print("✅ 修复的启动脚本已创建: start_system_fixed.bat")

def provide_solutions():
    """提供解决方案"""
    print("\n💡 常见问题解决方案")
    print("=" * 60)
    
    print("🔧 问题1: Python未安装或版本过低")
    print("解决方案:")
    print("1. 下载Python 3.8+: https://www.python.org/downloads/")
    print("2. 安装时勾选 'Add Python to PATH'")
    print("3. 重启命令行窗口")
    
    print("\n🔧 问题2: PyQt5安装失败")
    print("解决方案:")
    print("1. 升级pip: python -m pip install --upgrade pip")
    print("2. 重新安装: python -m pip install PyQt5")
    print("3. 如果还是失败，尝试: pip install PyQt5 --user")
    
    print("\n🔧 问题3: GUI窗口不显示")
    print("解决方案:")
    print("1. 检查任务栏是否有程序图标")
    print("2. 按Alt+Tab切换窗口")
    print("3. 检查是否被杀毒软件拦截")
    print("4. 尝试以管理员身份运行")
    
    print("\n🔧 问题4: 依赖包冲突")
    print("解决方案:")
    print("1. 创建虚拟环境: python -m venv lottery_env")
    print("2. 激活环境: lottery_env\\Scripts\\activate")
    print("3. 安装依赖: pip install PyQt5 numpy pandas scikit-learn")
    print("4. 运行程序: python lottery_prediction_gui.py")
    
    print("\n🔧 问题5: 文件路径问题")
    print("解决方案:")
    print("1. 确保在正确的目录中运行")
    print("2. 检查所有文件是否完整")
    print("3. 重新解压发布包")

def main():
    """主函数"""
    print("🎊 GUI启动问题诊断工具")
    print("=" * 60)
    
    # 检查Python环境
    python_ok = check_python_environment()
    
    # 检查必需包
    missing_packages = check_required_packages()
    
    # 安装缺失包
    if missing_packages:
        install_ok = install_missing_packages(missing_packages)
        if not install_ok:
            print("\n❌ 包安装失败，请手动安装")
    
    # 检查GUI文件
    files_ok = check_gui_file()
    
    # 测试PyQt5
    pyqt5_ok = test_pyqt5_import()
    
    # 测试GUI启动
    gui_ok = test_gui_startup()
    
    # 创建测试文件
    create_simple_gui_test()
    
    # 创建修复脚本
    create_fixed_startup_script()
    
    # 提供解决方案
    provide_solutions()
    
    print("\n" + "=" * 60)
    print("🎯 诊断总结:")
    print(f"Python环境: {'✅ 正常' if python_ok else '❌ 异常'}")
    print(f"必需包: {'✅ 完整' if not missing_packages else '❌ 缺失'}")
    print(f"GUI文件: {'✅ 完整' if files_ok else '❌ 缺失'}")
    print(f"PyQt5: {'✅ 正常' if pyqt5_ok else '❌ 异常'}")
    print(f"GUI启动: {'✅ 正常' if gui_ok else '❌ 异常'}")
    
    if all([python_ok, not missing_packages, files_ok, pyqt5_ok, gui_ok]):
        print("\n🎉 所有检查通过！GUI应该可以正常启动")
    else:
        print("\n⚠️ 发现问题，请按照上述解决方案处理")
        print("💡 建议使用修复的启动脚本: start_system_fixed.bat")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
