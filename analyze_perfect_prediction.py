import sys
import os
sys.path.append('src')

def analyze_perfect_prediction_workflow():
    """分析完美预测的运行流程"""
    print("🔍 完美预测运行流程分析")
    print("=" * 50)
    
    try:
        from perfect_prediction_system import PerfectPredictionSystem
        
        # 创建系统实例
        system = PerfectPredictionSystem()
        print("✅ 完美预测系统创建成功")
        
        # 检查系统的主要方法
        print(f"\n📋 检查系统主要方法:")
        
        main_methods = [
            'initialize_modules',
            'run_complete_prediction',
            '_predict_with_modules',
            '_fuse_predictions',
            '_apply_smart_filter',
            '_predict_zodiacs',
            '_generate_final_results'
        ]
        
        for method in main_methods:
            if hasattr(system, method):
                print(f"   ✅ {method}: 存在")
            else:
                print(f"   ❌ {method}: 不存在")
        
        # 初始化系统
        print(f"\n�� 初始化系统模组:")
        try:
            system.initialize_modules()
            print(f"   ✅ 模组初始化成功")
            
            # 检查初始化后的模组状态
            modules = [
                ('traditional_analysis', '传统分析'),
                ('machine_learning', '机器学习'),
                ('zodiac_extended', '生肖扩展'),
                ('special_zodiac_module', '特码生肖'),
                ('fusion_manager', '融合管理器'),
                ('smart_filter', '智能筛选器')
            ]
            
            for attr, name in modules:
                if hasattr(system, attr) and getattr(system, attr) is not None:
                    print(f"   ✅ {name}: 已加载")
                else:
                    print(f"   ❌ {name}: 未加载")
        
        except Exception as e:
            print(f"   ❌ 模组初始化失败: {e}")
        
        # 分析run_complete_prediction方法
        print(f"\n🎯 分析run_complete_prediction方法:")
        
        # 读取源码分析
        try:
            with open("src/perfect_prediction_system.py", "r", encoding="utf-8") as f:
                content = f.read()
            
            # 查找run_complete_prediction方法
            method_start = content.find("def run_complete_prediction")
            if method_start != -1:
                print(f"   ✅ 找到run_complete_prediction方法")
                
                # 提取方法内容
                lines = content[method_start:].split('\n')
                method_lines = []
                indent_level = None
                
                for i, line in enumerate(lines):
                    if i == 0:  # 方法定义行
                        method_lines.append(line)
                        continue
                    
                    # 确定缩进级别
                    if indent_level is None and line.strip():
                        indent_level = len(line) - len(line.lstrip())
                    
                    # 如果遇到同级别或更低级别的定义，停止
                    if line.strip() and not line.startswith(' ' * indent_level) and not line.startswith('\t'):
                        break
                    
                    method_lines.append(line)
                    
                    # 限制输出长度
                    if len(method_lines) > 40:
                        break
                
                print(f"   📊 方法内容分析:")
                for i, line in enumerate(method_lines[:25]):
                    if line.strip():
                        print(f"      {i+1:2d}: {line.strip()}")
            else:
                print(f"   ❌ 未找到run_complete_prediction方法")
        
        except Exception as e:
            print(f"   ❌ 源码分析失败: {e}")
        
        return system
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    system = analyze_perfect_prediction_workflow()
