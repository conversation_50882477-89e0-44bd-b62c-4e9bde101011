"""
历史模拟预测系统 - 对已开奖历史进行回测验证
验证预测模型在历史数据上的准确性和有效性
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple
from datetime import datetime, timedelta
import json
import hashlib
from collections import Counter, defaultdict
from consistent_predictor import ConsistentSpecialNumberPredictor

class HistoricalBacktestSystem:
    """历史模拟预测系统"""
    
    def __init__(self):
        """初始化历史回测系统"""
        self.predictor = ConsistentSpecialNumberPredictor()
        self.backtest_results = []
        
        print("🎯 历史模拟预测系统初始化完成")
        print("📊 功能: 对已开奖历史进行回测验证")

    def get_real_historical_data(self, start_date: str, end_date: str) -> List[Dict]:
        """从数据库获取真实历史数据"""
        import sqlite3

        try:
            conn = sqlite3.connect('data/lottery.db')
            cursor = conn.cursor()

            # 查询指定日期范围的数据
            query = '''
                SELECT draw_date, period_number, regular_1, regular_2, regular_3,
                       regular_4, regular_5, regular_6, special_number
                FROM lottery_results
                WHERE draw_date >= ? AND draw_date <= ?
                ORDER BY draw_date
            '''

            cursor.execute(query, (start_date, end_date))
            rows = cursor.fetchall()

            historical_data = []
            for row in rows:
                draw_date, period_number, r1, r2, r3, r4, r5, r6, special_number = row

                # 转换为系统需要的格式
                regular_numbers = [r1, r2, r3, r4, r5, r6]
                special_zodiac = self.predictor.zodiac_mapping.get(special_number, '未知')

                record = {
                    'draw_date': draw_date,
                    'period_number': period_number,
                    'regular_numbers': regular_numbers,
                    'special_number': special_number,
                    'special_zodiac': special_zodiac,
                    'sum_value': sum(regular_numbers) + special_number
                }
                historical_data.append(record)

            conn.close()
            print(f"✅ 从数据库获取到 {len(historical_data)} 条真实历史数据")
            return historical_data

        except Exception as e:
            print(f"❌ 从数据库获取真实数据失败: {e}")
            return []

    def get_historical_data(self, start_date: str, end_date: str) -> List[Dict]:
        """获取历史数据 (优先使用真实数据，不足时补充模拟数据)"""
        try:
            # 尝试从数据库获取真实数据
            real_data = self.get_real_historical_data(start_date, end_date)

            if len(real_data) >= 1:  # 只要有真实数据就使用
                print(f"✅ 使用真实历史数据: {len(real_data)} 条记录")
                return real_data
            else:
                print(f"⚠️ 没有找到真实数据，使用模拟数据")
                return self.generate_historical_data(start_date, end_date)

        except Exception as e:
            print(f"❌ 获取真实数据失败: {e}，使用模拟数据")
            return self.generate_historical_data(start_date, end_date)

    def generate_historical_data(self, start_date: str, end_date: str) -> List[Dict]:
        """
        生成历史开奖数据（模拟真实历史数据）
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
        """
        print(f"📊 生成历史开奖数据: {start_date} 到 {end_date}")
        
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        historical_data = []
        current_date = start_dt
        period_num = 1
        
        while current_date <= end_dt:
            # 为每个历史日期生成确定性的"真实"开奖结果
            date_str = current_date.strftime('%Y-%m-%d')
            
            # 使用日期作为种子生成确定性的历史开奖结果
            date_seed = int(hashlib.md5(f"historical_{date_str}".encode()).hexdigest()[:8], 16)
            np.random.seed(date_seed % 100000)
            
            # 生成6个正码和1个特码 (确保特码与正码不重复)
            all_numbers = np.random.choice(range(1, 50), size=7, replace=False).tolist()
            regular_numbers = sorted(all_numbers[:6])
            special_number = all_numbers[6]
            
            # 生成生肖
            special_zodiac = self.predictor.zodiac_mapping.get(special_number, '未知')
            
            record = {
                'draw_date': date_str,
                'period_number': f"2024{period_num:03d}",
                'regular_numbers': regular_numbers,
                'special_number': special_number,
                'special_zodiac': special_zodiac,
                'sum_value': sum(regular_numbers) + special_number
            }
            
            historical_data.append(record)
            
            current_date += timedelta(days=1)
            period_num += 1
        
        print(f"✅ 生成了 {len(historical_data)} 期历史开奖数据")
        return historical_data
    
    def run_single_prediction_backtest(self, target_date: str, historical_data: List[Dict]) -> Dict[str, Any]:
        """
        对单个日期进行预测回测
        
        Args:
            target_date: 目标预测日期
            historical_data: 历史数据（用于训练）
        """
        
        # 获取目标日期之前的历史数据作为训练数据
        target_dt = datetime.strptime(target_date, '%Y-%m-%d')
        training_data = []
        
        for record in historical_data:
            record_dt = datetime.strptime(record['draw_date'], '%Y-%m-%d')
            if record_dt < target_dt:
                # 转换为预测器需要的格式
                training_record = {
                    'draw_date': record['draw_date'],
                    'period_number': record['period_number'],
                    'special_number': record['special_number'],
                    'zodiac': record['special_zodiac']
                }
                training_data.append(training_record)
        
        if len(training_data) < 30:  # 至少需要30期历史数据
            return None
        
        # 使用历史数据进行预测
        prediction_result = self.run_prediction_with_historical_data(target_date, training_data)
        
        # 获取实际开奖结果
        actual_result = None
        for record in historical_data:
            if record['draw_date'] == target_date:
                actual_result = record
                break
        
        if actual_result is None:
            return None
        
        # 计算预测准确性
        accuracy_metrics = self.calculate_prediction_accuracy(prediction_result, actual_result)
        
        backtest_result = {
            'target_date': target_date,
            'prediction': prediction_result,
            'actual': actual_result,
            'accuracy': accuracy_metrics,
            'training_data_size': len(training_data)
        }
        
        return backtest_result
    
    def run_prediction_with_historical_data(self, target_date: str, training_data: List[Dict]) -> Dict[str, Any]:
        """使用历史数据进行预测"""

        print(f"   📊 使用 {len(training_data)} 期历史数据进行预测...")

        # 分析历史数据模式
        from special_number_predictor import SpecialNumberPredictor
        temp_predictor = SpecialNumberPredictor()
        analysis = temp_predictor.analyze_special_number_patterns(training_data)

        # 进行确定性预测
        initial_selection = self.predictor.predict_special_numbers_deterministic(analysis, target_date)
        recommended_numbers = self.predictor.cross_validate_deterministic(initial_selection, analysis, target_date)
        top_zodiacs = self.predictor.predict_top_zodiacs_deterministic(analysis, target_date)

        # 生成最终预测
        final_prediction = self.predictor.generate_final_prediction_deterministic(
            recommended_numbers, top_zodiacs, target_date
        )

        print(f"   ✅ 预测完成")

        return final_prediction
    
    def calculate_prediction_accuracy(self, prediction: Dict[str, Any], actual: Dict[str, Any]) -> Dict[str, Any]:
        """计算预测准确性"""
        
        # 提取预测和实际结果
        pred_special_numbers = prediction['special_number_prediction']['final_recommendations']
        pred_zodiacs = [z['zodiac'] for z in prediction['zodiac_prediction']['top_4_zodiacs']]
        pred_fusion = prediction['fusion_prediction']['recommended_numbers']
        
        actual_special = actual['special_number']
        actual_zodiac = actual['special_zodiac']
        actual_regulars = actual['regular_numbers']
        
        # 计算特码预测准确性
        special_hit = actual_special in pred_special_numbers
        special_hit_rank = pred_special_numbers.index(actual_special) + 1 if special_hit else None
        
        # 计算生肖预测准确性
        zodiac_hit = actual_zodiac in pred_zodiacs
        zodiac_hit_rank = pred_zodiacs.index(actual_zodiac) + 1 if zodiac_hit else None
        
        # 计算融合预测准确性
        fusion_special_hit = actual_special in pred_fusion
        fusion_regular_hits = sum(1 for num in actual_regulars if num in pred_fusion)
        
        # 计算号码覆盖率
        all_actual_numbers = actual_regulars + [actual_special]
        fusion_coverage = sum(1 for num in all_actual_numbers if num in pred_fusion)
        coverage_rate = fusion_coverage / len(all_actual_numbers)
        
        accuracy_metrics = {
            'special_number': {
                'hit': special_hit,
                'hit_rank': special_hit_rank,
                'predicted': pred_special_numbers,
                'actual': actual_special
            },
            'zodiac': {
                'hit': zodiac_hit,
                'hit_rank': zodiac_hit_rank,
                'predicted': pred_zodiacs,
                'actual': actual_zodiac
            },
            'fusion': {
                'special_hit': fusion_special_hit,
                'regular_hits': fusion_regular_hits,
                'total_coverage': fusion_coverage,
                'coverage_rate': coverage_rate,
                'predicted': pred_fusion,
                'actual_all': all_actual_numbers
            }
        }
        
        return accuracy_metrics
    
    def run_batch_backtest(self, start_date: str, end_date: str, prediction_window: int = 30) -> List[Dict]:
        """
        批量历史回测
        
        Args:
            start_date: 开始日期
            end_date: 结束日期  
            prediction_window: 预测窗口（需要多少期历史数据进行预测）
        """
        print(f"🚀 开始批量历史回测...")
        print(f"📅 回测期间: {start_date} 到 {end_date}")
        print(f"📊 预测窗口: {prediction_window} 期")
        print("=" * 60)
        
        # 获取历史数据 (优先使用真实数据)
        historical_data = self.get_historical_data(start_date, end_date)
        
        # 确定可以进行预测的日期范围
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        prediction_start_dt = start_dt + timedelta(days=prediction_window)
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        backtest_results = []
        current_date = prediction_start_dt
        
        print(f"📊 开始预测回测 (从 {prediction_start_dt.strftime('%Y-%m-%d')} 开始)...")
        
        while current_date <= end_dt:
            target_date = current_date.strftime('%Y-%m-%d')
            
            print(f"\n🎯 回测日期: {target_date}")
            
            # 进行单期预测回测
            result = self.run_single_prediction_backtest(target_date, historical_data)
            
            if result:
                backtest_results.append(result)
                
                # 显示回测结果
                accuracy = result['accuracy']
                special_hit = "✅" if accuracy['special_number']['hit'] else "❌"
                zodiac_hit = "✅" if accuracy['zodiac']['hit'] else "❌"
                coverage = accuracy['fusion']['coverage_rate']
                
                print(f"   特码预测: {special_hit} (实际: {accuracy['special_number']['actual']})")
                print(f"   生肖预测: {zodiac_hit} (实际: {accuracy['zodiac']['actual']})")
                print(f"   号码覆盖: {coverage:.1%} ({accuracy['fusion']['total_coverage']}/7)")
            
            current_date += timedelta(days=1)
        
        self.backtest_results = backtest_results
        
        print(f"\n✅ 批量回测完成！共回测 {len(backtest_results)} 期")
        return backtest_results
    
    def analyze_backtest_performance(self, results: List[Dict] = None) -> Dict[str, Any]:
        """分析回测性能"""
        
        if results is None:
            results = self.backtest_results
        
        if not results:
            print("❌ 没有回测结果可分析")
            return {}
        
        print("\n📊 回测性能分析...")
        print("=" * 60)
        
        total_tests = len(results)
        
        # 特码预测统计
        special_hits = sum(1 for r in results if r['accuracy']['special_number']['hit'])
        special_accuracy = special_hits / total_tests
        
        # 生肖预测统计
        zodiac_hits = sum(1 for r in results if r['accuracy']['zodiac']['hit'])
        zodiac_accuracy = zodiac_hits / total_tests
        
        # 覆盖率统计
        coverage_rates = [r['accuracy']['fusion']['coverage_rate'] for r in results]
        avg_coverage = np.mean(coverage_rates)
        
        # 排名统计
        special_ranks = [r['accuracy']['special_number']['hit_rank'] for r in results 
                        if r['accuracy']['special_number']['hit_rank'] is not None]
        zodiac_ranks = [r['accuracy']['zodiac']['hit_rank'] for r in results 
                       if r['accuracy']['zodiac']['hit_rank'] is not None]
        
        avg_special_rank = np.mean(special_ranks) if special_ranks else None
        avg_zodiac_rank = np.mean(zodiac_ranks) if zodiac_ranks else None
        
        performance_summary = {
            'total_tests': total_tests,
            'special_number': {
                'hits': special_hits,
                'accuracy': special_accuracy,
                'avg_hit_rank': avg_special_rank
            },
            'zodiac': {
                'hits': zodiac_hits,
                'accuracy': zodiac_accuracy,
                'avg_hit_rank': avg_zodiac_rank
            },
            'coverage': {
                'avg_rate': avg_coverage,
                'rates': coverage_rates
            }
        }
        
        # 显示性能分析结果
        print(f"📊 总回测期数: {total_tests}")
        print(f"\n🎯 特码预测性能:")
        print(f"   命中次数: {special_hits}/{total_tests}")
        print(f"   命中率: {special_accuracy:.1%}")
        if avg_special_rank:
            print(f"   平均命中排名: {avg_special_rank:.1f}")
        
        print(f"\n🐲 生肖预测性能:")
        print(f"   命中次数: {zodiac_hits}/{total_tests}")
        print(f"   命中率: {zodiac_accuracy:.1%}")
        if avg_zodiac_rank:
            print(f"   平均命中排名: {avg_zodiac_rank:.1f}")
        
        print(f"\n🔀 号码覆盖性能:")
        print(f"   平均覆盖率: {avg_coverage:.1%}")
        print(f"   覆盖率范围: {min(coverage_rates):.1%} - {max(coverage_rates):.1%}")
        
        return performance_summary

    def generate_backtest_report(self, results: List[Dict] = None) -> str:
        """生成详细的回测报告"""

        if results is None:
            results = self.backtest_results

        if not results:
            return "没有回测结果可生成报告"

        performance = self.analyze_backtest_performance(results)

        report = []
        report.append("🎯 澳门六合彩历史模拟预测回测报告")
        report.append("=" * 60)

        report.append(f"\n📊 回测概况:")
        report.append(f"   回测期数: {performance['total_tests']} 期")
        report.append(f"   回测日期: {results[0]['target_date']} 到 {results[-1]['target_date']}")

        report.append(f"\n🎯 特码预测表现:")
        special = performance['special_number']
        report.append(f"   命中次数: {special['hits']}/{performance['total_tests']}")
        report.append(f"   命中率: {special['accuracy']:.1%}")
        if special['avg_hit_rank']:
            report.append(f"   平均命中排名: {special['avg_hit_rank']:.1f}")

        report.append(f"\n🐲 生肖预测表现:")
        zodiac = performance['zodiac']
        report.append(f"   命中次数: {zodiac['hits']}/{performance['total_tests']}")
        report.append(f"   命中率: {zodiac['accuracy']:.1%}")
        if zodiac['avg_hit_rank']:
            report.append(f"   平均命中排名: {zodiac['avg_hit_rank']:.1f}")

        report.append(f"\n🔀 号码覆盖表现:")
        coverage = performance['coverage']
        report.append(f"   平均覆盖率: {coverage['avg_rate']:.1%}")
        report.append(f"   最高覆盖率: {max(coverage['rates']):.1%}")
        report.append(f"   最低覆盖率: {min(coverage['rates']):.1%}")

        # 详细结果示例
        report.append(f"\n📋 详细回测结果示例 (前5期):")
        for i, result in enumerate(results[:5]):
            report.append(f"\n   期号 {i+1}: {result['target_date']}")
            acc = result['accuracy']

            special_status = "✅命中" if acc['special_number']['hit'] else "❌未中"
            zodiac_status = "✅命中" if acc['zodiac']['hit'] else "❌未中"

            report.append(f"      特码: {special_status} (预测: {acc['special_number']['predicted'][:3]}..., 实际: {acc['special_number']['actual']})")
            report.append(f"      生肖: {zodiac_status} (预测: {acc['zodiac']['predicted']}, 实际: {acc['zodiac']['actual']})")
            report.append(f"      覆盖: {acc['fusion']['coverage_rate']:.1%} ({acc['fusion']['total_coverage']}/7)")

        report.append(f"\n💡 回测结论:")
        if special['accuracy'] > 0.15:  # 15%以上命中率
            report.append(f"   ✅ 特码预测表现良好，命中率 {special['accuracy']:.1%}")
        else:
            report.append(f"   ⚠️ 特码预测有待改进，命中率 {special['accuracy']:.1%}")

        if zodiac['accuracy'] > 0.25:  # 25%以上命中率
            report.append(f"   ✅ 生肖预测表现优秀，命中率 {zodiac['accuracy']:.1%}")
        else:
            report.append(f"   ⚠️ 生肖预测需要优化，命中率 {zodiac['accuracy']:.1%}")

        if coverage['avg_rate'] > 0.4:  # 40%以上覆盖率
            report.append(f"   ✅ 号码覆盖表现出色，平均覆盖率 {coverage['avg_rate']:.1%}")
        else:
            report.append(f"   ⚠️ 号码覆盖需要提升，平均覆盖率 {coverage['avg_rate']:.1%}")

        report.append(f"\n📊 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("=" * 60)

        return "\n".join(report)

    def save_backtest_results(self, filename: str = None, results: List[Dict] = None):
        """保存回测结果到文件"""

        if results is None:
            results = self.backtest_results

        if not results:
            print("❌ 没有回测结果可保存")
            return

        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"backtest_results_{timestamp}.json"

        # 保存详细结果
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)

        # 保存报告
        report_filename = filename.replace('.json', '_report.txt')
        report = self.generate_backtest_report(results)

        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"✅ 回测结果已保存:")
        print(f"   详细数据: {filename}")
        print(f"   分析报告: {report_filename}")

def main():
    """主函数 - 演示历史模拟预测功能"""
    print("🎯 澳门六合彩历史模拟预测系统")
    print("功能: 对已开奖历史进行回测验证")
    print()

    # 创建回测系统
    backtest_system = HistoricalBacktestSystem()

    # 设置回测参数
    start_date = "2024-01-01"
    end_date = "2024-01-31"  # 回测一个月的数据

    print(f"🎯 回测设置:")
    print(f"   开始日期: {start_date}")
    print(f"   结束日期: {end_date}")
    print(f"   预测窗口: 30期历史数据")
    print()

    # 运行批量回测
    results = backtest_system.run_batch_backtest(start_date, end_date, prediction_window=30)

    # 分析回测性能
    performance = backtest_system.analyze_backtest_performance(results)

    # 生成并显示报告
    report = backtest_system.generate_backtest_report(results)
    print("\n" + report)

    # 保存结果
    backtest_system.save_backtest_results()

    return results, performance

# 为了兼容性，创建别名
HistoricalBacktest = HistoricalBacktestSystem

if __name__ == "__main__":
    main()
