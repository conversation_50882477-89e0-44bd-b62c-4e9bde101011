"""
澳门六合彩智能预测系统 - 源码版本打包脚本
包含完整源码、开发文档、测试文件等
"""
import os
import shutil
import zipfile
import json
from datetime import datetime
import subprocess
import sys

class SourceCodePackager:
    """源码版本打包器"""
    
    def __init__(self):
        self.version = "1.0.0"
        self.build_date = datetime.now().strftime("%Y%m%d")
        self.project_name = "澳门六合彩智能预测系统"
        self.base_dir = os.getcwd()
        self.dist_dir = os.path.join(self.base_dir, "dist")
        
        # 源码核心文件
        self.source_files = [
            "lottery_prediction_gui.py",
            "main.py",
            "requirements.txt",
            "package_builder.py"
        ]
        
        # 源码目录
        self.source_dirs = [
            "src",
            "data", 
            "docs",
            "tests",
            "logs"
        ]
        
        # 开发相关文件
        self.dev_files = [
            "*.py",  # 所有Python文件
            "*.md",  # 所有Markdown文件
            "*.txt", # 所有文本文件
            "*.json", # 所有JSON文件
            "*.ini",  # 配置文件
            "*.bat",  # 批处理文件
            "*.sh",   # Shell脚本
        ]
        
        # 排除的文件和目录
        self.exclude_patterns = [
            "__pycache__",
            "*.pyc",
            "*.pyo",
            ".git",
            ".vscode",
            ".idea",
            "*.log",
            "dist",
            ".venv",
            "venv",
            "env"
        ]
    
    def should_include_file(self, file_path):
        """判断文件是否应该包含"""
        file_name = os.path.basename(file_path)
        
        # 检查排除模式
        for pattern in self.exclude_patterns:
            if pattern.startswith("*"):
                if file_name.endswith(pattern[1:]):
                    return False
            elif pattern in file_path:
                return False
        
        return True
    
    def copy_all_source_files(self, target_dir):
        """复制所有源码文件"""
        print(f"📁 复制源码文件到: {target_dir}")
        
        copied_count = 0
        
        # 遍历当前目录的所有文件
        for root, dirs, files in os.walk(self.base_dir):
            # 过滤目录
            dirs[:] = [d for d in dirs if self.should_include_file(os.path.join(root, d))]
            
            for file in files:
                source_path = os.path.join(root, file)
                
                # 检查是否应该包含此文件
                if not self.should_include_file(source_path):
                    continue
                
                # 计算相对路径
                rel_path = os.path.relpath(source_path, self.base_dir)
                target_path = os.path.join(target_dir, rel_path)
                
                # 创建目标目录
                target_file_dir = os.path.dirname(target_path)
                if not os.path.exists(target_file_dir):
                    os.makedirs(target_file_dir)
                
                # 复制文件
                try:
                    shutil.copy2(source_path, target_path)
                    copied_count += 1
                    
                    # 显示重要文件
                    if file.endswith(('.py', '.md', '.txt', '.json')):
                        print(f"   ✅ {rel_path}")
                except Exception as e:
                    print(f"   ❌ {rel_path}: {e}")
        
        print(f"   📊 总计复制 {copied_count} 个文件")
    
    def create_development_scripts(self, target_dir):
        """创建开发相关脚本"""
        print(f"🔧 创建开发脚本...")
        
        # 开发环境启动脚本
        dev_start_script = """@echo off
chcp 65001 > nul
title 澳门六合彩智能预测系统 - 源码开发版
echo 🎯 澳门六合彩智能预测系统 - 源码开发版
echo ==========================================
echo 正在检查开发环境...

python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python环境
    echo 💡 请先安装Python 3.7+
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo 正在安装开发依赖...
pip install -r requirements.txt
if exist requirements-dev.txt (
    pip install -r requirements-dev.txt
)

echo 正在启动开发版本...
python lottery_prediction_gui.py --dev
if errorlevel 1 (
    echo.
    echo ❌ 启动失败，请检查错误信息
    pause
)
"""
        
        with open(os.path.join(target_dir, "启动开发版.bat"), "w", encoding="utf-8") as f:
            f.write(dev_start_script)
        
        # 测试运行脚本
        test_script = """@echo off
chcp 65001 > nul
title 运行测试 - 澳门六合彩智能预测系统
echo 🧪 运行系统测试
echo ==========================================
echo 正在检查测试环境...

python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python环境
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo 正在安装测试依赖...
pip install pytest pytest-qt

echo 正在运行测试...
if exist tests (
    python -m pytest tests/ -v
) else (
    echo ⚠️ 未找到tests目录，运行基本功能测试
    python -c "import src.perfect_prediction_system; print('✅ 核心模块导入成功')"
)

echo.
echo ✅ 测试完成
pause
"""
        
        with open(os.path.join(target_dir, "运行测试.bat"), "w", encoding="utf-8") as f:
            f.write(test_script)
        
        # 代码检查脚本
        lint_script = """@echo off
chcp 65001 > nul
title 代码检查 - 澳门六合彩智能预测系统
echo 🔍 代码质量检查
echo ==========================================

echo 正在安装代码检查工具...
pip install flake8 pylint black

echo 正在检查代码风格...
echo.
echo 📋 Flake8 检查结果:
flake8 src/ --max-line-length=100 --ignore=E203,W503

echo.
echo 📋 Pylint 检查结果:
pylint src/ --disable=C0103,R0903,R0913

echo.
echo ✅ 代码检查完成
pause
"""
        
        with open(os.path.join(target_dir, "代码检查.bat"), "w", encoding="utf-8") as f:
            f.write(lint_script)
        
        print(f"   ✅ 启动开发版.bat")
        print(f"   ✅ 运行测试.bat")
        print(f"   ✅ 代码检查.bat")
    
    def create_development_requirements(self, target_dir):
        """创建开发依赖文件"""
        print(f"📋 创建开发依赖文件...")
        
        dev_requirements = """# 开发环境依赖
# 测试框架
pytest>=6.0.0
pytest-qt>=4.0.0
pytest-cov>=2.10.0

# 代码质量
flake8>=3.8.0
pylint>=2.6.0
black>=20.8b1
isort>=5.6.0

# 文档生成
sphinx>=3.0.0
sphinx-rtd-theme>=0.5.0

# 开发工具
ipython>=7.0.0
jupyter>=1.0.0
notebook>=6.0.0

# 调试工具
pdb++>=0.10.0
ipdb>=0.13.0

# 性能分析
memory-profiler>=0.58.0
line-profiler>=3.0.0

# 类型检查
mypy>=0.800
"""
        
        with open(os.path.join(target_dir, "requirements-dev.txt"), "w", encoding="utf-8") as f:
            f.write(dev_requirements)
        
        print(f"   ✅ requirements-dev.txt")
    
    def create_source_readme(self, target_dir):
        """创建源码版README"""
        print(f"📄 创建源码版README...")
        
        readme_content = f"""# 🎯 澳门六合彩智能预测系统 - 源码版

## 📦 版本信息
- **版本**: {self.version}
- **构建日期**: {self.build_date}
- **类型**: 源码版 (Source Code)
- **开发语言**: Python 3.7+

## 🚀 快速开始

### 开发环境设置
1. 确保已安装Python 3.7+
2. 克隆或解压源码到本地
3. 安装依赖: `pip install -r requirements.txt`
4. 安装开发依赖: `pip install -r requirements-dev.txt`
5. 运行系统: `python lottery_prediction_gui.py`

### 一键启动 (Windows)
1. 双击运行 `启动开发版.bat`
2. 系统将自动安装依赖并启动

## 📁 项目结构

```
澳门六合彩预测系统/
├── 📄 主程序文件
│   ├── lottery_prediction_gui.py    # 主GUI程序
│   ├── main.py                      # 程序入口
│   └── package_builder.py           # 打包脚本
├── 📂 核心源码 (src/)
│   ├── perfect_prediction_system.py # 完美预测系统
│   ├── fusion_manager.py           # 融合管理器
│   ├── special_number_predictor.py # 特码预测器
│   ├── special_zodiac_analyzer.py  # 特码生肖分析器
│   ├── consistency_validator.py    # 一致性验证器
│   └── ... (30+ 核心模块)
├── 📂 数据目录 (data/)
│   ├── historical_data.db          # 历史数据库
│   ├── sample_data.csv             # 示例数据
│   └── zodiac_mappings.json        # 生肖映射
├── 📂 文档目录 (docs/)
│   ├── API文档/
│   ├── 用户手册/
│   └── 开发指南/
├── 📂 测试目录 (tests/)
│   ├── test_prediction.py          # 预测功能测试
│   ├── test_gui.py                 # GUI测试
│   └── test_data.py                # 数据测试
├── 📂 日志目录 (logs/)
├── 📋 配置文件
│   ├── requirements.txt            # 运行依赖
│   ├── requirements-dev.txt        # 开发依赖
│   ├── config.json                 # 系统配置
│   └── settings.ini                # 用户设置
└── 📝 开发脚本
    ├── 启动开发版.bat              # 开发环境启动
    ├── 运行测试.bat                # 测试运行
    └── 代码检查.bat                # 代码质量检查
```

## 🔧 开发环境

### 系统要求
- **操作系统**: Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **Python**: 3.7 或更高版本
- **内存**: 4GB 可用内存 (开发环境)
- **磁盘**: 1GB 可用空间

### 依赖包
#### 运行依赖
- PyQt5 (GUI界面)
- numpy (数值计算)
- pandas (数据处理)
- scikit-learn (机器学习)
- matplotlib (图表显示)
- sqlite3 (数据库)

#### 开发依赖
- pytest (测试框架)
- flake8 (代码检查)
- pylint (代码分析)
- black (代码格式化)
- sphinx (文档生成)

## 🏗️ 架构设计

### 核心模块
1. **完美预测系统** (`perfect_prediction_system.py`)
   - 主要预测引擎
   - 多模组融合
   - 智能优化

2. **融合管理器** (`fusion_manager.py`)
   - 模组权重管理
   - 动态融合策略
   - 结果优化

3. **特码预测器** (`special_number_predictor.py`)
   - 传统分析算法
   - 机器学习模型
   - 生肖扩展分析

4. **GUI界面** (`lottery_prediction_gui.py`)
   - PyQt5界面
   - 多标签页设计
   - 实时进度显示

### 数据流
```
历史数据 → 特征提取 → 模型训练 → 预测生成 → 结果融合 → 智能筛选 → 最终输出
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试
python -m pytest tests/test_prediction.py -v

# 运行覆盖率测试
python -m pytest tests/ --cov=src --cov-report=html
```

### 测试覆盖
- 单元测试: 核心算法测试
- 集成测试: 模组交互测试
- GUI测试: 界面功能测试
- 性能测试: 算法性能测试

## 🔍 代码质量

### 代码检查
```bash
# 代码风格检查
flake8 src/ --max-line-length=100

# 代码质量分析
pylint src/

# 代码格式化
black src/
```

### 编码规范
- 遵循PEP 8代码风格
- 使用类型注解
- 完整的文档字符串
- 单元测试覆盖

## 📚 开发指南

### 添加新功能
1. 在`src/`目录创建新模块
2. 编写单元测试
3. 更新文档
4. 集成到主系统

### 修改预测算法
1. 修改相应的预测器模块
2. 更新融合权重
3. 运行回测验证
4. 更新性能指标

### GUI界面修改
1. 修改`lottery_prediction_gui.py`
2. 更新界面布局
3. 测试用户交互
4. 更新用户文档

## 🐛 调试

### 日志系统
- 日志文件位置: `logs/`
- 日志级别: DEBUG, INFO, WARNING, ERROR
- 实时日志查看: GUI界面日志标签页

### 常见问题
1. **导入错误**: 检查Python路径和依赖
2. **GUI显示问题**: 检查PyQt5安装
3. **预测错误**: 检查数据完整性
4. **性能问题**: 使用性能分析工具

## 📖 API文档

### 核心类
- `PerfectPredictionSystem`: 主预测系统
- `FusionManager`: 融合管理器
- `SpecialNumberPredictor`: 特码预测器
- `ConsistencyValidator`: 一致性验证器

### 主要方法
- `run_complete_prediction()`: 运行完整预测
- `validate_consistency()`: 验证一致性
- `run_backtest()`: 运行回测
- `export_results()`: 导出结果

## 🤝 贡献指南

### 开发流程
1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request

### 代码提交
- 清晰的提交信息
- 完整的测试覆盖
- 更新相关文档
- 遵循编码规范

## 📄 许可证
本项目遵循开源许可证，详见LICENSE文件。

## ⚠️ 免责声明
- 本软件仅供学习和研究使用
- 预测结果仅供参考，不构成投注建议
- 使用者需自行承担使用风险
- 严禁用于非法用途

## 📞 技术支持
- 查看文档: `docs/` 目录
- 查看日志: `logs/` 目录
- 运行测试: `python -m pytest tests/`
- 代码检查: `flake8 src/`

---

**欢迎参与澳门六合彩智能预测系统的开发！**
"""
        
        with open(os.path.join(target_dir, "README.md"), "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        print(f"   ✅ README.md")
    
    def create_development_config(self, target_dir):
        """创建开发配置文件"""
        print(f"⚙️ 创建开发配置文件...")
        
        # 开发配置
        dev_config = {
            "development": {
                "debug": True,
                "log_level": "DEBUG",
                "auto_reload": True,
                "test_mode": True
            },
            "database": {
                "path": "data/historical_data.db",
                "backup_enabled": True,
                "backup_interval": 3600
            },
            "logging": {
                "file_path": "logs/development.log",
                "max_size": "10MB",
                "backup_count": 5
            },
            "gui": {
                "theme": "default",
                "window_size": [1200, 800],
                "auto_save": True
            }
        }
        
        with open(os.path.join(target_dir, "config-dev.json"), "w", encoding="utf-8") as f:
            json.dump(dev_config, f, ensure_ascii=False, indent=2)
        
        # Git忽略文件
        gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyQt
*.ui

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# 日志
*.log
logs/*.log

# 数据库
*.db
*.sqlite
*.sqlite3

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# 测试
.coverage
htmlcov/
.pytest_cache/
.tox/

# 文档
docs/_build/
"""
        
        with open(os.path.join(target_dir, ".gitignore"), "w", encoding="utf-8") as f:
            f.write(gitignore_content)
        
        print(f"   ✅ config-dev.json")
        print(f"   ✅ .gitignore")
    
    def create_source_version_info(self, target_dir):
        """创建源码版本信息"""
        version_info = {
            "name": self.project_name,
            "version": self.version,
            "build_date": self.build_date,
            "package_type": "source_code",
            "python_version": "3.7+",
            "platform": "Cross-platform",
            "development": {
                "framework": "PyQt5",
                "testing": "pytest",
                "linting": "flake8, pylint",
                "formatting": "black",
                "documentation": "sphinx"
            },
            "features": [
                "完整源码",
                "开发环境",
                "测试框架",
                "代码检查",
                "文档生成",
                "调试工具"
            ],
            "modules": [
                "perfect_prediction_system",
                "fusion_manager",
                "special_number_predictor",
                "special_zodiac_analyzer",
                "consistency_validator",
                "gui_interface"
            ]
        }
        
        with open(os.path.join(target_dir, "version.json"), "w", encoding="utf-8") as f:
            json.dump(version_info, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ version.json")
    
    def create_source_package(self):
        """创建源码版本包"""
        print(f"\n💻 创建源码版本...")
        package_name = f"澳门六合彩预测系统_源码版_v{self.version}_{self.build_date}"
        package_dir = os.path.join(self.dist_dir, package_name)
        
        # 创建目录
        if os.path.exists(package_dir):
            shutil.rmtree(package_dir)
        os.makedirs(package_dir)
        
        # 复制所有源码文件
        self.copy_all_source_files(package_dir)
        
        # 创建开发脚本
        self.create_development_scripts(package_dir)
        
        # 创建开发依赖
        self.create_development_requirements(package_dir)
        
        # 创建源码README
        self.create_source_readme(package_dir)
        
        # 创建开发配置
        self.create_development_config(package_dir)
        
        # 创建版本信息
        self.create_source_version_info(package_dir)
        
        # 创建ZIP包
        zip_path = f"{package_dir}.zip"
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(package_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, self.dist_dir)
                    zipf.write(file_path, arc_path)
        
        print(f"✅ 源码版创建完成: {zip_path}")
        return zip_path
    
    def package_source(self):
        """打包源码版本"""
        print(f"💻 {self.project_name} - 源码版本打包")
        print(f"版本: {self.version}")
        print(f"构建日期: {self.build_date}")
        print("=" * 60)
        
        # 确保发行目录存在
        if not os.path.exists(self.dist_dir):
            os.makedirs(self.dist_dir)
        
        # 创建源码版本
        package_path = self.create_source_package()
        
        # 显示总结
        size = os.path.getsize(package_path) / (1024 * 1024)
        print(f"\n🎊 源码版本打包完成!")
        print(f"📦 源码包: {os.path.basename(package_path)} ({size:.1f} MB)")
        print(f"📁 发行目录: {self.dist_dir}")
        
        return package_path

def main():
    """主函数"""
    try:
        packager = SourceCodePackager()
        package_path = packager.package_source()
        
        print(f"\n✅ 源码版本创建成功!")
        print(f"💡 开发者可以使用此包进行二次开发")
        
    except Exception as e:
        print(f"❌ 打包失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
