"""
CSV/XLSX数据加载器
"""
import pandas as pd
from pathlib import Path
from loguru import logger

class CSVLoader:
    def __init__(self):
        self.supported_formats = ['.csv', '.xlsx', '.xls']
        self.encoding_options = ['utf-8', 'gbk', 'gb2312']
    
    def load_lottery_data(self, file_path: str):
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.error(f"文件不存在: {file_path}")
                return None
            
            if file_path.suffix.lower() == '.csv':
                return self._load_csv(file_path)
            else:
                return self._load_excel(file_path)
        except Exception as e:
            logger.error(f"加载文件失败: {e}")
            return None
    
    def _load_csv(self, file_path):
        for encoding in self.encoding_options:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                logger.info(f"成功加载CSV: {file_path}")
                return self._standardize_columns(df)
            except UnicodeDecodeError:
                continue
        return None
    
    def _load_excel(self, file_path):
        try:
            df = pd.read_excel(file_path)
            logger.info(f"成功加载Excel: {file_path}")
            return self._standardize_columns(df)
        except Exception as e:
            logger.error(f"Excel加载失败: {e}")
            return None
    
    def _standardize_columns(self, df):
        column_mapping = {
            'issue': ['期号', 'period', 'issue_number'],
            'date': ['日期', 'draw_date', 'date'],
            'numbers': ['正码', 'regular_numbers', 'numbers'],
            'special': ['特码', 'special_number', 'special']
        }
        
        for standard_name, possible_names in column_mapping.items():
            for col in df.columns:
                if col.lower() in [name.lower() for name in possible_names]:
                    df = df.rename(columns={col: standard_name})
                    break
        return df
