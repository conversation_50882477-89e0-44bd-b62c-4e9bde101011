"""
六合彩预测系统多版本封装发布工具 - 修复版
创建便携版、源码版和Windows版发布包
"""

import os
import shutil
import zipfile
import json
import subprocess
import sys
from datetime import datetime
from pathlib import Path

class LotterySystemPackager:
    """六合彩预测系统打包器"""
    
    def __init__(self):
        self.version = "v2.0.0"
        self.release_date = datetime.now().strftime("%Y%m%d")
        self.base_name = f"LotteryPredictionSystem_{self.version}_{self.release_date}"
        
        # 创建发布目录
        self.release_dir = Path("releases")
        self.release_dir.mkdir(exist_ok=True)
        
        print(f"🎊 六合彩预测系统多版本封装工具")
        print(f"📦 版本: {self.version}")
        print(f"📅 发布日期: {self.release_date}")
        print(f"📁 发布目录: {self.release_dir}")
    
    def create_all_packages(self):
        """创建所有版本的发布包"""
        print("\n🚀 开始创建多版本发布包...")
        
        try:
            # 1. 创建便携版
            portable_success = self.create_portable_version()
            
            # 2. 创建源码版
            source_success = self.create_source_version()
            
            # 3. 创建Windows版
            windows_success = self.create_windows_version()
            
            # 总结
            self.print_summary(portable_success, source_success, windows_success)
            
        except Exception as e:
            print(f"❌ 封装过程出错: {e}")
    
    def create_portable_version(self):
        """创建便携版"""
        print("\n📦 创建便携版...")
        print("-" * 50)
        
        try:
            portable_name = f"{self.base_name}_Portable"
            portable_dir = self.release_dir / portable_name
            
            # 清理旧版本
            if portable_dir.exists():
                shutil.rmtree(portable_dir)
            portable_dir.mkdir()
            
            # 复制核心文件
            self._copy_core_files(portable_dir)
            
            # 创建启动脚本
            self._create_portable_launcher(portable_dir)
            
            # 创建配置文件
            self._create_portable_config(portable_dir)
            
            # 创建说明文档
            self._create_portable_readme(portable_dir)
            
            # 打包为ZIP
            zip_path = self.release_dir / f"{portable_name}.zip"
            self._create_zip(portable_dir, zip_path)
            
            print(f"✅ 便携版创建成功: {zip_path}")
            return True
            
        except Exception as e:
            print(f"❌ 便携版创建失败: {e}")
            return False
    
    def create_source_version(self):
        """创建源码版"""
        print("\n📦 创建源码版...")
        print("-" * 50)
        
        try:
            source_name = f"{self.base_name}_Source"
            source_dir = self.release_dir / source_name
            
            # 清理旧版本
            if source_dir.exists():
                shutil.rmtree(source_dir)
            source_dir.mkdir()
            
            # 复制核心源码文件
            self._copy_essential_source_files(source_dir)
            
            # 创建requirements.txt
            self._create_requirements_file(source_dir)
            
            # 创建安装脚本
            self._create_install_scripts(source_dir)
            
            # 创建开发文档
            self._create_development_docs(source_dir)
            
            # 打包为ZIP
            zip_path = self.release_dir / f"{source_name}.zip"
            self._create_zip(source_dir, zip_path)
            
            print(f"✅ 源码版创建成功: {zip_path}")
            return True
            
        except Exception as e:
            print(f"❌ 源码版创建失败: {e}")
            return False
    
    def create_windows_version(self):
        """创建Windows版"""
        print("\n📦 创建Windows版...")
        print("-" * 50)
        
        try:
            windows_name = f"{self.base_name}_Windows"
            windows_dir = self.release_dir / windows_name
            
            # 清理旧版本
            if windows_dir.exists():
                shutil.rmtree(windows_dir)
            windows_dir.mkdir()
            
            # 复制核心文件
            self._copy_core_files(windows_dir)
            
            # 创建Windows启动器
            self._create_windows_launcher(windows_dir)
            
            # 创建Windows说明
            self._create_windows_readme(windows_dir)
            
            # 打包为ZIP
            zip_path = self.release_dir / f"{windows_name}.zip"
            self._create_zip(windows_dir, zip_path)
            
            print(f"✅ Windows版创建成功: {zip_path}")
            return True
            
        except Exception as e:
            print(f"❌ Windows版创建失败: {e}")
            return False
    
    def _copy_core_files(self, target_dir):
        """复制核心文件"""
        print("📁 复制核心文件...")
        
        core_files = [
            "lottery_prediction_gui.py",
            "consistent_predictor.py", 
            "special_number_predictor.py"
        ]
        
        for file_name in core_files:
            if os.path.exists(file_name):
                shutil.copy2(file_name, target_dir)
                print(f"  ✅ {file_name}")
        
        # 复制src目录
        if os.path.exists("src"):
            shutil.copytree("src", target_dir / "src")
            print(f"  ✅ src/")
        
        # 复制data目录
        if os.path.exists("data"):
            shutil.copytree("data", target_dir / "data")
            print(f"  ✅ data/")
    
    def _copy_essential_source_files(self, target_dir):
        """复制核心源码文件"""
        print("📁 复制核心源码文件...")
        
        # 核心Python文件
        core_files = [
            "lottery_prediction_gui.py",
            "consistent_predictor.py",
            "special_number_predictor.py",
            "enhanced_backtest.py",
            "comprehensive_evaluation.py"
        ]
        
        for file_name in core_files:
            if os.path.exists(file_name):
                shutil.copy2(file_name, target_dir)
                print(f"  ✅ {file_name}")
        
        # 复制src目录
        if os.path.exists("src"):
            shutil.copytree("src", target_dir / "src")
            print(f"  ✅ src/")
        
        # 复制data目录
        if os.path.exists("data"):
            shutil.copytree("data", target_dir / "data")
            print(f"  ✅ data/")
        
        # 复制配置文件
        config_files = ["*.json", "*.txt", "*.md"]
        for pattern in config_files:
            for file_path in Path(".").glob(pattern):
                if file_path.name not in ["version_info.json"]:
                    try:
                        shutil.copy2(file_path, target_dir)
                        print(f"  ✅ {file_path.name}")
                    except:
                        pass
    
    def _create_portable_launcher(self, target_dir):
        """创建便携版启动脚本"""
        print("🚀 创建启动脚本...")
        
        # Windows批处理文件 - 使用ASCII编码
        bat_content = f'''@echo off
chcp 65001 > nul
title Lottery Prediction System {self.version}

echo Lottery Prediction System {self.version}
echo ==========================================
echo Starting system...

REM Check Python environment
python --version > nul 2>&1
if errorlevel 1 (
    echo Error: Python not found
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

REM Install dependencies
echo Installing dependencies...
pip install PyQt5 numpy pandas scikit-learn --quiet

REM Start program
echo Starting GUI...
python lottery_prediction_gui.py

pause
'''
        
        with open(target_dir / "start_system.bat", "w", encoding="ascii", errors="ignore") as f:
            f.write(bat_content)
        
        print("  ✅ 启动脚本已创建")
    
    def _create_portable_config(self, target_dir):
        """创建便携版配置"""
        print("⚙️ 创建配置文件...")
        
        config = {
            "version": self.version,
            "release_date": self.release_date,
            "package_type": "portable",
            "database": {
                "path": "data/lottery.db",
                "auto_backup": True
            },
            "gui": {
                "theme": "default",
                "language": "zh_CN"
            }
        }
        
        with open(target_dir / "config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print("  ✅ 配置文件已创建")
    
    def _create_portable_readme(self, target_dir):
        """创建便携版说明文档"""
        print("📖 创建说明文档...")
        
        readme_content = f'''# Lottery Prediction System {self.version} - Portable Version

## Version Information
- Version: {self.version}
- Release Date: {self.release_date}
- Package Type: Portable (No Installation Required)

## Quick Start

### Windows Users
1. Double-click `start_system.bat`
2. Wait for dependencies installation
3. GUI will start automatically

## System Requirements
- Python 3.8 or higher
- At least 2GB available memory
- At least 500MB available disk space

## Main Features
- Special Number Prediction (16 recommended numbers)
- Consistency Prediction (deterministic algorithm)
- Perfect Prediction System (4-module fusion)
- Historical Backtest Analysis
- Enhanced Backtest Optimization
- Comprehensive Evaluation System
- Data Management Functions

## Usage Instructions
1. **Data Import**: Import historical lottery data in "Data Management"
2. **Special Prediction**: Use "Special Number Prediction" for recommendations
3. **Perfect Prediction**: Use "Perfect Prediction" for multi-module fusion results
4. **Backtest Analysis**: Use "Historical Backtest" to verify prediction effectiveness
5. **Save Results**: Prediction results are automatically saved to TXT files

## Notes
- First run requires internet connection for dependency installation
- Recommend regular backup of data directory
- Prediction results are for reference only

---
© 2025 Lottery Prediction System {self.version} - Portable Version
'''
        
        with open(target_dir / "README.md", "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        print("  ✅ 说明文档已创建")
    
    def _create_requirements_file(self, target_dir):
        """创建requirements.txt文件"""
        print("📦 创建requirements.txt...")
        
        requirements = [
            "PyQt5>=5.15.0",
            "numpy>=1.21.0", 
            "pandas>=1.3.0",
            "scikit-learn>=1.0.0",
            "matplotlib>=3.5.0"
        ]
        
        with open(target_dir / "requirements.txt", "w", encoding="utf-8") as f:
            f.write("\n".join(requirements))
        
        print(f"  ✅ requirements.txt已创建 ({len(requirements)}个包)")
    
    def _create_install_scripts(self, target_dir):
        """创建安装脚本"""
        print("🔧 创建安装脚本...")
        
        # Windows安装脚本
        install_bat = f'''@echo off
chcp 65001 > nul
title Lottery Prediction System {self.version} - Source Installation

echo Lottery Prediction System {self.version} - Source Version
echo ==========================================
echo Installing system...

REM Check Python environment
python --version > nul 2>&1
if errorlevel 1 (
    echo Error: Python not found
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt

REM Create data directory
if not exist "data" mkdir data

echo Installation complete!
echo Run: python lottery_prediction_gui.py
pause
'''
        
        with open(target_dir / "install.bat", "w", encoding="ascii", errors="ignore") as f:
            f.write(install_bat)
        
        print("  ✅ 安装脚本已创建")
    
    def _create_development_docs(self, target_dir):
        """创建开发文档"""
        print("📖 创建开发文档...")
        
        dev_readme = f'''# Lottery Prediction System {self.version} - Source Version

## Version Information
- Version: {self.version}
- Release Date: {self.release_date}
- Package Type: Source Version (Developer Edition)

## Installation

### Automatic Installation (Recommended)
#### Windows
```bash
install.bat
```

### Manual Installation
1. Install Python 3.8+
2. Install dependencies: `pip install -r requirements.txt`
3. Create data directory: `mkdir data`
4. Run program: `python lottery_prediction_gui.py`

## Project Structure
```
LotteryPredictionSystem_{self.version}_{self.release_date}_Source/
├── install.bat                    # Windows installation script
├── requirements.txt               # Python dependencies
├── README.md                      # Documentation
├── lottery_prediction_gui.py      # Main GUI program
├── consistent_predictor.py        # Consistency predictor
├── special_number_predictor.py    # Special number predictor
├── enhanced_backtest.py           # Enhanced backtest
├── comprehensive_evaluation.py    # Comprehensive evaluation
├── src/                          # Core source code
└── data/                         # Data directory
```

## Development Environment
- Python 3.8+
- PyQt5 5.15+
- NumPy 1.21+
- Pandas 1.3+
- Scikit-learn 1.0+

## Core Modules

### 1. GUI Interface (`lottery_prediction_gui.py`)
- Main interface controller
- Multi-page management
- User interaction handling

### 2. Prediction System (`src/perfect_prediction_system.py`)
- 4 major analysis modules integration
- Dynamic fusion management
- Result optimization processing

### 3. Machine Learning (`src/algorithm_layer/`)
- 5 ML algorithms integration
- Feature engineering system
- Model training and prediction

### 4. Independent Modules (`src/independent_modules/`)
- Traditional statistical analysis
- Zodiac dimension analysis
- Special number analysis

---
© 2025 Lottery Prediction System {self.version} - Source Version
'''
        
        with open(target_dir / "README.md", "w", encoding="utf-8") as f:
            f.write(dev_readme)
        
        print("  ✅ 开发文档已创建")
    
    def _create_windows_launcher(self, target_dir):
        """创建Windows启动器"""
        print("🚀 创建Windows启动器...")
        
        launcher_content = f'''@echo off
chcp 65001 > nul
title Lottery Prediction System {self.version}

echo Lottery Prediction System {self.version} - Windows Version
echo ==========================================

REM Check Python
python --version > nul 2>&1
if errorlevel 1 (
    echo Error: Python not found
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

REM Install dependencies
echo Installing dependencies...
pip install PyQt5 numpy pandas scikit-learn --quiet

REM Start system
echo Starting system...
python lottery_prediction_gui.py

pause
'''
        
        with open(target_dir / "start_system.bat", "w", encoding="ascii", errors="ignore") as f:
            f.write(launcher_content)
        
        print("  ✅ Windows启动器已创建")
    
    def _create_windows_readme(self, target_dir):
        """创建Windows版说明"""
        print("📖 创建Windows版说明...")
        
        readme_content = f'''# Lottery Prediction System {self.version} - Windows Version

## Version Information
- Version: {self.version}
- Release Date: {self.release_date}
- Package Type: Windows Version

## Quick Start
1. Extract to any directory
2. Double-click `start_system.bat`
3. GUI will start automatically

## System Requirements
- Windows 7/8/10/11 (64-bit)
- At least 2GB available memory
- At least 500MB available disk space
- Python 3.8+ (will be installed automatically)

## Main Features
- Special Number Prediction (16 recommended numbers)
- Consistency Prediction (deterministic algorithm)
- Perfect Prediction System (4-module fusion)
- Historical Backtest Analysis
- Enhanced Backtest Optimization
- Comprehensive Evaluation System
- Data Management Functions

## File Description
- `start_system.bat` - Startup script
- `lottery_prediction_gui.py` - Main program
- `src/` - Source code directory
- `data/` - Data directory
- `README.md` - Documentation

## Usage Instructions
1. **First Run**: System will automatically install dependencies
2. **Data Import**: Import historical lottery data in "Data Management"
3. **Start Prediction**: Select prediction function to begin
4. **View Results**: Prediction results are automatically saved

## Notes
- Recommend regular backup of data directory
- Prediction results are for reference only
- Restart program if encountering issues

---
© 2025 Lottery Prediction System {self.version} - Windows Version
'''
        
        with open(target_dir / "README.md", "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        print("  ✅ Windows版说明已创建")
    
    def _create_zip(self, source_dir, zip_path):
        """创建ZIP压缩包"""
        print(f"📦 创建压缩包: {zip_path.name}")
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in source_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(source_dir.parent)
                    zipf.write(file_path, arcname)
        
        # 获取文件大小
        size_mb = zip_path.stat().st_size / (1024 * 1024)
        print(f"  ✅ 压缩包已创建 ({size_mb:.1f}MB)")
    
    def print_summary(self, portable_success, source_success, windows_success):
        """打印总结信息"""
        print("\n" + "="*60)
        print("🎊 多版本封装完成总结")
        print("="*60)
        
        print(f"📦 版本: {self.version}")
        print(f"📅 发布日期: {self.release_date}")
        print(f"📁 发布目录: {self.release_dir}")
        
        print(f"\n📋 封装结果:")
        print(f"  便携版: {'✅ 成功' if portable_success else '❌ 失败'}")
        print(f"  源码版: {'✅ 成功' if source_success else '❌ 失败'}")
        print(f"  Windows版: {'✅ 成功' if windows_success else '❌ 失败'}")
        
        success_count = sum([portable_success, source_success, windows_success])
        print(f"\n🎯 成功率: {success_count}/3 ({success_count/3*100:.0f}%)")
        
        if success_count > 0:
            print(f"\n📁 发布文件:")
            for zip_file in self.release_dir.glob("*.zip"):
                size_mb = zip_file.stat().st_size / (1024 * 1024)
                print(f"  📦 {zip_file.name} ({size_mb:.1f}MB)")
        
        print("="*60)

def main():
    """主函数"""
    print("🎊 六合彩预测系统多版本封装工具")
    print("="*60)
    
    # 创建打包器
    packager = LotterySystemPackager()
    
    # 创建所有版本
    packager.create_all_packages()
    
    print(f"\n🎉 封装完成！")
    print(f"📁 所有发布包已保存到: {packager.release_dir}")

if __name__ == "__main__":
    main()
