"""
测试特征维度修复效果
"""

import sys
import os
from pathlib import Path
import numpy as np

def test_feature_dimension_fix():
    """测试特征维度修复效果"""
    print("🔧 测试特征维度修复效果")
    print("=" * 50)
    
    # 添加路径
    sys.path.insert(0, str(Path.cwd()))
    sys.path.insert(0, str(Path.cwd() / "src"))
    
    try:
        # 1. 测试机器学习模块
        print("\n1️⃣ 测试机器学习模块...")
        from src.independent_modules.ml_module import MachineLearningModule
        
        ml_module = MachineLearningModule()
        print(f"✅ 模块创建成功")
        print(f"📊 特征配置: {ml_module.feature_config}")
        
        # 2. 测试特征生成
        print("\n2️⃣ 测试特征生成...")
        
        # 模拟历史数据
        history_sequence = []
        for i in range(10):
            record = {
                'special_number': 20 + i,
                'draw_date': f'2025-06-{10+i:02d}'
            }
            history_sequence.append(record)
        
        current_record = {
            'special_number': 30,
            'draw_date': '2025-06-20'
        }
        
        # 生成特征向量
        feature_vector = ml_module._generate_feature_vector(current_record, history_sequence)
        
        if feature_vector is not None:
            print(f"✅ 特征向量生成成功")
            print(f"📊 特征维度: {feature_vector.shape}")
            print(f"📊 特征数量: {len(feature_vector)}")
            print(f"📊 特征值范围: [{np.min(feature_vector):.3f}, {np.max(feature_vector):.3f}]")
            
            # 验证特征数量
            if len(feature_vector) == 20:
                print("✅ 特征维度正确 (20维)")
            else:
                print(f"❌ 特征维度错误: 期望20维，实际{len(feature_vector)}维")
                return False
        else:
            print("❌ 特征向量生成失败")
            return False
        
        # 3. 测试模型训练
        print("\n3️⃣ 测试模型训练...")
        
        # 生成训练数据
        features_list = []
        targets_list = []
        
        for i in range(50):  # 生成50个样本
            # 模拟历史序列
            seq = []
            for j in range(10):
                seq.append({
                    'special_number': 10 + (i + j) % 40,
                    'draw_date': f'2025-06-{j+1:02d}'
                })
            
            current = {
                'special_number': 15 + i % 35,
                'draw_date': f'2025-06-{i+1:02d}'
            }
            
            feature_vec = ml_module._generate_feature_vector(current, seq)
            if feature_vec is not None:
                features_list.append(feature_vec)
                targets_list.append(current['special_number'])
        
        if features_list:
            features_array = np.array(features_list)
            targets_array = np.array(targets_list)
            
            print(f"✅ 训练数据生成成功")
            print(f"📊 训练特征形状: {features_array.shape}")
            print(f"📊 训练目标形状: {targets_array.shape}")
            
            # 验证特征维度一致性
            if features_array.shape[1] == 20:
                print("✅ 训练特征维度正确 (20维)")
            else:
                print(f"❌ 训练特征维度错误: 期望20维，实际{features_array.shape[1]}维")
                return False
            
            # 尝试训练模型
            try:
                ml_module._train_models(features_array, targets_array)
                print("✅ 模型训练成功")
            except Exception as e:
                print(f"❌ 模型训练失败: {e}")
                return False
        else:
            print("❌ 训练数据生成失败")
            return False
        
        # 4. 测试模型预测
        print("\n4️⃣ 测试模型预测...")
        
        try:
            # 使用训练好的模型进行预测
            test_feature = features_array[0:1]  # 取第一个样本作为测试
            
            predictions = {}
            for model_name, model_info in ml_module.models.items():
                if model_name in ml_module.trained_models:
                    try:
                        # 正确访问模型对象
                        model_data = ml_module.trained_models[model_name]
                        model = model_data['model']
                        pred = model.predict(test_feature)
                        predictions[model_name] = pred[0]
                        print(f"✅ {model_name} 预测成功: {pred[0]:.2f}")
                    except Exception as e:
                        print(f"❌ {model_name} 预测失败: {e}")
                        return False
            
            if predictions:
                print(f"✅ 所有模型预测成功")
                print(f"📊 预测结果: {predictions}")
            else:
                print("❌ 没有成功的预测")
                return False
                
        except Exception as e:
            print(f"❌ 预测测试失败: {e}")
            return False
        
        # 5. 测试完整预测流程
        print("\n5️⃣ 测试完整预测流程...")
        
        try:
            result = ml_module.predict("2025-06-27", analysis_days=50)
            
            if result and 'recommended_numbers' in result:
                recommended = result['recommended_numbers']
                print(f"✅ 完整预测成功")
                print(f"📊 推荐号码数量: {len(recommended)}")
                print(f"📊 推荐号码: {recommended}")
                
                if len(recommended) == 16:
                    print("✅ 推荐号码数量正确 (16个)")
                else:
                    print(f"⚠️ 推荐号码数量: 期望16个，实际{len(recommended)}个")
            else:
                print("❌ 完整预测失败或结果格式错误")
                return False
                
        except Exception as e:
            print(f"❌ 完整预测流程失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 特征维度修复效果测试")
    print("=" * 70)
    
    success = test_feature_dimension_fix()
    
    if success:
        print("\n🎉 测试完成！")
        print("✅ 特征维度修复成功")
        print("✅ 特征生成正常 (20维)")
        print("✅ 模型训练正常")
        print("✅ 模型预测正常")
        print("✅ 完整预测流程正常")
        
        print("\n📋 修复总结:")
        print("  1. 修复了特征维度不匹配问题")
        print("  2. 统一特征数量为20维")
        print("  3. 禁用了导致维度超出的生肖和时间特征")
        print("  4. 添加了扩展统计特征补充到20维")
        print("  5. 确保了特征向量的一致性")
        
        print("\n🔄 下一步:")
        print("  1. 重新启动GUI程序")
        print("  2. 测试机器学习预测功能")
        print("  3. 验证不再出现特征维度错误")
        print("  4. 检查预测结果的质量")
    else:
        print("\n❌ 测试失败")
        print("请检查错误信息并重试")
    
    return success

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
