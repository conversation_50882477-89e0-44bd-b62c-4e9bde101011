"""
分析系统依赖和准备封包环境
"""
import os
import sys
import subprocess
import json
from pathlib import Path

def analyze_system_dependencies():
    """分析系统依赖"""
    print("📦 分析系统依赖")
    print("=" * 50)
    
    # 1. 检查Python环境
    print("🐍 Python环境检查:")
    python_version = sys.version
    print(f"   Python版本: {python_version}")
    print(f"   Python路径: {sys.executable}")
    
    # 2. 检查已安装的包
    print(f"\n📋 检查已安装的包:")
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'list'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            packages = result.stdout.strip().split('\n')[2:]  # 跳过标题行
            print(f"   已安装包数量: {len(packages)}")
            
            # 提取关键包
            key_packages = []
            for package_line in packages:
                if package_line.strip():
                    parts = package_line.split()
                    if len(parts) >= 2:
                        name, version = parts[0], parts[1]
                        if any(key in name.lower() for key in ['numpy', 'pandas', 'scikit', 'matplotlib', 'seaborn', 'tkinter', 'pillow', 'requests']):
                            key_packages.append(f"{name}=={version}")
            
            print(f"   关键依赖包:")
            for pkg in key_packages:
                print(f"      {pkg}")
                
        else:
            print(f"   ❌ 无法获取包列表")
            
    except Exception as e:
        print(f"   ❌ 检查包失败: {e}")
    
    # 3. 检查项目文件结构
    print(f"\n📁 检查项目文件结构:")
    
    project_structure = {}
    
    def scan_directory(path, max_depth=3, current_depth=0):
        items = {}
        if current_depth >= max_depth:
            return items
            
        try:
            for item in os.listdir(path):
                if item.startswith('.'):
                    continue
                    
                item_path = os.path.join(path, item)
                if os.path.isdir(item_path):
                    items[item] = scan_directory(item_path, max_depth, current_depth + 1)
                else:
                    items[item] = "file"
        except PermissionError:
            pass
            
        return items
    
    project_structure = scan_directory(".", max_depth=3)
    
    # 统计文件
    def count_files(structure):
        count = 0
        for key, value in structure.items():
            if value == "file":
                count += 1
            elif isinstance(value, dict):
                count += count_files(value)
        return count
    
    total_files = count_files(project_structure)
    print(f"   项目文件总数: {total_files}")
    
    # 检查关键目录
    key_dirs = ['src', 'data', 'config', 'docs', 'tests']
    for dir_name in key_dirs:
        if dir_name in project_structure:
            print(f"   ✅ {dir_name}/: 存在")
        else:
            print(f"   ❌ {dir_name}/: 不存在")
    
    # 4. 检查关键文件
    print(f"\n📄 检查关键文件:")
    
    key_files = [
        'src/perfect_prediction_system.py',
        'src/fusion_manager.py', 
        'optimal_config.json',
        'requirements.txt',
        'README.md'
    ]
    
    existing_files = []
    for file_path in key_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            existing_files.append(file_path)
            print(f"   ✅ {file_path}: {size} bytes")
        else:
            print(f"   ❌ {file_path}: 不存在")
    
    return {
        'python_version': python_version,
        'python_executable': sys.executable,
        'key_packages': key_packages,
        'total_files': total_files,
        'existing_files': existing_files,
        'project_structure': project_structure
    }

def create_requirements_file(analysis_result):
    """创建requirements.txt文件"""
    print(f"\n📝 创建requirements.txt文件:")
    
    # 基础依赖包
    base_requirements = [
        "numpy>=1.21.0",
        "pandas>=1.3.0", 
        "scikit-learn>=1.0.0",
        "matplotlib>=3.4.0",
        "seaborn>=0.11.0",
        "requests>=2.25.0",
        "Pillow>=8.0.0",
        "openpyxl>=3.0.0",
        "xlsxwriter>=3.0.0",
        "python-dateutil>=2.8.0",
        "pytz>=2021.1",
        "tqdm>=4.60.0"
    ]
    
    # 如果有已安装的包，使用实际版本
    if 'key_packages' in analysis_result:
        installed_packages = analysis_result['key_packages']
        print(f"   使用已安装包版本: {len(installed_packages)}个")
        
        # 合并包列表
        all_requirements = set(base_requirements)
        for pkg in installed_packages:
            all_requirements.add(pkg)
        
        requirements_content = '\n'.join(sorted(all_requirements))
    else:
        requirements_content = '\n'.join(base_requirements)
    
    # 写入requirements.txt
    with open('requirements.txt', 'w', encoding='utf-8') as f:
        f.write(requirements_content)
    
    print(f"   ✅ requirements.txt 已创建")
    print(f"   📦 包含 {len(requirements_content.split())} 个依赖包")
    
    return requirements_content

def create_package_info():
    """创建包信息文件"""
    print(f"\n📋 创建包信息文件:")
    
    package_info = {
        "name": "澳门六合彩智能预测系统",
        "version": "1.0.0",
        "description": "基于机器学习和传统统计分析的六合彩智能预测系统",
        "author": "AI Assistant",
        "python_version": ">=3.8",
        "platform": "Windows 11",
        "release_date": "2025-06-23",
        "features": [
            "完美预测系统",
            "增强回测功能", 
            "特码预测",
            "生肖预测",
            "多模组协作",
            "智能融合算法"
        ],
        "modules": [
            "传统统计分析模组",
            "机器学习预测模组",
            "生肖扩展分析模组",
            "特码生肖预测模组",
            "融合管理器",
            "智能筛选器"
        ],
        "supported_formats": [
            "便携版 (Portable)",
            "源码版 (Source Code)", 
            "Windows安装版 (Windows Installer)"
        ]
    }
    
    # 保存包信息
    with open('package_info.json', 'w', encoding='utf-8') as f:
        json.dump(package_info, f, ensure_ascii=False, indent=2)
    
    print(f"   ✅ package_info.json 已创建")
    
    return package_info

if __name__ == "__main__":
    # 分析系统依赖
    analysis_result = analyze_system_dependencies()
    
    # 创建requirements文件
    requirements = create_requirements_file(analysis_result)
    
    # 创建包信息
    package_info = create_package_info()
    
    print(f"\n🎊 封包准备完成!")
    print(f"📁 生成文件:")
    print(f"   - requirements.txt")
    print(f"   - package_info.json")
