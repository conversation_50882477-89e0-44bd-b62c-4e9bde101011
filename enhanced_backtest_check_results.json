{"test_results": {"enhanced_hit_rate_optimizer": {"error": "结果格式异常"}, "historical_simulation_predictor": {"success": true, "simulation_count": 0, "overall_hit_rate": 0, "duration": 0.001001119613647461}, "backtest_configuration": {"optimization_config.json": {"exists": true, "valid_json": true, "keys": ["timestamp", "source", "module_weights", "fusion_strategy", "performance_targets", "system_parameters"], "size": 6}, "optimal_config.json": {"exists": true, "valid_json": true, "keys": ["config_version", "generation_date", "source", "description", "fusion_weights", "filter_thresholds", "prediction_strategy", "optimization_parameters", "backtest_performance", "validation_results", "confidence_threshold"], "size": 11}}, "backtest_data_access": {"total_records": 905, "date_range": ["2023-01-01", "2025-06-23"], "test_period_records": 31, "tables": ["lunar_year_mapping", "prediction_results", "model_performance", "number_zodiac_mapping", "lottery_data", "lottery_results", "sqlite_sequence", "test_table"]}, "backtest_performance": {"short_term": {"duration": 0.0, "success": false, "hit_rate": 0}, "medium_term": {"duration": 0.0, "success": false, "hit_rate": 0}}, "backtest_integration": {"config_loaded": true, "config_count": 3, "key_configs": [], "modules_initialized": true}}, "check_time": "2025-06-24T09:01:42.875314"}