"""
完整预测系统测试
"""
import sys
sys.path.insert(0, 'src')

from datetime import date
import json

print("🎯 完整预测系统测试")
print("=" * 60)

try:
    # 1. 验证数据库
    print("📊 验证数据库...")
    from src.data_layer.database.models import create_database_engine, get_session, LotteryData
    
    engine = create_database_engine()
    session = get_session(engine)
    
    total_records = session.query(LotteryData).count()
    print(f"✅ 数据库记录数: {total_records}")
    
    # 2. 创建预测系统
    print("📊 创建预测系统...")
    from src.data_layer.year_mapping.lunar_year_manager import LunarYearManager
    from src.data_layer.year_mapping.number_attribute_mapper import NumberAttributeMapper
    from src.algorithm_layer.model_fusion.cross_validation_fusion import CrossValidationFusion
    
    lunar_manager = LunarYearManager(session)
    attr_mapper = NumberAttributeMapper()
    fusion_system = CrossValidationFusion(session, lunar_manager, attr_mapper)
    print("✅ 预测系统创建成功")
    
    # 3. 执行预测
    print("📊 执行预测分析...")
    prediction_result = fusion_system.comprehensive_prediction(
        recent_periods=min(30, total_records),
        target_date=date(2025, 6, 21)
    )
    print("✅ 预测计算完成")
    
    # 4. 显示结果
    final_numbers = prediction_result['final_recommended_numbers']
    confidence = prediction_result['confidence_level']
    
    print(f"\n🎯 预测结果:")
    print(f"   推荐号码: {final_numbers[:10]}")
    print(f"   置信度: {confidence:.1%}")
    
    # 5. 与最新开奖对比
    latest_record = session.query(LotteryData).order_by(LotteryData.draw_date.desc()).first()
    if latest_record:
        latest_regular = json.loads(latest_record.regular_numbers)
        latest_special = latest_record.special_number
        
        print(f"\n📊 最新开奖 ({latest_record.period_number}):")
        print(f"   正码: {latest_regular}")
        print(f"   特码: {latest_special}")
        print(f"   生肖: {latest_record.special_zodiac}")
        
        if latest_special in final_numbers:
            print(f"   🎯 特码命中: {latest_special} ✅")
        else:
            print(f"   ❌ 特码未命中: {latest_special}")
    
    session.close()
    
    print(f"\n🎊 预测系统测试成功！")
    print(f"✅ 数据库: {total_records}期真实数据")
    print(f"✅ 预测系统: 运行正常")
    print(f"🎯 推荐号码: {final_numbers[:10]}")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
