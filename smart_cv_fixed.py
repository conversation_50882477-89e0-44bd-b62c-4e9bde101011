import numpy as np
from sklearn.model_selection import cross_val_score, StratifiedKFold, train_test_split
from sklearn.ensemble import RandomForestClassifier
from collections import Counter
import warnings
warnings.filterwarnings("ignore")

class SmartCrossValidator:
    def __init__(self):
        self.min_samples_per_class = 2
        
    def analyze_data(self, X, y):
        class_counts = Counter(y)
        return {
            "total_samples": len(X),
            "n_classes": len(class_counts),
            "min_class_size": min(class_counts.values()),
            "max_class_size": max(class_counts.values())
        }
    
    def determine_cv_strategy(self, X, y):
        analysis = self.analyze_data(X, y)
        
        total_samples = analysis["total_samples"]
        min_class_size = analysis["min_class_size"]
        
        if total_samples < 30:
            return {"method": "holdout", "params": {"test_size": 0.3}}
        elif min_class_size < 2:
            return {"method": "simple_cv", "params": {"cv": 2}}
        elif total_samples < 100:
            cv_splits = min(3, min_class_size)
            return {"method": "stratified_cv", "params": {"cv": cv_splits}}
        else:
            return {"method": "stratified_cv", "params": {"cv": 5}}
    
    def safe_cross_validate(self, model, X, y):
        strategy = self.determine_cv_strategy(X, y)
        
        try:
            if strategy["method"] == "holdout":
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=strategy["params"]["test_size"], random_state=42
                )
                model.fit(X_train, y_train)
                score = model.score(X_test, y_test)
                return {"success": True, "scores": [score], "mean": score, "strategy": strategy}
                
            elif strategy["method"] == "stratified_cv":
                cv = StratifiedKFold(n_splits=strategy["params"]["cv"], shuffle=True, random_state=42)
                scores = cross_val_score(model, X, y, cv=cv)
                return {"success": True, "scores": scores, "mean": scores.mean(), "strategy": strategy}
                
            else:
                scores = cross_val_score(model, X, y, cv=strategy["params"]["cv"])
                return {"success": True, "scores": scores, "mean": scores.mean(), "strategy": strategy}
                
        except Exception as e:
            try:
                X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
                model.fit(X_train, y_train)
                score = model.score(X_test, y_test)
                return {"success": True, "scores": [score], "mean": score, "strategy": {"method": "fallback"}}
            except:
                return {"success": False, "error": str(e)}

print("🧪 测试智能交叉验证解决方案")
print("=" * 50)

cv_validator = SmartCrossValidator()
model = RandomForestClassifier(n_estimators=10, random_state=42)

test_cases = [
    ("小数据集", 20, 5),
    ("中数据集", 60, 10),
    ("大数据集", 150, 15)
]

for name, n_samples, n_features in test_cases:
    print(f"\n📊 {name} ({n_samples}样本, {n_features}特征):")
    
    X = np.random.randn(n_samples, n_features)
    y = np.random.randint(1, 50, n_samples)
    
    analysis = cv_validator.analyze_data(X, y)
    n_classes = analysis["n_classes"]
    min_class_size = analysis["min_class_size"]
    print(f"   类别数: {n_classes}, 最小类别样本: {min_class_size}")
    
    result = cv_validator.safe_cross_validate(model, X, y)
    if result["success"]:
        method = result["strategy"]["method"]
        mean_score = result["mean"]
        std_score = np.std(result["scores"])
        print(f"   ✅ 策略: {method}")
        print(f"   📈 得分: {mean_score:.3f} ± {std_score:.3f}")
    else:
        error = result["error"]
        print(f"   ❌ 失败: {error}")

print("\n✅ 智能交叉验证解决方案测试完成!")
