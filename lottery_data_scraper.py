"""
六合彩数据抓取模块
测试从指定API抓取历史开奖数据
"""

import requests
import pandas as pd
import json
import time
from datetime import datetime
from typing import List, Dict, Any
import os

class LotteryDataScraper:
    """六合彩数据抓取器"""
    
    def __init__(self):
        self.base_url = "https://kj.616363.com/kjdata"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://kj.616363.com/'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
    def test_api_connection(self) -> bool:
        """测试API连接"""
        print("🔗 测试API连接...")
        
        try:
            # 测试单年数据
            test_params = {"g": "am", "y": 2024}
            response = self.session.get(self.base_url, params=test_params, timeout=10)
            
            print(f"  状态码: {response.status_code}")
            print(f"  响应头: {dict(response.headers)}")
            
            if response.ok:
                print("✅ API连接成功")
                
                # 尝试解析JSON
                try:
                    data = response.json()
                    print(f"  数据类型: {type(data)}")
                    print(f"  数据长度: {len(data) if isinstance(data, list) else 'N/A'}")
                    
                    if isinstance(data, list) and len(data) > 0:
                        print(f"  示例数据: {data[0] if data else 'None'}")
                        return True
                    else:
                        print("⚠️ 返回数据为空或格式异常")
                        return False
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"  响应内容: {response.text[:200]}...")
                    return False
            else:
                print(f"❌ API连接失败: {response.status_code}")
                print(f"  响应内容: {response.text[:200]}...")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求异常: {e}")
            return False
    
    def scrape_year_data(self, year: int) -> List[Dict]:
        """抓取指定年份的数据"""
        print(f"📅 抓取 {year} 年数据...")
        
        try:
            params = {"g": "am", "y": year}
            response = self.session.get(self.base_url, params=params, timeout=15)
            
            if response.ok:
                data = response.json()
                
                if isinstance(data, list):
                    print(f"✅ {year} 年数据抓取成功: {len(data)} 条记录")
                    
                    # 显示前几条数据样例
                    if len(data) > 0:
                        print(f"  数据样例:")
                        for i, record in enumerate(data[:3]):
                            print(f"    {i+1}. {record}")
                    
                    return data
                else:
                    print(f"⚠️ {year} 年数据格式异常: {type(data)}")
                    return []
            else:
                print(f"❌ {year} 年数据抓取失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ {year} 年数据抓取异常: {e}")
            return []
    
    def scrape_multiple_years(self, years: List[int]) -> List[Dict]:
        """抓取多年数据"""
        print(f"🎯 开始抓取多年数据: {years}")
        
        all_data = []
        
        for year in years:
            year_data = self.scrape_year_data(year)
            
            if year_data:
                # 为每条记录添加年份标识
                for record in year_data:
                    if isinstance(record, list) and len(record) >= 3:
                        record_dict = {
                            'year': year,
                            'period': record[0] if len(record) > 0 else '',
                            'numbers': record[1] if len(record) > 1 else '',
                            'date': record[2] if len(record) > 2 else ''
                        }
                        all_data.append(record_dict)
                    elif isinstance(record, dict):
                        record['year'] = year
                        all_data.append(record)
                    else:
                        print(f"⚠️ 未知数据格式: {record}")
            
            # 添加延迟避免请求过快
            time.sleep(1)
        
        print(f"🎉 多年数据抓取完成: 总计 {len(all_data)} 条记录")
        return all_data
    
    def parse_lottery_numbers(self, numbers_str: str) -> Dict[str, Any]:
        """解析开奖号码字符串"""
        try:
            if not numbers_str:
                return {}
            
            # 尝试不同的分隔符
            separators = ['+', ',', ' ', '|', '-']
            numbers = []
            
            for sep in separators:
                if sep in numbers_str:
                    parts = numbers_str.split(sep)
                    numbers = [int(part.strip()) for part in parts if part.strip().isdigit()]
                    break
            
            if not numbers:
                # 如果没有分隔符，尝试按固定长度分割
                if len(numbers_str) >= 12:  # 假设每个号码2位
                    numbers = [int(numbers_str[i:i+2]) for i in range(0, len(numbers_str), 2) if numbers_str[i:i+2].isdigit()]
            
            if len(numbers) >= 7:
                return {
                    'regular_numbers': numbers[:6],
                    'special_number': numbers[6],
                    'all_numbers': numbers
                }
            else:
                return {
                    'raw_numbers': numbers,
                    'parse_error': f'号码数量不足: {len(numbers)}'
                }
                
        except Exception as e:
            return {
                'parse_error': f'解析失败: {e}',
                'raw_string': numbers_str
            }
    
    def save_to_csv(self, data: List[Dict], filename: str = None) -> str:
        """保存数据到CSV文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"澳门六合彩数据_{timestamp}.csv"
        
        print(f"💾 保存数据到CSV文件: {filename}")
        
        try:
            # 处理数据格式
            processed_data = []
            
            for record in data:
                if isinstance(record, dict):
                    # 解析号码
                    numbers_info = self.parse_lottery_numbers(record.get('numbers', ''))
                    
                    processed_record = {
                        '年份': record.get('year', ''),
                        '期号': record.get('period', ''),
                        '开奖日期': record.get('date', ''),
                        '开奖号码': record.get('numbers', ''),
                        '正码1': numbers_info.get('regular_numbers', [None]*6)[0] if 'regular_numbers' in numbers_info else None,
                        '正码2': numbers_info.get('regular_numbers', [None]*6)[1] if 'regular_numbers' in numbers_info else None,
                        '正码3': numbers_info.get('regular_numbers', [None]*6)[2] if 'regular_numbers' in numbers_info else None,
                        '正码4': numbers_info.get('regular_numbers', [None]*6)[3] if 'regular_numbers' in numbers_info else None,
                        '正码5': numbers_info.get('regular_numbers', [None]*6)[4] if 'regular_numbers' in numbers_info else None,
                        '正码6': numbers_info.get('regular_numbers', [None]*6)[5] if 'regular_numbers' in numbers_info else None,
                        '特码': numbers_info.get('special_number', None),
                        '解析状态': '成功' if 'regular_numbers' in numbers_info else '失败',
                        '错误信息': numbers_info.get('parse_error', '')
                    }
                    processed_data.append(processed_record)
            
            # 创建DataFrame并保存
            df = pd.DataFrame(processed_data)
            df.to_csv(filename, index=False, encoding="utf-8-sig")
            
            print(f"✅ CSV文件保存成功")
            print(f"  文件名: {filename}")
            print(f"  记录数: {len(df)}")
            print(f"  列数: {len(df.columns)}")
            
            # 显示数据统计
            if len(df) > 0:
                print(f"  数据预览:")
                print(df.head().to_string())
                
                # 统计解析成功率
                success_count = len(df[df['解析状态'] == '成功'])
                success_rate = success_count / len(df) * 100
                print(f"  解析成功率: {success_rate:.1f}% ({success_count}/{len(df)})")
            
            return filename
            
        except Exception as e:
            print(f"❌ CSV保存失败: {e}")
            return ""
    
    def save_raw_json(self, data: List[Dict], filename: str = None) -> str:
        """保存原始JSON数据"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"澳门六合彩原始数据_{timestamp}.json"
        
        print(f"💾 保存原始数据到JSON文件: {filename}")
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ JSON文件保存成功: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ JSON保存失败: {e}")
            return ""
    
    def comprehensive_scrape(self, years: List[int] = [2021, 2022, 2023, 2024, 2025]) -> Dict[str, str]:
        """综合数据抓取"""
        print("🚀 开始综合数据抓取")
        print("=" * 60)
        
        # 1. 测试API连接
        if not self.test_api_connection():
            print("❌ API连接测试失败，终止抓取")
            return {}
        
        print()
        
        # 2. 抓取多年数据
        all_data = self.scrape_multiple_years(years)
        
        if not all_data:
            print("❌ 没有抓取到任何数据")
            return {}
        
        print()
        
        # 3. 保存数据
        results = {}
        
        # 保存CSV格式
        csv_file = self.save_to_csv(all_data)
        if csv_file:
            results['csv_file'] = csv_file
        
        print()
        
        # 保存JSON格式
        json_file = self.save_raw_json(all_data)
        if json_file:
            results['json_file'] = json_file
        
        print()
        
        # 4. 数据质量分析
        self.analyze_data_quality(all_data)
        
        return results
    
    def analyze_data_quality(self, data: List[Dict]):
        """分析数据质量"""
        print("📊 数据质量分析")
        print("-" * 40)
        
        if not data:
            print("❌ 无数据可分析")
            return
        
        # 按年份统计
        year_stats = {}
        total_records = len(data)
        
        for record in data:
            year = record.get('year', 'Unknown')
            if year not in year_stats:
                year_stats[year] = 0
            year_stats[year] += 1
        
        print(f"📈 数据统计:")
        print(f"  总记录数: {total_records}")
        print(f"  年份分布:")
        for year, count in sorted(year_stats.items()):
            print(f"    {year}: {count} 条")
        
        # 数据完整性检查
        complete_records = 0
        incomplete_records = 0
        
        for record in data:
            if all(key in record and record[key] for key in ['period', 'numbers', 'date']):
                complete_records += 1
            else:
                incomplete_records += 1
        
        print(f"  数据完整性:")
        print(f"    完整记录: {complete_records} 条")
        print(f"    不完整记录: {incomplete_records} 条")
        print(f"    完整率: {complete_records/total_records*100:.1f}%")

def main():
    """主函数"""
    print("🎯 六合彩数据抓取测试")
    print("=" * 80)
    
    # 创建抓取器
    scraper = LotteryDataScraper()
    
    # 执行综合抓取
    years = [2021, 2022, 2023, 2024, 2025]
    results = scraper.comprehensive_scrape(years)
    
    print("\n🎉 数据抓取完成!")
    
    if results:
        print("\n📁 生成的文件:")
        for file_type, filename in results.items():
            print(f"  {file_type}: {filename}")
            if os.path.exists(filename):
                file_size = os.path.getsize(filename)
                print(f"    文件大小: {file_size:,} 字节")
        
        print("\n💡 下一步建议:")
        print("  1. 检查生成的CSV文件数据质量")
        print("  2. 验证号码解析的准确性")
        print("  3. 如果数据质量良好，可考虑集成到六合彩工具中")
        print("  4. 设置定时任务自动更新数据")
    else:
        print("\n❌ 数据抓取失败，请检查:")
        print("  1. 网络连接是否正常")
        print("  2. API地址是否有效")
        print("  3. 请求参数是否正确")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
