#!/usr/bin/env python3
"""
分析模组详细检查脚本
检查每个分析模组的状态和正确性
"""

import sys
import os
import importlib
from datetime import datetime
from typing import Dict, List, Any

class AnalysisModuleChecker:
    """分析模组检查器"""
    
    def __init__(self):
        self.check_results = {}
        self.module_paths = {
            # 核心预测模组
            "特码预测系统": "src.lottery_prediction_system",
            "完美预测系统": "src.perfect_prediction_system", 
            "一致性预测器": "src.consistency_predictor",
            "特码预测器": "src.special_number_predictor",
            
            # 独立模组
            "传统分析模组": "src.independent_modules.traditional_module",
            "机器学习模组": "src.independent_modules.ml_module",
            "生肖扩展模组": "src.independent_modules.zodiac_extended_module",
            "特殊生肖模组": "src.independent_modules.special_zodiac_module",
            "融合验证器": "src.independent_modules.fusion_validator",
            
            # 增强功能模组
            "增强特征工程": "enhanced_feature_engineering",
            "高级融合优化": "advanced_fusion_optimizer",
            "命中率优化器": "enhanced_hit_rate_optimizer",
            
            # 系统集成模组
            "融合管理器": "src.fusion_manager",
            "ML模组适配器": "src.ml_module_adapter",
            "增强ML集成": "src.enhanced_ml_integration",
            "模组管理器": "src.module_manager",
            "优化系统集成器": "src.optimized_system_integrator"
        }
    
    def run_comprehensive_check(self):
        """运行全面检查"""
        print("🔍 分析模组全面检查")
        print("=" * 60)
        print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 检查每个模组
        for module_name, module_path in self.module_paths.items():
            print(f"🔍 检查: {module_name}")
            print("-" * 40)
            
            result = self.check_single_module(module_name, module_path)
            self.check_results[module_name] = result
            
            # 显示结果
            status = "✅ 正常" if result["status"] == "ok" else "❌ 问题"
            print(f"状态: {status}")
            
            for detail in result["details"]:
                print(f"  {detail}")
            
            if result["status"] == "ok" and "functionality" in result:
                print(f"  🎯 功能测试: {result['functionality']}")
            
            print()
        
        # 生成总结报告
        self.generate_summary_report()
    
    def check_single_module(self, module_name: str, module_path: str) -> Dict[str, Any]:
        """检查单个模组"""
        result = {
            "module_name": module_name,
            "module_path": module_path,
            "status": "unknown",
            "details": [],
            "errors": []
        }
        
        try:
            # 尝试导入模组
            module = importlib.import_module(module_path)
            result["details"].append("✅ 模组导入成功")
            
            # 检查模组内容
            module_content = self.analyze_module_content(module)
            result["details"].extend(module_content["details"])
            
            # 尝试功能测试
            if module_content["has_main_class"]:
                functionality_result = self.test_module_functionality(module, module_name)
                result["functionality"] = functionality_result
            
            result["status"] = "ok"
            
        except ImportError as e:
            result["status"] = "import_error"
            result["details"].append(f"❌ 导入失败: {e}")
            result["errors"].append(str(e))
            
            # 检查文件是否存在
            file_path = module_path.replace(".", "/") + ".py"
            if os.path.exists(file_path):
                result["details"].append(f"📁 文件存在: {file_path}")
            else:
                result["details"].append(f"📁 文件缺失: {file_path}")
        
        except Exception as e:
            result["status"] = "error"
            result["details"].append(f"❌ 检查失败: {e}")
            result["errors"].append(str(e))
        
        return result
    
    def analyze_module_content(self, module) -> Dict[str, Any]:
        """分析模组内容"""
        content_info = {
            "details": [],
            "has_main_class": False,
            "classes": [],
            "functions": []
        }
        
        # 获取模组中的类和函数
        for name in dir(module):
            if not name.startswith("_"):
                obj = getattr(module, name)
                if isinstance(obj, type):
                    content_info["classes"].append(name)
                elif callable(obj):
                    content_info["functions"].append(name)
        
        # 分析内容
        if content_info["classes"]:
            content_info["details"].append(f"📊 包含类: {len(content_info['classes'])}个")
            content_info["has_main_class"] = True
            
            # 显示主要类
            main_classes = [cls for cls in content_info["classes"] 
                          if any(keyword in cls.lower() for keyword in 
                               ["predictor", "analyzer", "module", "system", "manager", "optimizer"])]
            if main_classes:
                content_info["details"].append(f"  主要类: {', '.join(main_classes[:3])}")
        
        if content_info["functions"]:
            content_info["details"].append(f"📊 包含函数: {len(content_info['functions'])}个")
        
        return content_info
    
    def test_module_functionality(self, module, module_name: str) -> str:
        """测试模组功能"""
        try:
            # 根据模组类型进行不同的功能测试
            if "预测" in module_name:
                return self.test_prediction_module(module, module_name)
            elif "优化" in module_name:
                return self.test_optimization_module(module, module_name)
            elif "管理" in module_name:
                return self.test_management_module(module, module_name)
            else:
                return self.test_generic_module(module, module_name)
                
        except Exception as e:
            return f"测试失败: {e}"
    
    def test_prediction_module(self, module, module_name: str) -> str:
        """测试预测模组"""
        try:
            # 查找预测类
            predictor_classes = [name for name in dir(module) 
                               if not name.startswith("_") and 
                               any(keyword in name.lower() for keyword in 
                                 ["predictor", "system", "analyzer"])]
            
            if not predictor_classes:
                return "未找到预测类"
            
            # 尝试实例化主要类
            main_class_name = predictor_classes[0]
            main_class = getattr(module, main_class_name)
            
            # 实例化（使用默认参数）
            if "完美预测" in module_name:
                instance = main_class()
            elif "一致性" in module_name:
                instance = main_class()
            else:
                instance = main_class()
            
            # 检查是否有predict方法
            if hasattr(instance, "predict"):
                return f"✅ {main_class_name}实例化成功，具有predict方法"
            elif hasattr(instance, "run_complete_prediction"):
                return f"✅ {main_class_name}实例化成功，具有run_complete_prediction方法"
            else:
                return f"⚠️ {main_class_name}实例化成功，但缺少预测方法"
                
        except Exception as e:
            return f"预测模组测试失败: {e}"
    
    def test_optimization_module(self, module, module_name: str) -> str:
        """测试优化模组"""
        try:
            # 查找优化类
            optimizer_classes = [name for name in dir(module) 
                               if not name.startswith("_") and 
                               any(keyword in name.lower() for keyword in 
                                 ["optimizer", "enhancer", "fusion"])]
            
            if not optimizer_classes:
                return "未找到优化类"
            
            main_class_name = optimizer_classes[0]
            main_class = getattr(module, main_class_name)
            
            # 尝试实例化
            instance = main_class()
            
            # 检查优化方法
            optimization_methods = [method for method in dir(instance) 
                                  if not method.startswith("_") and 
                                  any(keyword in method.lower() for keyword in 
                                    ["optimize", "enhance", "fuse", "improve"])]
            
            if optimization_methods:
                return f"✅ {main_class_name}实例化成功，具有{len(optimization_methods)}个优化方法"
            else:
                return f"⚠️ {main_class_name}实例化成功，但缺少优化方法"
                
        except Exception as e:
            return f"优化模组测试失败: {e}"
    
    def test_management_module(self, module, module_name: str) -> str:
        """测试管理模组"""
        try:
            # 查找管理类
            manager_classes = [name for name in dir(module) 
                             if not name.startswith("_") and 
                             any(keyword in name.lower() for keyword in 
                               ["manager", "adapter", "integrator"])]
            
            if not manager_classes:
                return "未找到管理类"
            
            main_class_name = manager_classes[0]
            main_class = getattr(module, main_class_name)
            
            # 尝试实例化
            instance = main_class()
            
            # 检查管理方法
            management_methods = [method for method in dir(instance) 
                                if not method.startswith("_") and callable(getattr(instance, method))]
            
            return f"✅ {main_class_name}实例化成功，具有{len(management_methods)}个方法"
                
        except Exception as e:
            return f"管理模组测试失败: {e}"
    
    def test_generic_module(self, module, module_name: str) -> str:
        """测试通用模组"""
        try:
            # 查找主要类
            main_classes = [name for name in dir(module) 
                          if not name.startswith("_") and isinstance(getattr(module, name), type)]
            
            if not main_classes:
                return "未找到主要类"
            
            main_class_name = main_classes[0]
            main_class = getattr(module, main_class_name)
            
            # 尝试实例化
            instance = main_class()
            
            # 统计方法数量
            methods = [method for method in dir(instance) 
                      if not method.startswith("_") and callable(getattr(instance, method))]
            
            return f"✅ {main_class_name}实例化成功，具有{len(methods)}个方法"
                
        except Exception as e:
            return f"通用模组测试失败: {e}"
    
    def generate_summary_report(self):
        """生成总结报告"""
        print("=" * 60)
        print("📊 分析模组检查总结报告")
        print("=" * 60)
        
        # 统计结果
        total_modules = len(self.check_results)
        ok_modules = sum(1 for result in self.check_results.values() if result["status"] == "ok")
        import_error_modules = sum(1 for result in self.check_results.values() if result["status"] == "import_error")
        error_modules = sum(1 for result in self.check_results.values() if result["status"] == "error")
        
        print(f"📊 检查统计:")
        print(f"  总模组数: {total_modules}")
        print(f"  正常模组: {ok_modules}")
        print(f"  导入错误: {import_error_modules}")
        print(f"  其他错误: {error_modules}")
        print(f"  成功率: {ok_modules/total_modules:.1%}")
        print()
        
        # 按状态分类显示
        print("📋 详细结果:")
        
        # 正常模组
        ok_list = [name for name, result in self.check_results.items() if result["status"] == "ok"]
        if ok_list:
            print("✅ 正常运行的模组:")
            for module_name in ok_list:
                print(f"  • {module_name}")
        
        # 导入错误模组
        import_error_list = [name for name, result in self.check_results.items() if result["status"] == "import_error"]
        if import_error_list:
            print("\n❌ 导入错误的模组:")
            for module_name in import_error_list:
                print(f"  • {module_name}")
        
        # 其他错误模组
        error_list = [name for name, result in self.check_results.items() if result["status"] == "error"]
        if error_list:
            print("\n⚠️ 其他错误的模组:")
            for module_name in error_list:
                print(f"  • {module_name}")
        
        print()
        print("💡 建议:")
        if ok_modules >= total_modules * 0.8:
            print("🎊 大部分模组运行正常，系统状态良好！")
        elif ok_modules >= total_modules * 0.6:
            print("👍 多数模组正常，少数模组需要修复")
        else:
            print("⚠️ 多个模组存在问题，建议进行系统性修复")
        
        if import_error_modules > 0:
            print("📁 建议检查缺失的模组文件并补充")
        
        return ok_modules == total_modules

def main():
    """主函数"""
    checker = AnalysisModuleChecker()
    success = checker.run_comprehensive_check()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
