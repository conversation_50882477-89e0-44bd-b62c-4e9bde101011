# 🔄 分析模组、预测模组与数据库互动分析

## 📊 **系统架构概览**

```
┌─────────────────────────────────────────────────────────────────┐
│                    澳门六合彩预测系统架构                          │
├─────────────────────────────────────────────────────────────────┤
│  🖥️ GUI层 (lottery_prediction_gui.py)                          │
│  ├── 用户界面交互                                                │
│  ├── 结果展示                                                   │
│  └── 系统控制                                                   │
├─────────────────────────────────────────────────────────────────┤
│  🧠 预测模组层 (Prediction Modules)                             │
│  ├── 一致性预测器 (ConsistentSpecialNumberPredictor)             │
│  ├── 标准预测器 (SpecialNumberPredictor)                        │
│  ├── 历史回测系统 (HistoricalBacktestSystem)                    │
│  └── 融合预测器 (CrossValidationFusion)                         │
├─────────────────────────────────────────────────────────────────┤
│  📊 分析模组层 (Analysis Modules)                               │
│  ├── 生肖维度分析 (ZodiacExtendedAnalysis)                      │
│  ├── 传统统计分析 (TraditionalAnalysis)                         │
│  ├── 机器学习分析 (MachineLearningPredictor)                    │
│  └── 特征工程 (Feature Engineering)                             │
├─────────────────────────────────────────────────────────────────┤
│  🗄️ 数据库层 (Database Layer)                                   │
│  ├── 开奖数据表 (LotteryData)                                   │
│  ├── 预测结果表 (PredictionResult)                              │
│  ├── 模型性能表 (ModelPerformance)                              │
│  └── 生肖映射表 (LunarYearMapping)                              │
└─────────────────────────────────────────────────────────────────┘
```

---

## 🔄 **核心互动流程**

### **1. 数据流向 (Data Flow)**

```
📥 原始数据输入
    ↓
🗄️ 数据库存储 (LotteryData)
    ↓
📊 分析模组读取
    ├── 生肖维度分析
    ├── 传统统计分析
    └── 机器学习分析
    ↓
🧠 预测模组融合
    ├── 一致性预测器
    ├── 标准预测器
    └── 融合预测器
    ↓
💾 结果存储 (PredictionResult)
    ↓
🖥️ GUI展示
```

### **2. 模组间调用关系**

```
GUI层
├── 调用 → 一致性预测器
│   ├── 调用 → 标准预测器 (分析功能)
│   │   ├── 读取 → 数据库 (历史数据)
│   │   └── 返回 → 分析结果
│   ├── 生成 → 确定性预测
│   └── 返回 → 预测结果
├── 调用 → 历史回测系统
│   ├── 调用 → 一致性预测器 (每日预测)
│   ├── 读取 → 数据库 (历史数据)
│   └── 返回 → 回测结果
└── 调用 → 融合预测器
    ├── 调用 → 生肖维度分析
    ├── 调用 → 传统统计分析
    ├── 调用 → 机器学习分析
    ├── 读取 → 数据库 (多表数据)
    └── 返回 → 综合预测结果
```

---

## 🗄️ **数据库交互详解**

### **数据库表结构**

#### **1. 核心数据表**
```sql
-- 开奖数据表
LotteryData:
├── period_number (期号)
├── draw_date (开奖日期)
├── regular_numbers (正码JSON)
├── special_number (特码)
├── special_zodiac (特码生肖)
├── special_wuxing (特码五行)
└── 其他属性字段...

-- 预测结果表
PredictionResult:
├── target_period (目标期号)
├── model_name (模型名称)
├── predicted_numbers (预测号码JSON)
├── confidence_score (置信度)
├── actual_result (实际结果)
└── is_hit (是否命中)

-- 模型性能表
ModelPerformance:
├── model_name (模型名称)
├── accuracy (准确率)
├── recent_hit_rate (最近命中率)
└── fusion_weight (融合权重)
```

#### **2. 数据库操作模式**

**读取操作**:
```python
# 分析模组读取历史数据
query = session.query(LotteryData).filter(
    LotteryData.draw_date < target_date
).order_by(LotteryData.draw_date.desc()).limit(recent_periods)

records = query.all()
```

**写入操作**:
```python
# 预测模组保存结果
prediction = PredictionResult(
    target_period=target_period,
    model_name=model_name,
    predicted_numbers=json.dumps(numbers),
    confidence_score=confidence
)
session.add(prediction)
session.commit()
```

---

## 📊 **分析模组详解**

### **1. 生肖维度分析模组**

**功能**: 基于生肖属性进行多维度分析
```python
class ZodiacExtendedAnalysis:
    def predict_next_zodiac_range(self, recent_periods, target_date):
        # 1. 从数据库读取历史数据
        historical_data = self._get_historical_data(recent_periods)
        
        # 2. 分析生肖频率和趋势
        zodiac_analysis = self._analyze_zodiac_patterns(historical_data)
        
        # 3. 预测最高得分的4个生肖
        top_zodiacs = self._predict_top_zodiacs(zodiac_analysis)
        
        return {
            'top_4_zodiacs': top_zodiacs,
            'analysis_method': '19维度生肖分析',
            'confidence': 'high'
        }
```

**数据库交互**:
- **读取**: `LotteryData` (特码、生肖、日期)
- **读取**: `LunarYearMapping` (生肖年份映射)
- **写入**: 分析结果缓存

### **2. 传统统计分析模组**

**功能**: 基于频率、冷热、遗漏等统计指标分析
```python
class TraditionalAnalysis:
    def analyze_number_patterns(self, recent_periods):
        # 1. 读取历史开奖数据
        data = self._fetch_lottery_data(recent_periods)
        
        # 2. 计算统计指标
        frequency_analysis = self._calculate_frequency(data)
        trend_analysis = self._analyze_trends(data)
        
        # 3. 生成推荐号码
        recommended_numbers = self._generate_recommendations(
            frequency_analysis, trend_analysis
        )
        
        return {
            'recommended_numbers': recommended_numbers,
            'analysis_details': {...}
        }
```

**数据库交互**:
- **读取**: `LotteryData` (全部历史数据)
- **计算**: 实时统计分析
- **缓存**: 分析结果

### **3. 机器学习分析模组**

**功能**: 使用ML算法进行预测
```python
class MachineLearningPredictor:
    def predict_numbers(self, recent_periods, target_date, min_numbers, max_numbers):
        # 1. 获取训练数据
        historical_data = self._get_historical_data(recent_periods, target_date)
        
        # 2. 特征工程
        features, targets = self._prepare_features_and_targets(historical_data)
        
        # 3. 训练模型
        self._train_models(features, targets)
        
        # 4. 生成预测
        predictions = self._ensemble_predict(prediction_features)
        
        return {
            'predicted_numbers': recommended_numbers,
            'confidence_level': confidence,
            'model_performance': performance
        }
```

**数据库交互**:
- **读取**: `LotteryData` (训练数据)
- **读取**: `ModelPerformance` (模型性能)
- **写入**: `ModelPerformance` (更新性能)

---

## 🧠 **预测模组详解**

### **1. 一致性预测器**

**功能**: 确保同期预测结果一致
```python
class ConsistentSpecialNumberPredictor:
    def run_consistent_prediction(self, target_date):
        # 1. 设置确定性种子
        self.set_deterministic_seed(target_date)
        
        # 2. 生成测试数据
        test_data = self.generate_deterministic_test_data(target_date)
        
        # 3. 调用标准预测器进行分析
        from special_number_predictor import SpecialNumberPredictor
        temp_predictor = SpecialNumberPredictor()
        analysis = temp_predictor.analyze_special_number_patterns(test_data)
        
        # 4. 确定性预测
        initial_selection = self.predict_special_numbers_deterministic(analysis, target_date)
        recommended_numbers = self.cross_validate_deterministic(initial_selection, analysis, target_date)
        top_zodiacs = self.predict_top_zodiacs_deterministic(analysis, target_date)
        
        return {
            'special_number_prediction': {...},
            'zodiac_prediction': {...},
            'fusion_prediction': {...}
        }
```

**模组交互**:
- **调用**: `SpecialNumberPredictor` (分析功能)
- **生成**: 确定性测试数据
- **返回**: 一致性预测结果

### **2. 融合预测器**

**功能**: 整合多个分析模组的结果
```python
class CrossValidationFusion:
    def comprehensive_prediction(self, recent_periods, target_date):
        # 1. 执行所有分析模组
        analysis_results = self._execute_all_analyses(recent_periods, target_date)
        
        # 2. 融合分析结果
        fusion_result = self._fusion_analysis_results(analysis_results)
        
        # 3. 生成最终推荐
        final_numbers = self._generate_final_recommendations(fusion_result)
        
        return {
            'final_recommended_numbers': final_numbers,
            'analysis_breakdown': analysis_results,
            'fusion_details': fusion_result
        }
    
    def _execute_all_analyses(self, recent_periods, target_date):
        analysis_results = {}
        
        # 调用生肖维度分析
        zodiac_analyzer = ZodiacExtendedAnalysis(self.db, self.attr_mapper)
        analysis_results['zodiac_analysis'] = zodiac_analyzer.predict_next_zodiac_range(recent_periods, target_date)
        
        # 调用传统统计分析
        traditional_analyzer = TraditionalAnalysis(self.db, self.attr_mapper)
        analysis_results['traditional_analysis'] = traditional_analyzer.analyze_number_patterns(recent_periods)
        
        # 调用机器学习预测
        ml_predictor = MachineLearningPredictor(self.db, self.attr_mapper, self.lunar_manager)
        analysis_results['ml_prediction'] = ml_predictor.predict_numbers(recent_periods, target_date, 16, 24)
        
        return analysis_results
```

**模组交互**:
- **调用**: 所有分析模组
- **读取**: 数据库 (协调数据访问)
- **融合**: 多模组结果
- **写入**: 融合预测结果

---

## 🔄 **互动时序图**

```
用户操作 → GUI → 预测模组 → 分析模组 → 数据库
    ↓        ↓        ↓         ↓         ↓
1. 点击预测  启动     调用      读取      查询历史数据
2. 设置参数  验证     传参      分析      返回数据集
3. 执行预测  调用     处理      计算      ←─────────
4. 等待结果  显示     融合      返回      写入结果
5. 查看结果  展示     返回      ←──      ←─────────
```

### **详细交互步骤**

1. **用户发起预测请求**
   ```python
   # GUI层
   def run_prediction(self):
       target_date = self.prediction_date.date().toString("yyyy-MM-dd")
       if self.consistent_mode.isChecked():
           result = self.consistent_predictor.run_consistent_prediction(target_date)
   ```

2. **预测模组调用分析模组**
   ```python
   # 一致性预测器
   def run_consistent_prediction(self, target_date):
       # 调用标准预测器的分析功能
       temp_predictor = SpecialNumberPredictor()
       analysis = temp_predictor.analyze_special_number_patterns(test_data)
   ```

3. **分析模组访问数据库**
   ```python
   # 标准预测器
   def analyze_special_number_patterns(self, data):
       # 分析历史模式
       frequency_analysis = self._analyze_frequency(data)
       trend_analysis = self._analyze_trends(data)
   ```

4. **结果回传和存储**
   ```python
   # 预测结果存储
   prediction_result = PredictionResult(
       target_period=target_period,
       model_name="ConsistentPredictor",
       predicted_numbers=json.dumps(predicted_numbers),
       confidence_score=confidence
   )
   session.add(prediction_result)
   session.commit()
   ```

---

## 💡 **关键设计模式**

### **1. 依赖注入模式**
```python
class CrossValidationFusion:
    def __init__(self, db_session, lunar_year_manager, number_attribute_mapper):
        self.db = db_session
        self.lunar_manager = lunar_year_manager
        self.attr_mapper = number_attribute_mapper
```

### **2. 策略模式**
```python
# 不同的预测策略
strategies = {
    'consistent': ConsistentSpecialNumberPredictor,
    'standard': SpecialNumberPredictor,
    'fusion': CrossValidationFusion
}
```

### **3. 观察者模式**
```python
# GUI监听预测结果
def on_prediction_complete(self, result):
    self.update_prediction_display(result)
    self.save_prediction_result(result)
```

---

## 🔧 **性能优化策略**

### **1. 数据库连接池**
```python
# 使用连接池管理数据库连接
engine = create_engine(DATABASE_URL, pool_size=10, max_overflow=20)
```

### **2. 结果缓存**
```python
# 缓存分析结果避免重复计算
@lru_cache(maxsize=100)
def analyze_zodiac_patterns(self, data_hash):
    # 分析逻辑
    pass
```

### **3. 异步处理**
```python
# 异步执行多个分析模组
async def execute_analyses_async(self):
    tasks = [
        self.zodiac_analysis(),
        self.traditional_analysis(),
        self.ml_prediction()
    ]
    results = await asyncio.gather(*tasks)
```

---

## 📊 **总结**

### **核心互动关系**
1. **GUI层** 作为用户接口，协调各模组调用
2. **预测模组** 作为业务逻辑核心，整合分析结果
3. **分析模组** 作为算法引擎，提供专业分析
4. **数据库层** 作为数据基础，支撑所有计算

### **数据流特点**
- **单向流动**: 数据库 → 分析模组 → 预测模组 → GUI
- **结果回写**: 预测结果存储回数据库
- **缓存优化**: 避免重复计算和数据库访问

### **模组解耦**
- **接口标准化**: 统一的输入输出格式
- **依赖注入**: 降低模组间耦合度
- **配置驱动**: 通过配置控制模组行为

**🎯 这种架构设计确保了系统的可扩展性、可维护性和高性能！**
