"""
简化的预测功能测试
"""
import numpy as np
import pandas as pd
import json
from datetime import datetime, timedelta
import random

class SimplePredictionTest:
    """简化的预测测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_data = self.generate_sample_data()
        
    def generate_sample_data(self, days=50):
        """生成样本数据"""
        print("📊 生成样本测试数据...")
        
        data = []
        base_date = datetime.now() - timedelta(days=days)
        
        for i in range(days):
            current_date = base_date + timedelta(days=i)
            
            # 生成6个正码（1-49，不重复）
            regular_numbers = sorted(random.sample(range(1, 50), 6))
            
            # 生成1个特码（1-49）
            special_number = random.randint(1, 49)
            
            record = {
                'draw_date': current_date.strftime('%Y-%m-%d'),
                'period_number': f"2024{i+1:03d}",
                'regular_numbers': regular_numbers,
                'special_number': special_number
            }
            
            data.append(record)
        
        df = pd.DataFrame(data)
        print(f"✅ 生成了 {len(df)} 条样本数据")
        return df
    
    def simple_frequency_analysis(self):
        """简单的频率分析"""
        print("\n🔍 执行简单频率分析...")
        
        # 统计所有号码的出现频率
        all_numbers = []
        
        for _, row in self.test_data.iterrows():
            all_numbers.extend(row['regular_numbers'])
            all_numbers.append(row['special_number'])
        
        # 计算频率
        from collections import Counter
        frequency = Counter(all_numbers)
        
        # 获取热号（出现频率最高的10个号码）
        hot_numbers = [num for num, freq in frequency.most_common(10)]
        
        # 获取冷号（出现频率最低的10个号码）
        cold_numbers = [num for num, freq in frequency.most_common()[-10:]]
        
        print(f"   🔥 热号 (前10): {hot_numbers}")
        print(f"   ❄️ 冷号 (后10): {cold_numbers}")
        
        return {
            'hot_numbers': hot_numbers,
            'cold_numbers': cold_numbers,
            'frequency': dict(frequency)
        }
    
    def simple_pattern_analysis(self):
        """简单的模式分析"""
        print("\n🔍 执行简单模式分析...")
        
        # 分析奇偶比例
        odd_counts = []
        sum_values = []
        
        for _, row in self.test_data.iterrows():
            all_nums = row['regular_numbers'] + [row['special_number']]
            
            # 奇数个数
            odd_count = sum(1 for num in all_nums if num % 2 == 1)
            odd_counts.append(odd_count)
            
            # 和值
            sum_values.append(sum(all_nums))
        
        avg_odd_count = np.mean(odd_counts)
        avg_sum = np.mean(sum_values)
        
        print(f"   📊 平均奇数个数: {avg_odd_count:.2f}")
        print(f"   📊 平均和值: {avg_sum:.2f}")
        
        return {
            'avg_odd_count': avg_odd_count,
            'avg_sum': avg_sum,
            'odd_counts': odd_counts,
            'sum_values': sum_values
        }
    
    def generate_simple_prediction(self):
        """生成简单预测"""
        print("\n🎯 生成简单预测...")
        
        # 执行分析
        freq_analysis = self.simple_frequency_analysis()
        pattern_analysis = self.simple_pattern_analysis()
        
        # 策略1: 基于频率的预测
        hot_numbers = freq_analysis['hot_numbers']
        prediction_1 = random.sample(hot_numbers[:15], 6)  # 从热号中随机选6个
        special_1 = random.choice(hot_numbers[:10])  # 特码从热号中选
        
        # 策略2: 混合策略
        mixed_pool = hot_numbers[:8] + freq_analysis['cold_numbers'][:8]  # 热号+冷号
        prediction_2 = sorted(random.sample(mixed_pool, 6))
        special_2 = random.randint(1, 49)
        
        # 策略3: 基于模式的预测
        # 根据历史奇偶比例调整
        target_odd_count = round(pattern_analysis['avg_odd_count'])
        prediction_3 = self.generate_by_odd_count(target_odd_count)
        special_3 = random.randint(1, 49)
        
        predictions = {
            'frequency_based': {
                'regular_numbers': sorted(prediction_1),
                'special_number': special_1,
                'strategy': '基于频率分析',
                'confidence': 'medium'
            },
            'mixed_strategy': {
                'regular_numbers': prediction_2,
                'special_number': special_2,
                'strategy': '混合策略（热号+冷号）',
                'confidence': 'medium'
            },
            'pattern_based': {
                'regular_numbers': prediction_3,
                'special_number': special_3,
                'strategy': '基于模式分析',
                'confidence': 'low'
            }
        }
        
        return predictions
    
    def generate_by_odd_count(self, target_odd_count):
        """根据目标奇数个数生成号码"""
        odd_numbers = [i for i in range(1, 50) if i % 2 == 1]
        even_numbers = [i for i in range(1, 50) if i % 2 == 0]
        
        target_odd_count = max(0, min(6, target_odd_count))  # 限制在0-6之间
        target_even_count = 6 - target_odd_count
        
        selected_odds = random.sample(odd_numbers, target_odd_count) if target_odd_count > 0 else []
        selected_evens = random.sample(even_numbers, target_even_count) if target_even_count > 0 else []
        
        return sorted(selected_odds + selected_evens)
    
    def display_predictions(self, predictions):
        """显示预测结果"""
        print("\n🎊 预测结果:")
        print("=" * 50)
        
        for strategy_name, pred in predictions.items():
            print(f"\n📋 {pred['strategy']}:")
            print(f"   正码: {pred['regular_numbers']}")
            print(f"   特码: {pred['special_number']}")
            print(f"   置信度: {pred['confidence']}")
        
        # 生成融合预测
        print(f"\n🔀 融合预测 (综合多种策略):")
        all_regular = []
        all_special = []
        
        for pred in predictions.values():
            all_regular.extend(pred['regular_numbers'])
            all_special.append(pred['special_number'])
        
        # 统计频率并选择最常见的号码
        from collections import Counter
        regular_freq = Counter(all_regular)
        fusion_regular = [num for num, freq in regular_freq.most_common(6)]
        fusion_special = Counter(all_special).most_common(1)[0][0]
        
        print(f"   正码: {sorted(fusion_regular)}")
        print(f"   特码: {fusion_special}")
        print(f"   置信度: high")
    
    def run_test(self):
        """运行完整测试"""
        print("🚀 开始预测功能测试...")
        print("=" * 60)
        
        # 显示测试数据概览
        print(f"\n📊 测试数据概览:")
        print(f"   数据量: {len(self.test_data)} 条")
        print(f"   日期范围: {self.test_data['draw_date'].min()} 到 {self.test_data['draw_date'].max()}")
        
        # 显示最近几期数据
        print(f"\n📋 最近5期历史数据:")
        for i in range(min(5, len(self.test_data))):
            row = self.test_data.iloc[-(i+1)]
            print(f"   {row['draw_date']}: 正码 {row['regular_numbers']}, 特码 {row['special_number']}")
        
        # 生成预测
        predictions = self.generate_simple_prediction()
        
        # 显示预测结果
        self.display_predictions(predictions)
        
        # 生成下期信息
        last_date = pd.to_datetime(self.test_data['draw_date'].max())
        next_date = (last_date + timedelta(days=1)).strftime('%Y-%m-%d')
        
        print(f"\n📅 预测期号: {next_date}")
        print(f"📅 预测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("\n✅ 预测功能测试完成！")
        
        return predictions

def main():
    """主函数"""
    print("🧪 澳门六合彩预测功能测试")
    
    # 创建测试器
    tester = SimplePredictionTest()
    
    # 运行测试
    predictions = tester.run_test()
    
    print("\n💡 说明:")
    print("   • 这是基于随机生成数据的测试")
    print("   • 实际预测需要真实的历史数据")
    print("   • 预测结果仅供测试参考")

if __name__ == "__main__":
    main()
