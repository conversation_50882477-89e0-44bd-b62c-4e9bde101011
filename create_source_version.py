"""
创建源码版封包
"""
import os
import shutil
import zipfile
import json
from datetime import datetime

def create_source_version():
    """创建源码版"""
    print("📦 创建源码版封包")
    print("=" * 50)
    
    # 1. 创建源码版目录结构
    source_dir = "LotteryPrediction_Source_v1.0.0"
    
    if os.path.exists(source_dir):
        shutil.rmtree(source_dir)
    
    print(f"📁 创建源码版目录: {source_dir}")
    
    # 创建完整目录结构
    dirs_to_create = [
        f"{source_dir}",
        f"{source_dir}/src",
        f"{source_dir}/data",
        f"{source_dir}/config", 
        f"{source_dir}/docs",
        f"{source_dir}/tests",
        f"{source_dir}/logs",
        f"{source_dir}/output",
        f"{source_dir}/backup",
        f"{source_dir}/scripts",
        f"{source_dir}/examples",
        f"{source_dir}/tools"
    ]
    
    for dir_path in dirs_to_create:
        os.makedirs(dir_path, exist_ok=True)
        print(f"   ✅ {dir_path}")
    
    # 2. 复制完整源码
    print(f"\n📄 复制完整源码:")
    
    # 复制整个src目录
    if os.path.exists('src'):
        src_dest = f'{source_dir}/src'
        for root, dirs, files in os.walk('src'):
            for file in files:
                if file.endswith(('.py', '.json', '.txt', '.md')):
                    src_path = os.path.join(root, file)
                    rel_path = os.path.relpath(src_path, 'src')
                    dst_path = os.path.join(src_dest, rel_path)
                    
                    # 确保目标目录存在
                    dst_dir = os.path.dirname(dst_path)
                    if dst_dir and not os.path.exists(dst_dir):
                        os.makedirs(dst_dir, exist_ok=True)
                    
                    shutil.copy2(src_path, dst_path)
                    print(f"   ✅ {src_path} → {dst_path}")
    
    # 3. 复制配置文件
    print(f"\n🔧 复制配置文件:")
    
    config_files = [
        'optimal_config.json',
        'enhanced_backtest_config.json',
        'config_summary.json',
        'requirements.txt',
        'package_info.json'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            dst_path = f'{source_dir}/config/{config_file}'
            shutil.copy2(config_file, dst_path)
            print(f"   ✅ {config_file} → {dst_path}")
        else:
            print(f"   ⚠️ {config_file}: 文件不存在")
    
    # 4. 复制根目录文件
    print(f"\n📋 复制根目录文件:")
    
    root_files = [
        'requirements.txt',
        'package_info.json'
    ]
    
    for root_file in root_files:
        if os.path.exists(root_file):
            dst_path = f'{source_dir}/{root_file}'
            shutil.copy2(root_file, dst_path)
            print(f"   ✅ {root_file} → {dst_path}")
    
    # 5. 创建开发工具脚本
    print(f"\n🛠️ 创建开发工具脚本:")
    
    # 开发环境设置脚本
    setup_script = f'''@echo off
chcp 65001 > nul
title 澳门六合彩智能预测系统 - 开发环境设置

echo.
echo ========================================
echo   澳门六合彩智能预测系统 - 源码版
echo   开发环境设置脚本
echo   版本: v1.0.0
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请确保已安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 📦 安装开发依赖...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo.
echo 🧪 运行测试...
python -m pytest tests/ -v
if errorlevel 1 (
    echo ⚠️ 部分测试失败，但不影响基本功能
)

echo.
echo 🚀 启动开发服务器...
python -c "
import sys
sys.path.append('src')
from perfect_prediction_system import PerfectPredictionSystem

print('🎯 澳门六合彩智能预测系统 - 开发模式')
print('=' * 50)

try:
    system = PerfectPredictionSystem()
    system.initialize_modules()
    
    print('✅ 开发环境初始化成功')
    print('📊 配置源:', system.config_source)
    print('🔧 增强配置:', system.enhanced_config_loaded)
    print()
    print('🎯 可用功能:')
    print('  1. 完美预测: system.run_complete_prediction(date)')
    print('  2. 增强回测: 运行增强回测脚本')
    print('  3. 特码预测: 使用特码预测模组')
    print('  4. 模组测试: 运行单元测试')
    print()
    print('📚 开发文档: docs/ 目录')
    print('🧪 测试文件: tests/ 目录')
    print('🔧 工具脚本: scripts/ 目录')
    print()
    print('开发环境准备完成!')
    
except Exception as e:
    print(f'❌ 初始化失败: {{e}}')
"

echo.
echo 开发环境设置完成!
echo 按任意键退出...
pause > nul
'''
    
    with open(f'{source_dir}/setup_dev_env.bat', 'w', encoding='utf-8') as f:
        f.write(setup_script)
    
    print(f"   ✅ setup_dev_env.bat")
    
    # 测试运行脚本
    test_script = f'''@echo off
chcp 65001 > nul
title 运行测试套件

echo.
echo ========================================
echo   澳门六合彩智能预测系统 - 测试套件
echo ========================================
echo.

echo 🧪 运行单元测试...
python -m pytest tests/ -v --tb=short

echo.
echo 🔍 运行代码质量检查...
python -m flake8 src/ --max-line-length=120 --ignore=E501,W503

echo.
echo 📊 运行性能测试...
python scripts/performance_test.py

echo.
echo 测试完成!
pause
'''
    
    with open(f'{source_dir}/run_tests.bat', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print(f"   ✅ run_tests.bat")
    
    # 6. 创建示例脚本
    print(f"\n📚 创建示例脚本:")
    
    # 基础使用示例
    basic_example = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
澳门六合彩智能预测系统 - 基础使用示例
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from perfect_prediction_system import PerfectPredictionSystem

def basic_prediction_example():
    """基础预测示例"""
    print("🎯 基础预测示例")
    print("=" * 30)
    
    # 创建预测系统
    system = PerfectPredictionSystem()
    system.initialize_modules()
    
    print("✅ 系统初始化完成")
    
    # 运行预测
    target_date = "2025-06-23"
    result = system.run_complete_prediction(target_date)
    
    # 显示结果
    final_results = result.get('final_results', {{}})
    if final_results:
        numbers = final_results.get('recommended_16_numbers', [])
        zodiacs = final_results.get('recommended_4_zodiacs', [])
        confidence = final_results.get('overall_confidence', 0)
        
        print(f"📊 推荐号码: {{numbers}}")
        print(f"🐲 推荐生肖: {{zodiacs}}")
        print(f"📈 置信度: {{confidence}}")
    
    return result

def advanced_prediction_example():
    """高级预测示例"""
    print("\\n🚀 高级预测示例")
    print("=" * 30)
    
    system = PerfectPredictionSystem()
    system.initialize_modules()
    
    # 检查配置状态
    config_info = system.get_config_info()
    print(f"📊 配置信息: {{config_info}}")
    
    # 运行多次预测进行对比
    dates = ["2025-06-23", "2025-06-24", "2025-06-25"]
    results = []
    
    for date in dates:
        result = system.run_complete_prediction(date)
        results.append(result)
        print(f"✅ {{date}} 预测完成")
    
    # 分析结果
    print("\\n📈 预测结果分析:")
    for i, result in enumerate(results):
        final = result.get('final_results', {{}})
        confidence = final.get('overall_confidence', 0)
        numbers_count = len(final.get('recommended_16_numbers', []))
        print(f"  {{dates[i]}}: 置信度{{confidence}}, 号码{{numbers_count}}个")
    
    return results

if __name__ == "__main__":
    try:
        # 运行基础示例
        basic_result = basic_prediction_example()
        
        # 运行高级示例
        advanced_results = advanced_prediction_example()
        
        print("\\n🎊 示例运行完成!")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {{e}}")
        import traceback
        traceback.print_exc()
'''
    
    with open(f'{source_dir}/examples/basic_usage.py', 'w', encoding='utf-8') as f:
        f.write(basic_example)
    
    print(f"   ✅ examples/basic_usage.py")
    
    # 7. 创建开发文档
    print(f"\n📚 创建开发文档:")
    
    dev_readme = f'''# 澳门六合彩智能预测系统 - 源码版

## 📋 版本信息
- **版本**: v1.0.0
- **发布日期**: {datetime.now().strftime("%Y-%m-%d")}
- **类型**: 源码版 (Source Code)
- **平台**: Windows 11
- **Python版本**: 3.8+

## 🚀 快速开始

### 1. 环境设置
```bash
# 运行开发环境设置脚本
setup_dev_env.bat

# 或手动安装依赖
pip install -r requirements.txt
```

### 2. 运行系统
```python
import sys
sys.path.append('src')
from perfect_prediction_system import PerfectPredictionSystem

# 创建系统实例
system = PerfectPredictionSystem()
system.initialize_modules()

# 运行预测
result = system.run_complete_prediction("2025-06-23")
print(result)
```

### 3. 运行测试
```bash
# 运行完整测试套件
run_tests.bat

# 或手动运行
python -m pytest tests/ -v
```

## 📁 项目结构
```
LotteryPrediction_Source_v1.0.0/
├── src/                           # 源代码目录
│   ├── algorithm_layer/           # 算法层
│   │   ├── traditional_analysis/  # 传统分析
│   │   ├── ml_models/            # 机器学习模型
│   │   ├── zodiac_extended/      # 生肖扩展分析
│   │   └── ...
│   ├── data_layer/               # 数据层
│   ├── application_layer/        # 应用层
│   ├── evaluation_layer/         # 评估层
│   └── utility_layer/            # 工具层
├── config/                       # 配置文件
├── data/                         # 数据目录
├── docs/                         # 文档目录
├── tests/                        # 测试目录
├── examples/                     # 示例代码
├── scripts/                      # 工具脚本
├── tools/                        # 开发工具
├── requirements.txt              # 依赖包列表
└── README.md                     # 说明文档
```

## 🔧 开发指南

### 核心模组
1. **完美预测系统** (`perfect_prediction_system.py`)
   - 主控制器，协调所有模组
   - 配置管理和模组初始化
   - 预测流程控制

2. **融合管理器** (`fusion_manager.py`)
   - 多模组结果融合
   - 权重管理和优化
   - 智能融合算法

3. **算法层模组**
   - 传统统计分析
   - 机器学习预测
   - 生肖扩展分析
   - 特码生肖预测

### 开发工作流
1. **代码修改** → 在 `src/` 目录下修改源码
2. **测试验证** → 运行 `run_tests.bat` 验证修改
3. **功能测试** → 使用 `examples/` 中的示例测试
4. **性能测试** → 运行性能测试脚本

### 配置管理
- **基础配置**: `config/optimal_config.json`
- **增强配置**: `config/enhanced_backtest_config.json`
- **系统配置**: 在代码中动态配置

## 🧪 测试框架

### 测试类型
- **单元测试**: 测试单个模组功能
- **集成测试**: 测试模组间协作
- **性能测试**: 测试系统性能
- **功能测试**: 测试完整功能流程

### 运行测试
```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试
python -m pytest tests/test_perfect_prediction.py -v

# 运行性能测试
python scripts/performance_test.py
```

## 📊 性能优化

### 优化建议
1. **算法优化**: 优化核心算法性能
2. **内存管理**: 优化内存使用
3. **并行处理**: 使用多线程/多进程
4. **缓存机制**: 实现智能缓存

### 监控指标
- 预测执行时间
- 内存使用量
- 预测准确性
- 系统稳定性

## 🔌 扩展开发

### 添加新模组
1. 在 `src/algorithm_layer/` 下创建新目录
2. 实现预测接口 `predict(target_date)`
3. 在 `perfect_prediction_system.py` 中注册
4. 添加相应测试

### 自定义配置
1. 修改 `config/` 目录下的配置文件
2. 在代码中读取和应用配置
3. 验证配置有效性

## 🐛 调试指南

### 日志系统
- 日志文件保存在 `logs/` 目录
- 支持不同级别的日志记录
- 可配置日志输出格式

### 常见问题
1. **模组初始化失败**: 检查依赖包安装
2. **预测结果异常**: 检查配置文件
3. **性能问题**: 使用性能分析工具

## 📚 API 文档

### 核心类
- `PerfectPredictionSystem`: 主预测系统
- `FusionManager`: 融合管理器
- `各算法模组`: 具体算法实现

### 主要方法
- `initialize_modules()`: 初始化所有模组
- `run_complete_prediction(date)`: 运行完整预测
- `get_config_info()`: 获取配置信息

## 🤝 贡献指南

### 代码规范
- 遵循 PEP 8 代码风格
- 添加适当的注释和文档
- 编写相应的测试用例

### 提交流程
1. Fork 项目
2. 创建功能分支
3. 提交代码修改
4. 运行测试验证
5. 提交 Pull Request

## 📞 技术支持

### 获取帮助
- 查看 `docs/` 目录下的详细文档
- 运行 `examples/` 中的示例代码
- 检查 `logs/` 目录下的日志文件

### 报告问题
- 提供详细的错误信息
- 包含重现步骤
- 附上相关日志文件

---
© 2025 澳门六合彩智能预测系统 - 源码版
'''
    
    with open(f'{source_dir}/README.md', 'w', encoding='utf-8') as f:
        f.write(dev_readme)
    
    print(f"   ✅ README.md")
    
    # 8. 创建开发者配置文件
    dev_config = {
        "development": {
            "debug_mode": True,
            "log_level": "DEBUG",
            "test_mode": True,
            "performance_monitoring": True
        },
        "testing": {
            "test_data_path": "tests/data/",
            "mock_modules": True,
            "test_coverage": True
        },
        "build": {
            "version": "1.0.0",
            "build_type": "source",
            "target_platform": "Windows 11",
            "python_version": "3.8+"
        }
    }
    
    with open(f'{source_dir}/config/dev_config.json', 'w', encoding='utf-8') as f:
        json.dump(dev_config, f, ensure_ascii=False, indent=2)
    
    print(f"   ✅ config/dev_config.json")
    
    # 9. 创建性能测试脚本
    performance_test = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试脚本
"""
import sys
import os
import time
import psutil
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from perfect_prediction_system import PerfectPredictionSystem

def performance_test():
    """性能测试"""
    print("📊 性能测试")
    print("=" * 30)
    
    # 记录初始内存
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    print(f"🔍 初始内存使用: {{initial_memory:.2f}} MB")
    
    # 测试系统初始化时间
    start_time = time.time()
    system = PerfectPredictionSystem()
    system.initialize_modules()
    init_time = time.time() - start_time
    
    print(f"⚡ 系统初始化时间: {{init_time:.3f}} 秒")
    
    # 测试预测执行时间
    dates = ["2025-06-23", "2025-06-24", "2025-06-25"]
    prediction_times = []
    
    for date in dates:
        start_time = time.time()
        result = system.run_complete_prediction(date)
        prediction_time = time.time() - start_time
        prediction_times.append(prediction_time)
        
        print(f"🎯 {{date}} 预测时间: {{prediction_time:.3f}} 秒")
    
    # 计算平均性能
    avg_prediction_time = sum(prediction_times) / len(prediction_times)
    print(f"�� 平均预测时间: {{avg_prediction_time:.3f}} 秒")
    
    # 检查内存使用
    final_memory = process.memory_info().rss / 1024 / 1024  # MB
    memory_increase = final_memory - initial_memory
    
    print(f"💾 最终内存使用: {{final_memory:.2f}} MB")
    print(f"📊 内存增长: {{memory_increase:.2f}} MB")
    
    # 性能评级
    if avg_prediction_time < 1.0:
        performance_rating = "优秀"
    elif avg_prediction_time < 3.0:
        performance_rating = "良好"
    elif avg_prediction_time < 5.0:
        performance_rating = "中等"
    else:
        performance_rating = "需要优化"
    
    print(f"🏆 性能评级: {{performance_rating}}")
    
    return {{
        'init_time': init_time,
        'avg_prediction_time': avg_prediction_time,
        'memory_usage': final_memory,
        'memory_increase': memory_increase,
        'performance_rating': performance_rating
    }}

if __name__ == "__main__":
    try:
        result = performance_test()
        print("\\n🎊 性能测试完成!")
    except Exception as e:
        print(f"❌ 性能测试失败: {{e}}")
'''
    
    with open(f'{source_dir}/scripts/performance_test.py', 'w', encoding='utf-8') as f:
        f.write(performance_test)
    
    print(f"   ✅ scripts/performance_test.py")
    
    # 10. 创建版本信息
    version_info = {
        "version": "1.0.0",
        "build_date": datetime.now().isoformat(),
        "build_type": "source",
        "platform": "Windows 11",
        "python_version": "3.8+",
        "features": [
            "完整源代码",
            "开发工具",
            "测试框架",
            "示例代码",
            "详细文档",
            "性能测试"
        ],
        "development_features": [
            "调试模式",
            "单元测试",
            "性能监控",
            "代码质量检查",
            "开发文档"
        ]
    }
    
    with open(f'{source_dir}/version_info.json', 'w', encoding='utf-8') as f:
        json.dump(version_info, f, ensure_ascii=False, indent=2)
    
    print(f"   ✅ version_info.json")
    
    # 11. 创建ZIP压缩包
    print(f"\n📦 创建ZIP压缩包:")
    
    zip_filename = f"{source_dir}.zip"
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_path = os.path.relpath(file_path, '.')
                zipf.write(file_path, arc_path)
    
    # 获取压缩包大小
    zip_size = os.path.getsize(zip_filename)
    zip_size_mb = zip_size / (1024 * 1024)
    
    print(f"   ✅ {zip_filename}")
    print(f"   📊 压缩包大小: {zip_size_mb:.2f} MB")
    
    # 统计文件数量
    total_files = 0
    for root, dirs, files in os.walk(source_dir):
        total_files += len(files)
    
    return {
        'source_dir': source_dir,
        'zip_file': zip_filename,
        'size_mb': zip_size_mb,
        'total_files': total_files
    }

if __name__ == "__main__":
    result = create_source_version()
    
    print(f"\n🎊 源码版创建完成!")
    print(f"📁 目录: {result['source_dir']}")
    print(f"📦 压缩包: {result['zip_file']}")
    print(f"📊 大小: {result['size_mb']:.2f} MB")
    print(f"📄 文件数: {result['total_files']}")
