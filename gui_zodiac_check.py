import re

# 读取GUI文件
with open("lottery_prediction_gui.py", "r", encoding="utf-8") as f:
    content = f.read()

print("🖥️ GUI生肖显示逻辑检查")
print("=" * 40)

# 查找处理完美预测结果的方法
perfect_prediction_methods = [
    "run_perfect_prediction",
    "display_perfect_prediction_results",
    "update_perfect_prediction_display",
    "show_perfect_prediction_results"
]

found_methods = []

for method in perfect_prediction_methods:
    if method in content:
        found_methods.append(method)
        print(f"✅ 找到方法: {method}")
        
        # 提取方法内容
        start_idx = content.find(f"def {method}")
        if start_idx != -1:
            lines = content[start_idx:].split("\n")
            method_lines = []
            
            for i, line in enumerate(lines):
                if i > 0 and line.strip().startswith("def ") and method not in line:
                    break
                method_lines.append(line)
                if len(method_lines) > 30:  # 限制输出长度
                    break
            
            # 检查是否处理生肖
            zodiac_found = False
            for line in method_lines:
                if "zodiac" in line.lower() or "生肖" in line:
                    zodiac_found = True
                    print(f"      🐲 生肖处理: {line.strip()}")
            
            if not zodiac_found:
                print(f"      ❌ 未找到生肖处理逻辑")
    else:
        print(f"❌ 未找到方法: {method}")

# 查找生肖显示相关的控件
print(f"\n🔍 查找生肖显示控件:")
zodiac_widget_patterns = [
    r"self\.(\w*zodiac\w*)\s*=\s*Q\w+",
    r"self\.(\w*生肖\w*)\s*=\s*Q\w+",
    r"(\w*zodiac\w*_label)",
    r"(\w*zodiac\w*_text)"
]

found_widgets = []

for pattern in zodiac_widget_patterns:
    matches = re.finditer(pattern, content, re.IGNORECASE)
    for match in matches:
        widget_name = match.group(1)
        found_widgets.append(widget_name)
        print(f"   ✅ 生肖控件: {widget_name}")

if not found_widgets:
    print(f"   ❌ 未找到生肖显示控件")

# 查找结果显示相关的代码
print(f"\n📊 查找结果显示逻辑:")
result_display_patterns = [
    r"result\[\"recommended_4_zodiacs\"\]",
    r"result\[\".*zodiac.*\"\]",
    r"\.setText\([^)]*zodiac[^)]*\)",
    r"\.setText\([^)]*生肖[^)]*\)"
]

found_displays = []

for pattern in result_display_patterns:
    matches = re.finditer(pattern, content, re.IGNORECASE)
    for match in matches:
        display_code = match.group(0)
        found_displays.append(display_code)
        print(f"   ✅ 显示代码: {display_code}")

if not found_displays:
    print(f"   ❌ 未找到生肖显示代码")

# 查找完美预测标签页的创建
print(f"\n🏗️ 查找完美预测标签页创建:")
if "create_perfect_prediction_tab" in content:
    print(f"✅ 找到create_perfect_prediction_tab方法")
    
    # 提取方法内容查找生肖相关控件
    start_idx = content.find("def create_perfect_prediction_tab")
    method_section = content[start_idx:start_idx+2000]
    
    zodiac_controls = []
    lines = method_section.split('\n')
    for line in lines:
        if "zodiac" in line.lower() or "生肖" in line:
            zodiac_controls.append(line.strip())
    
    if zodiac_controls:
        print(f"   🐲 生肖控件创建:")
        for control in zodiac_controls[:5]:
            print(f"      {control}")
    else:
        print(f"   ❌ 未找到生肖控件创建代码")
else:
    print(f"❌ 未找到create_perfect_prediction_tab方法")

print(f"\n📋 总结:")
print(f"   找到的方法: {len(found_methods)}")
print(f"   找到的生肖控件: {len(found_widgets)}")
print(f"   找到的显示代码: {len(found_displays)}")
