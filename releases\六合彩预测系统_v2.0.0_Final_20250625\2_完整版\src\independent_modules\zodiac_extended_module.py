"""
生肖扩展模组 - 独立预测和回测
支持多维度生肖分析和号码预测
"""
import sqlite3
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import Counter
import json

class ZodiacExtendedModule:
    """生肖扩展模组"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        self.db_path = db_path
        self.module_name = "生肖扩展模组"
        
        # 生肖映射 (2025年)
        self.zodiac_mapping = self._get_zodiac_mapping()
        self.reverse_zodiac_mapping = self._get_reverse_zodiac_mapping()
        
        # 生肖属性配置
        self.zodiac_attributes = {
            "波色": {
                "红肖": ["鼠", "兔", "马", "鸡"],
                "绿肖": ["牛", "龙", "羊", "狗"], 
                "蓝肖": ["虎", "蛇", "猴", "猪"]
            },
            "季节": {
                "春": ["虎", "兔", "龙"],
                "夏": ["蛇", "马", "羊"],
                "秋": ["猴", "鸡", "狗"],
                "冬": ["猪", "鼠", "牛"]
            },
            "阴阳": {
                "阳": ["鼠", "虎", "龙", "马", "猴", "狗"],
                "阴": ["牛", "兔", "蛇", "羊", "鸡", "猪"]
            },
            "五行": {
                "金": ["猴", "鸡"],
                "木": ["虎", "兔"],
                "水": ["鼠", "猪"],
                "火": ["马", "蛇"],
                "土": ["牛", "龙", "羊", "狗"]
            }
        }
        
        # 分析配置
        self.analysis_config = {
            "zodiac_window": 60,        # 生肖分析窗口
            "pattern_window": 30,       # 模式分析窗口
            "attribute_weight": 0.3,    # 属性权重
            "frequency_weight": 0.4,    # 频率权重
            "pattern_weight": 0.3       # 模式权重
        }
    
    def predict(self, target_date: str, analysis_days: int = 100) -> Dict[str, Any]:
        """独立预测方法"""
        print(f"🐲 {self.module_name}开始预测 - 目标日期: {target_date}")
        
        # 获取历史数据
        historical_data = self._get_historical_data(target_date, analysis_days)
        
        if len(historical_data) < 30:
            return self._get_default_result()
        
        # 生肖频率分析
        zodiac_frequency = self._analyze_zodiac_frequency(historical_data)
        
        # 生肖属性分析
        attribute_analysis = self._analyze_zodiac_attributes(historical_data)
        
        # 生肖模式分析
        pattern_analysis = self._analyze_zodiac_patterns(historical_data)
        
        # 综合评分
        zodiac_scores = self._calculate_zodiac_scores(
            zodiac_frequency, attribute_analysis, pattern_analysis
        )
        
        # 选择推荐生肖
        recommended_zodiacs = self._select_recommended_zodiacs(zodiac_scores)
        
        # 生成推荐号码
        recommended_numbers = self._generate_numbers_from_zodiacs(recommended_zodiacs)
        
        # 计算置信度
        confidence = self._calculate_confidence(zodiac_scores, recommended_zodiacs)
        
        result = {
            "predicted_numbers": recommended_numbers,
            "confidence": confidence,
            "recommended_zodiacs": recommended_zodiacs,
            "zodiac_scores": zodiac_scores,
            "analysis_details": {
                "frequency": zodiac_frequency,
                "attributes": attribute_analysis,
                "patterns": pattern_analysis
            },
            "metadata": {
                "analysis_days": analysis_days,
                "data_count": len(historical_data),
                "prediction_time": datetime.now().isoformat()
            }
        }
        
        print(f"✅ {self.module_name}预测完成 - 推荐{len(recommended_numbers)}个号码")
        return result
    
    def predict_historical(self, target_date: str, window_size: int = 30) -> Dict[str, Any]:
        """历史预测方法（用于回测）"""
        end_date = datetime.strptime(target_date, "%Y-%m-%d") - timedelta(days=1)
        end_date_str = end_date.strftime("%Y-%m-%d")
        
        return self.predict(end_date_str, window_size)
    
    def run_backtest(self, start_date: str, end_date: str, window_size: int = 30) -> Dict[str, Any]:
        """运行回测"""
        print(f"🔄 {self.module_name}开始回测: {start_date} 到 {end_date}")
        
        test_dates = self._get_test_dates(start_date, end_date)
        results = []
        
        for test_date in test_dates:
            try:
                actual_number = self._get_actual_number(test_date)
                if actual_number is None:
                    continue
                
                prediction = self.predict_historical(test_date, window_size)
                predicted_numbers = prediction["predicted_numbers"]
                
                hit = actual_number in predicted_numbers
                hit_rank = predicted_numbers.index(actual_number) + 1 if hit else None
                
                # 生肖命中分析
                actual_zodiac = self.zodiac_mapping.get(actual_number, "未知")
                predicted_zodiacs = prediction["recommended_zodiacs"]
                zodiac_hit = actual_zodiac in predicted_zodiacs
                
                result = {
                    "test_date": test_date,
                    "actual_number": actual_number,
                    "actual_zodiac": actual_zodiac,
                    "predicted_numbers": predicted_numbers,
                    "predicted_zodiacs": predicted_zodiacs,
                    "hit": hit,
                    "zodiac_hit": zodiac_hit,
                    "hit_rank": hit_rank,
                    "confidence": prediction["confidence"],
                    "zodiac_scores": prediction["zodiac_scores"]
                }
                
                results.append(result)
                print(f"  {test_date}: 实际={actual_number}({actual_zodiac}), 命中={'✅' if hit else '❌'}, 生肖={'✅' if zodiac_hit else '❌'}")
                
            except Exception as e:
                print(f"  {test_date}: 回测失败 - {e}")
        
        hit_count = sum(1 for r in results if r["hit"])
        zodiac_hit_count = sum(1 for r in results if r["zodiac_hit"])
        hit_rate = hit_count / len(results) if results else 0
        zodiac_hit_rate = zodiac_hit_count / len(results) if results else 0
        
        print(f"✅ {self.module_name}回测完成:")
        print(f"   号码命中: {hit_count}/{len(results)} = {hit_rate:.1%}")
        print(f"   生肖命中: {zodiac_hit_count}/{len(results)} = {zodiac_hit_rate:.1%}")
        
        return {
            "results": results,
            "statistics": {
                "total_tests": len(results),
                "hit_count": hit_count,
                "zodiac_hit_count": zodiac_hit_count,
                "hit_rate": hit_rate,
                "zodiac_hit_rate": zodiac_hit_rate,
                "avg_confidence": np.mean([r["confidence"] for r in results]) if results else 0
            }
        }
    
    def _get_historical_data(self, before_date: str, days: int) -> List[Dict[str, Any]]:
        """获取历史数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT draw_date, special_number, period_number
            FROM lottery_results 
            WHERE draw_date < ? 
            ORDER BY draw_date DESC 
            LIMIT ?
        ''', (before_date, days))
        
        data = []
        for row in cursor.fetchall():
            special_number = row[1]
            zodiac = self.zodiac_mapping.get(special_number, "未知")
            
            data.append({
                "draw_date": row[0],
                "special_number": special_number,
                "zodiac": zodiac,
                "period_number": row[2]
            })
        
        conn.close()
        return data
    
    def _analyze_zodiac_frequency(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """生肖频率分析"""
        window = self.analysis_config["zodiac_window"]
        recent_data = historical_data[:window]
        
        zodiac_counts = Counter(record["zodiac"] for record in recent_data)
        total_count = len(recent_data)
        
        # 计算频率
        zodiac_frequencies = {}
        for zodiac in ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]:
            count = zodiac_counts.get(zodiac, 0)
            zodiac_frequencies[zodiac] = count / total_count if total_count > 0 else 0
        
        # 找出热门和冷门生肖
        avg_frequency = 1/12  # 理论平均频率
        hot_zodiacs = [zodiac for zodiac, freq in zodiac_frequencies.items() if freq > avg_frequency * 1.3]
        cold_zodiacs = [zodiac for zodiac, freq in zodiac_frequencies.items() if freq < avg_frequency * 0.7]
        
        return {
            "frequencies": zodiac_frequencies,
            "hot_zodiacs": hot_zodiacs,
            "cold_zodiacs": cold_zodiacs,
            "analysis_window": window
        }
    
    def _analyze_zodiac_attributes(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """生肖属性分析"""
        recent_data = historical_data[:self.analysis_config["pattern_window"]]
        
        attribute_stats = {}
        
        for attr_name, attr_groups in self.zodiac_attributes.items():
            group_counts = {}
            
            for group_name, zodiacs in attr_groups.items():
                count = sum(1 for record in recent_data if record["zodiac"] in zodiacs)
                group_counts[group_name] = count / len(recent_data) if recent_data else 0
            
            attribute_stats[attr_name] = group_counts
        
        return attribute_stats
    
    def _analyze_zodiac_patterns(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """生肖模式分析"""
        recent_zodiacs = [record["zodiac"] for record in historical_data[:self.analysis_config["pattern_window"]]]
        
        # 连续性分析
        consecutive_patterns = {}
        for i in range(len(recent_zodiacs) - 1):
            current = recent_zodiacs[i]
            next_zodiac = recent_zodiacs[i + 1]
            
            if current not in consecutive_patterns:
                consecutive_patterns[current] = {}
            
            if next_zodiac not in consecutive_patterns[current]:
                consecutive_patterns[current][next_zodiac] = 0
            
            consecutive_patterns[current][next_zodiac] += 1
        
        # 周期性分析
        zodiac_positions = {}
        for i, zodiac in enumerate(recent_zodiacs):
            if zodiac not in zodiac_positions:
                zodiac_positions[zodiac] = []
            zodiac_positions[zodiac].append(i)
        
        # 计算平均间隔
        avg_intervals = {}
        for zodiac, positions in zodiac_positions.items():
            if len(positions) > 1:
                intervals = [positions[i] - positions[i+1] for i in range(len(positions)-1)]
                avg_intervals[zodiac] = np.mean(intervals) if intervals else 0
            else:
                avg_intervals[zodiac] = len(recent_zodiacs)
        
        return {
            "consecutive_patterns": consecutive_patterns,
            "avg_intervals": avg_intervals,
            "recent_sequence": recent_zodiacs[:10]
        }
    
    def _calculate_zodiac_scores(self, frequency: Dict, attributes: Dict, patterns: Dict) -> Dict[str, float]:
        """计算生肖综合评分"""
        scores = {}
        
        for zodiac in ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]:
            score = 0.0
            
            # 频率得分
            freq_score = frequency["frequencies"].get(zodiac, 0) * 100
            score += freq_score * self.analysis_config["frequency_weight"]
            
            # 属性得分
            attr_score = 0
            for attr_name, attr_groups in self.zodiac_attributes.items():
                for group_name, zodiacs in attr_groups.items():
                    if zodiac in zodiacs:
                        group_freq = attributes.get(attr_name, {}).get(group_name, 0)
                        attr_score += group_freq * 25  # 每个属性组最高25分
            
            score += attr_score * self.analysis_config["attribute_weight"]
            
            # 模式得分
            interval = patterns["avg_intervals"].get(zodiac, 0)
            if interval > 0:
                pattern_score = min(100, 100 / interval)  # 间隔越短得分越高
            else:
                pattern_score = 50
            
            score += pattern_score * self.analysis_config["pattern_weight"]
            
            scores[zodiac] = score
        
        return scores
    
    def _select_recommended_zodiacs(self, zodiac_scores: Dict[str, float]) -> List[str]:
        """选择推荐生肖"""
        sorted_zodiacs = sorted(zodiac_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 选择前4个生肖
        recommended = [zodiac for zodiac, score in sorted_zodiacs[:4]]
        
        return recommended
    
    def _generate_numbers_from_zodiacs(self, recommended_zodiacs: List[str]) -> List[int]:
        """从推荐生肖生成号码"""
        numbers = []
        
        for zodiac in recommended_zodiacs:
            zodiac_numbers = self.reverse_zodiac_mapping.get(zodiac, [])
            numbers.extend(zodiac_numbers)
        
        # 去重并排序
        unique_numbers = sorted(list(set(numbers)))
        
        # 如果号码不足16个，补充相邻号码
        if len(unique_numbers) < 16:
            all_numbers = list(range(1, 50))
            for num in all_numbers:
                if num not in unique_numbers and len(unique_numbers) < 16:
                    # 检查是否与已选号码相邻
                    for selected in unique_numbers:
                        if abs(num - selected) <= 2:
                            unique_numbers.append(num)
                            break
        
        return sorted(unique_numbers[:16])
    
    def _calculate_confidence(self, zodiac_scores: Dict[str, float], 
                            recommended_zodiacs: List[str]) -> float:
        """计算置信度"""
        if not recommended_zodiacs:
            return 0.0
        
        recommended_scores = [zodiac_scores[zodiac] for zodiac in recommended_zodiacs]
        avg_recommended_score = np.mean(recommended_scores)
        
        all_scores = list(zodiac_scores.values())
        avg_all_score = np.mean(all_scores)
        
        if avg_all_score > 0:
            confidence = min(0.90, max(0.35, avg_recommended_score / avg_all_score / 2))
        else:
            confidence = 0.5
        
        return confidence
    
    def _get_zodiac_mapping(self) -> Dict[int, str]:
        """获取号码到生肖的映射"""
        zodiac_mapping = {}
        zodiacs = ["蛇", "马", "羊", "猴", "鸡", "狗", "猪", "鼠", "牛", "虎", "兔", "龙"]
        
        for i in range(1, 50):
            zodiac_mapping[i] = zodiacs[(i - 1) % 12]
        
        return zodiac_mapping
    
    def _get_reverse_zodiac_mapping(self) -> Dict[str, List[int]]:
        """获取生肖到号码的映射"""
        reverse_mapping = {}
        
        for number, zodiac in self.zodiac_mapping.items():
            if zodiac not in reverse_mapping:
                reverse_mapping[zodiac] = []
            reverse_mapping[zodiac].append(number)
        
        return reverse_mapping
    
    def _get_test_dates(self, start_date: str, end_date: str) -> List[str]:
        """获取测试日期"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT draw_date FROM lottery_results 
            WHERE draw_date BETWEEN ? AND ? 
            ORDER BY draw_date
        ''', (start_date, end_date))
        
        dates = [row[0] for row in cursor.fetchall()]
        conn.close()
        return dates
    
    def _get_actual_number(self, test_date: str) -> Optional[int]:
        """获取真实开奖号码"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT special_number FROM lottery_results 
            WHERE draw_date = ?
        ''', (test_date,))
        
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else None
    
    def _get_default_result(self) -> Dict[str, Any]:
        """获取默认结果"""
        default_zodiacs = ["鼠", "龙", "马", "猴"]
        default_numbers = []
        
        for zodiac in default_zodiacs:
            zodiac_numbers = self.reverse_zodiac_mapping.get(zodiac, [])
            default_numbers.extend(zodiac_numbers[:4])
        
        return {
            "predicted_numbers": sorted(default_numbers[:16]),
            "confidence": 0.35,
            "recommended_zodiacs": default_zodiacs,
            "zodiac_scores": {zodiac: 50.0 for zodiac in ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]},
            "analysis_details": {},
            "metadata": {"note": "数据不足，使用默认预测"}
        }
