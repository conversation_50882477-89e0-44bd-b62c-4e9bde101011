"""
创建完整的EXE集成封包
包含所有依赖、数据库、配置文件等
使用PyInstaller打包成单个EXE文件
"""

import os
import sys
import shutil
import subprocess
import json
from datetime import datetime
from pathlib import Path

class CompleteEXEPackager:
    """完整EXE封包器"""
    
    def __init__(self):
        self.version = "v2.0.0"
        self.release_date = datetime.now().strftime("%Y%m%d")
        self.package_name = f"LotteryPredictionSystem_Complete_{self.version}_{self.release_date}"
        
        # 创建工作目录
        self.work_dir = Path("exe_package_build")
        self.work_dir.mkdir(exist_ok=True)
        
        # 最终输出目录
        self.output_dir = Path("releases") / "complete_exe"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🎊 六合彩预测系统完整EXE封包工具")
        print(f"📦 版本: {self.version}")
        print(f"📅 发布日期: {self.release_date}")
        print(f"🔨 工作目录: {self.work_dir}")
        print(f"📁 输出目录: {self.output_dir}")
    
    def prepare_source_files(self):
        """准备源文件"""
        print("\n📋 准备源文件...")
        print("-" * 50)
        
        # 核心Python文件
        core_files = [
            "lottery_prediction_gui.py",
            "consistent_predictor.py", 
            "special_number_predictor.py"
        ]
        
        for file_name in core_files:
            if os.path.exists(file_name):
                shutil.copy2(file_name, self.work_dir)
                print(f"✅ 复制: {file_name}")
            else:
                print(f"❌ 缺失: {file_name}")
        
        # 复制src目录
        if os.path.exists("src"):
            if (self.work_dir / "src").exists():
                shutil.rmtree(self.work_dir / "src")
            shutil.copytree("src", self.work_dir / "src")
            print(f"✅ 复制: src/")
        
        # 复制data目录
        if os.path.exists("data"):
            if (self.work_dir / "data").exists():
                shutil.rmtree(self.work_dir / "data")
            shutil.copytree("data", self.work_dir / "data")
            print(f"✅ 复制: data/")
        
        # 复制配置文件
        config_files = ["*.json", "*.txt", "*.ini", "*.cfg"]
        for pattern in config_files:
            for file_path in Path(".").glob(pattern):
                if file_path.name not in ["version_info.json"]:
                    try:
                        shutil.copy2(file_path, self.work_dir)
                        print(f"✅ 复制: {file_path.name}")
                    except:
                        pass
    
    def install_dependencies(self):
        """安装所有依赖"""
        print("\n📦 安装所有依赖...")
        print("-" * 50)
        
        # 核心依赖
        dependencies = [
            "PyInstaller>=5.0",
            "PyQt5>=5.15.0",
            "numpy>=1.21.0",
            "pandas>=1.3.0", 
            "scikit-learn>=1.0.0",
            "matplotlib>=3.5.0",
            "seaborn>=0.11.0",
            "xgboost>=1.5.0",
            "loguru>=0.6.0",
            "sqlalchemy>=1.4.0"
        ]
        
        print("正在安装依赖包...")
        for dep in dependencies:
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", dep
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    print(f"✅ {dep}")
                else:
                    print(f"⚠️ {dep} (可能已安装)")
            except Exception as e:
                print(f"❌ {dep}: {e}")
        
        print("✅ 依赖安装完成")
    
    def create_main_entry_point(self):
        """创建主入口点"""
        print("\n🚀 创建主入口点...")
        print("-" * 50)
        
        main_entry = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六合彩预测系统主入口点
EXE版本专用启动器
"""

import sys
import os
from pathlib import Path
import warnings

# 忽略警告
warnings.filterwarnings("ignore")

def setup_environment():
    """设置运行环境"""
    # 获取程序运行目录
    if getattr(sys, 'frozen', False):
        # PyInstaller打包后的环境
        application_path = Path(sys.executable).parent
    else:
        # 开发环境
        application_path = Path(__file__).parent
    
    # 添加路径到sys.path
    sys.path.insert(0, str(application_path))
    sys.path.insert(0, str(application_path / "src"))
    
    # 设置工作目录
    os.chdir(application_path)
    
    print(f"🏠 程序目录: {application_path}")
    print(f"📁 工作目录: {os.getcwd()}")
    
    return application_path

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_modules = [
        ('PyQt5.QtWidgets', 'PyQt5'),
        ('numpy', 'numpy'),
        ('pandas', 'pandas'),
        ('sklearn', 'scikit-learn')
    ]
    
    missing = []
    for module_name, package_name in required_modules:
        try:
            __import__(module_name)
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name}")
            missing.append(package_name)
    
    if missing:
        print(f"⚠️ 缺失依赖: {', '.join(missing)}")
        return False
    
    return True

def start_gui():
    """启动GUI"""
    print("🚀 启动GUI界面...")
    
    try:
        # 导入GUI模块
        import lottery_prediction_gui
        
        # 检查是否有main函数
        if hasattr(lottery_prediction_gui, 'main'):
            lottery_prediction_gui.main()
        else:
            # 直接执行模块
            exec(open('lottery_prediction_gui.py', encoding='utf-8').read())
            
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        
        # 显示详细错误
        import traceback
        print("\\n📋 详细错误信息:")
        traceback.print_exc()
        
        # 尝试备用启动方法
        print("\\n🔄 尝试备用启动方法...")
        try:
            from PyQt5.QtWidgets import QApplication, QMessageBox
            
            app = QApplication(sys.argv)
            
            msg = QMessageBox()
            msg.setWindowTitle("启动失败")
            msg.setText(f"GUI启动失败:\\n{str(e)}")
            msg.setDetailedText(traceback.format_exc())
            msg.exec_()
            
        except Exception as e2:
            print(f"❌ 备用方法也失败: {e2}")
            input("按回车键退出...")

def main():
    """主函数"""
    print("🎊 六合彩预测系统 EXE版")
    print("=" * 50)
    
    # 设置环境
    app_path = setup_environment()
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败")
        input("按回车键退出...")
        return
    
    # 启动GUI
    start_gui()

if __name__ == "__main__":
    main()
'''
        
        main_file = self.work_dir / "main.py"
        with open(main_file, "w", encoding="utf-8") as f:
            f.write(main_entry)
        
        print(f"✅ 主入口点已创建: {main_file}")
        return main_file
    
    def create_pyinstaller_spec(self, main_file):
        """创建PyInstaller规格文件"""
        print("\n📝 创建PyInstaller规格文件...")
        print("-" * 50)
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 获取当前目录
current_dir = Path.cwd()

# 数据文件列表
datas = [
    (str(current_dir / 'src'), 'src'),
    (str(current_dir / 'data'), 'data'),
]

# 添加配置文件
config_files = ['*.json', '*.txt', '*.ini', '*.cfg']
for pattern in config_files:
    for file_path in current_dir.glob(pattern):
        if file_path.name not in ['version_info.json']:
            datas.append((str(file_path), '.'))

# 隐藏导入
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'numpy',
    'pandas',
    'sklearn',
    'sklearn.ensemble',
    'sklearn.svm',
    'sklearn.neighbors',
    'sklearn.naive_bayes',
    'sklearn.model_selection',
    'sklearn.preprocessing',
    'sklearn.metrics',
    'matplotlib',
    'seaborn',
    'sqlite3',
    'json',
    'datetime',
    'pathlib',
    'collections',
    'hashlib',
    'random',
    'warnings',
    'logging',
    'src.perfect_prediction_system',
    'src.dynamic_fusion_manager_v3',
    'src.algorithm_layer.ml_models.ml_predictor',
    'src.independent_modules.traditional_module',
    'src.independent_modules.ml_module',
    'src.independent_modules.zodiac_extended_module',
    'src.independent_modules.special_zodiac_module'
]

# 排除模块
excludes = [
    'tkinter',
    'unittest',
    'test',
    'distutils',
    'setuptools'
]

block_cipher = None

a = Analysis(
    ['{main_file.name}'],
    pathex=[str(current_dir)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.package_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
        
        spec_file = self.work_dir / f"{self.package_name}.spec"
        with open(spec_file, "w", encoding="utf-8") as f:
            f.write(spec_content)
        
        print(f"✅ 规格文件已创建: {spec_file}")
        return spec_file
    
    def build_exe(self, spec_file):
        """构建EXE文件"""
        print("\n🔨 构建EXE文件...")
        print("-" * 50)
        
        try:
            # 切换到工作目录
            original_cwd = os.getcwd()
            os.chdir(self.work_dir)
            
            # 运行PyInstaller
            cmd = [sys.executable, "-m", "PyInstaller", "--clean", str(spec_file.name)]
            print(f"执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            # 恢复原目录
            os.chdir(original_cwd)
            
            if result.returncode == 0:
                print("✅ EXE构建成功")
                
                # 查找生成的EXE文件
                dist_dir = self.work_dir / "dist"
                exe_file = dist_dir / f"{self.package_name}.exe"
                
                if exe_file.exists():
                    print(f"✅ EXE文件已生成: {exe_file}")
                    return exe_file
                else:
                    print("❌ 未找到生成的EXE文件")
                    return None
            else:
                print(f"❌ EXE构建失败: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"❌ 构建过程出错: {e}")
            return None
    
    def create_final_package(self, exe_file):
        """创建最终发布包"""
        print("\n📦 创建最终发布包...")
        print("-" * 50)
        
        if not exe_file or not exe_file.exists():
            print("❌ EXE文件不存在")
            return False
        
        # 创建最终包目录
        final_dir = self.output_dir / self.package_name
        if final_dir.exists():
            shutil.rmtree(final_dir)
        final_dir.mkdir(parents=True)
        
        # 复制EXE文件
        final_exe = final_dir / f"{self.package_name}.exe"
        shutil.copy2(exe_file, final_exe)
        print(f"✅ 复制EXE: {final_exe.name}")
        
        # 创建启动脚本
        self._create_launcher_script(final_dir)
        
        # 创建说明文档
        self._create_readme(final_dir)
        
        # 创建卸载脚本
        self._create_uninstaller(final_dir)
        
        # 获取文件大小
        exe_size = final_exe.stat().st_size / (1024 * 1024)
        print(f"✅ EXE大小: {exe_size:.1f}MB")
        
        return final_dir
    
    def _create_launcher_script(self, target_dir):
        """创建启动脚本"""
        launcher_content = f'''@echo off
chcp 65001 > nul
title 六合彩预测系统 {self.version} - EXE版

echo 🎊 六合彩预测系统 {self.version} - EXE版
echo ==========================================
echo 正在启动系统...

REM 检查EXE文件
if not exist "{self.package_name}.exe" (
    echo ❌ 错误: 未找到主程序文件
    echo 请确保所有文件完整
    pause
    exit /b 1
)

REM 启动程序
echo 🚀 启动系统...
echo 注意: 首次启动可能需要较长时间
echo 如果没有窗口显示，请检查任务栏或按Alt+Tab

start "" "{self.package_name}.exe"

REM 等待程序启动
timeout /t 3 > nul
echo ✅ 系统已启动

REM 检查是否需要保持窗口打开
set /p keep="是否保持此窗口打开？(y/N): "
if /i "%keep%"=="y" (
    echo 窗口将保持打开...
    pause
)
'''
        
        with open(target_dir / "启动系统.bat", "w", encoding="gbk", errors="ignore") as f:
            f.write(launcher_content)
        
        print("✅ 启动脚本已创建")
    
    def _create_readme(self, target_dir):
        """创建说明文档"""
        readme_content = f'''# 六合彩预测系统 {self.version} - EXE完整版

## 📋 版本信息
- 版本号: {self.version}
- 发布日期: {self.release_date}
- 包类型: EXE完整版 (集成所有依赖)

## 🚀 快速开始
1. 解压到任意目录
2. 双击 `启动系统.bat` 或直接运行 `{self.package_name}.exe`
3. 等待程序启动（首次启动可能较慢）
4. 开始使用系统

## 📦 系统要求
- Windows 7/8/10/11 (64位)
- 至少 4GB 可用内存
- 至少 1GB 可用磁盘空间
- **无需安装Python或其他依赖**

## 🔧 主要功能
- ✅ 特码预测 (16个推荐号码)
- ✅ 一致性预测 (确定性算法)
- ✅ 完美预测系统 (4模组融合)
- ✅ 历史回测分析
- ✅ 增强回测优化
- ✅ 综合评估系统
- ✅ 数据管理功能
- ✅ 机器学习集成
- ✅ 多维生肖分析

## 📁 文件说明
- `{self.package_name}.exe` - 主程序（集成版）
- `启动系统.bat` - 启动脚本
- `卸载系统.bat` - 卸载程序
- `README.md` - 说明文档

## 🎯 使用说明
1. **首次运行**: 程序会自动初始化数据库
2. **数据导入**: 在"数据管理"页面导入历史开奖数据
3. **开始预测**: 选择预测功能开始使用
4. **结果查看**: 预测结果会自动保存到TXT文件

## ⚠️ 注意事项
- **首次启动较慢**: EXE解压和初始化需要时间
- **杀毒软件**: 可能被误报，请添加到白名单
- **防火墙**: 如有提示请允许程序运行
- **预测结果仅供参考**: 请理性对待

## 🔧 故障排除
1. **程序无法启动**: 
   - 以管理员身份运行
   - 检查杀毒软件设置
   - 重新解压文件

2. **启动缓慢**:
   - 首次启动正常现象
   - 后续启动会更快

3. **界面无响应**:
   - 等待程序完全加载
   - 检查系统内存是否充足

## 🗑️ 卸载说明
运行 `卸载系统.bat` 可完全卸载系统。

---
© 2025 六合彩预测系统 {self.version} - EXE完整版
'''
        
        with open(target_dir / "README.md", "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        print("✅ 说明文档已创建")
    
    def _create_uninstaller(self, target_dir):
        """创建卸载脚本"""
        uninstall_content = f'''@echo off
chcp 65001 > nul
title 六合彩预测系统 {self.version} - 卸载程序

echo 🗑️ 六合彩预测系统 {self.version} - 卸载程序
echo ==========================================

set /p confirm="确定要卸载系统吗？(y/N): "
if /i not "%confirm%"=="y" (
    echo 取消卸载
    pause
    exit /b 0
)

echo 正在卸载...

REM 关闭可能运行的程序
taskkill /f /im "{self.package_name}.exe" > nul 2>&1

REM 删除文件
echo 删除程序文件...
del /q "{self.package_name}.exe" > nul 2>&1
del /q "启动系统.bat" > nul 2>&1
del /q "README.md" > nul 2>&1

echo ✅ 卸载完成
echo 程序文件已删除
pause
del "%~f0"
'''
        
        with open(target_dir / "卸载系统.bat", "w", encoding="gbk", errors="ignore") as f:
            f.write(uninstall_content)
        
        print("✅ 卸载脚本已创建")
    
    def cleanup(self):
        """清理临时文件"""
        print("\n🧹 清理临时文件...")
        print("-" * 50)
        
        try:
            # 清理构建目录
            build_dir = self.work_dir / "build"
            if build_dir.exists():
                shutil.rmtree(build_dir)
                print("✅ 清理build目录")
            
            dist_dir = self.work_dir / "dist"
            if dist_dir.exists():
                shutil.rmtree(dist_dir)
                print("✅ 清理dist目录")
            
            # 清理spec文件
            for spec_file in self.work_dir.glob("*.spec"):
                spec_file.unlink()
                print(f"✅ 清理{spec_file.name}")
            
        except Exception as e:
            print(f"⚠️ 清理过程中出现问题: {e}")
    
    def create_complete_package(self):
        """创建完整封包"""
        print("\n🚀 开始创建完整EXE封包...")
        print("=" * 60)
        
        try:
            # 1. 准备源文件
            self.prepare_source_files()
            
            # 2. 安装依赖
            self.install_dependencies()
            
            # 3. 创建主入口点
            main_file = self.create_main_entry_point()
            
            # 4. 创建PyInstaller规格文件
            spec_file = self.create_pyinstaller_spec(main_file)
            
            # 5. 构建EXE
            exe_file = self.build_exe(spec_file)
            
            # 6. 创建最终包
            final_package = self.create_final_package(exe_file)
            
            # 7. 清理临时文件
            self.cleanup()
            
            if final_package:
                print("\n" + "=" * 60)
                print("🎉 EXE封包创建成功！")
                print("=" * 60)
                print(f"📁 封包位置: {final_package}")
                print(f"📦 包含文件:")
                for file in final_package.iterdir():
                    if file.is_file():
                        size = file.stat().st_size / (1024 * 1024)
                        print(f"  📄 {file.name} ({size:.1f}MB)")
                print("=" * 60)
                return True
            else:
                print("\n❌ EXE封包创建失败")
                return False
                
        except Exception as e:
            print(f"\n❌ 封包过程出错: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    packager = CompleteEXEPackager()
    success = packager.create_complete_package()
    
    if success:
        print("\n🎊 封包完成！")
        print("💡 使用说明:")
        print("1. 进入releases/complete_exe目录")
        print("2. 双击运行启动脚本或直接运行EXE文件")
        print("3. 首次启动可能较慢，请耐心等待")
    else:
        print("\n❌ 封包失败，请检查错误信息")

if __name__ == "__main__":
    main()
