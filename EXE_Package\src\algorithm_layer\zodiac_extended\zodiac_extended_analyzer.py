"""
澳门六合彩生肖维度扩展分析器 - 正确数据版本
基于用户提供的准确数据
"""
from typing import Dict, List, Set, Tuple
from datetime import date
import json

class ZodiacExtendedAnalyzer:
    def __init__(self):
        """初始化生肖扩展分析器"""
        self.init_zodiac_mappings()
        self.init_extended_categories()
        self.init_number_attributes()
    
    def init_zodiac_mappings(self):
        """初始化生肖号码映射 - 按农历年份"""
        self.zodiac_mappings = {
            # 2025年 - 蛇年 (蛇为头肖)
            2025: {
                '鼠': [6, 18, 30, 42],
                '牛': [5, 17, 29, 41],
                '虎': [4, 16, 28, 40],
                '兔': [3, 15, 27, 39],
                '龙': [2, 14, 26, 38],
                '蛇': [1, 13, 25, 37, 49],  # 头肖，5个号码
                '马': [12, 24, 36, 48],
                '羊': [11, 23, 35, 47],
                '猴': [10, 22, 34, 46],
                '鸡': [9, 21, 33, 45],
                '狗': [8, 20, 32, 44],
                '猪': [7, 19, 31, 43]
            },
            # 2024年 - 龙年 (龙为头肖)
            2024: {
                '鼠': [5, 17, 29, 41],
                '牛': [4, 16, 28, 40],
                '虎': [3, 15, 27, 39],
                '兔': [2, 14, 26, 38],
                '龙': [1, 13, 25, 37, 49],  # 头肖，5个号码
                '蛇': [12, 24, 36, 48],
                '马': [11, 23, 35, 47],
                '羊': [10, 22, 34, 46],
                '猴': [9, 21, 33, 45],
                '鸡': [8, 20, 32, 44],
                '狗': [7, 19, 31, 43],
                '猪': [6, 18, 30, 42]
            },
            # 2023年 - 兔年 (兔为头肖)
            2023: {
                '鼠': [4, 16, 28, 40],
                '牛': [3, 15, 27, 39],
                '虎': [2, 14, 26, 38],
                '兔': [1, 13, 25, 37, 49],  # 头肖，5个号码
                '龙': [12, 24, 36, 48],
                '蛇': [11, 23, 35, 47],
                '马': [10, 22, 34, 46],
                '羊': [9, 21, 33, 45],
                '猴': [8, 20, 32, 44],
                '鸡': [7, 19, 31, 43],
                '狗': [6, 18, 30, 42],
                '猪': [5, 17, 29, 41]
            },
            # 2022年 - 虎年 (虎为头肖)
            2022: {
                '鼠': [3, 15, 27, 39],
                '牛': [2, 14, 26, 38],
                '虎': [1, 13, 25, 37, 49],  # 头肖，5个号码
                '兔': [12, 24, 36, 48],
                '龙': [11, 23, 35, 47],
                '蛇': [10, 22, 34, 46],
                '马': [9, 21, 33, 45],
                '羊': [8, 20, 32, 44],
                '猴': [7, 19, 31, 43],
                '鸡': [6, 18, 30, 42],
                '狗': [5, 17, 29, 41],
                '猪': [4, 16, 28, 40]
            }
        }
    
    def init_extended_categories(self):
        """初始化扩展分类 - 琴棋书画、四季、波色等"""
        
        # 琴棋书画分类
        self.qin_qi_shu_hua = {
            '琴': ['兔', '蛇', '鸡'],
            '棋': ['鼠', '牛', '狗'],
            '书': ['马', '龙', '虎'],
            '画': ['羊', '猴', '猪']
        }
        
        # 四季分类
        self.four_seasons = {
            '春': ['虎', '兔', '龙'],  # 东方
            '夏': ['蛇', '马', '羊'],  # 南方
            '秋': ['猴', '狗', '鸡'],  # 西方
            '冬': ['鼠', '牛', '猪']   # 北方
        }
        
        # 波色分类
        self.wave_colors = {
            '红肖': ['鼠', '兔', '马', '鸡'],
            '绿肖': ['牛', '龙', '羊', '狗'],
            '蓝肖': ['虎', '蛇', '猴', '猪']
        }
        
        # 日夜分类
        self.day_night = {
            '日肖': ['兔', '龙', '蛇', '马', '羊', '猴'],
            '夜肖': ['鼠', '牛', '虎', '鸡', '狗', '猪']
        }
        
        # 左右分类
        self.left_right = {
            '左肖': ['鼠', '牛', '龙', '蛇', '猴', '鸡'],
            '右肖': ['虎', '兔', '马', '羊', '狗', '猪']
        }
        
        # 阴阳分类
        self.yin_yang = {
            '阴肖': ['鼠', '龙', '蛇', '马', '狗', '猪'],
            '阳肖': ['牛', '虎', '兔', '羊', '猴', '鸡']
        }
        
        # 合独分类
        self.he_du = {
            '合肖': ['龙', '蛇', '猴', '鸡', '狗', '猪'],
            '独肖': ['鼠', '牛', '虎', '兔', '马', '羊']
        }
        
        # 家野分类
        self.jia_ye = {
            '家肖': ['牛', '马', '羊', '狗', '鸡', '猪'],
            '野肖': ['鼠', '虎', '兔', '龙', '蛇', '猴']
        }
        
        # 天地分类
        self.tian_di = {
            '天肖': ['牛', '猴', '兔', '猪', '马', '龙'],
            '地肖': ['蛇', '虎', '羊', '鸡', '狗', '鼠']
        }
        
        # 男女分类
        self.nan_nv = {
            '男肖': ['鼠', '牛', '虎', '龙', '马', '猴', '狗'],
            '女肖': ['兔', '蛇', '羊', '鸡', '猪']
        }
        
        # 吉凶分类
        self.ji_xiong = {
            '吉肖': ['兔', '龙', '蛇', '马', '羊', '鸡'],
            '凶肖': ['鼠', '牛', '虎', '猴', '狗', '猪']
        }
        
        # 前后分类
        self.qian_hou = {
            '前肖': ['鼠', '牛', '虎', '兔', '龙', '蛇'],
            '后肖': ['马', '羊', '猴', '鸡', '狗', '猪']
        }
        
        # 单双笔分类
        self.dan_shuang_bi = {
            '单笔': ['鼠', '龙', '马', '蛇', '鸡', '猪'],
            '双笔': ['虎', '猴', '狗', '兔', '羊', '牛']
        }
        
        # 胆大胆小分类
        self.dan_da_xiao = {
            '胆大肖': ['牛', '虎', '马', '猴', '狗', '猪'],
            '胆小肖': ['鼠', '兔', '龙', '蛇', '羊', '鸡']
        }
    
    def init_number_attributes(self):
        """初始化号码属性 - 波色、正反码、大小数等"""
        
        # 波色号码
        self.wave_color_numbers = {
            '红波': [1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46],
            '蓝波': [3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48],
            '绿波': [5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49]
        }
        
        # 正反码
        self.zheng_fan_ma = {
            '正码': [5, 6, 7, 8, 9, 15, 16, 17, 18, 19, 25, 26, 27, 28, 29, 35, 36, 37, 38, 39, 45, 46, 47, 48, 49],
            '反码': [1, 2, 3, 4, 10, 11, 12, 13, 14, 20, 21, 22, 23, 24, 30, 31, 32, 33, 34, 40, 41, 42, 43, 44]
        }
        
        # 大小数
        self.da_xiao_shu = {
            '小数': list(range(1, 25)),  # 1-24
            '大数': list(range(25, 50))  # 25-49
        }
        
        # 合数大小
        self.he_shu_da_xiao = {
            '合数大': [7, 8, 9, 16, 17, 18, 19, 25, 26, 27, 28, 29, 34, 35, 36, 37, 38, 39, 43, 44, 45, 46, 47, 48, 49],
            '合数小': [1, 2, 3, 4, 5, 6, 10, 11, 12, 13, 14, 15, 20, 21, 22, 23, 24, 30, 31, 32, 33, 40, 41, 42]
        }
        
        # 合数单双
        self.he_shu_dan_shuang = {
            '合数单': [1, 3, 5, 7, 9, 10, 12, 14, 16, 18, 21, 23, 25, 27, 29, 30, 32, 34, 36, 38, 41, 43, 45, 47, 49],
            '合数双': [2, 4, 6, 8, 11, 13, 15, 17, 19, 20, 22, 24, 26, 28, 31, 33, 35, 37, 39, 40, 42, 44, 46, 48]
        }
        
        # 七段分类
        self.qi_duan = {
            '1段': list(range(1, 8)),    # 01-07
            '2段': list(range(8, 15)),   # 08-14
            '3段': list(range(15, 22)),  # 15-21
            '4段': list(range(22, 29)),  # 22-28
            '5段': list(range(29, 36)),  # 29-35
            '6段': list(range(36, 43)),  # 36-42
            '7段': list(range(43, 50))   # 43-49
        }
        
        # 五行属性 - 按年份
        self.wu_xing_by_year = {
            2025: {
                '金': [3, 4, 11, 12, 25, 26, 33, 34, 41, 42],
                '木': [7, 8, 15, 16, 23, 24, 37, 38, 45, 46],
                '水': [13, 14, 20, 21, 28, 29, 42, 43],
                '火': [1, 2, 9, 10, 17, 18, 31, 32, 39, 40, 47, 48],
                '土': [5, 6, 19, 20, 27, 28, 35, 36, 49]
            },
            2024: {
                '金': [2, 3, 10, 11, 24, 25, 32, 33, 40, 41],
                '木': [6, 7, 14, 15, 22, 23, 36, 37, 44, 45],
                '水': [12, 13, 20, 21, 28, 29, 42, 43],
                '火': [1, 8, 9, 16, 17, 30, 31, 38, 39, 46, 47],
                '土': [4, 5, 18, 19, 26, 27, 34, 35, 48, 49]
            },
            2023: {
                '金': [1, 2, 9, 10, 23, 24, 31, 32, 39, 40],
                '木': [5, 6, 13, 14, 21, 22, 35, 36, 43, 44],
                '水': [11, 12, 19, 20, 27, 28, 41, 42, 49],
                '火': [7, 8, 15, 16, 29, 30, 37, 38, 45, 46],
                '土': [3, 4, 17, 18, 25, 26, 33, 34, 47, 48]
            }
        }
    
    def get_zodiac_by_number(self, number: int, lunar_year: int) -> str:
        """根据号码和农历年份获取生肖"""
        if lunar_year not in self.zodiac_mappings:
            # 如果年份不在映射中，使用最近的年份
            available_years = sorted(self.zodiac_mappings.keys())
            lunar_year = min(available_years, key=lambda x: abs(x - lunar_year))
        
        year_mapping = self.zodiac_mappings[lunar_year]
        for zodiac, numbers in year_mapping.items():
            if number in numbers:
                return zodiac
        return '未知'
    
    def get_numbers_by_zodiac(self, zodiac: str, lunar_year: int) -> List[int]:
        """根据生肖和农历年份获取号码列表"""
        if lunar_year not in self.zodiac_mappings:
            available_years = sorted(self.zodiac_mappings.keys())
            lunar_year = min(available_years, key=lambda x: abs(x - lunar_year))
        
        return self.zodiac_mappings[lunar_year].get(zodiac, [])
    
    def comprehensive_zodiac_analysis(self, recent_data: List[Dict], lunar_year: int) -> Dict:
        """综合生肖分析"""
        zodiac_count = {}
        
        # 统计生肖出现次数
        for record in recent_data:
            special_number = record.get('special_number')
            if special_number:
                zodiac = self.get_zodiac_by_number(special_number, lunar_year)
                zodiac_count[zodiac] = zodiac_count.get(zodiac, 0) + 1
        
        # 计算频率
        total_records = len(recent_data)
        zodiac_frequency = {k: v/total_records for k, v in zodiac_count.items()}
        
        # 找出冷门生肖（预测回补）
        sorted_zodiacs = sorted(zodiac_frequency.items(), key=lambda x: x[1])
        cold_zodiacs = [zodiac for zodiac, freq in sorted_zodiacs[:4]]
        
        # 获取预测生肖的号码
        predicted_numbers = []
        for zodiac in cold_zodiacs:
            numbers = self.get_numbers_by_zodiac(zodiac, lunar_year)
            predicted_numbers.extend(numbers)
        
        return {
            'predicted_zodiac': cold_zodiacs,
            'predicted_numbers': predicted_numbers,
            'confidence_level': 0.8,
            'zodiac_count': zodiac_count,
            'analysis_time': date.today().isoformat()
        }

