#!/usr/bin/env python3
"""
高级融合算法优化器 - 实现多层次、自适应的预测融合
"""

import numpy as np
from typing import Dict, List, Tuple, Any
from collections import defaultdict, deque
import json
from datetime import datetime

class AdvancedFusionOptimizer:
    """高级融合算法优化器"""
    
    def __init__(self):
        # 融合策略配置
        self.fusion_strategies = {
            "adaptive_weighted": self._adaptive_weighted_fusion,
            "confidence_cascade": self._confidence_cascade_fusion,
            "ensemble_voting": self._ensemble_voting_fusion,
            "dynamic_threshold": self._dynamic_threshold_fusion,
            "meta_learning": self._meta_learning_fusion
        }
        
        # 历史性能追踪
        self.performance_history = defaultdict(lambda: deque(maxlen=50))
        self.strategy_weights = {
            "adaptive_weighted": 0.25,
            "confidence_cascade": 0.20,
            "ensemble_voting": 0.20,
            "dynamic_threshold": 0.20,
            "meta_learning": 0.15
        }
        
        # 学习参数
        self.learning_rate = 0.1
        self.adaptation_window = 20
        
    def optimize_fusion(self, module_predictions: Dict[str, Dict], target_date: str) -> Dict[str, Any]:
        """优化融合预测"""
        print("🔀 开始高级融合优化...")
        
        # 第一层：各策略独立融合
        strategy_results = {}
        
        for strategy_name, strategy_func in self.fusion_strategies.items():
            try:
                result = strategy_func(module_predictions, target_date)
                strategy_results[strategy_name] = result
                print(f"  ✅ {strategy_name}: {len(result['numbers'])}个号码")
            except Exception as e:
                print(f"  ❌ {strategy_name}失败: {e}")
        
        # 第二层：策略融合
        final_result = self._fuse_strategies(strategy_results, module_predictions)
        
        # 第三层：结果优化
        optimized_result = self._optimize_final_result(final_result, module_predictions)
        
        print(f"✅ 高级融合完成，最终推荐 {len(optimized_result['final_numbers'])} 个号码")
        
        return optimized_result
    
    def _adaptive_weighted_fusion(self, module_predictions: Dict[str, Dict], target_date: str) -> Dict[str, Any]:
        """自适应权重融合"""
        # 计算动态权重
        dynamic_weights = self._calculate_adaptive_weights(module_predictions)
        
        number_scores = defaultdict(float)
        total_weight = 0
        
        for module_name, pred_data in module_predictions.items():
            if "numbers" not in pred_data:
                continue
                
            weight = dynamic_weights.get(module_name, 0.25)
            confidence = pred_data.get("confidence", 0.5)
            numbers = pred_data["numbers"]
            
            # 自适应权重 = 基础权重 × 置信度 × 历史表现
            historical_performance = self._get_historical_performance(module_name)
            adaptive_weight = weight * confidence * (1 + historical_performance)
            
            for i, number in enumerate(numbers):
                # 位置权重：排名越前权重越高
                position_weight = (len(numbers) - i) / len(numbers)
                final_weight = adaptive_weight * position_weight
                number_scores[number] += final_weight
            
            total_weight += adaptive_weight
        
        # 归一化并选择前16个
        if total_weight > 0:
            for number in number_scores:
                number_scores[number] /= total_weight
        
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        selected_numbers = [num for num, score in sorted_numbers[:16]]
        
        return {
            "numbers": selected_numbers,
            "scores": dict(number_scores),
            "weights_used": dynamic_weights,
            "strategy": "adaptive_weighted"
        }
    
    def _confidence_cascade_fusion(self, module_predictions: Dict[str, Dict], target_date: str) -> Dict[str, Any]:
        """置信度级联融合"""
        # 按置信度排序模组
        sorted_modules = sorted(
            module_predictions.items(),
            key=lambda x: x[1].get("confidence", 0),
            reverse=True
        )
        
        cascade_numbers = []
        confidence_weights = []
        
        for module_name, pred_data in sorted_modules:
            if "numbers" not in pred_data:
                continue
                
            confidence = pred_data.get("confidence", 0.5)
            numbers = pred_data["numbers"]
            
            # 级联策略：高置信度模组的号码优先
            if confidence > 0.8:
                # 高置信度：选择前8个
                cascade_numbers.extend(numbers[:8])
                confidence_weights.extend([confidence] * min(8, len(numbers)))
            elif confidence > 0.6:
                # 中置信度：选择前6个
                cascade_numbers.extend(numbers[:6])
                confidence_weights.extend([confidence] * min(6, len(numbers)))
            else:
                # 低置信度：选择前4个
                cascade_numbers.extend(numbers[:4])
                confidence_weights.extend([confidence] * min(4, len(numbers)))
        
        # 去重并按权重排序
        number_weights = defaultdict(float)
        for number, weight in zip(cascade_numbers, confidence_weights):
            number_weights[number] += weight
        
        sorted_cascade = sorted(number_weights.items(), key=lambda x: x[1], reverse=True)
        final_numbers = [num for num, weight in sorted_cascade[:16]]
        
        return {
            "numbers": final_numbers,
            "cascade_weights": dict(number_weights),
            "strategy": "confidence_cascade"
        }
    
    def _ensemble_voting_fusion(self, module_predictions: Dict[str, Dict], target_date: str) -> Dict[str, Any]:
        """集成投票融合"""
        # 多轮投票机制
        voting_rounds = [
            {"threshold": 0.75, "weight": 3.0},  # 高门槛投票
            {"threshold": 0.50, "weight": 2.0},  # 中门槛投票
            {"threshold": 0.25, "weight": 1.0}   # 低门槛投票
        ]
        
        final_scores = defaultdict(float)
        
        for round_config in voting_rounds:
            threshold = round_config["threshold"]
            weight = round_config["weight"]
            
            vote_counts = defaultdict(int)
            total_modules = len(module_predictions)
            
            # 统计投票
            for module_name, pred_data in module_predictions.items():
                if "numbers" not in pred_data:
                    continue
                    
                confidence = pred_data.get("confidence", 0.5)
                if confidence >= threshold:  # 只有达到置信度阈值的模组才能投票
                    for number in pred_data["numbers"]:
                        vote_counts[number] += 1
            
            # 计算投票得分
            for number, votes in vote_counts.items():
                vote_rate = votes / total_modules
                final_scores[number] += vote_rate * weight
        
        # 选择得分最高的16个
        sorted_votes = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
        selected_numbers = [num for num, score in sorted_votes[:16]]
        
        return {
            "numbers": selected_numbers,
            "voting_scores": dict(final_scores),
            "strategy": "ensemble_voting"
        }
    
    def _dynamic_threshold_fusion(self, module_predictions: Dict[str, Dict], target_date: str) -> Dict[str, Any]:
        """动态阈值融合"""
        # 计算动态阈值
        all_confidences = [pred.get("confidence", 0.5) for pred in module_predictions.values()]
        avg_confidence = np.mean(all_confidences)
        std_confidence = np.std(all_confidences)
        
        # 动态阈值 = 平均置信度 + 0.5 * 标准差
        dynamic_threshold = avg_confidence + 0.5 * std_confidence
        dynamic_threshold = max(0.3, min(0.9, dynamic_threshold))  # 限制在合理范围
        
        qualified_predictions = {}
        for module_name, pred_data in module_predictions.items():
            confidence = pred_data.get("confidence", 0.5)
            if confidence >= dynamic_threshold:
                qualified_predictions[module_name] = pred_data
        
        # 如果合格的模组太少，降低阈值
        if len(qualified_predictions) < 2:
            dynamic_threshold = avg_confidence
            qualified_predictions = {
                name: pred for name, pred in module_predictions.items()
                if pred.get("confidence", 0.5) >= dynamic_threshold
            }
        
        # 对合格的预测进行加权融合
        number_scores = defaultdict(float)
        total_weight = 0
        
        for module_name, pred_data in qualified_predictions.items():
            if "numbers" not in pred_data:
                continue
                
            confidence = pred_data["confidence"]
            weight = confidence / dynamic_threshold  # 相对权重
            
            for number in pred_data["numbers"]:
                number_scores[number] += weight
            
            total_weight += weight
        
        # 归一化
        if total_weight > 0:
            for number in number_scores:
                number_scores[number] /= total_weight
        
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        selected_numbers = [num for num, score in sorted_numbers[:16]]
        
        return {
            "numbers": selected_numbers,
            "dynamic_threshold": dynamic_threshold,
            "qualified_modules": list(qualified_predictions.keys()),
            "strategy": "dynamic_threshold"
        }
    
    def _meta_learning_fusion(self, module_predictions: Dict[str, Dict], target_date: str) -> Dict[str, Any]:
        """元学习融合"""
        # 基于历史表现的元学习
        meta_weights = {}
        
        for module_name in module_predictions.keys():
            # 获取历史表现
            history = list(self.performance_history[module_name])
            
            if len(history) >= 5:
                # 计算多维表现指标
                recent_hit_rate = np.mean([h.get("hit", 0) for h in history[-10:]])
                stability = 1 - np.std([h.get("hit", 0) for h in history[-10:]])
                trend = self._calculate_performance_trend(history)
                
                # 元权重 = 命中率 × 稳定性 × 趋势
                meta_weight = recent_hit_rate * stability * (1 + trend)
                meta_weights[module_name] = max(0.1, meta_weight)
            else:
                meta_weights[module_name] = 0.5  # 默认权重
        
        # 归一化元权重
        total_meta_weight = sum(meta_weights.values())
        if total_meta_weight > 0:
            for module_name in meta_weights:
                meta_weights[module_name] /= total_meta_weight
        
        # 使用元权重进行融合
        number_scores = defaultdict(float)
        
        for module_name, pred_data in module_predictions.items():
            if "numbers" not in pred_data:
                continue
                
            meta_weight = meta_weights.get(module_name, 0.25)
            confidence = pred_data.get("confidence", 0.5)
            
            # 最终权重 = 元权重 × 置信度
            final_weight = meta_weight * confidence
            
            for number in pred_data["numbers"]:
                number_scores[number] += final_weight
        
        sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
        selected_numbers = [num for num, score in sorted_numbers[:16]]
        
        return {
            "numbers": selected_numbers,
            "meta_weights": meta_weights,
            "strategy": "meta_learning"
        }
    
    def _fuse_strategies(self, strategy_results: Dict[str, Dict], module_predictions: Dict) -> Dict[str, Any]:
        """融合各策略结果"""
        # 策略级别的融合
        strategy_scores = defaultdict(float)
        
        for strategy_name, result in strategy_results.items():
            if "numbers" not in result:
                continue
                
            strategy_weight = self.strategy_weights.get(strategy_name, 0.2)
            numbers = result["numbers"]
            
            for i, number in enumerate(numbers):
                # 策略内位置权重
                position_weight = (len(numbers) - i) / len(numbers)
                strategy_scores[number] += strategy_weight * position_weight
        
        # 选择最终16个号码
        sorted_strategies = sorted(strategy_scores.items(), key=lambda x: x[1], reverse=True)
        final_numbers = [num for num, score in sorted_strategies[:16]]
        
        return {
            "final_numbers": final_numbers,
            "strategy_scores": dict(strategy_scores),
            "strategy_results": strategy_results
        }
    
    def _optimize_final_result(self, fusion_result: Dict, module_predictions: Dict) -> Dict[str, Any]:
        """优化最终结果"""
        final_numbers = fusion_result["final_numbers"]
        
        # 多样性检查
        diversity_score = self._calculate_diversity(final_numbers)
        
        # 如果多样性不足，进行调整
        if diversity_score < 0.6:
            final_numbers = self._enhance_diversity(final_numbers, fusion_result["strategy_scores"])
        
        # 计算整体置信度
        overall_confidence = self._calculate_overall_confidence(final_numbers, module_predictions)
        
        # 计算稳定性得分
        stability_score = self._calculate_stability_score(final_numbers)
        
        return {
            "final_numbers": final_numbers,
            "overall_confidence": overall_confidence,
            "stability_score": stability_score,
            "diversity_score": diversity_score,
            "fusion_analysis": fusion_result,
            "optimization_applied": True
        }
    
    def _calculate_adaptive_weights(self, module_predictions: Dict) -> Dict[str, float]:
        """计算自适应权重"""
        weights = {}
        
        for module_name, pred_data in module_predictions.items():
            confidence = pred_data.get("confidence", 0.5)
            historical_perf = self._get_historical_performance(module_name)
            
            # 自适应权重 = 置信度 × (1 + 历史表现)
            adaptive_weight = confidence * (1 + historical_perf)
            weights[module_name] = adaptive_weight
        
        # 归一化
        total_weight = sum(weights.values())
        if total_weight > 0:
            for module_name in weights:
                weights[module_name] /= total_weight
        
        return weights
    
    def _get_historical_performance(self, module_name: str) -> float:
        """获取历史表现"""
        history = list(self.performance_history[module_name])
        if not history:
            return 0.0
        
        recent_performance = history[-10:]  # 最近10期
        hit_rate = np.mean([h.get("hit", 0) for h in recent_performance])
        return hit_rate - 0.5  # 相对于随机表现的提升
    
    def _calculate_performance_trend(self, history: List[Dict]) -> float:
        """计算性能趋势"""
        if len(history) < 5:
            return 0.0
        
        hit_rates = [h.get("hit", 0) for h in history]
        x = np.arange(len(hit_rates))
        trend = np.polyfit(x, hit_rates, 1)[0]
        return trend
    
    def _calculate_diversity(self, numbers: List[int]) -> float:
        """计算号码多样性"""
        if not numbers:
            return 0.0
        
        # 检查分布多样性
        segments = [0, 0, 0, 0]  # 四个段位
        for num in numbers:
            if 1 <= num <= 12:
                segments[0] += 1
            elif 13 <= num <= 24:
                segments[1] += 1
            elif 25 <= num <= 36:
                segments[2] += 1
            elif 37 <= num <= 49:
                segments[3] += 1
        
        # 计算分布均匀性
        total = len(numbers)
        expected = total / 4
        diversity = 1 - np.std(segments) / expected if expected > 0 else 0
        
        return max(0, min(1, diversity))
    
    def _enhance_diversity(self, numbers: List[int], scores: Dict[int, float]) -> List[int]:
        """增强多样性"""
        # 保留前12个高分号码
        high_score_numbers = numbers[:12]
        
        # 从剩余号码中选择4个以增强多样性
        remaining_candidates = [(num, score) for num, score in scores.items() if num not in high_score_numbers]
        remaining_candidates.sort(key=lambda x: x[1], reverse=True)
        
        # 选择不同段位的号码
        segments_needed = [1, 1, 1, 1]  # 每个段位至少1个
        enhanced_numbers = high_score_numbers.copy()
        
        for num, score in remaining_candidates:
            if len(enhanced_numbers) >= 16:
                break
                
            segment = (num - 1) // 12
            if segment < 4 and segments_needed[segment] > 0:
                enhanced_numbers.append(num)
                segments_needed[segment] -= 1
        
        # 如果还不够16个，补充高分号码
        while len(enhanced_numbers) < 16 and remaining_candidates:
            num, score = remaining_candidates.pop(0)
            if num not in enhanced_numbers:
                enhanced_numbers.append(num)
        
        return enhanced_numbers[:16]
    
    def _calculate_overall_confidence(self, final_numbers: List[int], module_predictions: Dict) -> float:
        """计算整体置信度"""
        total_confidence = 0
        total_weight = 0
        
        for module_name, pred_data in module_predictions.items():
            if "numbers" not in pred_data:
                continue
                
            confidence = pred_data.get("confidence", 0.5)
            overlap = len(set(pred_data["numbers"]) & set(final_numbers))
            overlap_rate = overlap / len(final_numbers) if final_numbers else 0
            
            weighted_confidence = confidence * overlap_rate
            total_confidence += weighted_confidence
            total_weight += overlap_rate
        
        return total_confidence / total_weight if total_weight > 0 else 0.5
    
    def _calculate_stability_score(self, numbers: List[int]) -> float:
        """计算稳定性得分"""
        if not numbers:
            return 0.0
        
        # 基于号码分布的稳定性
        mean_num = np.mean(numbers)
        std_num = np.std(numbers)
        cv = std_num / mean_num if mean_num > 0 else 0
        
        # 稳定性 = 1 - 变异系数（归一化）
        stability = 1 - min(cv / 2, 1)  # 限制在[0,1]范围
        
        return stability
    
    def update_performance(self, module_name: str, actual_result: int, predicted_numbers: List[int]):
        """更新性能记录"""
        hit = 1 if actual_result in predicted_numbers else 0
        
        performance_record = {
            "timestamp": datetime.now().isoformat(),
            "hit": hit,
            "predicted_count": len(predicted_numbers),
            "actual_result": actual_result
        }
        
        self.performance_history[module_name].append(performance_record)
        
        # 更新策略权重
        self._update_strategy_weights()
    
    def _update_strategy_weights(self):
        """更新策略权重"""
        # 基于最近表现调整策略权重
        if len(self.performance_history) < 2:
            return
        
        # 简化的权重更新逻辑
        for strategy_name in self.strategy_weights:
            # 这里可以实现更复杂的权重更新逻辑
            pass

if __name__ == "__main__":
    # 测试高级融合优化器
    optimizer = AdvancedFusionOptimizer()
    
    # 模拟模组预测
    test_predictions = {
        "traditional": {"numbers": [1, 5, 12, 18, 23, 28, 34, 39, 42, 45, 47, 49, 3, 8, 15, 21], "confidence": 0.75},
        "ml": {"numbers": [2, 6, 13, 19, 24, 29, 35, 40, 43, 46, 48, 1, 4, 9, 16, 22], "confidence": 0.84},
        "zodiac": {"numbers": [3, 7, 14, 20, 25, 30, 36, 41, 44, 47, 49, 2, 5, 10, 17, 23], "confidence": 0.78}
    }
    
    result = optimizer.optimize_fusion(test_predictions, "2025-06-23")
    print(f"🎊 高级融合测试完成，推荐号码: {result['final_numbers']}")
    print(f"整体置信度: {result['overall_confidence']:.1%}")
    print(f"稳定性得分: {result['stability_score']:.1%}")
