"""
资源路径处理器 - 用于EXE打包后的路径处理
"""
import os
import sys
from pathlib import Path

def get_resource_path(relative_path):
    """获取资源文件的绝对路径，兼容开发环境和打包后环境"""
    try:
        # PyInstaller打包后的临时目录
        base_path = sys._MEIPASS
    except AttributeError:
        # 开发环境
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def get_data_path(relative_path):
    """获取数据文件路径，优先使用用户目录"""
    # 在用户目录创建应用数据目录
    import os
    user_data_dir = os.path.expanduser("~/LotteryPredictionSystem")
    os.makedirs(user_data_dir, exist_ok=True)
    
    return os.path.join(user_data_dir, relative_path)

def ensure_data_directory():
    """确保数据目录存在"""
    data_dir = get_data_path("data")
    os.makedirs(data_dir, exist_ok=True)
    return data_dir
