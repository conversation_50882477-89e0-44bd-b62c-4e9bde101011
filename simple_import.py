"""
简化版历史数据导入脚本
"""
import sys
sys.path.insert(0, 'src')

import pandas as pd
from datetime import datetime
import sqlite3
import json

def simple_import():
    print("🚀 简化版历史数据导入")
    print("=" * 60)
    
    try:
        # 1. 读取CSV文件
        print("📊 读取历史数据文件...")
        df = pd.read_csv("历史数据.csv", encoding='utf-8')
        print(f"✅ 成功读取 {len(df)} 条记录")
        
        # 2. 连接数据库
        print("📊 连接数据库...")
        conn = sqlite3.connect("data/lottery.db")
        cursor = conn.cursor()
        
        # 3. 创建表（如果不存在）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lottery_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                period_number TEXT UNIQUE,
                draw_date DATE,
                regular_numbers TEXT,
                special_number INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 4. 导入数据
        print("📊 开始导入数据...")
        success_count = 0
        error_count = 0
        
        for index, row in df.iterrows():
            try:
                period_number = str(row['issue'])
                draw_date = str(row['date'])
                numbers_str = str(row['numbers']).replace('"', '').strip()
                special_number = int(row['special'])
                
                # 验证正码
                regular_numbers = [int(x.strip()) for x in numbers_str.split(',')]
                if len(regular_numbers) != 6:
                    error_count += 1
                    continue
                
                # 插入数据
                cursor.execute('''
                    INSERT OR REPLACE INTO lottery_data 
                    (period_number, draw_date, regular_numbers, special_number)
                    VALUES (?, ?, ?, ?)
                ''', (period_number, draw_date, json.dumps(regular_numbers), special_number))
                
                success_count += 1
                if success_count % 100 == 0:
                    print(f"   已导入 {success_count} 条记录...")
                    
            except Exception as e:
                error_count += 1
                print(f"   错误: {e}")
        
        # 5. 提交并关闭
        conn.commit()
        conn.close()
        
        print(f"\n📊 导入完成:")
        print(f"   ✅ 成功: {success_count} 条")
        print(f"   ❌ 失败: {error_count} 条")
        print(f"   📊 成功率: {success_count/(success_count+error_count)*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

if __name__ == "__main__":
    result = simple_import()
    if result:
        print("\n🎊 数据导入成功！")
    else:
        print("\n❌ 数据导入失败！")
