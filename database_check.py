import sqlite3
import pandas as pd
from pathlib import Path

def check_database_structure():
    """检查数据库结构"""
    print("🗄️ 数据库结构和内容检查")
    print("=" * 60)
    
    # 检查主数据库
    db_path = "data/lottery.db"
    if Path(db_path).exists():
        print(f"📊 主数据库: {db_path}")
        conn = sqlite3.connect(db_path)
        
        # 获取所有表名
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📋 数据表数量: {len(tables)}")
        for i, (table_name,) in enumerate(tables, 1):
            print(f"   {i}. {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            print(f"      字段数: {len(columns)}")
            
            # 获取记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"      记录数: {count}")
            
            # 显示字段信息
            print(f"      字段列表:")
            for col in columns[:5]:  # 只显示前5个字段
                col_id, name, type_name, not_null, default, pk = col
                print(f"        - {name} ({type_name})")
            if len(columns) > 5:
                print(f"        ... 还有 {len(columns) - 5} 个字段")
            print()
        
        conn.close()
    else:
        print(f"❌ 数据库文件不存在: {db_path}")
    
    # 检查测试数据库
    test_db_path = "data/lottery_test.db"
    if Path(test_db_path).exists():
        print(f"📊 测试数据库: {test_db_path}")
        conn = sqlite3.connect(test_db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        test_tables = cursor.fetchall()
        print(f"�� 测试数据表数量: {len(test_tables)}")
        
        for table_name, in test_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"   - {table_name}: {count} 条记录")
        
        conn.close()
    else:
        print(f"❌ 测试数据库文件不存在: {test_db_path}")

def check_lottery_data_sample():
    """检查开奖数据样本"""
    print("\n🎯 开奖数据样本检查")
    print("=" * 40)
    
    db_path = "data/lottery.db"
    if Path(db_path).exists():
        conn = sqlite3.connect(db_path)
        
        # 检查开奖数据
        try:
            df = pd.read_sql_query("""
                SELECT period_number, draw_date, special_number, 
                       special_zodiac, special_wuxing, lunar_year
                FROM lottery_data 
                ORDER BY draw_date DESC 
                LIMIT 10
            """, conn)
            
            print("📊 最近10期开奖数据:")
            print(df.to_string(index=False))
            
        except Exception as e:
            print(f"❌ 查询开奖数据失败: {e}")
        
        # 检查生肖映射
        try:
            df_zodiac = pd.read_sql_query("""
                SELECT lunar_year, zodiac_name, wuxing_element
                FROM lunar_year_mapping 
                ORDER BY lunar_year DESC 
                LIMIT 5
            """, conn)
            
            print(f"\n🐲 生肖年份映射 (最近5年):")
            print(df_zodiac.to_string(index=False))
            
        except Exception as e:
            print(f"❌ 查询生肖映射失败: {e}")
        
        # 检查号码生肖映射
        try:
            df_number = pd.read_sql_query("""
                SELECT number, zodiac_name, wuxing_element, lunar_year
                FROM number_zodiac_mapping 
                WHERE number <= 10
                ORDER BY number, lunar_year DESC
                LIMIT 20
            """, conn)
            
            print(f"\n🔢 号码生肖映射 (1-10号码):")
            print(df_number.to_string(index=False))
            
        except Exception as e:
            print(f"❌ 查询号码映射失败: {e}")
        
        conn.close()

def check_database_integrity():
    """检查数据库完整性"""
    print("\n🔍 数据库完整性检查")
    print("=" * 40)
    
    db_path = "data/lottery.db"
    if Path(db_path).exists():
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查外键约束
        try:
            cursor.execute("PRAGMA foreign_key_check;")
            fk_errors = cursor.fetchall()
            if fk_errors:
                print(f"❌ 外键约束错误: {len(fk_errors)} 个")
                for error in fk_errors[:5]:
                    print(f"   - {error}")
            else:
                print("✅ 外键约束检查通过")
        except Exception as e:
            print(f"⚠️ 外键检查失败: {e}")
        
        # 检查数据一致性
        try:
            # 检查开奖数据的生肖映射是否正确
            cursor.execute("""
                SELECT COUNT(*) FROM lottery_data 
                WHERE special_zodiac IS NULL OR special_wuxing IS NULL
            """)
            null_count = cursor.fetchone()[0]
            
            if null_count > 0:
                print(f"⚠️ 有 {null_count} 条记录缺少生肖或五行信息")
            else:
                print("✅ 所有开奖记录都有完整的生肖五行信息")
                
        except Exception as e:
            print(f"⚠️ 数据一致性检查失败: {e}")
        
        conn.close()

if __name__ == "__main__":
    check_database_structure()
    check_lottery_data_sample()
    check_database_integrity()
