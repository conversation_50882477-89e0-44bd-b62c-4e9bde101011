"""
智能融合管理器 - 多模组协同预测的核心
实现静态加权、动态评分、投票机制三层融合架构
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict
import json
import logging

class DynamicScoreManager:
    """动态评分管理器"""
    
    def __init__(self, evaluation_window: int = 30):
        self.performance_history = {}
        self.evaluation_window = evaluation_window
        self.logger = logging.getLogger(__name__)
        
    def update_performance(self, module_name: str, actual_result: int, prediction: List[int]):
        """更新模块性能"""
        try:
            # 计算性能指标
            hit_rate = 1.0 if actual_result in prediction else 0.0
            precision = 1.0 / len(prediction) if actual_result in prediction else 0.0
            coverage = len(prediction) / 49.0
            
            # 记录性能
            performance_record = {
                "hit_rate": hit_rate,
                "precision": precision,
                "coverage": coverage,
                "timestamp": datetime.now(),
                "prediction_count": len(prediction)
            }
            
            if module_name not in self.performance_history:
                self.performance_history[module_name] = []
                
            self.performance_history[module_name].append(performance_record)
            
            # 保持窗口大小
            if len(self.performance_history[module_name]) > self.evaluation_window:
                self.performance_history[module_name].pop(0)
                
            self.logger.info(f"Updated performance for {module_name}: hit_rate={hit_rate:.3f}")
            
        except Exception as e:
            self.logger.error(f"Error updating performance for {module_name}: {e}")
    
    def get_dynamic_weights(self) -> Dict[str, float]:
        """计算动态权重"""
        weights = {}
        total_score = 0
        
        try:
            for module_name, history in self.performance_history.items():
                if len(history) >= 5:  # 至少5期数据
                    recent_performance = history[-10:]  # 最近10期
                    
                    # 计算各项指标
                    avg_hit_rate = np.mean([h["hit_rate"] for h in recent_performance])
                    avg_precision = np.mean([h["precision"] for h in recent_performance])
                    stability = 1 - np.std([h["hit_rate"] for h in recent_performance])
                    
                    # 综合评分 (命中率40% + 精确度40% + 稳定性20%)
                    module_score = 0.4 * avg_hit_rate + 0.4 * avg_precision + 0.2 * stability
                    weights[module_name] = max(module_score, 0.1)  # 最小权重0.1
                    total_score += weights[module_name]
                else:
                    # 数据不足时使用默认权重
                    weights[module_name] = 0.25
                    total_score += 0.25
            
            # 归一化权重
            if total_score > 0:
                for module_name in weights:
                    weights[module_name] /= total_score
            
            self.logger.info(f"Dynamic weights calculated: {weights}")
            return weights
            
        except Exception as e:
            self.logger.error(f"Error calculating dynamic weights: {e}")
            # 返回均等权重作为备用
            module_count = len(self.performance_history) if self.performance_history else 4
            return {f"module_{i}": 1.0/module_count for i in range(module_count)}
    
    def get_module_statistics(self, module_name: str) -> Dict[str, float]:
        """获取模块统计信息"""
        if module_name not in self.performance_history:
            return {}
            
        history = self.performance_history[module_name]
        if not history:
            return {}
        
        recent_data = history[-20:]  # 最近20期
        
        return {
            "total_predictions": len(history),
            "recent_hit_rate": np.mean([h["hit_rate"] for h in recent_data]),
            "recent_precision": np.mean([h["precision"] for h in recent_data]),
            "stability": 1 - np.std([h["hit_rate"] for h in recent_data]),
            "avg_coverage": np.mean([h["coverage"] for h in recent_data]),
            "last_update": history[-1]["timestamp"].isoformat()
        }

class FusionManager:
    """智能融合管理器"""
    
    def __init__(self):
        self.score_manager = DynamicScoreManager()
        self.logger = logging.getLogger(__name__)
        
        # 静态权重配置
        self.static_weights = {
            "traditional_analysis": 0.30,
            "machine_learning": 0.40,
            "zodiac_extended": 0.20,
            "special_zodiac": 0.10
        }
        
        # 融合策略配置
        self.fusion_config = {
            "voting_threshold": 0.5,
            "max_numbers": 16,
            "min_consensus": 2,
            "diversity_factor": 0.3
        }
    
    def fuse_predictions(self, module_predictions: Dict[str, Dict]) -> Dict[str, Any]:
        """融合多模组预测结果"""
        try:
            self.logger.info("Starting prediction fusion process")
            
            # 第一层：静态加权融合
            static_result = self._static_weighted_fusion(module_predictions)
            
            # 第二层：动态评分融合
            dynamic_result = self._dynamic_score_fusion(module_predictions)
            
            # 第三层：投票机制融合
            voting_result = self._voting_fusion(module_predictions)
            
            # 综合三种融合结果
            final_result = self._combine_fusion_results(
                static_result, dynamic_result, voting_result, module_predictions
            )
            
            # 计算整体置信度
            overall_confidence = self._calculate_overall_confidence(module_predictions, final_result)
            
            # 计算预测稳定性
            stability_score = self._calculate_stability_score(module_predictions)
            
            fusion_analysis = {
                "final_16_numbers": final_result,
                "fusion_methods": {
                    "static_weighted": static_result,
                    "dynamic_scored": dynamic_result,
                    "voting_based": voting_result
                },
                "weights_used": self.score_manager.get_dynamic_weights(),
                "overall_confidence": overall_confidence,
                "stability_score": stability_score,
                "fusion_timestamp": datetime.now().isoformat()
            }
            
            self.logger.info(f"Fusion completed: {len(final_result)} numbers selected")
            return fusion_analysis
            
        except Exception as e:
            self.logger.error(f"Error in prediction fusion: {e}")
            # 返回备用结果
            return self._get_fallback_result(module_predictions)
    
    def _static_weighted_fusion(self, predictions: Dict[str, Dict]) -> List[int]:
        """静态加权融合"""
        weighted_scores = defaultdict(float)
        
        for module_name, pred_data in predictions.items():
            if "numbers" in pred_data and module_name in self.static_weights:
                weight = self.static_weights[module_name]
                confidence = pred_data.get("confidence", 0.5)
                
                for number in pred_data["numbers"]:
                    weighted_scores[number] += weight * confidence
        
        # 按得分排序，选择前16个
        sorted_numbers = sorted(weighted_scores.items(), key=lambda x: x[1], reverse=True)
        return [num for num, score in sorted_numbers[:16]]
    
    def _dynamic_score_fusion(self, predictions: Dict[str, Dict]) -> List[int]:
        """动态评分融合"""
        dynamic_weights = self.score_manager.get_dynamic_weights()
        weighted_scores = defaultdict(float)
        
        for module_name, pred_data in predictions.items():
            if "numbers" in pred_data and module_name in dynamic_weights:
                weight = dynamic_weights[module_name]
                confidence = pred_data.get("confidence", 0.5)
                
                for number in pred_data["numbers"]:
                    weighted_scores[number] += weight * confidence
        
        # 按得分排序，选择前16个
        sorted_numbers = sorted(weighted_scores.items(), key=lambda x: x[1], reverse=True)
        return [num for num, score in sorted_numbers[:16]]
    
    def _voting_fusion(self, predictions: Dict[str, Dict]) -> List[int]:
        """投票机制融合"""
        vote_counts = defaultdict(int)
        total_modules = len(predictions)
        
        # 统计投票
        for module_name, pred_data in predictions.items():
            if "numbers" in pred_data:
                for number in pred_data["numbers"]:
                    vote_counts[number] += 1
        
        # 选择得票数超过阈值的号码
        threshold_votes = max(1, int(total_modules * self.fusion_config["voting_threshold"]))
        selected_numbers = []
        
        for number, votes in vote_counts.items():
            if votes >= threshold_votes:
                selected_numbers.append((number, votes))
        
        # 按得票数排序
        selected_numbers.sort(key=lambda x: x[1], reverse=True)
        
        # 如果选中的号码不足16个，补充高得票的号码
        result = [num for num, votes in selected_numbers[:16]]
        
        if len(result) < 16:
            # 补充剩余号码
            all_voted = sorted(vote_counts.items(), key=lambda x: x[1], reverse=True)
            for number, votes in all_voted:
                if number not in result and len(result) < 16:
                    result.append(number)
        
        return result[:16]
    
    def _combine_fusion_results(self, static_result: List[int], dynamic_result: List[int], 
                               voting_result: List[int], module_predictions: Dict) -> List[int]:
        """综合三种融合结果"""
        
        # 计算每个号码在三种方法中的综合得分
        combined_scores = defaultdict(float)
        
        # 静态融合结果权重
        for i, number in enumerate(static_result):
            combined_scores[number] += (16 - i) * 0.3
        
        # 动态融合结果权重
        for i, number in enumerate(dynamic_result):
            combined_scores[number] += (16 - i) * 0.4
        
        # 投票融合结果权重
        for i, number in enumerate(voting_result):
            combined_scores[number] += (16 - i) * 0.3
        
        # 按综合得分排序
        final_ranking = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 确保多样性 - 避免某个模组贡献过多号码
        final_result = []
        module_contributions = defaultdict(int)
        max_contribution_per_module = 8  # 每个模组最多贡献8个号码
        
        for number, score in final_ranking:
            if len(final_result) >= 16:
                break
                
            # 检查这个号码来自哪些模组
            contributing_modules = []
            for module_name, pred_data in module_predictions.items():
                if "numbers" in pred_data and number in pred_data["numbers"]:
                    contributing_modules.append(module_name)
            
            # 检查是否会导致某个模组贡献过多
            can_add = True
            for module in contributing_modules:
                if module_contributions[module] >= max_contribution_per_module:
                    can_add = False
                    break
            
            if can_add:
                final_result.append(number)
                for module in contributing_modules:
                    module_contributions[module] += 1
        
        # 如果还不足16个，补充剩余的高分号码
        if len(final_result) < 16:
            for number, score in final_ranking:
                if number not in final_result and len(final_result) < 16:
                    final_result.append(number)
        
        return final_result[:16]
    
    def _calculate_overall_confidence(self, module_predictions: Dict, final_numbers: List[int]) -> float:
        """计算整体置信度"""
        try:
            total_confidence = 0
            total_weight = 0
            
            dynamic_weights = self.score_manager.get_dynamic_weights()
            
            for module_name, pred_data in module_predictions.items():
                if "confidence" in pred_data and module_name in dynamic_weights:
                    module_confidence = pred_data["confidence"]
                    module_weight = dynamic_weights[module_name]
                    
                    # 计算该模组对最终结果的贡献度
                    module_numbers = pred_data.get("numbers", [])
                    overlap = len(set(module_numbers) & set(final_numbers))
                    contribution_factor = overlap / len(final_numbers) if final_numbers else 0
                    
                    weighted_confidence = module_confidence * module_weight * (1 + contribution_factor)
                    total_confidence += weighted_confidence
                    total_weight += module_weight
            
            return min(total_confidence / total_weight if total_weight > 0 else 0.5, 1.0)
            
        except Exception as e:
            self.logger.error(f"Error calculating overall confidence: {e}")
            return 0.5
    
    def _calculate_stability_score(self, module_predictions: Dict) -> float:
        """计算预测稳定性"""
        try:
            # 计算各模组预测的一致性
            all_numbers = []
            module_number_sets = []

            for pred_data in module_predictions.values():
                if "numbers" in pred_data and pred_data["numbers"]:
                    numbers = pred_data["numbers"]
                    all_numbers.extend(numbers)
                    module_number_sets.append(set(numbers))

            if not all_numbers or len(module_number_sets) < 2:
                return 0.6  # 默认稳定性

            # 计算模组间的重叠度（Jaccard相似度的平均值）
            total_similarity = 0
            comparison_count = 0

            for i in range(len(module_number_sets)):
                for j in range(i + 1, len(module_number_sets)):
                    set1 = module_number_sets[i]
                    set2 = module_number_sets[j]

                    # 计算Jaccard相似度
                    intersection = len(set1.intersection(set2))
                    union = len(set1.union(set2))

                    if union > 0:
                        similarity = intersection / union
                        total_similarity += similarity
                        comparison_count += 1

            if comparison_count == 0:
                return 0.6

            # 平均相似度作为稳定性指标
            avg_similarity = total_similarity / comparison_count

            # 调整稳定性范围到0.3-0.9之间
            stability = 0.3 + (avg_similarity * 0.6)

            return max(0.3, min(0.9, stability))

        except Exception as e:
            self.logger.error(f"Error calculating stability score: {e}")
            return 0.6
    
    def _get_fallback_result(self, module_predictions: Dict) -> Dict[str, Any]:
        """获取备用结果"""
        # 简单合并所有模组的预测号码
        all_numbers = []
        for pred_data in module_predictions.values():
            if "numbers" in pred_data:
                all_numbers.extend(pred_data["numbers"])
        
        # 去重并选择前16个
        unique_numbers = list(set(all_numbers))
        fallback_numbers = unique_numbers[:16] if len(unique_numbers) >= 16 else unique_numbers
        
        return {
            "final_16_numbers": fallback_numbers,
            "fusion_methods": {"fallback": fallback_numbers},
            "weights_used": self.static_weights,
            "overall_confidence": 0.5,
            "stability_score": 0.5,
            "fusion_timestamp": datetime.now().isoformat(),
            "note": "Fallback result due to fusion error"
        }
    
    def update_module_performance(self, module_name: str, actual_result: int, prediction: List[int]):
        """更新模组性能（外部接口）"""
        self.score_manager.update_performance(module_name, actual_result, prediction)
    
    def get_module_statistics(self) -> Dict[str, Dict]:
        """获取所有模组统计信息"""
        stats = {}
        for module_name in self.score_manager.performance_history.keys():
            stats[module_name] = self.score_manager.get_module_statistics(module_name)
        return stats
    
    def configure_fusion_parameters(self, **kwargs):
        """配置融合参数"""
        for key, value in kwargs.items():
            if key in self.fusion_config:
                self.fusion_config[key] = value
                self.logger.info(f"Updated fusion config: {key} = {value}")
    
    def configure_static_weights(self, weights: Dict[str, float]):
        """配置静态权重"""
        # 验证权重总和为1
        total_weight = sum(weights.values())
        if abs(total_weight - 1.0) > 0.01:
            # 归一化权重
            weights = {k: v/total_weight for k, v in weights.items()}
        
        self.static_weights.update(weights)
        self.logger.info(f"Updated static weights: {self.static_weights}")
