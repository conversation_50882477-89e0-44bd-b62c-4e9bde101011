# -*- coding: utf-8 -*-
"""
六合彩预测系统 - EXE版本启动脚本
"""

import sys
import os
from pathlib import Path

def setup_environment():
    """设置运行环境"""
    # 获取EXE运行目录
    if getattr(sys, 'frozen', False):
        # PyInstaller打包后的路径
        application_path = Path(sys._MEIPASS)
        exe_dir = Path(sys.executable).parent
    else:
        # 开发环境路径
        application_path = Path(__file__).parent
        exe_dir = application_path
    
    # 添加路径到sys.path
    sys.path.insert(0, str(application_path))
    sys.path.insert(0, str(application_path / "src"))
    
    # 设置工作目录
    os.chdir(exe_dir)
    
    # 创建必要目录
    for directory in ["data", "config", "logs"]:
        (exe_dir / directory).mkdir(exist_ok=True)
    
    # 设置编码
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    return application_path, exe_dir

def main():
    """主函数"""
    try:
        # 设置环境
        app_path, exe_dir = setup_environment()
        
        print("🚀 启动六合彩预测系统...")
        print(f"📁 应用路径: {app_path}")
        print(f"📁 执行路径: {exe_dir}")
        
        # 导入并启动GUI
        from lottery_prediction_gui import MainWindow
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        app.setApplicationName("六合彩预测系统")
        app.setApplicationVersion("v2.0.0")
        
        window = MainWindow()
        window.show()
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
