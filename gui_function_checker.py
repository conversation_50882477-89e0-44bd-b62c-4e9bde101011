#!/usr/bin/env python3
"""
GUI功能自动检查器
全面检查所有功能模块的运行状态
"""

import sys
import time
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

def check_data_management():
    """检查数据管理功能"""
    print("📊 检查数据管理功能")
    print("-" * 40)
    
    try:
        # 检查数据库连接
        import sqlite3
        conn = sqlite3.connect("data/lottery.db")
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"✅ 数据库连接成功，发现 {len(tables)} 个表")
        
        # 检查数据量
        cursor.execute("SELECT COUNT(*) FROM lottery_results")
        count = cursor.fetchone()[0]
        print(f"✅ 历史数据: {count} 条记录")
        
        # 检查最新数据
        cursor.execute("SELECT MAX(draw_date) FROM lottery_results")
        latest_date = cursor.fetchone()[0]
        print(f"✅ 最新数据日期: {latest_date}")
        
        conn.close()
        return True, {"tables": len(tables), "records": count, "latest_date": latest_date}
        
    except Exception as e:
        print(f"❌ 数据管理检查失败: {e}")
        return False, {"error": str(e)}

def check_prediction_modules():
    """检查预测模块功能"""
    print("\n🎯 检查预测模块功能")
    print("-" * 40)
    
    results = {}
    
    # 检查特码预测系统
    try:
        from src.special_number_predictor import SpecialNumberPredictor
        predictor = SpecialNumberPredictor()
        
        target_date = "2025-06-25"
        result = predictor.predict(target_date)
        
        if result and "predicted_numbers" in result:
            print(f"✅ 特码预测系统: {len(result['predicted_numbers'])}个号码")
            results["special_number"] = True
        else:
            print(f"❌ 特码预测系统: 结果格式异常")
            results["special_number"] = False
            
    except Exception as e:
        print(f"❌ 特码预测系统: {e}")
        results["special_number"] = False
    
    # 检查一致性预测系统
    try:
        from src.consistency_predictor import ConsistencyPredictor
        predictor = ConsistencyPredictor()
        
        target_date = "2025-06-25"
        result = predictor.predict_with_consistency(target_date)
        
        if result and "recommended_numbers" in result:
            print(f"✅ 一致性预测系统: {len(result['recommended_numbers'])}个号码")
            results["consistency"] = True
        else:
            print(f"❌ 一致性预测系统: 结果格式异常")
            results["consistency"] = False
            
    except Exception as e:
        print(f"❌ 一致性预测系统: {e}")
        results["consistency"] = False
    
    # 检查完美预测系统
    try:
        from src.perfect_prediction_system import PerfectPredictionSystem
        system = PerfectPredictionSystem()
        system.initialize_modules()
        
        target_date = "2025-06-25"
        result = system.run_complete_prediction(target_date)
        
        if result and "final_results" in result:
            final_results = result["final_results"]
            numbers = final_results.get("recommended_16_numbers", [])
            zodiacs = final_results.get("recommended_4_zodiacs", [])
            print(f"✅ 完美预测系统: {len(numbers)}个号码, {len(zodiacs)}个生肖")
            results["perfect_prediction"] = True
        else:
            print(f"❌ 完美预测系统: 结果格式异常")
            results["perfect_prediction"] = False
            
    except Exception as e:
        print(f"❌ 完美预测系统: {e}")
        results["perfect_prediction"] = False
    
    return results

def check_analysis_modules():
    """检查分析模块功能"""
    print("\n📈 检查分析模块功能")
    print("-" * 40)
    
    results = {}
    
    # 检查传统分析模块
    try:
        from src.independent_modules.traditional_analysis_module import TraditionalAnalysisModule
        module = TraditionalAnalysisModule()
        
        target_date = "2025-06-25"
        result = module.predict(target_date)
        
        if result and "predicted_numbers" in result:
            print(f"✅ 传统分析模块: {len(result['predicted_numbers'])}个号码")
            results["traditional"] = True
        else:
            print(f"❌ 传统分析模块: 结果格式异常")
            results["traditional"] = False
            
    except Exception as e:
        print(f"❌ 传统分析模块: {e}")
        results["traditional"] = False
    
    # 检查机器学习模块
    try:
        from src.independent_modules.ml_module import MachineLearningModule
        module = MachineLearningModule()
        
        target_date = "2025-06-25"
        result = module.predict(target_date)
        
        if result and "predicted_numbers" in result:
            algorithm_info = result.get("algorithm_info", {})
            version = algorithm_info.get("version", "v1.0")
            xgboost_enabled = algorithm_info.get("xgboost_enabled", False)
            print(f"✅ 机器学习模块: {len(result['predicted_numbers'])}个号码 (版本: {version}, XGBoost: {'✅' if xgboost_enabled else '❌'})")
            results["machine_learning"] = True
        else:
            print(f"❌ 机器学习模块: 结果格式异常")
            results["machine_learning"] = False
            
    except Exception as e:
        print(f"❌ 机器学习模块: {e}")
        results["machine_learning"] = False
    
    # 检查生肖扩展模块
    try:
        from src.independent_modules.zodiac_extended_module import ZodiacExtendedModule
        module = ZodiacExtendedModule()
        
        target_date = "2025-06-25"
        result = module.predict(target_date)
        
        if result and "predicted_numbers" in result:
            print(f"✅ 生肖扩展模块: {len(result['predicted_numbers'])}个号码")
            results["zodiac_extended"] = True
        else:
            print(f"❌ 生肖扩展模块: 结果格式异常")
            results["zodiac_extended"] = False
            
    except Exception as e:
        print(f"❌ 生肖扩展模块: {e}")
        results["zodiac_extended"] = False
    
    # 检查特殊生肖模块
    try:
        from src.independent_modules.special_zodiac_module import SpecialZodiacModule
        module = SpecialZodiacModule()
        
        target_date = "2025-06-25"
        result = module.predict(target_date)
        
        if result and "predicted_numbers" in result:
            print(f"✅ 特殊生肖模块: {len(result['predicted_numbers'])}个号码")
            results["special_zodiac"] = True
        else:
            print(f"❌ 特殊生肖模块: 结果格式异常")
            results["special_zodiac"] = False
            
    except Exception as e:
        print(f"❌ 特殊生肖模块: {e}")
        results["special_zodiac"] = False
    
    return results

def check_fusion_and_optimization():
    """检查融合和优化功能"""
    print("\n🔀 检查融合和优化功能")
    print("-" * 40)
    
    results = {}
    
    # 检查动态融合管理器
    try:
        from src.dynamic_fusion_manager_v3 import DynamicFusionManager
        manager = DynamicFusionManager()
        
        # 模拟模块预测
        module_predictions = {
            "traditional_analysis": {"numbers": [1, 5, 12, 18, 23, 28, 33, 38, 42, 47, 2, 8, 15, 22, 29, 35], "confidence": 0.75},
            "machine_learning": {"numbers": [3, 7, 14, 19, 25, 31, 36, 41, 45, 49, 6, 11, 17, 24, 30, 37], "confidence": 0.85}
        }
        
        target_date = "2025-06-25"
        result = manager.fuse_predictions(module_predictions, target_date)
        
        if result and "numbers" in result:
            stability = result.get("stability_score", 0)
            print(f"✅ 动态融合管理器: {len(result['numbers'])}个号码 (稳定性: {stability:.1%})")
            results["dynamic_fusion"] = True
        else:
            print(f"❌ 动态融合管理器: 结果格式异常")
            results["dynamic_fusion"] = False
            
    except Exception as e:
        print(f"❌ 动态融合管理器: {e}")
        results["dynamic_fusion"] = False
    
    # 检查稳定性优化器
    try:
        from src.stability_optimizer_v3 import StabilityOptimizer
        optimizer = StabilityOptimizer()
        
        prediction_result = {
            "numbers": [5, 12, 18, 23, 28, 33, 38, 42, 47, 3, 9, 15, 21, 29, 35, 41],
            "confidence": 0.75
        }
        
        target_date = "2025-06-25"
        result = optimizer.optimize_stability(prediction_result, target_date)
        
        if result and "numbers" in result:
            stability = result.get("stability_score", 0)
            final_stability = result.get("final_stability_score", 0)
            print(f"✅ 稳定性优化器: {len(result['numbers'])}个号码 (稳定性: {stability:.1%}, 最终: {final_stability:.1%})")
            results["stability_optimizer"] = True
        else:
            print(f"❌ 稳定性优化器: 结果格式异常")
            results["stability_optimizer"] = False
            
    except Exception as e:
        print(f"❌ 稳定性优化器: {e}")
        results["stability_optimizer"] = False
    
    # 检查增强特征工程
    try:
        from src.enhanced_feature_engineering_v2 import EnhancedFeatureEngineering
        engine = EnhancedFeatureEngineering()
        
        # 模拟历史数据
        historical_data = []
        import numpy as np
        for i in range(50):
            historical_data.append({
                'draw_date': (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d'),
                'special_number': np.random.randint(1, 50),
                'period_number': f'2024{50-i:03d}'
            })
        
        target_date = "2025-06-25"
        result = engine.generate_enhanced_features(historical_data, target_date)
        
        if result and "features" in result:
            feature_count = result.get("feature_count", 0)
            version = result.get("generation_info", {}).get("version", "unknown")
            print(f"✅ 增强特征工程: {feature_count}个特征 (版本: {version})")
            results["enhanced_features"] = True
        else:
            print(f"❌ 增强特征工程: 结果格式异常")
            results["enhanced_features"] = False
            
    except Exception as e:
        print(f"❌ 增强特征工程: {e}")
        results["enhanced_features"] = False
    
    return results

def check_backtest_and_evaluation():
    """检查回测和评估功能"""
    print("\n📊 检查回测和评估功能")
    print("-" * 40)
    
    results = {}
    
    # 检查历史模拟预测
    try:
        from src.historical_simulation_predictor import HistoricalSimulationPredictor
        predictor = HistoricalSimulationPredictor()
        
        # 简单的回测
        start_date = "2025-01-01"
        end_date = "2025-01-05"
        result = predictor.run_historical_simulation(start_date, end_date)
        
        if result and "simulation_results" in result:
            simulation_count = len(result["simulation_results"])
            print(f"✅ 历史模拟预测: {simulation_count}个模拟结果")
            results["historical_simulation"] = True
        else:
            print(f"❌ 历史模拟预测: 结果格式异常")
            results["historical_simulation"] = False
            
    except Exception as e:
        print(f"❌ 历史模拟预测: {e}")
        results["historical_simulation"] = False
    
    # 检查增强回测优化器
    try:
        from enhanced_hit_rate_optimizer import EnhancedHitRateOptimizer
        optimizer = EnhancedHitRateOptimizer()
        
        # 简单的优化测试
        start_date = "2025-01-01"
        end_date = "2025-01-03"
        result = optimizer.run_enhanced_backtest(start_date, end_date, iterations=2)
        
        if result and "best_config" in result:
            hit_rate = result.get("best_hit_rate", 0)
            print(f"✅ 增强回测优化器: 最佳命中率 {hit_rate:.1%}")
            results["enhanced_backtest"] = True
        else:
            print(f"❌ 增强回测优化器: 结果格式异常")
            results["enhanced_backtest"] = False
            
    except Exception as e:
        print(f"❌ 增强回测优化器: {e}")
        results["enhanced_backtest"] = False
    
    return results

def check_gui_integration():
    """检查GUI集成功能"""
    print("\n🖥️ 检查GUI集成功能")
    print("-" * 40)
    
    results = {}
    
    # 检查GUI主要组件
    try:
        import tkinter as tk
        print(f"✅ Tkinter GUI库: 可用")
        results["tkinter"] = True
    except Exception as e:
        print(f"❌ Tkinter GUI库: {e}")
        results["tkinter"] = False
    
    # 检查GUI配置文件
    try:
        import os
        config_files = [
            "optimization_config.json",
            "data/lottery.db"
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"✅ 配置文件: {config_file}")
            else:
                print(f"⚠️ 配置文件缺失: {config_file}")
        
        results["config_files"] = True
        
    except Exception as e:
        print(f"❌ 配置文件检查: {e}")
        results["config_files"] = False
    
    return results

def generate_comprehensive_report(all_results: Dict[str, Any]):
    """生成综合检查报告"""
    print("\n" + "=" * 60)
    print("📊 GUI功能全面检查报告")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 统计总体情况
    total_modules = 0
    working_modules = 0
    
    for category, results in all_results.items():
        if isinstance(results, dict):
            for module, status in results.items():
                if isinstance(status, bool):
                    total_modules += 1
                    if status:
                        working_modules += 1
    
    success_rate = (working_modules / total_modules * 100) if total_modules > 0 else 0
    
    print(f"📊 总体状况:")
    print(f"  总模块数: {total_modules}")
    print(f"  正常模块: {working_modules}")
    print(f"  成功率: {success_rate:.1f}%")
    print()
    
    # 详细结果
    print("📋 详细检查结果:")
    
    for category, results in all_results.items():
        print(f"\n🔍 {category}:")
        if isinstance(results, dict):
            for module, status in results.items():
                if isinstance(status, bool):
                    status_icon = "✅" if status else "❌"
                    print(f"  {status_icon} {module}")
                else:
                    print(f"  📊 {module}: {status}")
    
    print()
    
    # 升级状态总结
    print("🚀 系统升级状态:")
    
    # 检查第1阶段升级
    ml_upgraded = all_results.get("analysis_modules", {}).get("machine_learning", False)
    print(f"  第1阶段 (算法真实化): {'✅ 完成' if ml_upgraded else '❌ 未完成'}")
    
    # 检查第2阶段升级
    features_upgraded = all_results.get("fusion_optimization", {}).get("enhanced_features", False)
    print(f"  第2阶段 (特征工程): {'✅ 完成' if features_upgraded else '❌ 未完成'}")
    
    # 检查第3阶段升级
    fusion_upgraded = all_results.get("fusion_optimization", {}).get("dynamic_fusion", False)
    stability_upgraded = all_results.get("fusion_optimization", {}).get("stability_optimizer", False)
    phase3_complete = fusion_upgraded and stability_upgraded
    print(f"  第3阶段 (融合优化): {'✅ 完成' if phase3_complete else '❌ 未完成'}")
    
    print()
    
    # 建议和总结
    if success_rate >= 80:
        print("🎊 系统状态: 优秀")
        print("所有主要功能模块运行正常，系统升级成功！")
    elif success_rate >= 60:
        print("👍 系统状态: 良好")
        print("大部分功能模块正常，少数模块需要修复。")
    else:
        print("⚠️ 系统状态: 需要优化")
        print("多个功能模块存在问题，建议进行修复。")
    
    print()
    print("🎯 下一步建议:")
    
    if not ml_upgraded:
        print("  • 完成机器学习模块升级")
    if not features_upgraded:
        print("  • 完成特征工程升级")
    if not phase3_complete:
        print("  • 完成融合策略优化")
    
    if success_rate >= 80:
        print("  • 系统已就绪，可以正常使用")
        print("  • 建议进行实际预测测试")
        print("  • 可以开始生产环境部署")

def main():
    """主检查函数"""
    print("🎊 GUI功能全面检查开始")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    all_results = {}
    
    # 执行各项检查
    try:
        # 数据管理检查
        data_success, data_results = check_data_management()
        all_results["data_management"] = data_results
        
        # 预测模块检查
        prediction_results = check_prediction_modules()
        all_results["prediction_modules"] = prediction_results
        
        # 分析模块检查
        analysis_results = check_analysis_modules()
        all_results["analysis_modules"] = analysis_results
        
        # 融合优化检查
        fusion_results = check_fusion_and_optimization()
        all_results["fusion_optimization"] = fusion_results
        
        # 回测评估检查
        backtest_results = check_backtest_and_evaluation()
        all_results["backtest_evaluation"] = backtest_results
        
        # GUI集成检查
        gui_results = check_gui_integration()
        all_results["gui_integration"] = gui_results
        
        # 生成综合报告
        generate_comprehensive_report(all_results)
        
        return True
        
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
