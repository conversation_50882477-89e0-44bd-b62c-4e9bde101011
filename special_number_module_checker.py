#!/usr/bin/env python3
"""
特码预测模块工作状态检查
检查特码预测系统中各个模块是否正常参与工作
"""

import sys
import time
import traceback
from datetime import datetime
from typing import Dict, List, Any, Optional

class SpecialNumberModuleChecker:
    """特码预测模块检查器"""
    
    def __init__(self):
        self.test_results = {}
        self.module_status = {}
        
    def check_special_number_predictor(self):
        """检查特码预测器主模块"""
        print("🎯 检查特码预测器主模块")
        print("-" * 50)
        
        try:
            from src.special_number_predictor import SpecialNumberPredictor
            
            # 初始化预测器
            predictor = SpecialNumberPredictor()
            print("✅ 特码预测器初始化成功")
            
            # 测试预测功能
            target_date = "2025-06-25"
            
            print(f"🔧 开始特码预测: {target_date}")
            start_time = time.time()
            
            result = predictor.predict(target_date)
            
            duration = time.time() - start_time
            
            if result and "predicted_numbers" in result:
                predicted_numbers = result["predicted_numbers"]
                confidence = result.get("confidence", 0)
                method = result.get("method", "unknown")
                
                print(f"✅ 特码预测完成")
                print(f"📊 预测号码数: {len(predicted_numbers)}")
                print(f"📊 置信度: {confidence:.1%}")
                print(f"📊 预测方法: {method}")
                print(f"📊 执行时间: {duration:.3f}秒")
                print(f"📊 预测号码: {predicted_numbers}")
                
                return True, {
                    "success": True,
                    "numbers_count": len(predicted_numbers),
                    "confidence": confidence,
                    "method": method,
                    "duration": duration,
                    "predicted_numbers": predicted_numbers
                }
            else:
                print(f"❌ 特码预测结果格式异常")
                return False, {"error": "结果格式异常", "result": result}
                
        except Exception as e:
            print(f"❌ 特码预测器检查失败: {e}")
            traceback.print_exc()
            return False, {"error": str(e)}
    
    def check_consistency_predictor(self):
        """检查一致性预测器"""
        print("\n🔒 检查一致性预测器")
        print("-" * 50)
        
        try:
            from src.consistency_predictor import ConsistencyPredictor
            
            # 初始化预测器
            predictor = ConsistencyPredictor()
            print("✅ 一致性预测器初始化成功")
            
            # 测试一致性预测
            target_date = "2025-06-25"
            
            print(f"🔧 开始一致性预测: {target_date}")
            start_time = time.time()
            
            result = predictor.predict_with_consistency(target_date)
            
            duration = time.time() - start_time
            
            if result and "recommended_numbers" in result:
                recommended_numbers = result["recommended_numbers"]
                confidence = result.get("confidence", 0)
                consistency_score = result.get("consistency_score", 0)
                
                print(f"✅ 一致性预测完成")
                print(f"📊 推荐号码数: {len(recommended_numbers)}")
                print(f"📊 置信度: {confidence:.1%}")
                print(f"📊 一致性得分: {consistency_score:.1%}")
                print(f"📊 执行时间: {duration:.3f}秒")
                print(f"📊 推荐号码: {recommended_numbers}")
                
                return True, {
                    "success": True,
                    "numbers_count": len(recommended_numbers),
                    "confidence": confidence,
                    "consistency_score": consistency_score,
                    "duration": duration,
                    "recommended_numbers": recommended_numbers
                }
            else:
                print(f"❌ 一致性预测结果格式异常")
                return False, {"error": "结果格式异常", "result": result}
                
        except Exception as e:
            print(f"❌ 一致性预测器检查失败: {e}")
            traceback.print_exc()
            return False, {"error": str(e)}
    
    def check_independent_modules(self):
        """检查独立分析模块"""
        print("\n📈 检查独立分析模块")
        print("-" * 50)
        
        results = {}
        target_date = "2025-06-25"
        
        # 检查机器学习模块
        try:
            from src.independent_modules.ml_module import MachineLearningModule
            
            print("🤖 检查机器学习模块...")
            module = MachineLearningModule()
            
            start_time = time.time()
            result = module.predict(target_date)
            duration = time.time() - start_time
            
            if result and "predicted_numbers" in result:
                algorithm_info = result.get("algorithm_info", {})
                results["ml_module"] = {
                    "success": True,
                    "numbers_count": len(result["predicted_numbers"]),
                    "confidence": result.get("confidence", 0),
                    "version": algorithm_info.get("version", "unknown"),
                    "xgboost_enabled": algorithm_info.get("xgboost_enabled", False),
                    "models_count": algorithm_info.get("total_models", 0),
                    "duration": duration,
                    "predicted_numbers": result["predicted_numbers"]
                }
                print(f"✅ 机器学习模块: {len(result['predicted_numbers'])}个号码, v{algorithm_info.get('version', '1.0')}")
            else:
                results["ml_module"] = {"success": False, "error": "结果格式异常"}
                print(f"❌ 机器学习模块: 结果格式异常")
                
        except Exception as e:
            results["ml_module"] = {"success": False, "error": str(e)}
            print(f"❌ 机器学习模块: {e}")
        
        # 检查传统分析模块
        try:
            from src.independent_modules.traditional_module import TraditionalAnalysisModule
            
            print("📊 检查传统分析模块...")
            module = TraditionalAnalysisModule()
            
            start_time = time.time()
            result = module.predict(target_date)
            duration = time.time() - start_time
            
            if result and "predicted_numbers" in result:
                results["traditional_module"] = {
                    "success": True,
                    "numbers_count": len(result["predicted_numbers"]),
                    "confidence": result.get("confidence", 0),
                    "duration": duration,
                    "predicted_numbers": result["predicted_numbers"]
                }
                print(f"✅ 传统分析模块: {len(result['predicted_numbers'])}个号码")
            else:
                results["traditional_module"] = {"success": False, "error": "结果格式异常"}
                print(f"❌ 传统分析模块: 结果格式异常")
                
        except Exception as e:
            results["traditional_module"] = {"success": False, "error": str(e)}
            print(f"❌ 传统分析模块: {e}")
        
        # 检查生肖扩展模块
        try:
            from src.independent_modules.zodiac_extended_module import ZodiacExtendedModule
            
            print("🐲 检查生肖扩展模块...")
            module = ZodiacExtendedModule()
            
            start_time = time.time()
            result = module.predict(target_date)
            duration = time.time() - start_time
            
            if result and "predicted_numbers" in result:
                results["zodiac_extended_module"] = {
                    "success": True,
                    "numbers_count": len(result["predicted_numbers"]),
                    "confidence": result.get("confidence", 0),
                    "duration": duration,
                    "predicted_numbers": result["predicted_numbers"]
                }
                print(f"✅ 生肖扩展模块: {len(result['predicted_numbers'])}个号码")
            else:
                results["zodiac_extended_module"] = {"success": False, "error": "结果格式异常"}
                print(f"❌ 生肖扩展模块: 结果格式异常")
                
        except Exception as e:
            results["zodiac_extended_module"] = {"success": False, "error": str(e)}
            print(f"❌ 生肖扩展模块: {e}")
        
        # 检查特殊生肖模块
        try:
            from src.independent_modules.special_zodiac_module import SpecialZodiacModule
            
            print("⭐ 检查特殊生肖模块...")
            module = SpecialZodiacModule()
            
            start_time = time.time()
            result = module.predict(target_date)
            duration = time.time() - start_time
            
            if result and "predicted_numbers" in result:
                results["special_zodiac_module"] = {
                    "success": True,
                    "numbers_count": len(result["predicted_numbers"]),
                    "confidence": result.get("confidence", 0),
                    "duration": duration,
                    "predicted_numbers": result["predicted_numbers"]
                }
                print(f"✅ 特殊生肖模块: {len(result['predicted_numbers'])}个号码")
            else:
                results["special_zodiac_module"] = {"success": False, "error": "结果格式异常"}
                print(f"❌ 特殊生肖模块: 结果格式异常")
                
        except Exception as e:
            results["special_zodiac_module"] = {"success": False, "error": str(e)}
            print(f"❌ 特殊生肖模块: {e}")
        
        return results
    
    def check_perfect_prediction_system(self):
        """检查完美预测系统"""
        print("\n🎊 检查完美预测系统")
        print("-" * 50)
        
        try:
            from src.perfect_prediction_system import PerfectPredictionSystem
            
            # 初始化系统
            system = PerfectPredictionSystem()
            print("✅ 完美预测系统初始化成功")
            
            # 初始化模块
            system.initialize_modules()
            print("✅ 模块初始化成功")
            
            # 执行完美预测
            target_date = "2025-06-25"
            
            print(f"🔧 开始完美预测: {target_date}")
            start_time = time.time()
            
            result = system.run_complete_prediction(target_date)
            
            duration = time.time() - start_time
            
            if result and "final_results" in result:
                final_results = result["final_results"]
                
                # 提取结果
                numbers = final_results.get("recommended_16_numbers", [])
                zodiacs = final_results.get("recommended_4_zodiacs", [])
                confidence = final_results.get("overall_confidence", 0)
                stability = final_results.get("prediction_stability", 0)
                
                # 检查模块参与情况
                module_results = result.get("module_results", {})
                fusion_info = result.get("fusion_info", {})
                
                print(f"✅ 完美预测系统测试成功")
                print(f"📊 推荐号码: {len(numbers)}个")
                print(f"📊 推荐生肖: {len(zodiacs)}个")
                print(f"📊 整体置信度: {confidence:.1%}")
                print(f"📊 预测稳定性: {stability:.1%}")
                print(f"📊 参与模块数: {len(module_results)}")
                print(f"📊 执行时间: {duration:.3f}秒")
                
                # 显示模块参与情况
                print(f"📊 模块参与情况:")
                for module_name, module_result in module_results.items():
                    if isinstance(module_result, dict) and "predicted_numbers" in module_result:
                        module_numbers = len(module_result["predicted_numbers"])
                        module_confidence = module_result.get("confidence", 0)
                        print(f"  ✅ {module_name}: {module_numbers}个号码, 置信度{module_confidence:.1%}")
                    else:
                        print(f"  ❌ {module_name}: 未正常工作")
                
                return True, {
                    "success": True,
                    "numbers_count": len(numbers),
                    "zodiacs_count": len(zodiacs),
                    "confidence": confidence,
                    "stability": stability,
                    "modules_count": len(module_results),
                    "duration": duration,
                    "module_results": module_results,
                    "recommended_numbers": numbers,
                    "recommended_zodiacs": zodiacs
                }
            else:
                print(f"❌ 完美预测系统: 结果格式异常")
                return False, {"error": "结果格式异常", "result": result}
                
        except Exception as e:
            print(f"❌ 完美预测系统检查失败: {e}")
            traceback.print_exc()
            return False, {"error": str(e)}
    
    def check_module_integration(self):
        """检查模块集成情况"""
        print("\n🔗 检查模块集成情况")
        print("-" * 50)
        
        try:
            # 检查各模块是否能正常协作
            target_date = "2025-06-25"
            
            # 获取各模块预测结果
            module_predictions = {}
            
            # 特码预测器
            try:
                from src.special_number_predictor import SpecialNumberPredictor
                predictor = SpecialNumberPredictor()
                result = predictor.predict(target_date)
                if result and "predicted_numbers" in result:
                    module_predictions["special_number_predictor"] = result["predicted_numbers"]
                    print(f"✅ 特码预测器集成: {len(result['predicted_numbers'])}个号码")
                else:
                    print(f"❌ 特码预测器集成失败")
            except Exception as e:
                print(f"❌ 特码预测器集成异常: {e}")
            
            # 一致性预测器
            try:
                from src.consistency_predictor import ConsistencyPredictor
                predictor = ConsistencyPredictor()
                result = predictor.predict_with_consistency(target_date)
                if result and "recommended_numbers" in result:
                    module_predictions["consistency_predictor"] = result["recommended_numbers"]
                    print(f"✅ 一致性预测器集成: {len(result['recommended_numbers'])}个号码")
                else:
                    print(f"❌ 一致性预测器集成失败")
            except Exception as e:
                print(f"❌ 一致性预测器集成异常: {e}")
            
            # 分析模块集成
            try:
                from src.independent_modules.ml_module import MachineLearningModule
                module = MachineLearningModule()
                result = module.predict(target_date)
                if result and "predicted_numbers" in result:
                    module_predictions["ml_module"] = result["predicted_numbers"]
                    print(f"✅ 机器学习模块集成: {len(result['predicted_numbers'])}个号码")
                else:
                    print(f"❌ 机器学习模块集成失败")
            except Exception as e:
                print(f"❌ 机器学习模块集成异常: {e}")
            
            # 检查号码重叠情况
            if len(module_predictions) >= 2:
                all_numbers = []
                for module_name, numbers in module_predictions.items():
                    all_numbers.extend(numbers)
                
                unique_numbers = set(all_numbers)
                overlap_rate = (len(all_numbers) - len(unique_numbers)) / len(all_numbers) if all_numbers else 0
                
                print(f"📊 模块集成统计:")
                print(f"  参与模块: {len(module_predictions)}个")
                print(f"  总预测号码: {len(all_numbers)}个")
                print(f"  唯一号码: {len(unique_numbers)}个")
                print(f"  重叠率: {overlap_rate:.1%}")
                
                return True, {
                    "success": True,
                    "modules_count": len(module_predictions),
                    "total_numbers": len(all_numbers),
                    "unique_numbers": len(unique_numbers),
                    "overlap_rate": overlap_rate,
                    "module_predictions": module_predictions
                }
            else:
                print(f"⚠️ 集成模块数量不足: {len(module_predictions)}个")
                return False, {"error": "集成模块数量不足"}
                
        except Exception as e:
            print(f"❌ 模块集成检查失败: {e}")
            return False, {"error": str(e)}
    
    def generate_module_status_report(self):
        """生成模块状态报告"""
        print("\n" + "=" * 60)
        print("📊 特码预测模块工作状态报告")
        print("=" * 60)
        print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 统计检查结果
        total_checks = len(self.test_results)
        successful_checks = sum(1 for result in self.test_results.values() if result.get("success", False))
        
        print(f"📊 检查统计:")
        print(f"  总检查项: {total_checks}")
        print(f"  成功项目: {successful_checks}")
        print(f"  成功率: {successful_checks/total_checks:.1%}" if total_checks > 0 else "  成功率: 0%")
        print()
        
        # 详细模块状态
        print("📋 模块工作状态:")
        
        # 主要预测系统
        special_predictor = self.test_results.get("special_number_predictor", {})
        consistency_predictor = self.test_results.get("consistency_predictor", {})
        perfect_system = self.test_results.get("perfect_prediction_system", {})
        
        print("🎯 主要预测系统:")
        print(f"  {'✅' if special_predictor.get('success') else '❌'} 特码预测器: {special_predictor.get('numbers_count', 0)}个号码")
        print(f"  {'✅' if consistency_predictor.get('success') else '❌'} 一致性预测器: {consistency_predictor.get('numbers_count', 0)}个号码")
        print(f"  {'✅' if perfect_system.get('success') else '❌'} 完美预测系统: {perfect_system.get('numbers_count', 0)}个号码")
        
        # 独立分析模块
        independent_modules = self.test_results.get("independent_modules", {})
        
        print("\n📈 独立分析模块:")
        for module_name, module_result in independent_modules.items():
            status = "✅" if module_result.get("success") else "❌"
            numbers_count = module_result.get("numbers_count", 0)
            confidence = module_result.get("confidence", 0)
            print(f"  {status} {module_name}: {numbers_count}个号码, 置信度{confidence:.1%}")
        
        # 模块集成状态
        integration = self.test_results.get("module_integration", {})
        
        print("\n🔗 模块集成状态:")
        if integration.get("success"):
            print(f"  ✅ 集成成功: {integration.get('modules_count', 0)}个模块参与")
            print(f"  📊 总预测号码: {integration.get('total_numbers', 0)}个")
            print(f"  📊 唯一号码: {integration.get('unique_numbers', 0)}个")
            print(f"  📊 重叠率: {integration.get('overlap_rate', 0):.1%}")
        else:
            print(f"  ❌ 集成失败: {integration.get('error', '未知错误')}")
        
        print()
        
        # 性能分析
        print("⚡ 性能分析:")
        total_duration = 0
        module_count = 0
        
        for test_name, result in self.test_results.items():
            if result.get("success") and "duration" in result:
                duration = result["duration"]
                total_duration += duration
                module_count += 1
                print(f"  {test_name}: {duration:.3f}秒")
        
        if module_count > 0:
            avg_duration = total_duration / module_count
            print(f"  平均响应时间: {avg_duration:.3f}秒")
            print(f"  总执行时间: {total_duration:.3f}秒")
        
        print()
        
        # 系统健康度评估
        if successful_checks == total_checks:
            health_status = "🎊 优秀"
            health_desc = "所有模块正常工作，系统健康度极佳"
        elif successful_checks >= total_checks * 0.8:
            health_status = "👍 良好"
            health_desc = "大部分模块正常工作，系统健康度良好"
        elif successful_checks >= total_checks * 0.6:
            health_status = "⚠️ 一般"
            health_desc = "部分模块存在问题，需要关注"
        else:
            health_status = "❌ 较差"
            health_desc = "多个模块存在问题，需要修复"
        
        print(f"🏥 系统健康度: {health_status}")
        print(f"   {health_desc}")
        print(f"   健康度评分: {successful_checks/total_checks:.1%}" if total_checks > 0 else "   健康度评分: 0%")
        print()
        
        # 建议
        print("💡 建议:")
        if successful_checks == total_checks:
            print("  • 所有模块正常工作，特码预测系统运行完美")
            print("  • 建议定期监控模块性能和预测准确性")
            print("  • 可以考虑进一步优化模块协作效率")
        else:
            failed_modules = [name for name, result in self.test_results.items() if not result.get("success", False)]
            if failed_modules:
                print(f"  • 需要修复的模块: {', '.join(failed_modules)}")
            print("  • 建议检查失败模块的依赖和配置")
            print("  • 优先修复核心预测模块")
        
        print()
        print("🎯 总结:")
        print("  特码预测模块工作状态检查完成")
        print(f"  系统状态: {health_status.split()[1]}")
        print("  所有核心模块已验证")

def main():
    """主检查函数"""
    print("🎊 特码预测模块工作状态检查开始")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    checker = SpecialNumberModuleChecker()
    
    try:
        # 检查特码预测器主模块
        success1, result1 = checker.check_special_number_predictor()
        checker.test_results["special_number_predictor"] = result1
        
        # 检查一致性预测器
        success2, result2 = checker.check_consistency_predictor()
        checker.test_results["consistency_predictor"] = result2
        
        # 检查独立分析模块
        result3 = checker.check_independent_modules()
        checker.test_results["independent_modules"] = result3
        
        # 检查完美预测系统
        success4, result4 = checker.check_perfect_prediction_system()
        checker.test_results["perfect_prediction_system"] = result4
        
        # 检查模块集成
        success5, result5 = checker.check_module_integration()
        checker.test_results["module_integration"] = result5
        
        # 生成模块状态报告
        checker.generate_module_status_report()
        
        return True
        
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
