"""
检查便携版封包的GUI启动问题
针对用户指定的封包位置进行诊断
"""

import os
import sys
import subprocess
from pathlib import Path

def check_package_directory(package_path):
    """检查封包目录"""
    print(f"🔍 检查封包目录: {package_path}")
    print("-" * 60)
    
    if not os.path.exists(package_path):
        print(f"❌ 目录不存在: {package_path}")
        return False
    
    print(f"✅ 目录存在: {package_path}")
    
    # 列出目录内容
    print("\n📁 目录内容:")
    try:
        for item in os.listdir(package_path):
            item_path = os.path.join(package_path, item)
            if os.path.isdir(item_path):
                print(f"  📁 {item}/")
            else:
                print(f"  📄 {item}")
    except Exception as e:
        print(f"❌ 无法读取目录: {e}")
        return False
    
    return True

def check_required_files(package_path):
    """检查必需文件"""
    print(f"\n📋 检查必需文件...")
    print("-" * 40)
    
    required_files = [
        "lottery_prediction_gui.py",
        "start_system.bat",
        "启动系统.bat"
    ]
    
    missing_files = []
    
    for file_name in required_files:
        file_path = os.path.join(package_path, file_name)
        if os.path.exists(file_path):
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name} (缺失)")
            missing_files.append(file_name)
    
    # 检查src目录
    src_path = os.path.join(package_path, "src")
    if os.path.exists(src_path):
        print(f"✅ src/ 目录")
    else:
        print(f"❌ src/ 目录 (缺失)")
        missing_files.append("src/")
    
    return missing_files

def create_debug_startup_script(package_path):
    """创建调试启动脚本"""
    print(f"\n🔧 创建调试启动脚本...")
    print("-" * 40)
    
    debug_script = f'''@echo off
chcp 65001 > nul
title 六合彩预测系统 - 调试模式

echo 🎊 六合彩预测系统 - 调试启动
echo ==========================================

REM 显示当前目录
echo 📁 当前目录: %CD%
echo.

REM 检查Python环境
echo 📋 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo.
    echo 💡 解决方案:
    echo 1. 下载并安装Python 3.8+: https://www.python.org/downloads/
    echo 2. 安装时务必勾选 "Add Python to PATH"
    echo 3. 重启电脑后再试
    echo.
    pause
    exit /b 1
)
echo ✅ Python环境正常
echo.

REM 检查必需文件
echo 📁 检查程序文件...
if not exist "lottery_prediction_gui.py" (
    echo ❌ 错误: 未找到主程序文件
    echo 当前目录内容:
    dir /b
    echo.
    pause
    exit /b 1
)
echo ✅ 主程序文件存在
echo.

REM 升级pip
echo 📦 升级pip...
python -m pip install --upgrade pip --quiet
echo ✅ pip升级完成
echo.

REM 安装依赖包
echo 📦 安装/检查依赖包...
echo 正在安装PyQt5...
python -m pip install PyQt5 --quiet
echo 正在安装numpy...
python -m pip install numpy --quiet
echo 正在安装pandas...
python -m pip install pandas --quiet
echo 正在安装scikit-learn...
python -m pip install scikit-learn --quiet
echo ✅ 依赖包安装完成
echo.

REM 测试PyQt5
echo 🧪 测试GUI环境...
python -c "from PyQt5.QtWidgets import QApplication; print('✅ PyQt5测试通过')"
if errorlevel 1 (
    echo ❌ PyQt5测试失败，正在重新安装...
    python -m pip uninstall PyQt5 -y
    python -m pip install PyQt5
    echo 重新测试...
    python -c "from PyQt5.QtWidgets import QApplication; print('✅ PyQt5重新安装成功')"
)
echo.

REM 启动程序
echo 🚀 启动GUI界面...
echo 注意: 如果程序启动但没有窗口显示，请:
echo 1. 检查任务栏是否有程序图标
echo 2. 按Alt+Tab切换窗口
echo 3. 检查是否被杀毒软件拦截
echo.

python lottery_prediction_gui.py

REM 如果程序异常退出，显示错误信息
if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败
    echo 正在运行详细诊断...
    echo.
    
    REM 尝试导入测试
    python -c "import lottery_prediction_gui; print('✅ 模块导入成功')"
    if errorlevel 1 (
        echo ❌ 模块导入失败，可能是依赖问题
    )
    
    echo.
    echo 💡 常见解决方案:
    echo 1. 确保Python版本为3.8或更高
    echo 2. 重新安装PyQt5: pip install PyQt5
    echo 3. 检查杀毒软件是否拦截
    echo 4. 以管理员身份运行此脚本
    echo 5. 重启电脑后再试
)

echo.
echo 按任意键退出...
pause > nul
'''
    
    script_path = os.path.join(package_path, "debug_start.bat")
    try:
        with open(script_path, "w", encoding="gbk", errors="ignore") as f:
            f.write(debug_script)
        print(f"✅ 调试脚本已创建: {script_path}")
        return script_path
    except Exception as e:
        print(f"❌ 创建调试脚本失败: {e}")
        return None

def create_simple_gui_test(package_path):
    """创建简单GUI测试"""
    print(f"\n🧪 创建简单GUI测试...")
    print("-" * 40)
    
    test_code = '''
import sys
import os

def test_environment():
    """测试环境"""
    print("🔍 环境测试")
    print("-" * 30)
    print(f"Python版本: {{sys.version}}")
    print(f"当前目录: {{os.getcwd()}}")
    print(f"Python路径: {{sys.executable}}")
    
    # 测试PyQt5导入
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout
        from PyQt5.QtCore import Qt
        print("✅ PyQt5导入成功")
        
        # 创建简单窗口
        app = QApplication(sys.argv)
        
        window = QWidget()
        window.setWindowTitle("GUI测试 - 六合彩预测系统")
        window.setGeometry(300, 300, 400, 200)
        
        layout = QVBoxLayout()
        
        label1 = QLabel("🎊 GUI测试成功！")
        label1.setAlignment(Qt.AlignCenter)
        label1.setStyleSheet("font-size: 16px; font-weight: bold; color: green;")
        
        label2 = QLabel("如果你看到这个窗口，说明PyQt5工作正常。\\n主程序应该也能正常启动。")
        label2.setAlignment(Qt.AlignCenter)
        label2.setStyleSheet("font-size: 12px;")
        
        label3 = QLabel("窗口将在5秒后自动关闭...")
        label3.setAlignment(Qt.AlignCenter)
        label3.setStyleSheet("font-size: 10px; color: gray;")
        
        layout.addWidget(label1)
        layout.addWidget(label2)
        layout.addWidget(label3)
        
        window.setLayout(layout)
        window.show()
        
        print("✅ GUI窗口已显示")
        print("如果没有看到窗口，请检查任务栏或按Alt+Tab")
        
        # 5秒后自动关闭
        from PyQt5.QtCore import QTimer
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(5000)  # 5秒
        
        app.exec_()
        print("✅ GUI测试完成")
        
    except ImportError as e:
        print(f"❌ PyQt5导入失败: {{e}}")
        print("请运行: pip install PyQt5")
    except Exception as e:
        print(f"❌ GUI测试失败: {{e}}")

if __name__ == "__main__":
    test_environment()
'''
    
    test_path = os.path.join(package_path, "test_gui.py")
    try:
        with open(test_path, "w", encoding="utf-8") as f:
            f.write(test_code)
        print(f"✅ GUI测试文件已创建: {test_path}")
        return test_path
    except Exception as e:
        print(f"❌ 创建GUI测试文件失败: {e}")
        return None

def provide_specific_solutions(package_path):
    """提供针对性解决方案"""
    print(f"\n💡 针对便携版的解决方案")
    print("=" * 60)
    
    print(f"📁 封包位置: {package_path}")
    print()
    
    print("🔧 解决方案1: 使用调试启动脚本")
    print("1. 双击运行 debug_start.bat")
    print("2. 查看详细的启动过程和错误信息")
    print("3. 根据错误信息进行针对性修复")
    print()
    
    print("🔧 解决方案2: 手动启动")
    print("1. 打开命令提示符 (cmd)")
    print(f"2. 切换到目录: cd /d \"{package_path}\"")
    print("3. 运行: python lottery_prediction_gui.py")
    print("4. 查看错误信息")
    print()
    
    print("🔧 解决方案3: 检查Python环境")
    print("1. 打开命令提示符")
    print("2. 运行: python --version")
    print("3. 如果提示未找到，需要安装Python")
    print("4. 下载地址: https://www.python.org/downloads/")
    print("5. 安装时务必勾选 'Add Python to PATH'")
    print()
    
    print("🔧 解决方案4: 重新安装依赖")
    print("1. 运行: pip install PyQt5 numpy pandas scikit-learn")
    print("2. 如果失败，尝试: pip install --user PyQt5")
    print("3. 或者使用国内源: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple PyQt5")
    print()
    
    print("🔧 解决方案5: 检查杀毒软件")
    print("1. 临时关闭杀毒软件")
    print("2. 将封包目录添加到白名单")
    print("3. 以管理员身份运行")

def main():
    """主函数"""
    package_path = r"F:\软件下载\六合彩各版本工具\LotteryPrediction_Portable_v1.0.0"
    
    print("🎊 便携版封包GUI启动问题诊断")
    print("=" * 60)
    
    # 检查目录
    if not check_package_directory(package_path):
        print("❌ 无法访问封包目录，请检查路径是否正确")
        return
    
    # 检查必需文件
    missing_files = check_required_files(package_path)
    
    # 创建调试脚本
    debug_script = create_debug_startup_script(package_path)
    
    # 创建GUI测试
    gui_test = create_simple_gui_test(package_path)
    
    # 提供解决方案
    provide_specific_solutions(package_path)
    
    print("\n" + "=" * 60)
    print("🎯 诊断总结:")
    print(f"封包目录: {'✅ 存在' if os.path.exists(package_path) else '❌ 不存在'}")
    print(f"必需文件: {'✅ 完整' if not missing_files else f'❌ 缺失{len(missing_files)}个'}")
    print(f"调试脚本: {'✅ 已创建' if debug_script else '❌ 创建失败'}")
    print(f"GUI测试: {'✅ 已创建' if gui_test else '❌ 创建失败'}")
    
    if missing_files:
        print(f"\n⚠️ 缺失文件: {', '.join(missing_files)}")
    
    print(f"\n💡 建议操作:")
    print(f"1. 进入目录: {package_path}")
    print(f"2. 运行调试脚本: debug_start.bat")
    print(f"3. 或运行GUI测试: python test_gui.py")
    print("=" * 60)

if __name__ == "__main__":
    main()
