import re

# 读取完美预测系统文件
with open("src/perfect_prediction_system.py", "r", encoding="utf-8") as f:
    content = f.read()

print("🐲 完美预测系统生肖预测分析")
print("=" * 50)

# 查找生肖预测相关的方法
zodiac_methods = [
    "predict_zodiacs",
    "_predict_zodiacs", 
    "get_zodiac_predictions",
    "special_zodiac_prediction"
]

for method in zodiac_methods:
    if method in content:
        print(f"✅ 找到方法: {method}")
        
        # 提取方法内容
        start_idx = content.find(f"def {method}")
        if start_idx != -1:
            lines = content[start_idx:].split("\n")
            method_lines = []
            
            for i, line in enumerate(lines):
                if i > 0 and line.strip().startswith("def ") and method not in line:
                    break
                method_lines.append(line)
                if len(method_lines) > 20:  # 限制输出长度
                    break
            
            print(f"   方法内容:")
            for j, line in enumerate(method_lines[:15]):
                print(f"   {j+1:2d}: {line}")
            print()
    else:
        print(f"❌ 未找到方法: {method}")

# 查找返回结果中的生肖字段
print("\n🔍 查找返回结果中的生肖字段:")
zodiac_result_patterns = [
    r"\"predicted_zodiacs\":\s*[^,\}]*",
    r"\"zodiac_predictions\":\s*[^,\}]*",
    r"\"special_zodiacs\":\s*[^,\}]*",
    r"\"final_zodiacs\":\s*[^,\}]*",
    r"result\[\".*zodiac.*\"\]"
]

for pattern in zodiac_result_patterns:
    matches = re.findall(pattern, content, re.MULTILINE)
    if matches:
        print(f"✅ 找到生肖结果字段:")
        for match in matches[:3]:  # 显示前3个
            print(f"   {match}")
    else:
        print(f"❌ 未找到模式: {pattern}")

# 查找run_complete_prediction方法的返回结构
print("\n🔍 查找run_complete_prediction方法的返回结构:")
if "def run_complete_prediction" in content:
    start_idx = content.find("def run_complete_prediction")
    method_section = content[start_idx:start_idx+3000]
    
    # 查找return语句
    return_statements = re.findall(r"return\s+{[^}]*}", method_section, re.DOTALL)
    if return_statements:
        print(f"✅ 找到返回语句:")
        for ret in return_statements[:2]:
            # 清理和格式化
            clean_ret = ret.replace("\n", " ").replace("  ", " ")
            print(f"   {clean_ret[:150]}...")
    else:
        print(f"❌ 未找到return语句")
