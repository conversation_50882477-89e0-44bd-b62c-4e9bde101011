"""
简单CSV抓取测试
"""

import sys
from pathlib import Path

# 添加路径
sys.path.insert(0, str(Path.cwd()))
sys.path.insert(0, str(Path.cwd() / "src"))

def test_csv_only():
    """测试纯CSV抓取"""
    print("🧪 测试纯CSV抓取功能")
    print("=" * 50)
    
    try:
        from src.data_layer.data_import.web_scraper import WebScraper
        
        # 创建抓取器
        scraper = WebScraper()
        print("✅ WebScraper模块导入成功")
        
        # 1. 先获取数据
        print("\n📡 获取数据...")
        scrape_result = scraper.scrape_kjdata_api()
        
        if not scrape_result.get('success'):
            print(f"❌ 数据获取失败: {scrape_result.get('error')}")
            return False
        
        print(f"✅ 数据获取成功: {scrape_result.get('total_records')} 条")
        
        # 2. 保存为CSV
        print("\n💾 保存为CSV...")
        processed_data = scrape_result['data']
        csv_result = scraper.save_kjdata_to_csv(processed_data)
        
        if csv_result.get('success'):
            print("✅ CSV保存成功")
            print(f"  保存记录数: {csv_result.get('saved_count', 0):,}")
            
            filename = csv_result.get('filename')
            print(f"  文件名: {filename}")
            
            if filename and Path(filename).exists():
                file_size = Path(filename).stat().st_size
                print(f"  文件大小: {file_size:,} 字节")
                print(f"  文件存在: ✅")
                
                # 验证CSV内容
                import pandas as pd
                df = pd.read_csv(filename, encoding='utf-8-sig')
                print(f"  CSV行数: {len(df):,}")
                print(f"  CSV列数: {len(df.columns)}")
                
                # 显示前几行
                print(f"\n📊 数据预览:")
                print(df.head(3).to_string())
                
                return True
            else:
                print(f"❌ 文件不存在: {filename}")
                return False
        else:
            print(f"❌ CSV保存失败: {csv_result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comprehensive_csv():
    """测试综合CSV抓取"""
    print("\n🚀 测试综合CSV抓取")
    print("=" * 50)
    
    try:
        from src.data_layer.data_import.web_scraper import WebScraper
        
        scraper = WebScraper()
        
        # 执行综合抓取 - 仅CSV格式
        print("🔄 执行综合CSV抓取...")
        result = scraper.comprehensive_scrape_and_save(save_format="csv", save_to_db=False)
        
        print(f"📊 抓取结果:")
        print(f"  成功: {result.get('success')}")
        print(f"  总记录数: {result.get('total_records', 0):,}")
        print(f"  处理记录数: {result.get('processed_records', 0):,}")
        print(f"  保存记录数: {result.get('saved_records', 0):,}")
        print(f"  文件名: {result.get('filename')}")
        
        if result.get('success') and result.get('filename'):
            filename = result.get('filename')
            if Path(filename).exists():
                print(f"✅ 综合CSV抓取成功")
                print(f"  文件: {filename}")
                print(f"  大小: {Path(filename).stat().st_size:,} 字节")
                return True
            else:
                print(f"❌ 文件未找到: {filename}")
                return False
        else:
            print(f"❌ 综合抓取失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 综合测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 简单CSV抓取测试")
    print("=" * 80)
    
    # 测试1: 纯CSV抓取
    test1_result = test_csv_only()
    
    # 测试2: 综合CSV抓取
    test2_result = test_comprehensive_csv()
    
    # 总结
    print("\n🎉 测试总结")
    print("=" * 50)
    print(f"纯CSV抓取: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"综合CSV抓取: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎊 所有CSV测试通过！")
        print("💡 CSV抓取功能正常工作")
        
        # 显示生成的文件
        data_dir = Path("data")
        csv_files = list(data_dir.glob("六合彩数据_kjdata_*.csv"))
        if csv_files:
            print(f"\n📁 生成的CSV文件:")
            for csv_file in sorted(csv_files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]:
                size = csv_file.stat().st_size
                print(f"  {csv_file.name} ({size:,} 字节)")
    else:
        print("\n❌ 部分测试失败")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
