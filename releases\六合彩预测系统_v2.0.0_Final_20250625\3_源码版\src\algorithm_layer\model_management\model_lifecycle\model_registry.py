"""
模型注册中心 - 模型版本管理和元数据存储
"""
import json
import sqlite3
import pickle
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import joblib

class ModelRegistry:
    def __init__(self, registry_db_path: str = "data/model_registry.db"):
        """初始化模型注册中心"""
        self.registry_db_path = registry_db_path
        self.init_registry_db()
    
    def init_registry_db(self):
        """初始化注册中心数据库"""
        Path(self.registry_db_path).parent.mkdir(parents=True, exist_ok=True)
        
        conn = sqlite3.connect(self.registry_db_path)
        cursor = conn.cursor()
        
        # 创建模型注册表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS model_registry (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_name TEXT NOT NULL,
                model_version TEXT NOT NULL,
                model_type TEXT NOT NULL,
                model_path TEXT NOT NULL,
                model_hash TEXT NOT NULL,
                metadata TEXT,
                dependencies TEXT,
                status TEXT DEFAULT 'registered',
                performance_metrics TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(model_name, model_version)
            )
        """)
        
        # 创建模型依赖关系表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS model_dependencies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_id INTEGER,
                dependency_name TEXT NOT NULL,
                dependency_version TEXT NOT NULL,
                dependency_type TEXT NOT NULL,
                FOREIGN KEY (model_id) REFERENCES model_registry (id)
            )
        """)
        
        conn.commit()
        conn.close()
    
    def register_model(self, 
                      model_name: str,
                      model_version: str,
                      model_type: str,
                      model_object: Any,
                      metadata: Dict = None,
                      dependencies: List[Dict] = None,
                      performance_metrics: Dict = None) -> str:
        """注册模型"""
        
        # 保存模型文件
        model_dir = Path(f"models/{model_name}/{model_version}")
        model_dir.mkdir(parents=True, exist_ok=True)
        
        model_path = model_dir / "model.pkl"
        joblib.dump(model_object, model_path)
        
        # 计算模型哈希
        model_hash = self._calculate_model_hash(model_path)
        
        # 保存到数据库
        conn = sqlite3.connect(self.registry_db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                INSERT INTO model_registry 
                (model_name, model_version, model_type, model_path, model_hash, 
                 metadata, dependencies, performance_metrics)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                model_name, model_version, model_type, str(model_path), model_hash,
                json.dumps(metadata or {}),
                json.dumps(dependencies or []),
                json.dumps(performance_metrics or {})
            ))
            
            model_id = cursor.lastrowid
            
            # 保存依赖关系
            if dependencies:
                for dep in dependencies:
                    cursor.execute("""
                        INSERT INTO model_dependencies 
                        (model_id, dependency_name, dependency_version, dependency_type)
                        VALUES (?, ?, ?, ?)
                    """, (model_id, dep['name'], dep['version'], dep['type']))
            
            conn.commit()
            return f"{model_name}:{model_version}"
            
        except sqlite3.IntegrityError:
            raise ValueError(f"模型 {model_name}:{model_version} 已存在")
        finally:
            conn.close()
    
    def load_model(self, model_name: str, model_version: str = "latest") -> Any:
        """加载模型"""
        conn = sqlite3.connect(self.registry_db_path)
        cursor = conn.cursor()
        
        if model_version == "latest":
            cursor.execute("""
                SELECT model_path FROM model_registry 
                WHERE model_name = ? AND status = 'active'
                ORDER BY created_at DESC LIMIT 1
            """, (model_name,))
        else:
            cursor.execute("""
                SELECT model_path FROM model_registry 
                WHERE model_name = ? AND model_version = ?
            """, (model_name, model_version))
        
        result = cursor.fetchone()
        conn.close()
        
        if not result:
            raise ValueError(f"模型 {model_name}:{model_version} 不存在")
        
        return joblib.load(result[0])
    
    def get_model_info(self, model_name: str, model_version: str = None) -> Dict:
        """获取模型信息"""
        conn = sqlite3.connect(self.registry_db_path)
        cursor = conn.cursor()
        
        if model_version:
            cursor.execute("""
                SELECT * FROM model_registry 
                WHERE model_name = ? AND model_version = ?
            """, (model_name, model_version))
        else:
            cursor.execute("""
                SELECT * FROM model_registry 
                WHERE model_name = ?
                ORDER BY created_at DESC
            """, (model_name,))
        
        results = cursor.fetchall()
        conn.close()
        
        if not results:
            return {}
        
        columns = [desc[0] for desc in cursor.description]
        return [dict(zip(columns, row)) for row in results]
    
    def update_model_status(self, model_name: str, model_version: str, status: str):
        """更新模型状态"""
        conn = sqlite3.connect(self.registry_db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            UPDATE model_registry 
            SET status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE model_name = ? AND model_version = ?
        """, (status, model_name, model_version))
        
        conn.commit()
        conn.close()
    
    def list_models(self, status: str = None) -> List[Dict]:
        """列出所有模型"""
        conn = sqlite3.connect(self.registry_db_path)
        cursor = conn.cursor()
        
        if status:
            cursor.execute("""
                SELECT model_name, model_version, model_type, status, created_at
                FROM model_registry WHERE status = ?
                ORDER BY created_at DESC
            """, (status,))
        else:
            cursor.execute("""
                SELECT model_name, model_version, model_type, status, created_at
                FROM model_registry
                ORDER BY created_at DESC
            """)
        
        results = cursor.fetchall()
        conn.close()
        
        columns = ['model_name', 'model_version', 'model_type', 'status', 'created_at']
        return [dict(zip(columns, row)) for row in results]
    
    def _calculate_model_hash(self, model_path: Path) -> str:
        """计算模型文件哈希"""
        hash_md5 = hashlib.md5()
        with open(model_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
