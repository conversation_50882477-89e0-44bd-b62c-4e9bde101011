"""
农历年份管理器
"""
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple
import calendar
from loguru import logger

class LunarYearManager:
    """农历年份和生肖变更管理"""
    
    def __init__(self, db_session):
        self.db = db_session
        self.zodiac_cycle = ["鼠", "牛", "虎", "兔", "龙", "蛇", 
                            "马", "羊", "猴", "鸡", "狗", "猪"]
        self.wuxing_cycle = ["金", "木", "水", "火", "土"]
        
        # 春节日期数据 (部分年份，实际使用时需要完整数据)
        self.spring_festival_dates = {
            2020: date(2020, 1, 25),  # 鼠年
            2021: date(2021, 2, 12),  # 牛年
            2022: date(2022, 2, 1),   # 虎年
            2023: date(2023, 1, 22),  # 兔年
            2024: date(2024, 2, 10),  # 龙年
            2025: date(2025, 1, 29),  # 蛇年
            2026: date(2026, 2, 17),  # 马年
            2027: date(2027, 2, 6),   # 羊年
            2028: date(2028, 1, 26),  # 猴年
            2029: date(2029, 2, 13),  # 鸡年
            2030: date(2030, 2, 3),   # 狗年
        }
    
    def get_zodiac_by_year(self, year: int) -> str:
        """根据年份获取生肖"""
        # 以1984年为鼠年基准
        base_year = 1984
        zodiac_index = (year - base_year) % 12
        return self.zodiac_cycle[zodiac_index]
    
    def get_wuxing_by_year(self, year: int) -> str:
        """根据年份获取五行"""
        # 简化的五行计算，实际可能更复杂
        base_year = 1984
        wuxing_index = ((year - base_year) // 2) % 5
        return self.wuxing_cycle[wuxing_index]
    
    def get_spring_festival_date(self, year: int) -> date:
        """获取春节日期"""
        if year in self.spring_festival_dates:
            return self.spring_festival_dates[year]
        else:
            # 如果没有数据，使用估算方法
            logger.warning(f"年份 {year} 的春节日期数据缺失，使用估算")
            return self._estimate_spring_festival(year)
    
    def _estimate_spring_festival(self, year: int) -> date:
        """估算春节日期（简化方法）"""
        # 春节大致在1月21日到2月20日之间
        # 这里使用简化的估算，实际应该使用准确的农历计算
        base_date = date(year, 2, 1)
        return base_date
    
    def get_lunar_year_by_date(self, target_date: date) -> Dict:
        """根据公历日期获取对应的农历年份信息"""
        year = target_date.year
        
        # 获取当年和下一年的春节日期
        current_spring = self.get_spring_festival_date(year)
        next_spring = self.get_spring_festival_date(year + 1)
        
        # 判断属于哪个农历年
        if target_date >= current_spring:
            lunar_year = year
            year_start = current_spring
            year_end = next_spring - timedelta(days=1)
        else:
            lunar_year = year - 1
            prev_spring = self.get_spring_festival_date(year - 1)
            year_start = prev_spring
            year_end = current_spring - timedelta(days=1)
        
        zodiac = self.get_zodiac_by_year(lunar_year)
        wuxing = self.get_wuxing_by_year(lunar_year)
        zodiac_order = self.zodiac_cycle.index(zodiac) + 1
        
        return {
            'lunar_year': lunar_year,
            'zodiac_name': zodiac,
            'zodiac_order': zodiac_order,
            'wuxing_element': wuxing,
            'spring_festival_date': self.get_spring_festival_date(lunar_year),
            'year_start_date': year_start,
            'year_end_date': year_end
        }
    
    def initialize_lunar_years(self, start_year: int = 2000, end_year: int = 2050):
        """初始化农历年份映射数据"""
        from src.data_layer.database.models import LunarYearMapping
        
        logger.info(f"初始化农历年份数据: {start_year} - {end_year}")
        
        for year in range(start_year, end_year + 1):
            # 检查是否已存在
            existing = self.db.query(LunarYearMapping).filter_by(lunar_year=year).first()
            if existing:
                continue
            
            # 计算年份信息
            zodiac = self.get_zodiac_by_year(year)
            wuxing = self.get_wuxing_by_year(year)
            zodiac_order = self.zodiac_cycle.index(zodiac) + 1
            spring_festival = self.get_spring_festival_date(year)
            
            # 计算农历年起止日期
            year_start = spring_festival
            year_end = self.get_spring_festival_date(year + 1) - timedelta(days=1)
            
            # 创建记录
            lunar_mapping = LunarYearMapping(
                lunar_year=year,
                zodiac_name=zodiac,
                zodiac_order=zodiac_order,
                wuxing_element=wuxing,
                spring_festival_date=spring_festival,
                year_start_date=year_start,
                year_end_date=year_end
            )
            
            self.db.add(lunar_mapping)
        
        self.db.commit()
        logger.info("农历年份数据初始化完成")
    
    def get_zodiac_mapping_by_date(self, target_date: date) -> Dict[int, Dict]:
        """根据日期获取号码生肖映射"""
        lunar_year_info = self.get_lunar_year_by_date(target_date)
        lunar_year = lunar_year_info['lunar_year']
        
        # 生成1-49号码的生肖映射
        zodiac_mapping = {}
        for number in range(1, 50):
            # 简化的生肖映射算法：每12个号码一个循环
            zodiac_index = (number - 1) % 12
            zodiac_name = self.zodiac_cycle[zodiac_index]
            wuxing = self.get_wuxing_by_year(lunar_year)
            
            zodiac_mapping[number] = {
                'zodiac': zodiac_name,
                'wuxing': wuxing
            }
        
        return zodiac_mapping
    
    def is_year_transition_period(self, target_date: date, days_threshold: int = 7) -> bool:
        """检查是否处于年份变更期间"""
        year = target_date.year
        spring_festival = self.get_spring_festival_date(year)
        
        # 检查是否在春节前后指定天数内
        days_diff = abs((target_date - spring_festival).days)
        return days_diff <= days_threshold
    
    def get_year_transition_warning(self, target_date: date) -> Optional[str]:
        """获取年份变更警告信息"""
        if not self.is_year_transition_period(target_date):
            return None
        
        year = target_date.year
        spring_festival = self.get_spring_festival_date(year)
        days_diff = (spring_festival - target_date).days
        
        if days_diff > 0:
            return f"⚠️ 注意：{days_diff}天后将迎来春节，生肖属性将发生变更"
        elif days_diff == 0:
            return f"🎊 今天是春节，生肖属性已变更"
        else:
            return f"ℹ️ 春节已过{abs(days_diff)}天，请确认使用正确的生肖属性"
