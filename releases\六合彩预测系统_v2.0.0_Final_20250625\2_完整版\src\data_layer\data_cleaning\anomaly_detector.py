"""
异常数据检测器
"""
import pandas as pd
import numpy as np
from typing import Dict, List
from loguru import logger

class AnomalyDetector:
    def __init__(self):
        self.anomaly_rules = {
            'number_range': self._check_number_range,
            'date_sequence': self._check_date_sequence,
            'special_in_regular': self._check_special_in_regular
        }
    
    def detect_anomalies(self, df: pd.DataFrame) -> Dict:
        """检测异常数据"""
        anomalies = {
            'total_anomalies': 0,
            'anomaly_details': {},
            'anomaly_indices': set()
        }
        
        for rule_name, rule_func in self.anomaly_rules.items():
            try:
                rule_result = rule_func(df)
                anomalies['anomaly_details'][rule_name] = rule_result
                anomalies['anomaly_indices'].update(rule_result.get('indices', []))
            except Exception as e:
                logger.error(f"异常检测规则 {rule_name} 执行失败: {e}")
        
        anomalies['total_anomalies'] = len(anomalies['anomaly_indices'])
        anomalies['anomaly_indices'] = list(anomalies['anomaly_indices'])
        
        return anomalies
    
    def _check_number_range(self, df: pd.DataFrame) -> Dict:
        """检查号码范围异常"""
        anomalies = {'rule': 'number_range', 'indices': [], 'details': []}
        
        for index, row in df.iterrows():
            # 检查特码范围
            if 'special' in row:
                try:
                    special = int(row['special'])
                    if not (1 <= special <= 49):
                        anomalies['indices'].append(index)
                        anomalies['details'].append(f"特码超出范围: {special}")
                except:
                    anomalies['indices'].append(index)
                    anomalies['details'].append(f"特码格式错误: {row['special']}")
            
            # 检查正码范围
            if 'numbers' in row:
                try:
                    numbers_str = str(row['numbers']).replace('"', '').strip()
                    numbers = [int(x.strip()) for x in numbers_str.split(',')]
                    for num in numbers:
                        if not (1 <= num <= 49):
                            anomalies['indices'].append(index)
                            anomalies['details'].append(f"正码超出范围: {num}")
                            break
                except:
                    anomalies['indices'].append(index)
                    anomalies['details'].append(f"正码格式错误: {row['numbers']}")
        
        return anomalies
    
    def _check_date_sequence(self, df: pd.DataFrame) -> Dict:
        """检查日期序列异常"""
        anomalies = {'rule': 'date_sequence', 'indices': [], 'details': []}
        
        if 'date' not in df.columns:
            return anomalies
        
        try:
            dates = pd.to_datetime(df['date'])
            sorted_dates = dates.sort_values()
            
            # 检查日期间隔异常（超过30天）
            for i in range(1, len(sorted_dates)):
                diff = (sorted_dates.iloc[i] - sorted_dates.iloc[i-1]).days
                if diff > 30:
                    anomalies['indices'].append(sorted_dates.index[i])
                    anomalies['details'].append(f"日期间隔异常: {diff}天")
        except Exception as e:
            logger.error(f"日期序列检查失败: {e}")
        
        return anomalies
    
    def _check_special_in_regular(self, df: pd.DataFrame) -> Dict:
        """检查特码是否在正码中"""
        anomalies = {'rule': 'special_in_regular', 'indices': [], 'details': []}
        
        for index, row in df.iterrows():
            try:
                if 'special' in row and 'numbers' in row:
                    special = int(row['special'])
                    numbers_str = str(row['numbers']).replace('"', '').strip()
                    numbers = [int(x.strip()) for x in numbers_str.split(',')]
                    
                    if special in numbers:
                        anomalies['indices'].append(index)
                        anomalies['details'].append(f"特码{special}出现在正码中")
            except Exception as e:
                logger.warning(f"检查特码正码重复失败 {index}: {e}")
        
        return anomalies
