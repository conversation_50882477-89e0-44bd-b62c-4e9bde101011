"""
诊断融合管理器问题
"""

import sys
import os
from pathlib import Path

def diagnose_fusion_manager():
    """诊断融合管理器问题"""
    print("🔍 诊断融合管理器问题")
    print("=" * 50)
    
    # 切换到Windows版本目录
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    original_dir = Path.cwd()
    
    if not windows_dir.exists():
        print("❌ Windows版本目录不存在")
        return False
    
    try:
        os.chdir(windows_dir)
        
        # 添加路径
        sys.path.insert(0, str(Path.cwd()))
        sys.path.insert(0, str(Path.cwd() / "src"))
        
        print(f"📁 当前目录: {Path.cwd()}")
        
        # 1. 测试完美预测系统导入
        print("\n1️⃣ 测试完美预测系统导入...")
        try:
            from src.perfect_prediction_system import PerfectPredictionSystem
            print("✅ PerfectPredictionSystem 导入成功")
        except Exception as e:
            print(f"❌ PerfectPredictionSystem 导入失败: {e}")
            return False
        
        # 2. 测试完美预测系统创建
        print("\n2️⃣ 测试完美预测系统创建...")
        try:
            perfect_system = PerfectPredictionSystem()
            print("✅ PerfectPredictionSystem 创建成功")
        except Exception as e:
            print(f"❌ PerfectPredictionSystem 创建失败: {e}")
            return False
        
        # 3. 测试模块初始化
        print("\n3️⃣ 测试模块初始化...")
        try:
            perfect_system.initialize_modules()
            print("✅ 模块初始化成功")
        except Exception as e:
            print(f"❌ 模块初始化失败: {e}")
            print(f"详细错误: {type(e).__name__}: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 4. 测试融合管理器访问
        print("\n4️⃣ 测试融合管理器访问...")
        try:
            fusion_manager = perfect_system.fusion_manager
            if fusion_manager is not None:
                print("✅ 融合管理器访问成功")
                print(f"📊 融合管理器类型: {type(fusion_manager)}")
            else:
                print("❌ 融合管理器为 None")
                return False
        except Exception as e:
            print(f"❌ 融合管理器访问失败: {e}")
            return False
        
        # 5. 测试融合管理器方法
        print("\n5️⃣ 测试融合管理器方法...")
        try:
            # 测试关键方法
            methods_to_test = [
                'get_module_statistics',
                'show_config',
                'fuse_predictions_simple'
            ]
            
            for method_name in methods_to_test:
                if hasattr(fusion_manager, method_name):
                    print(f"  ✅ 方法 {method_name} 存在")
                else:
                    print(f"  ❌ 方法 {method_name} 不存在")
            
            # 测试统计信息获取
            stats = fusion_manager.get_module_statistics()
            if isinstance(stats, dict):
                print(f"  ✅ 统计信息获取成功 (模块数: {len(stats)})")
            else:
                print(f"  ❌ 统计信息格式错误: {type(stats)}")
                
        except Exception as e:
            print(f"❌ 融合管理器方法测试失败: {e}")
            return False
        
        # 6. 测试GUI初始化模拟
        print("\n6️⃣ 测试GUI初始化模拟...")
        try:
            # 模拟GUI初始化过程
            PERFECT_SYSTEM_AVAILABLE = True
            
            if PERFECT_SYSTEM_AVAILABLE:
                test_perfect_system = PerfectPredictionSystem()
                test_perfect_system.initialize_modules()
                test_fusion_manager = test_perfect_system.fusion_manager
                
                if test_fusion_manager is not None:
                    print("✅ GUI初始化模拟成功")
                else:
                    print("❌ GUI初始化模拟失败: 融合管理器为None")
                    return False
            else:
                print("❌ PERFECT_SYSTEM_AVAILABLE 为 False")
                return False
                
        except Exception as e:
            print(f"❌ GUI初始化模拟失败: {e}")
            print(f"这可能是GUI中出现问题的原因")
            import traceback
            traceback.print_exc()
            return False
        
        return True
        
    finally:
        os.chdir(original_dir)

def fix_fusion_manager_issue():
    """修复融合管理器问题"""
    print("\n🔧 修复融合管理器问题")
    print("=" * 50)
    
    windows_dir = Path("releases/六合彩预测系统_v2.0.0_20250624_Windows版")
    gui_file = windows_dir / "lottery_prediction_gui.py"
    
    if not gui_file.exists():
        print("❌ GUI文件不存在")
        return False
    
    try:
        # 读取文件内容
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复初始化代码，添加更详细的错误处理
        old_init_code = '''            # 初始化完美预测系统
            if PERFECT_SYSTEM_AVAILABLE:
                self.perfect_prediction_system = PerfectPredictionSystem()
                self.perfect_prediction_system.initialize_modules()
                self.fusion_manager = self.perfect_prediction_system.fusion_manager
                self.status_bar.showMessage("[OK] 完美预测系统初始化成功", 3000)
            else:
                self.perfect_prediction_system = None
                self.fusion_manager = None
                self.status_bar.showMessage("WARNING: 完美预测系统不可用，使用标准模式", 3000)'''
        
        new_init_code = '''            # 初始化完美预测系统
            if PERFECT_SYSTEM_AVAILABLE:
                try:
                    print("正在初始化完美预测系统...")
                    self.perfect_prediction_system = PerfectPredictionSystem()
                    print("完美预测系统创建成功，正在初始化模块...")
                    self.perfect_prediction_system.initialize_modules()
                    print("模块初始化成功，获取融合管理器...")
                    self.fusion_manager = self.perfect_prediction_system.fusion_manager
                    
                    if self.fusion_manager is not None:
                        print("融合管理器获取成功")
                        self.status_bar.showMessage("[OK] 完美预测系统初始化成功", 3000)
                    else:
                        print("WARNING: 融合管理器为None")
                        self.fusion_manager = None
                        self.status_bar.showMessage("WARNING: 融合管理器不可用", 3000)
                        
                except Exception as init_error:
                    print(f"ERROR: 完美预测系统初始化失败: {init_error}")
                    import traceback
                    traceback.print_exc()
                    self.perfect_prediction_system = None
                    self.fusion_manager = None
                    self.status_bar.showMessage(f"[ERROR] 完美预测系统初始化失败: {init_error}", 5000)
            else:
                self.perfect_prediction_system = None
                self.fusion_manager = None
                self.status_bar.showMessage("WARNING: 完美预测系统不可用，使用标准模式", 3000)'''
        
        if old_init_code in content:
            content = content.replace(old_init_code, new_init_code)
            print("✅ 初始化代码已修复")
        else:
            print("⚠️ 未找到目标代码，可能已经修改过")
        
        # 备份并保存
        backup_file = gui_file.with_suffix('.py.backup3')
        with open(backup_file, 'w', encoding='utf-8') as f:
            with open(gui_file, 'r', encoding='utf-8') as original:
                f.write(original.read())
        
        with open(gui_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"💾 文件已备份: {backup_file}")
        print("✅ 修复完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 融合管理器问题诊断和修复")
    print("=" * 70)
    
    # 1. 诊断问题
    success = diagnose_fusion_manager()
    
    if success:
        print("\n🎉 诊断完成：融合管理器功能正常！")
        print("问题可能在GUI初始化过程中的异常处理")
    else:
        print("\n❌ 诊断发现问题")
    
    # 2. 修复问题
    print("\n🔧 应用修复...")
    fix_success = fix_fusion_manager_issue()
    
    if fix_success:
        print("\n🎉 修复完成！")
        print("📋 下一步:")
        print("  1. 重新启动程序")
        print("  2. 查看控制台输出")
        print("  3. 测试融合管理器功能")
    else:
        print("\n❌ 修复失败")
    
    return success and fix_success

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
