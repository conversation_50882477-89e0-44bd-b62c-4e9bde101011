import numpy as np
from sklearn.ensemble import RandomForestClassifier
from collections import Counter
import warnings
warnings.filterwarnings("ignore")

class SmallDatasetHandler:
    def __init__(self):
        self.min_training_samples = 50
        
    def augment_data(self, X, y, target_size=100):
        """数据增强策略"""
        if len(X) >= target_size:
            return X, y
            
        print(f"🔧 数据增强: {len(X)} -> {target_size}")
        
        current_size = len(X)
        needed_samples = target_size - current_size
        
        # 计算特征标准差用于噪声注入
        feature_std = np.std(X, axis=0)
        noise_scale = 0.1
        
        # 随机选择样本进行复制
        indices = np.random.choice(current_size, needed_samples, replace=True)
        X_noise = X[indices].copy()
        y_noise = y[indices].copy()
        
        # 添加高斯噪声
        noise = np.random.normal(0, noise_scale * feature_std, X_noise.shape)
        X_noise += noise
        
        # 合并数据
        X_augmented = np.vstack([X, X_noise])
        y_augmented = np.hstack([y, y_noise])
        
        return X_augmented, y_augmented
    
    def get_smart_default_prediction(self, historical_data, n_predictions=16):
        """智能默认预测策略"""
        if not historical_data:
            # 没有历史数据时的均匀分布
            return list(range(1, n_predictions + 1))
        
        # 基于历史频率的智能预测
        freq_counter = Counter(historical_data)
        
        # 热号策略：最频繁的号码
        hot_numbers = [num for num, _ in freq_counter.most_common(n_predictions//2)]
        
        # 冷号回补策略：最不频繁的号码
        all_numbers = list(range(1, 50))
        cold_numbers = [num for num in all_numbers if num not in freq_counter][:n_predictions//4]
        
        # 中频号码：中等频率的号码
        medium_freq = [num for num, count in freq_counter.items() 
                      if count > 1 and num not in hot_numbers][:n_predictions//4]
        
        # 合并策略
        predictions = hot_numbers + cold_numbers + medium_freq
        
        # 补充到目标数量
        while len(predictions) < n_predictions:
            candidate = np.random.randint(1, 50)
            if candidate not in predictions:
                predictions.append(candidate)
        
        return sorted(predictions[:n_predictions])
    
    def robust_model_training(self, X, y, models_config):
        """鲁棒的模型训练策略"""
        trained_models = {}
        training_results = {}
        
        for model_name, config in models_config.items():
            try:
                model = config["model"]
                
                # 检查数据量是否足够
                if len(X) < 10:
                    print(f"⚠️ {model_name}: 数据量太少({len(X)}个)，跳过训练")
                    continue
                
                # 简单训练（不使用CV）
                model.fit(X, y)
                
                # 简单验证
                train_score = model.score(X, y)
                
                trained_models[model_name] = model
                training_results[model_name] = {
                    "train_score": train_score,
                    "status": "success"
                }
                
                print(f"✅ {model_name}: 训练成功, 训练得分={train_score:.3f}")
                
            except Exception as e:
                training_results[model_name] = {
                    "status": "failed",
                    "error": str(e)
                }
                print(f"❌ {model_name}: 训练失败 - {str(e)[:50]}...")
        
        return trained_models, training_results

# 测试小数据集处理策略
print("🧪 测试小数据集处理策略")
print("=" * 50)

handler = SmallDatasetHandler()

# 测试1: 数据增强
print("\n📊 测试1: 数据增强")
X_small = np.random.randn(25, 8)
y_small = np.random.randint(1, 50, 25)

print(f"原始数据: {X_small.shape[0]}样本")
X_aug, y_aug = handler.augment_data(X_small, y_small, target_size=80)
print(f"增强后数据: {X_aug.shape[0]}样本")

# 测试2: 智能默认预测
print("\n📊 测试2: 智能默认预测")
historical_data = [1, 5, 12, 23, 34, 45, 1, 12, 23, 5, 34, 45, 12, 23]
default_pred = handler.get_smart_default_prediction(historical_data, 16)
print(f"历史数据: {historical_data}")
print(f"智能预测: {default_pred}")

# 测试3: 鲁棒模型训练
print("\n📊 测试3: 鲁棒模型训练")
models_config = {
    "random_forest": {"model": RandomForestClassifier(n_estimators=10, random_state=42)},
    "random_forest_small": {"model": RandomForestClassifier(n_estimators=5, max_depth=3, random_state=42)}
}

trained_models, results = handler.robust_model_training(X_small, y_small, models_config)
print(f"成功训练的模型数量: {len(trained_models)}")

print("\n✅ 小数据集处理策略测试完成!")
