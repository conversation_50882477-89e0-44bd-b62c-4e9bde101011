import re

# 读取融合管理器文件
with open("src/fusion_manager.py", "r", encoding="utf-8") as f:
    content = f.read()

print("🔍 融合管理器权重配置分析")
print("=" * 50)

# 查找权重相关的方法
weight_methods = [
    "get_static_weights",
    "get_dynamic_weights", 
    "calculate_weights",
    "update_weights",
    "weights_used"
]

for method in weight_methods:
    if method in content:
        print(f"✅ 找到方法: {method}")
        
        # 提取方法内容
        start_idx = content.find(f"def {method}")
        if start_idx != -1:
            lines = content[start_idx:].split("\n")
            method_lines = []
            
            for i, line in enumerate(lines):
                if i > 0 and line.strip().startswith("def ") and method not in line:
                    break
                method_lines.append(line)
                if len(method_lines) > 15:  # 限制输出长度
                    break
            
            print(f"   方法内容:")
            for j, line in enumerate(method_lines[:10]):
                print(f"   {j+1:2d}: {line}")
            print()
    else:
        print(f"❌ 未找到方法: {method}")

# 查找权重字典定义
print("\n🔍 查找权重字典定义:")
weight_patterns = [
    r"weights\s*=\s*{[^}]*}",
    r"static_weights\s*=\s*{[^}]*}",
    r"default_weights\s*=\s*{[^}]*}"
]

for pattern in weight_patterns:
    matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
    if matches:
        print(f"✅ 找到权重定义:")
        for match in matches[:2]:  # 只显示前2个
            print(f"   {match[:100]}...")
    else:
        print(f"❌ 未找到权重定义模式: {pattern}")
