"""
测试统一年份映射模块
"""

import sys
from pathlib import Path

# 添加路径
sys.path.insert(0, str(Path.cwd()))
sys.path.insert(0, str(Path.cwd() / "src"))

from src.unified_year_mapping import year_mapper, get_lunar_year, get_zodiac_for_number, get_wuxing_for_number

def test_unified_mapping():
    """测试统一映射模块"""
    print("🧪 统一年份映射模块测试")
    print("=" * 60)
    
    # 测试关键日期的农历年份判断
    test_dates = [
        "2024-02-09",  # 春节前一天
        "2024-02-10",  # 春节当天
        "2025-01-28",  # 春节前一天
        "2025-01-29",  # 春节当天
        "2025-06-26"   # 当前日期
    ]
    
    print("📅 农历年份判断测试:")
    for test_date in test_dates:
        lunar_year = get_lunar_year(test_date)
        print(f"  {test_date}: {lunar_year}年")
    
    # 测试号码映射
    print("\n🔢 号码映射测试:")
    test_numbers = [1, 25, 37, 49]
    test_date = "2025-06-26"
    
    for number in test_numbers:
        zodiac = get_zodiac_for_number(number, test_date)
        wuxing = get_wuxing_for_number(number, test_date)
        print(f"  号码{number:02d} ({test_date}): {zodiac}/{wuxing}")
    
    # 测试跨年映射
    print("\n🔄 跨年映射测试:")
    cross_year_tests = [
        (25, "2024-02-09"),  # 春节前
        (25, "2024-02-10"),  # 春节当天
        (37, "2025-01-28"),  # 春节前
        (37, "2025-01-29"),  # 春节当天
    ]
    
    for number, date in cross_year_tests:
        zodiac = get_zodiac_for_number(number, date)
        wuxing = get_wuxing_for_number(number, date)
        lunar_year = get_lunar_year(date)
        print(f"  号码{number:02d} ({date}, {lunar_year}年): {zodiac}/{wuxing}")
    
    # 测试映射一致性
    print("\n✅ 映射一致性验证:")
    validation_result = year_mapper.validate_mapping_consistency("2025-06-26")
    for check, result in validation_result.items():
        status = "✅" if result else "❌"
        print(f"  {check}: {status}")
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    test_unified_mapping()
    input("\n按回车键退出...")
