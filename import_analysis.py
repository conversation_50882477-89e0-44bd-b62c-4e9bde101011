import re

# 读取完美预测系统文件
with open("src/perfect_prediction_system.py", "r", encoding="utf-8") as f:
    content = f.read()

print("🔍 完美预测系统模组导入分析")
print("=" * 60)

# 分析导入语句
import_lines = []
for line in content.split("\n"):
    if "import" in line and ("src." in line or "from src" in line):
        import_lines.append(line.strip())

print("📦 导入的模组:")
for i, line in enumerate(import_lines, 1):
    print(f"   {i}. {line}")

print("\n🏗️ 模组初始化分析:")

# 查找模组初始化
init_patterns = [
    (r"self\.traditional_module\s*=", "传统分析模组"),
    (r"self\.ml_module\s*=", "机器学习模组"),
    (r"self\.zodiac_extended_module\s*=", "生肖扩展模组"),
    (r"self\.special_zodiac_module\s*=", "特码生肖模组"),
    (r"self\.fusion_manager\s*=", "融合管理器")
]

for pattern, name in init_patterns:
    matches = re.findall(pattern, content)
    if matches:
        print(f"   ✅ {name}: 已初始化")
    else:
        print(f"   ❓ {name}: 未找到初始化")

# 查找预测方法调用
print("\n🎯 预测方法调用分析:")
prediction_patterns = [
    (r"\.predict\(", "预测方法调用"),
    (r"\.analyze\(", "分析方法调用"),
    (r"\.get_prediction\(", "获取预测方法"),
    (r"fusion_manager\.", "融合管理器调用")
]

for pattern, name in prediction_patterns:
    matches = re.findall(pattern, content)
    count = len(matches)
    if count > 0:
        print(f"   ✅ {name}: {count}次调用")
    else:
        print(f"   ❓ {name}: 未找到调用")
