"""
修复版历史数据导入脚本
"""
import sys
sys.path.insert(0, 'src')

import pandas as pd
from datetime import datetime
import sqlite3
import json
import os

def fixed_import():
    print("🔧 修复版历史数据导入")
    print("=" * 60)
    
    try:
        # 1. 读取CSV文件
        print("📊 读取历史数据文件...")
        df = pd.read_csv("历史数据.csv", encoding='utf-8')
        print(f"✅ 成功读取 {len(df)} 条记录")
        
        # 2. 确保数据目录存在
        os.makedirs("data", exist_ok=True)
        
        # 3. 连接数据库
        print("📊 连接数据库...")
        conn = sqlite3.connect("data/lottery.db")
        cursor = conn.cursor()
        
        # 4. 创建简化表结构
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lottery_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                period_number TEXT UNIQUE,
                draw_date DATE,
                regular_numbers TEXT,
                special_number INTEGER,
                natural_year INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 5. 导入数据
        print("�� 开始导入数据...")
        success_count = 0
        error_count = 0
        
        for index, row in df.iterrows():
            try:
                period_number = str(row['issue'])
                draw_date_str = str(row['date'])
                numbers_str = str(row['numbers']).replace('"', '').strip()
                special_number = int(row['special'])
                
                # 从日期提取年份
                draw_date = datetime.strptime(draw_date_str, '%Y-%m-%d')
                natural_year = draw_date.year
                
                # 验证正码
                regular_numbers = [int(x.strip()) for x in numbers_str.split(',')]
                if len(regular_numbers) != 6:
                    error_count += 1
                    continue
                
                # 插入数据
                cursor.execute('''
                    INSERT OR REPLACE INTO lottery_data 
                    (period_number, draw_date, regular_numbers, special_number, natural_year)
                    VALUES (?, ?, ?, ?, ?)
                ''', (period_number, draw_date_str, json.dumps(regular_numbers), special_number, natural_year))
                
                success_count += 1
                if success_count % 100 == 0:
                    print(f"   已导入 {success_count} 条记录...")
                    
            except Exception as e:
                error_count += 1
                print(f"   错误 {index}: {e}")
        
        # 6. 提交并验证
        conn.commit()
        
        # 验证导入结果
        cursor.execute("SELECT COUNT(*) FROM lottery_data")
        total_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT period_number, draw_date, special_number FROM lottery_data ORDER BY draw_date DESC LIMIT 5")
        latest_records = cursor.fetchall()
        
        conn.close()
        
        print(f"\n📊 导入完成:")
        print(f"   ✅ 成功: {success_count} 条")
        print(f"   ❌ 失败: {error_count} 条")
        print(f"   📊 成功率: {success_count/(success_count+error_count)*100:.1f}%")
        print(f"   📋 数据库总记录: {total_count} 条")
        
        print(f"\n📋 最新5条记录:")
        for record in latest_records:
            print(f"   {record[0]} | {record[1]} | 特码: {record[2]}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

if __name__ == "__main__":
    result = fixed_import()
    if result:
        print("\n🎊 数据导入成功！")
        print("现在可以使用真实历史数据进行预测了。")
    else:
        print("\n❌ 数据导入失败！")
