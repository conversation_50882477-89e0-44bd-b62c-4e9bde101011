"""
验证五行映射数据的准确性和完整性
"""

def verify_wuxing_mappings():
    """验证五行映射数据"""
    print("⚡ 五行映射数据验证工具")
    print("=" * 60)
    
    # 您提供的五行映射数据
    standard_wuxing = {
        "2023": {
            "金": [1, 2, 9, 10, 23, 24, 31, 32, 39, 40],
            "木": [5, 6, 13, 14, 21, 22, 35, 36, 43, 44],
            "水": [11, 12, 19, 20, 27, 28, 41, 42, 49],
            "火": [7, 8, 15, 16, 29, 30, 37, 38, 45, 46],
            "土": [3, 4, 17, 18, 25, 26, 33, 34, 47, 48]
        },
        "2024": {
            "金": [2, 3, 10, 11, 24, 25, 32, 33, 40, 41],
            "木": [6, 7, 14, 15, 22, 23, 36, 37, 44, 45],
            "水": [12, 13, 20, 21, 28, 29, 42, 43],
            "火": [1, 8, 9, 16, 17, 30, 31, 38, 39, 46, 47],
            "土": [4, 5, 18, 19, 26, 27, 34, 35, 48, 49]
        },
        "2025": {
            "金": [3, 4, 11, 12, 25, 26, 33, 34, 41, 42],
            "木": [7, 8, 15, 16, 23, 24, 37, 38, 45, 46],
            "水": [13, 14, 21, 22, 29, 30, 43, 44],
            "火": [1, 2, 9, 10, 17, 18, 31, 32, 39, 40, 47, 48],
            "土": [5, 6, 19, 20, 27, 28, 35, 36, 49]
        }
    }
    
    # 验证数据完整性
    for year in ["2023", "2024", "2025"]:
        print(f"\n🔍 验证 {year} 年五行数据:")
        print("-" * 40)
        
        year_data = standard_wuxing[year]
        all_numbers = set()
        
        # 检查每个五行的号码
        for element in ["金", "木", "水", "火", "土"]:
            numbers = year_data[element]
            count = len(numbers)
            
            # 检查号码范围
            valid_range = all(1 <= num <= 49 for num in numbers)
            
            # 检查重复
            has_duplicates = len(numbers) != len(set(numbers))
            
            print(f"  {element}: {count}个号码 - {numbers}")
            
            if not valid_range:
                print(f"    ❌ 包含无效号码 (应在1-49范围内)")
            
            if has_duplicates:
                print(f"    ❌ 包含重复号码")
            
            # 添加到总集合
            for num in numbers:
                if num in all_numbers:
                    print(f"    ❌ 号码 {num} 在多个五行中重复")
                all_numbers.add(num)
        
        # 检查总数
        total_count = len(all_numbers)
        missing_numbers = set(range(1, 50)) - all_numbers
        extra_numbers = all_numbers - set(range(1, 50))
        
        print(f"\n  📊 {year} 年统计:")
        print(f"    总号码数: {total_count}/49")
        
        if missing_numbers:
            print(f"    ❌ 缺失号码: {sorted(missing_numbers)}")
        
        if extra_numbers:
            print(f"    ❌ 超范围号码: {sorted(extra_numbers)}")
        
        if total_count == 49 and not missing_numbers and not extra_numbers:
            print(f"    ✅ 数据完整，覆盖所有1-49号码")

def analyze_wuxing_patterns():
    """分析五行变化规律"""
    print(f"\n🔀 五行变化规律分析")
    print("=" * 60)
    
    # 您提供的五行映射数据
    wuxing_data = {
        "2023": {
            "金": [1, 2, 9, 10, 23, 24, 31, 32, 39, 40],
            "木": [5, 6, 13, 14, 21, 22, 35, 36, 43, 44],
            "水": [11, 12, 19, 20, 27, 28, 41, 42, 49],
            "火": [7, 8, 15, 16, 29, 30, 37, 38, 45, 46],
            "土": [3, 4, 17, 18, 25, 26, 33, 34, 47, 48]
        },
        "2024": {
            "金": [2, 3, 10, 11, 24, 25, 32, 33, 40, 41],
            "木": [6, 7, 14, 15, 22, 23, 36, 37, 44, 45],
            "水": [12, 13, 20, 21, 28, 29, 42, 43],
            "火": [1, 8, 9, 16, 17, 30, 31, 38, 39, 46, 47],
            "土": [4, 5, 18, 19, 26, 27, 34, 35, 48, 49]
        },
        "2025": {
            "金": [3, 4, 11, 12, 25, 26, 33, 34, 41, 42],
            "木": [7, 8, 15, 16, 23, 24, 37, 38, 45, 46],
            "水": [13, 14, 21, 22, 29, 30, 43, 44],
            "火": [1, 2, 9, 10, 17, 18, 31, 32, 39, 40, 47, 48],
            "土": [5, 6, 19, 20, 27, 28, 35, 36, 49]
        }
    }
    
    print("🔍 年度变化规律:")
    
    # 分析每个五行的变化
    for element in ["金", "木", "水", "火", "土"]:
        print(f"\n  {element}:")
        
        numbers_2023 = set(wuxing_data["2023"][element])
        numbers_2024 = set(wuxing_data["2024"][element])
        numbers_2025 = set(wuxing_data["2025"][element])
        
        # 分析2023->2024的变化
        added_2024 = numbers_2024 - numbers_2023
        removed_2024 = numbers_2023 - numbers_2024
        
        # 分析2024->2025的变化
        added_2025 = numbers_2025 - numbers_2024
        removed_2025 = numbers_2024 - numbers_2025
        
        print(f"    2023: {sorted(wuxing_data['2023'][element])}")
        print(f"    2024: {sorted(wuxing_data['2024'][element])}")
        print(f"    2025: {sorted(wuxing_data['2025'][element])}")
        
        if added_2024 or removed_2024:
            print(f"    2023→2024: +{sorted(added_2024)} -{sorted(removed_2024)}")
        
        if added_2025 or removed_2025:
            print(f"    2024→2025: +{sorted(added_2025)} -{sorted(removed_2025)}")

def check_number_wuxing_mapping():
    """检查特定号码的五行归属"""
    print(f"\n🔢 号码五行归属检查")
    print("=" * 60)
    
    wuxing_data = {
        "2023": {
            "金": [1, 2, 9, 10, 23, 24, 31, 32, 39, 40],
            "木": [5, 6, 13, 14, 21, 22, 35, 36, 43, 44],
            "水": [11, 12, 19, 20, 27, 28, 41, 42, 49],
            "火": [7, 8, 15, 16, 29, 30, 37, 38, 45, 46],
            "土": [3, 4, 17, 18, 25, 26, 33, 34, 47, 48]
        },
        "2024": {
            "金": [2, 3, 10, 11, 24, 25, 32, 33, 40, 41],
            "木": [6, 7, 14, 15, 22, 23, 36, 37, 44, 45],
            "水": [12, 13, 20, 21, 28, 29, 42, 43],
            "火": [1, 8, 9, 16, 17, 30, 31, 38, 39, 46, 47],
            "土": [4, 5, 18, 19, 26, 27, 34, 35, 48, 49]
        },
        "2025": {
            "金": [3, 4, 11, 12, 25, 26, 33, 34, 41, 42],
            "木": [7, 8, 15, 16, 23, 24, 37, 38, 45, 46],
            "水": [13, 14, 21, 22, 29, 30, 43, 44],
            "火": [1, 2, 9, 10, 17, 18, 31, 32, 39, 40, 47, 48],
            "土": [5, 6, 19, 20, 27, 28, 35, 36, 49]
        }
    }
    
    # 测试一些关键号码
    test_numbers = [1, 7, 13, 25, 37, 49]
    
    for number in test_numbers:
        print(f"\n  号码 {number:02d} 的五行归属:")
        for year in ["2023", "2024", "2025"]:
            for element, numbers in wuxing_data[year].items():
                if number in numbers:
                    print(f"    {year}年: {element}")
                    break

def validate_wuxing_logic():
    """验证五行逻辑的合理性"""
    print(f"\n🧮 五行逻辑验证")
    print("=" * 60)
    
    # 检查是否存在明显的数学规律
    wuxing_2025 = {
        "金": [3, 4, 11, 12, 25, 26, 33, 34, 41, 42],
        "木": [7, 8, 15, 16, 23, 24, 37, 38, 45, 46],
        "水": [13, 14, 21, 22, 29, 30, 43, 44],
        "火": [1, 2, 9, 10, 17, 18, 31, 32, 39, 40, 47, 48],
        "土": [5, 6, 19, 20, 27, 28, 35, 36, 49]
    }
    
    print("🔍 2025年五行分布规律分析:")
    
    for element, numbers in wuxing_2025.items():
        print(f"\n  {element} ({len(numbers)}个):")
        print(f"    号码: {numbers}")
        
        # 分析数字模式
        pairs = []
        singles = []
        
        sorted_nums = sorted(numbers)
        i = 0
        while i < len(sorted_nums):
            if i + 1 < len(sorted_nums) and sorted_nums[i+1] == sorted_nums[i] + 1:
                pairs.append((sorted_nums[i], sorted_nums[i+1]))
                i += 2
            else:
                singles.append(sorted_nums[i])
                i += 1
        
        if pairs:
            print(f"    连续对: {pairs}")
        if singles:
            print(f"    单独数: {singles}")
        
        # 检查模运算规律
        mod_patterns = {}
        for num in numbers:
            for mod in [5, 7, 10]:
                remainder = num % mod
                if mod not in mod_patterns:
                    mod_patterns[mod] = {}
                if remainder not in mod_patterns[mod]:
                    mod_patterns[mod][remainder] = 0
                mod_patterns[mod][remainder] += 1
        
        # 显示明显的模运算规律
        for mod, pattern in mod_patterns.items():
            if len(pattern) <= 3:  # 如果余数种类很少，可能有规律
                print(f"    模{mod}余数: {pattern}")

def main():
    """主函数"""
    print("⚡ 五行映射数据完整性验证工具")
    print("=" * 80)
    
    # 执行各项验证
    verify_wuxing_mappings()
    analyze_wuxing_patterns()
    check_number_wuxing_mapping()
    validate_wuxing_logic()
    
    print(f"\n🎉 验证完成!")
    print(f"\n📋 验证总结:")
    print("  ✅ 检查了2023-2025年五行映射数据的完整性")
    print("  ✅ 分析了年度变化规律")
    print("  ✅ 验证了号码归属的一致性")
    print("  ✅ 检查了五行分布的逻辑性")
    
    print(f"\n💡 结论:")
    print("  如果所有检查都通过，说明您提供的五行数据是正确和完整的")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
