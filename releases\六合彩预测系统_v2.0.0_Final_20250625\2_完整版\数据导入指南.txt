
澳门六合彩数据导入指南

一、支持的数据格式
1. CSV文件 (.csv) - 推荐使用UTF-8编码
2. Excel文件 (.xlsx)
3. JSON文件 (.json)

二、标准列名格式
- draw_date: 开奖日期 (格式: YYYY-MM-DD)
- period_number: 期号 (如: 2024001)
- regular_1 到 regular_6: 正码1-6 (数字1-49)
- special_number: 特码 (数字1-49)

三、支持的列名变体
系统可以自动识别以下列名:

日期列:
- date, Date, 日期, 开奖日期, 开奖时间, draw_date, drawdate

期号列:
- period, Period, 期号, 期数, period_number, periodnumber, issue

正码列:
- 正码1-6, regular1-6, regular_1-6, num1-6, number1-6, 号码1-6

特码列:
- 特码, special, Special, special_number, specialnumber, 特别号码, 特号

四、导入方法
1. 标准导入: 数据列名完全符合标准格式
2. 智能导入: 系统自动识别和转换列名
3. 手动映射: 通过对话框手动指定列名对应关系

五、使用步骤
1. 准备数据文件 (可使用"生成模板"功能获取标准格式)
2. 在GUI中选择数据文件
3. 根据数据格式选择导入方式:
   - 列名标准: 直接"导入到数据库"
   - 列名不标准: 使用"智能导入"
   - 完全不匹配: 系统会弹出手动映射对话框
4. 确认导入结果

六、注意事项
- 日期格式必须为 YYYY-MM-DD
- 号码范围必须在 1-49 之间
- 期号建议使用唯一标识
- 正码不能重复，特码不能与正码重复
- 建议先使用"临时加载"预览数据

七、故障排除
- 如果提示"数据格式错误"，请检查列名是否正确
- 如果智能导入失败，请使用"生成模板"功能查看标准格式
- 如果数据验证失败，请检查号码范围和重复性

生成时间: 2025-06-22 12:39:03